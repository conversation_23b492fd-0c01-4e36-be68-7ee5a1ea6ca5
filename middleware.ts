import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();
  const { pathname } = url;

  // If the pathname doesn't have a trailing slash and it's not a file request (with extension)
  // Add a trailing slash for consistency
  if (!pathname.endsWith('/') && !pathname.includes('.') && pathname !== '/') {
    url.pathname = `${pathname}/`;
    return NextResponse.redirect(url);
  }

  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    // Match all paths except for:
    // - API routes
    // - Static files (images, js, css, etc)
    // - Favicon
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};

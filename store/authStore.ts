"use client";

import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

interface AuthState {
  isAuthenticated: boolean;
  accessToken: string | null;
  roles: string[];
  userEmail: string | null;
  employeeId: string | null;
  setLoginData: (
    loginData: {
      auth: { access_token: string; roles?: string[]; EmployeeId?: string };
      success: boolean;
    },
    email: string
  ) => void;
  logout: () => void;
}

const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      isAuthenticated: !!localStorage.getItem("token"),
      accessToken: localStorage.getItem("token") || null,
      roles: [],
      userEmail: localStorage.getItem("userEmail") || null,
      employeeId: null,

      setLoginData: (loginData, email) => {
        console.log("Setting login data in store:", loginData, email);
        localStorage.setItem("token", loginData.auth.access_token);
        localStorage.setItem("userEmail", email);
        set({
          isAuthenticated: loginData.success,
          accessToken: loginData.auth.access_token,
          roles: loginData.auth.roles || [],
          userEmail: email,
          employeeId: loginData.auth.EmployeeId || null,
        });
      },

      logout: () => {
        localStorage.removeItem("token");
        localStorage.removeItem("userEmail");
        set({
          isAuthenticated: false,
          accessToken: null,
          roles: [],
          userEmail: null,
          employeeId: null,
        });
      },
    }),
    {
      name: "auth-storage",
      storage: createJSONStorage(() => sessionStorage),
      partialize: (state) => ({
        roles: state.roles,
        employeeId: state.employeeId,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

export default useAuthStore;
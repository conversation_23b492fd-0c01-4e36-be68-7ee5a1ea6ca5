// This script helps with client-side routing in static exports on S3
(function() {
  // Only run this script if we're on a page that doesn't exist
  // (S3 will serve index.html for 404s if configured correctly)
  if (document.title === "404" || document.title.includes("Error")) {
    // Get the path from the URL
    const path = window.location.pathname;
    
    // Check if this is a known route pattern
    const isDashboardRoute = path.includes('/Dashboard/');
    const isEmployeesRoute = path.includes('/employees/');
    
    // Redirect to the appropriate route
    if (isDashboardRoute) {
      window.location.href = '/';
    } else if (isEmployeesRoute) {
      window.location.href = '/employees/';
    } else {
      // For other routes, go to home
      window.location.href = '/';
    }
  }
})();

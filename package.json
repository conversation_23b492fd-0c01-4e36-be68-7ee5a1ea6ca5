{"name": "hrms-fe", "version": "01.00.100-2025.04.16", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@mui/icons-material": "^6.4.4", "@mui/material": "^6.4.11", "@mui/x-charts": "^7.28.0", "@mui/x-data-grid": "^7.26.0", "@mui/x-date-pickers": "^7.27.3", "@mui/x-date-pickers-pro": "^7.27.0", "@toolpad/core": "^0.12.0", "@types/xlsx": "^0.0.35", "axios": "^1.7.9", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "firebase": "^11.8.1", "formik": "^2.4.6", "html2pdf.js": "^0.10.3", "jodit-react": "^5.2.19", "next": "^15.1.6", "react": "^19.0.0", "react-big-calendar": "^1.18.0", "react-d3-tree": "^3.6.6", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-loader-spinner": "^6.1.6", "react-toastify": "^11.0.5", "recharts": "^2.15.1", "sass": "^1.84.0", "xlsx": "^0.18.5", "yup": "^1.6.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "eslint": "^9", "eslint-config-next": "15.1.6", "eslint-plugin-react": "^7.37.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}
name: Deploy Next.js App to S3 on Develop Push

on:
  push:
    branches:
      - develop

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install dependencies
        run: npm install --legacy-peer-deps

      - name: Build the app
        run: npm run build

      - name: Deploy to S3
        uses: jakejarvis/s3-sync-action@master
        with:
          args: --delete
        env:
          AWS_S3_BUCKET: app-adv-dev-001
          AWS_ACCESS_KEY_ID: ********************
          AWS_SECRET_ACCESS_KEY: Mop1XNGr7B56W3Qal4096iE+QLIwkSt20baEDojw
          AWS_REGION: ap-south-1
          SOURCE_DIR: "build_dir"

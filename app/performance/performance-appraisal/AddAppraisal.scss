.dialog {

    label {
        font-size: 14px;
        font-weight: 500;
        color: #202C4B;
        margin-bottom: 0.5rem;
        margin-top: 0px !important;
        display: block;
    }

    .MuiInputBase-root {
        padding: 5.5px 14px !important;
        height: 36px !important;
        box-sizing: border-box;

        &.MuiInputBase-multiline {
            height: auto !important;
            padding: 5.5px 14px !important;
        }

        .MuiInputBase-input {
            padding: 0 !important;
            height: 100% !important;
            box-sizing: border-box;
        }
    }

    .MuiInputBase-root.MuiOutlinedInput-root.MuiSelect-root,
    .MuiInputBase-root.MuiOutlinedInput-root {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }

    .MuiFormControl-root {
        .MuiInputBase-root.MuiOutlinedInput-root {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
        }
    }

    .MuiAutocomplete-inputRoot {
        padding: 5.5px 14px !important;
        height: 36px !important;
        box-sizing: border-box;

        .MuiAutocomplete-input {
            padding: 0 !important;
            height: 100% !important;
        }
    }

    .MuiTextField-root,
    .MuiSelect-root,
    .MuiAutocomplete-root {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }

    .MuiTabs-root {
        margin-bottom: 1rem !important;
        min-height: fit-content !important;
        height: fit-content !important;

        .MuiTabs-indicator {
            display: none;
        }

        .MuiTabs-scroller {
            height: fit-content !important;
            min-height: fit-content !important;
        }

        .MuiTabs-flexContainer {
            height: fit-content !important;
            min-height: fit-content !important;
        }

        button {
            color: #111827;
            border: 1px solid #E5E7EB !important;
            border-radius: 0.375rem;
            margin-right: 0.5rem;
            text-transform: none;
            padding: 0.5rem 1rem;
            min-height: 39px;
            height: fit-content !important;
            font-weight: 400;
        }

        .Mui-selected {
            background-color: #3B7080;
            color: #FFF;
            border-radius: 0.375rem;
        }
    }


    .DataGrid-container {
        box-shadow: 0px 1px 1px 1px rgba(198, 198, 198, 0.2);
        border-radius: 5px;
        border: 1px solid #E5E7EB;
        // margin-top: 16px;

        .DataGrid-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;

            h5 {
                font-size: 18px;
                font-weight: 600;
            }

            .filter-dropdown {
                display: flex;
                gap: 13px;

                button {
                    font-weight: 400;
                    font-size: 14px;
                    color: #111827;
                    border: 1px solid #E5E7EB;
                    border-radius: 5px;
                    text-transform: none;
                    padding: 8px 13.6px;
                }

                .sort-dropdown {
                    background-color: #F26522;
                    border-color: #F26522;
                    color: #FFF
                }
            }
        }



        .MuiDataGrid-root {
            .MuiBox-root {
                display: flex;

                // SEARCH BAR FOR GRID-DATA
                .grid-search {

                    .MuiInputBase-root {
                        border: 1px solid #E5E7EB;
                        border-radius: 5px;


                        .MuiSvgIcon-root {
                            display: none;
                        }
                    }
                }

                .grid-export {
                    button {
                        font-weight: 400;
                        font-size: 14px;
                        color: #111827;
                        border: 1px solid #E5E7EB;
                        border-radius: 5px;
                        text-transform: none;
                        padding: 8px 13.6px;
                    }
                }
            }

            .MuiDataGrid-main {

                .MuiDataGrid-virtualScroller {
                    .MuiDataGrid-topContainer {
                        .MuiDataGrid-columnHeaders {

                            .MuiDataGrid-row--borderBottom {
                                background: #E5E7EB;

                                .MuiDataGrid-columnHeader {
                                    height: fit-content !important;
                                    padding: 0 !important;

                                    .MuiDataGrid-columnHeaderDraggableContainer {
                                        .MuiDataGrid-columnHeaderTitleContainer {
                                            .MuiDataGrid-columnHeaderTitleContainerContent {
                                                .MuiDataGrid-columnHeaderTitle {
                                                    color: #111827;
                                                    font-size: 14px;
                                                    font-weight: 600;
                                                    padding: 8px 20px;
                                                    background-color: #E5E7EB;

                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    .MuiDataGrid-virtualScrollerContent {
                        .MuiDataGrid-virtualScrollerRenderZone {
                            .MuiDataGrid-row {
                                .MuiDataGrid-cell {
                                    padding: 8px 20px !important;

                                    &.MuiDataGrid-cell--textLeft {
                                        font-size: 14px;
                                        color: #111827;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .MuiDataGrid-footerContainer {
                .MuiTablePagination-root {
                    .MuiToolbar-root {
                        display: flex;
                        justify-content: center;

                        .MuiTablePagination-spacer {
                            display: none;
                        }
                    }
                }
            }
        }

    }
}
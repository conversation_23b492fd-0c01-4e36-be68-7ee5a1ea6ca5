"use client";

import { useState, useEffect, useMemo, useRef } from "react";
import "./AddAppraisal.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Box,
  FormControl,
  Divider,
  Typography,
  SelectChangeEvent,
  FormHelperText,
} from "@mui/material";
import {
  GridRowsProp,
  GridColDef,
  useGridApiRef,
  GridRowModel,
} from "@mui/x-data-grid";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import { Cancel } from "@mui/icons-material";
import { getUsers } from "@/app/services/users.service";
import {
  getPerformanceAppraisalById,
  getPerformanceIndicatorByDesignationId,
} from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import useAuthStore from "@/store/authStore";

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  designationId: string;
  isActive: boolean;
}

interface ApiResponse {
  success: boolean;
  statusCode: number;
  message: string;
  users: {
    results: User[];
    total: number;
    activeCount: number;
    inactiveCount: number;
    newJoiners: number;
    page: string;
    limit: string;
    totalPages: number;
  };
  meta: {
    version: string;
    forceUpdate: boolean;
    maintenance: boolean;
    hasUpdate: boolean;
  };
}

interface AppraisalCompetency {
  indicator: string;
  indicatorName?: string;
  expectedValue?: string;
  setValue: string;
}

interface AppraisalData {
  _id: string;
  empId: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  appraisalDate: string;
  competencies: AppraisalCompetency[];
  isActive: boolean;
}

// interface AddAppraisalProps {
//   open: boolean;
//   onClose: () => void;
//   onSubmit: (formData: FormData) => void;
//   mode?: "add" | "edit";
//   selectedAppraisalId?: string | null;
// }

const AddAppraisal = ({
  open,
  onClose,
  onSubmit,
  mode = "add",
  selectedAppraisalId = null,
}: {
  open: boolean;
  onClose: () => void;
  onSubmit: (formData: FormData) => void;
  mode?: "add" | "edit";
  selectedAppraisalId?: string | null;
}) => {
  const [tabIndex, setTabIndex] = useState(0);
  const [users, setUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [errorUsers, setErrorUsers] = useState<string | null>(null);
  const apiRef = useGridApiRef();
  const [loading, setLoading] = useState(false);

  const [rowsTec, setRowsTec] = useState<GridRowsProp>([
    {
      id: 1,
      indicator: "Customer Experience",
      expectedValue: "",
      setValue: "",
    },
    { id: 2, indicator: "Marketing", expectedValue: "", setValue: "" },
    { id: 3, indicator: "Management", expectedValue: "", setValue: "" },
    { id: 4, indicator: "Administration", expectedValue: "", setValue: "" },
    { id: 5, indicator: "Presentation Skill", expectedValue: "", setValue: "" },
    { id: 6, indicator: "Quality of Work", expectedValue: "", setValue: "" },
    { id: 7, indicator: "Efficiency", expectedValue: "", setValue: "" },
  ]);

  const [rowsOrg, setRowsOrg] = useState<GridRowsProp>([
    { id: 1, indicator: "Integrity", expectedValue: "", setValue: "" },
    { id: 2, indicator: "Professionalism", expectedValue: "", setValue: "" },
    { id: 3, indicator: "Teamwork", expectedValue: "", setValue: "" },
    { id: 4, indicator: "Critical Thinking", expectedValue: "", setValue: "" },
    { id: 5, indicator: "Attendance", expectedValue: "", setValue: "" },
    { id: 6, indicator: "Meet Deadline", expectedValue: "", setValue: "" },
    {
      id: 7,
      indicator: "Conflict Management",
      expectedValue: "",
      setValue: "",
    },
  ]);

  const [tecValidationError, setTecValidationError] = useState<string | null>(
    null
  );
  const [orgValidationError, setOrgValidationError] = useState<string | null>(
    null
  );

  const [initialData, setInitialData] = useState<AppraisalData | null>(null);

  const reverseCompetencyMap: { [key: string]: string } = {
    customerExperience: "Customer Experience",
    marketing: "Marketing",
    management: "Management",
    administration: "Administration",
    presentationSkill: "Presentation Skill",
    qualityWork: "Quality of Work",
    efficiency: "Efficiency",
    integrity: "Integrity",
    professionalism: "Professionalism",
    teamWork: "Teamwork",
    criticalThinking: "Critical Thinking",
    attendence: "Attendance",
    meetDedline: "Meet Deadline",
    conflictManagement: "Conflict Management",
  };

  // Reset function to clear form data
  const resetFormData = () => {
    setInitialData(null);
    setRowsTec([
      {
        id: 1,
        indicator: "Customer Experience",
        expectedValue: "",
        setValue: "",
      },
      { id: 2, indicator: "Marketing", expectedValue: "", setValue: "" },
      { id: 3, indicator: "Management", expectedValue: "", setValue: "" },
      { id: 4, indicator: "Administration", expectedValue: "", setValue: "" },
      {
        id: 5,
        indicator: "Presentation Skill",
        expectedValue: "",
        setValue: "",
      },
      { id: 6, indicator: "Quality of Work", expectedValue: "", setValue: "" },
      { id: 7, indicator: "Efficiency", expectedValue: "", setValue: "" },
    ]);
    setRowsOrg([
      { id: 1, indicator: "Integrity", expectedValue: "", setValue: "" },
      { id: 2, indicator: "Professionalism", expectedValue: "", setValue: "" },
      { id: 3, indicator: "Teamwork", expectedValue: "", setValue: "" },
      {
        id: 4,
        indicator: "Critical Thinking",
        expectedValue: "",
        setValue: "",
      },
      { id: 5, indicator: "Attendance", expectedValue: "", setValue: "" },
      { id: 6, indicator: "Meet Deadline", expectedValue: "", setValue: "" },
      {
        id: 7,
        indicator: "Conflict Management",
        expectedValue: "",
        setValue: "",
      },
    ]);
  };

  useEffect(() => {
    if (!open) {
      resetFormData();
    }
  }, [open]);

  useEffect(() => {
    const fetchAppraisalData = async () => {
      if (mode === "edit" && selectedAppraisalId && open) {
        resetFormData(); // Reset before fetching new data
        try {
          const response =
            await getPerformanceAppraisalById(selectedAppraisalId);
          if (response.success) {
            // Set initial data first
            setInitialData(response.appraisal);

            // Update technical competencies
            const updatedRowsTec = rowsTec.map((row) => {
              const competency = response.appraisal.competencies.find(
                (c: AppraisalCompetency) =>
                  reverseCompetencyMap[c.indicator] === row.indicator ||
                  c.indicatorName === row.indicator
              );
              return competency
                ? { ...row, setValue: competency.setValue }
                : row;
            });
            setRowsTec(updatedRowsTec);

            // Update organizational competencies
            const updatedRowsOrg = rowsOrg.map((row) => {
              const competency = response.appraisal.competencies.find(
                (c: AppraisalCompetency) =>
                  reverseCompetencyMap[c.indicator] === row.indicator ||
                  c.indicatorName === row.indicator
              );
              return competency
                ? { ...row, setValue: competency.setValue }
                : row;
            });
            setRowsOrg(updatedRowsOrg);
          }
        } catch (error) {
          console.error("Error fetching appraisal data:", error);
          // toast.error("Failed to fetch appraisal details");
        }
      }
    };

    fetchAppraisalData();
  }, [mode, selectedAppraisalId, open]);

  const { employeeId: currentUserId } = useAuthStore();

  useEffect(() => {
    const fetchUsers = async () => {
      setLoadingUsers(true);
      try {
        const response: ApiResponse = await getUsers();
        if (response.success) {
          // Filter out the current user from the list
          const filteredUsers = response.users.results.filter(
            (user: User) => user._id !== currentUserId
          );
          setUsers(filteredUsers);
        } else {
          setErrorUsers("Failed to fetch users");
        }
      } catch (error) {
        setErrorUsers("Error fetching users");
        console.error("Error fetching users:", error);
      } finally {
        setLoadingUsers(false);
      }
    };

    if (open) {
      fetchUsers();
    }
  }, [open, currentUserId]);

  const validateCompetencies = (
    rows: GridRowsProp,
    setError: (error: string | null) => void
  ) => {
    const emptyFields = rows
      .filter((row) => !row.setValue)
      .map((row) => row.indicator);
    if (emptyFields.length > 0) {
      setError(`Please set values for: ${emptyFields.join(", ")}`);
      return false;
    }
    setError(null);
    return true;
  };

  const renderCellHandler = (
    params: { row: GridRowModel },
    rows: GridRowsProp,
    setRows: React.Dispatch<React.SetStateAction<GridRowsProp>>
  ) => {
    const handleChange = (event: SelectChangeEvent) => {
      const newValue = event.target.value as string;
      const updatedRows = rows.map((row) =>
        row.id === params.row.id ? { ...row, setValue: newValue } : row
      );
      setRows(updatedRows);
    };

    return (
      <FormControl variant="outlined" fullWidth>
        <Select value={params.row.setValue} onChange={handleChange}>
          <MenuItem value="None">None</MenuItem>
          <MenuItem value="Excellent">Beginner</MenuItem>
          <MenuItem value="Average">Average</MenuItem>
          <MenuItem value="Good">Intermediate</MenuItem>
          <MenuItem value="Fair">Advanced</MenuItem>
          <MenuItem value="Poor">Expert</MenuItem>
        </Select>
      </FormControl>
    );
  };

  const columnsTec: GridColDef[] = [
    {
      field: "indicator",
      headerName: "Indicator",
      editable: false,
      flex: 1,
      minWidth: 310.64,
      renderCell: (params) => (
        <Typography
          sx={{ fontSize: "14px", fontWeight: "400", color: "#6B7280" }}
        >
          {params.row.indicator}
        </Typography>
      ),
    },
    {
      field: "expectedValue",
      headerName: "Expected Value",
      editable: false,
      flex: 1,
      minWidth: 246.17,
      renderCell: (params) => (
        <Typography
          sx={{ fontSize: "14px", fontWeight: "400", color: "#6B7280" }}
        >
          {params.row.expectedValue}
        </Typography>
      ),
    },
    {
      field: "setValue",
      headerName: "Set Value",
      editable: true,
      flex: 1,
      renderCell: (params) => renderCellHandler(params, rowsTec, setRowsTec),
    },
  ];

  const columnsOrg: GridColDef[] = [
    {
      field: "indicator",
      headerName: "Indicator",
      editable: false,
      flex: 1,
      minWidth: 328.39,
      renderCell: (params) => (
        <Typography
          sx={{ fontSize: "14px", fontWeight: "400", color: "#6B7280" }}
        >
          {params.row.indicator}
        </Typography>
      ),
    },
    {
      field: "expectedValue",
      headerName: "Expected Value",
      editable: false,
      flex: 1,
      minWidth: 236.55,
      renderCell: (params) => (
        <Typography
          sx={{ fontSize: "14px", fontWeight: "400", color: "#6B7280" }}
        >
          {params.row.expectedValue}
        </Typography>
      ),
    },
    {
      field: "setValue",
      headerName: "Set Value",
      editable: true,
      flex: 1,
      renderCell: (params) => renderCellHandler(params, rowsOrg, setRowsOrg),
    },
  ];

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  const validationSchema = Yup.object().shape({
    empId: Yup.string().required("Employee is required"),
    appraisalDate: Yup.string().required("Appraisal date is required"),
    status: Yup.string()
      .required("Status is required")
      .oneOf(["Active", "InActive"], "Invalid status"),
  });

  const initialValues = useMemo(() => {
    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split("T")[0];

    return {
      empId: initialData?.empId?._id || "",
      appraisalDate: initialData?.appraisalDate
        ? new Date(initialData.appraisalDate).toISOString().split("T")[0]
        : today, // Default to today's date
      status:
        initialData?.isActive !== undefined
          ? initialData.isActive
            ? "Active"
            : "InActive"
          : "Active", // Default to Active
    };
  }, [initialData]);

  const handleSubmit = (values: typeof initialValues) => {
    const technicalCompetenciesValid = validateCompetencies(
      rowsTec,
      setTecValidationError
    );
    const organizationalCompetenciesValid = validateCompetencies(
      rowsOrg,
      setOrgValidationError
    );

    if (!technicalCompetenciesValid || !organizationalCompetenciesValid) {
      return;
    }

    const competencyMap: { [key: string]: string } = {
      "Customer Experience": "customerExperience",
      Marketing: "marketing",
      Management: "management",
      Administration: "administration",
      "Presentation Skill": "presentationSkill",
      "Quality of Work": "qualityWork",
      Efficiency: "efficiency",
      Integrity: "integrity",
      Professionalism: "professionalism",
      Teamwork: "teamWork",
      "Critical Thinking": "criticalThinking",
      Attendance: "attendence",
      "Meet Deadline": "meetDedline",
      "Conflict Management": "conflictManagement",
    };

    // Include indicator name, expected value, and set value in the competencies
    const competencies = [
      ...rowsTec.map((row) => ({
        competencyKey: competencyMap[row.indicator],
        indicatorName: row.indicator, // Human-readable name (e.g., "Customer Experience")
        expectedValue: row.expectedValue || "", // Value from performance indicator (e.g., "Advanced")
        setValue: row.setValue || "", // Value set during appraisal (e.g., "Excellent")
      })),
      ...rowsOrg.map((row) => ({
        competencyKey: competencyMap[row.indicator],
        indicatorName: row.indicator, // Human-readable name (e.g., "Integrity")
        expectedValue: row.expectedValue || "", // Value from performance indicator (e.g., "Advanced")
        setValue: row.setValue || "", // Value set during appraisal (e.g., "Good")
      })),
    ];

    const formData = new FormData();
    formData.append("empId", values.empId);
    formData.append(
      "designationId",
      users.find((user) => user._id === values.empId)?.designationId || ""
    );
    formData.append("appraisalDate", values.appraisalDate);
    formData.append("competencies", JSON.stringify(competencies));
    formData.append("isActive", values.status === "Active" ? "true" : "false");

    // Log the data being sent to verify all three fields are included
    console.log("Sending competencies:", competencies);

    onSubmit(formData);
    onClose();
  };

  interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
  }

  function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;
    return (
      <div
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box>{children}</Box>}
      </div>
    );
  }

  function a11yProps(index: number) {
    return {
      id: `simple-tab-${index}`,
      "aria-controls": `simple-tabpanel-${index}`,
    };
  }

  // Add a function to fetch performance indicator data for an employee
  const fetchEmployeeIndicatorData = async (employeeId: string) => {
    try {
      setLoading(true);

      // First, find the employee to get their designation
      const selectedEmployee = users.find((user) => user._id === employeeId);
      if (!selectedEmployee || !selectedEmployee.designationId) {
        toast.error("Employee designation information not found");
        return;
      }

      // Get the designation ID (handle both string and object cases)
      let designationId = "";
      if (
        typeof selectedEmployee.designationId === "object" &&
        selectedEmployee.designationId !== null &&
        "_id" in selectedEmployee.designationId
      ) {
        designationId = (selectedEmployee.designationId as { _id: string })._id;
      } else if (typeof selectedEmployee.designationId === "string") {
        designationId = selectedEmployee.designationId;
      }

      if (!designationId) {
        toast.error("Employee designation ID not found");
        return;
      }

      console.log(
        "Fetching performance indicators for designation:",
        designationId
      );

      // Now fetch the performance indicator for this designation - regardless of active status
      const response =
        await getPerformanceIndicatorByDesignationId(designationId);

      if (response.success && response.performance) {
        const indicator = response.performance;
        console.log("Retrieved performance indicators:", indicator);

        // Map the API response to our row structure
        const updatedRowsTec = rowsTec.map((row) => {
          let expectedValue = "";

          // Map indicator names to API field names
          switch (row.indicator) {
            case "Customer Experience":
              expectedValue = indicator.customerExperience || "";
              break;
            case "Marketing":
              expectedValue = indicator.marketing || "";
              break;
            case "Management":
              expectedValue = indicator.management || "";
              break;
            case "Administration":
              expectedValue = indicator.administration || "";
              break;
            case "Presentation Skill":
              expectedValue = indicator.presentationSkill || "";
              break;
            case "Quality of Work":
              expectedValue = indicator.qualityWork || "";
              break;
            case "Efficiency":
              expectedValue = indicator.efficiency || "";
              break;
          }

          return { ...row, expectedValue };
        });

        const updatedRowsOrg = rowsOrg.map((row) => {
          let expectedValue = "";

          // Map indicator names to API field names
          switch (row.indicator) {
            case "Integrity":
              expectedValue = indicator.integrity || "";
              break;
            case "Professionalism":
              expectedValue = indicator.professionalism || "";
              break;
            case "Teamwork":
              expectedValue = indicator.teamWork || "";
              break;
            case "Critical Thinking":
              expectedValue = indicator.criticalThinking || "";
              break;
            case "Attendance":
              expectedValue = indicator.attendence || "";
              break;
            case "Meet Deadline":
              expectedValue = indicator.meetDedline || "";
              break;
            case "Conflict Management":
              expectedValue = indicator.conflictManagement || "";
              break;
          }

          return { ...row, expectedValue };
        });

        setRowsTec(updatedRowsTec);
        setRowsOrg(updatedRowsOrg);
      } else {
        console.error(
          "No performance indicators found or invalid response format:",
          response
        );
        toast.warning(
          "No performance indicators found for this employee's designation"
        );
      }
    } catch (error) {
      console.error(
        "Error fetching performance indicators for designation:",
        error
      );
      toast.error(
        "Failed to fetch performance indicators for this employee's designation"
      );
    } finally {
      setLoading(false);
    }
  };

  // Add this effect at the component level
  useEffect(() => {
    if (open && initialValues.empId) {
      fetchEmployeeIndicatorData(initialValues.empId);
    }
  }, [open, initialValues.empId]);

  return (
    <Dialog
      className="dialog"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth={false}
      sx={{
        "& .MuiDialog-paper": {
          maxWidth: "800px",
          margin: "auto",
        },
      }}
    >
      <DialogTitle sx={{ display: "flex", justifyContent: "space-between" }}>
        {mode === "edit" ? "Edit Appraisal" : "Add Appraisal"}
        <Cancel
          onClick={onClose}
          sx={{
            color: "#6B7280",
            fontSize: "20px",
            ":hover": { color: "#D93D3D!important" },
          }}
        />
      </DialogTitle>
      <DialogContent>
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({
            values,
            handleChange,
            submitForm,
            errors,
            touched,
            setFieldValue,
            isSubmitting,
          }) => {
            // Remove the useEffect from here

            return (
              <Form>
                <Box display="flex" gap={2} sx={{ marginBottom: "1rem" }}>
                  <FormControl
                    fullWidth
                    sx={{
                      display: "flex !important",
                      flexDirection: "row",
                      gap: 2,
                    }}
                    error={touched.empId && Boolean(errors.empId)}
                  >
                    <Box sx={{ width: "100%" }}>
                      <label>Employee</label>
                      <Select
                        sx={{ minWidth: "100%" }}
                        name="empId"
                        value={values.empId}
                        onChange={(e) => {
                          handleChange(e);
                          // Call API to fetch performance indicators when employee changes
                          if (e.target.value) {
                            fetchEmployeeIndicatorData(
                              e.target.value as string
                            );
                          }
                          // Reset the rows when employee changes
                          setRowsTec([
                            {
                              id: 1,
                              indicator: "Customer Experience",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 2,
                              indicator: "Marketing",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 3,
                              indicator: "Management",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 4,
                              indicator: "Administration",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 5,
                              indicator: "Presentation Skill",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 6,
                              indicator: "Quality of Work",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 7,
                              indicator: "Efficiency",
                              expectedValue: "",
                              setValue: "",
                            },
                          ]);
                          setRowsOrg([
                            {
                              id: 1,
                              indicator: "Integrity",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 2,
                              indicator: "Professionalism",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 3,
                              indicator: "Teamwork",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 4,
                              indicator: "Critical Thinking",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 5,
                              indicator: "Attendance",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 6,
                              indicator: "Meet Deadline",
                              expectedValue: "",
                              setValue: "",
                            },
                            {
                              id: 7,
                              indicator: "Conflict Management",
                              expectedValue: "",
                              setValue: "",
                            },
                          ]);
                        }}
                        disabled={mode === "edit" || loading}
                      >
                        {loadingUsers && (
                          <MenuItem value="">Loading...</MenuItem>
                        )}
                        {errorUsers && (
                          <MenuItem value="">Error loading users</MenuItem>
                        )}
                        {!loadingUsers && !errorUsers && users.length === 0 && (
                          <MenuItem value="">No users found</MenuItem>
                        )}
                        {users.map((user) => (
                          <MenuItem key={user._id} value={user._id}>
                            {`${user.firstName} ${user.lastName}`}
                          </MenuItem>
                        ))}
                      </Select>
                      {touched.empId && errors.empId && (
                        <FormHelperText error>
                          {typeof errors.empId === "string"
                            ? errors.empId
                            : "Invalid input"}
                        </FormHelperText>
                      )}
                    </Box>

                    <Box sx={{ width: "100%" }}>
                      <label>Appraisal Date</label>
                      <TextField
                        fullWidth
                        type="date"
                        name="appraisalDate"
                        InputLabelProps={{ shrink: true }}
                        value={values.appraisalDate}
                        onChange={handleChange}
                        error={
                          touched.appraisalDate && Boolean(errors.appraisalDate)
                        }
                        helperText={
                          touched.appraisalDate && errors.appraisalDate
                        }
                      />
                      {touched.appraisalDate && errors.appraisalDate && (
                        <FormHelperText error>
                          {typeof errors.appraisalDate === "string"
                            ? errors.appraisalDate
                            : "Invalid input"}
                        </FormHelperText>
                      )}
                    </Box>
                  </FormControl>
                </Box>

                <Box sx={{ width: "100%" }}>
                  <Tabs
                    value={tabIndex}
                    onChange={handleTabChange}
                    aria-label="basic tabs example"
                    sx={{ marginBottom: "0px" }}
                  >
                    <Tab label="Technical" {...a11yProps(0)} />
                    <Tab label="Organizational" {...a11yProps(1)} />
                  </Tabs>
                  <CustomTabPanel value={tabIndex} index={0}>
                    <Box sx={{ padding: "0px !important" }}>
                      <Box
                        className="DataGrid-container"
                        sx={{
                          padding: "0px !important",
                          display: "flex",
                          flexDirection: "column",
                          minHeight: 477.19,
                          maxHeight: "calc(100vh - 300px)",
                        }}
                      >
                        <Box className="DataGrid-header">
                          <Typography variant="h5">
                            Technical Competencies
                          </Typography>
                        </Box>
                        <Divider />
                        <CustomDataGrid
                          apiRef={apiRef}
                          disableRowSelectionOnClick
                          disableColumnFilter
                          disableColumnSelector
                          disableDensitySelector
                          hideFooter
                          rows={rowsTec}
                          columns={columnsTec}
                          autoHeight // Add this prop
                          sx={{
                            width: "100%",
                            "& .MuiDataGrid-main": {
                              overflow: "hidden",
                            },
                          }}
                        />
                        {tecValidationError && (
                          <FormHelperText error sx={{ mt: 1, ml: 2 }}>
                            {tecValidationError}
                          </FormHelperText>
                        )}
                      </Box>
                    </Box>
                  </CustomTabPanel>
                  <CustomTabPanel value={tabIndex} index={1}>
                    <Box>
                      <Box
                        className="DataGrid-container"
                        sx={{
                          padding: "0px !important",
                          display: "flex",
                          flexDirection: "column",
                          minHeight: 477.19,
                          maxHeight: "calc(100vh - 300px)",
                        }}
                      >
                        <Box className="DataGrid-header">
                          <Typography variant="h5">
                            Organizational Competencies
                          </Typography>
                        </Box>
                        <Divider />
                        <CustomDataGrid
                          apiRef={apiRef}
                          disableRowSelectionOnClick
                          disableColumnFilter
                          disableColumnSelector
                          disableDensitySelector
                          hideFooter
                          rows={rowsOrg}
                          columns={columnsOrg}
                          autoHeight // Add this prop
                          sx={{
                            width: "100%",
                            "& .MuiDataGrid-main": {
                              overflow: "hidden",
                            },
                          }}
                        />
                        {orgValidationError && (
                          <FormHelperText error sx={{ mt: 1, ml: 2 }}>
                            {orgValidationError}
                          </FormHelperText>
                        )}
                      </Box>
                    </Box>
                  </CustomTabPanel>
                </Box>

                <FormControl
                  fullWidth
                  sx={{ mt: 2 }}
                  error={touched.status && Boolean(errors.status)}
                >
                  <label>Status</label>
                  <Select
                    name="status"
                    value={values.status}
                    onChange={handleChange}
                    error={touched.status && Boolean(errors.status)}
                  >
                    <MenuItem value="Active">Active</MenuItem>
                    <MenuItem value="InActive">Inactive</MenuItem>
                  </Select>
                  {touched.status && errors.status && (
                    <FormHelperText error>{errors.status}</FormHelperText>
                  )}
                </FormControl>

                <DialogActions
                  sx={{
                    padding: "0px !important",
                    paddingTop: "1rem !important",
                  }}
                >
                  <Button
                    onClick={onClose}
                    sx={{
                      textTransform: "none",
                      borderColor: "#ccc",
                      color: "#000",
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="button"
                    onClick={submitForm}
                    variant="contained"
                    sx={{
                      backgroundColor: "#F26522",
                      color: "#FFF",
                      textTransform: "none",
                      "&:hover": { backgroundColor: "#d55a1d" },
                    }}
                  >
                    {mode === "edit" ? "Save Changes" : "Add Appraisal"}
                  </Button>
                </DialogActions>
              </Form>
            );
          }}
        </Formik>
      </DialogContent>
    </Dialog>
  );
};

export default AddAppraisal;
function setLoading(arg0: boolean) {
  throw new Error("Function not implemented.");
}

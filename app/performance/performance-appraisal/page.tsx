"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Di<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Avatar,
  Paper,
} from "@mui/material";
import { useCallback, useEffect } from "react";
import { useState } from "react";
import "./performance-appraisal.scss";
import {
  HomeOutlined,
  EditNote,
  Delete,
  ControlPoint,
  Circle,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  useGridApiRef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import AddAppraisal from "@/app/performance/performance-appraisal/AddAppraisal";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import PolicyFilters, {
  getDateRang<PERSON>,
} from "@/components/policyFilter/PolicyFilters";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import {
  addPerformanceAppraisal,
  updatePerformanceAppraisal,
  deletePerformanceAppraisal,
  getPerformanceAppraisalById,
} from "@/app/services/performance/performance.service";
import useFetchPerformanceAppraisalData from "@/app/hooks/performance/useFetchPerformanceAppraisalData";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

interface PerformanceAppraisal {
  _id: string;
  empId: {
    _id: string;
    firstName: string;
    departmentName: string;
    designationName: string;
  };
  designationId: string;
  appraisalDate: string;
  competencies: Array<{
    _id: string;
    competencyKey: string;
    setValue: string;
  }>;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}


function PerformanceAppraisal() {
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Performance Management", href: "" },
    { label: "Appraisal Process" },
  ];
  
  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);
  const [openModal, setOpenModal] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedAppraisalId, setSelectedAppraisalId] = useState<string | null>(
    null
  );
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [appraisalToDelete, setAppraisalToDelete] = useState<string | null>(
    null
  );
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [refresh, setRefresh] = useState(false);
  const apiRef = useGridApiRef();

  // Add effect to handle date changes
  useEffect(() => {
    if (startDate || endDate) {
      console.log("Date change detected:", { startDate, endDate });
      setPage(1); // Reset to first page when dates change
      setRefresh(!refresh);
    }
  }, [startDate, endDate]);

  // Access to admin and Employee roles (Admin, SuperAdmin, Manager, HR,Employee)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const { data, loading } = useFetchPerformanceAppraisalData(
    limit,
    page,
    selectedSortBy === "Ascending"
      ? "asc"
      : selectedSortBy === "Descending"
        ? "desc"
        : "",
    startDate, // Pass the selected start date
    endDate, // Pass the selected end date
    refresh, // refresh boolean
    debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined
  );

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  const rows: GridRowsProp = data?.appraisals.results
    ? data.appraisals.results.map((record: PerformanceAppraisal) => {
        // Extract department and designation names from the nested structure
        let departmentName = "-";
        let designationName = "-";

        if (record.empId && typeof record.empId === "object") {
          // Extract department name
          if (
            (record.empId as any).departmentId &&
            typeof (record.empId as any).departmentId === "object"
          ) {
            departmentName =
              (record.empId as any).departmentId.departmentName || "-";
          }

          // Extract designation name
          if (
            (record.empId as any).designationId &&
            typeof (record.empId as any).designationId === "object"
          ) {
            designationName =
              (record.empId as any).designationId.designationName || "-";
          }
        }

        return {
          id: record._id,
          employeeName: record?.empId?.firstName || "-",
          designation: designationName,
          department: departmentName,
          avatar: "/assets/users/default.jpg",
          appraisalDate: record.appraisalDate || "-",
          isActive: record.isActive, // Store the boolean value directly
        };
      })
    : [];

  const columns: GridColDef[] = [
    {
      field: "employeeName",
      headerName: "Name",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={params.row.avatar}
            alt={params.row.employeeName}
            sx={{ width: 32, height: 32 }}
          />
          <Typography variant="body2" className="field-employee-name">
            {params.row.employeeName}
          </Typography>
        </Box>
      ),
    },
    {
      field: "designation",
      headerName: "Designation",
      flex: 1,
      renderCell: (params) => (
        <Typography className="field-designation">{params.value}</Typography>
      ),
    },
    {
      field: "department",
      headerName: "Department",
      flex: 1,
      renderCell: (params) => (
        <Typography className="field-department">{params.value}</Typography>
      ),
    },
    {
      field: "appraisalDate",
      headerName: "Appraisal Date",
      flex: 1,
      renderCell: (params) => {
        const date = new Date(params.value);
        return (
          <Typography className="field-appraisal-date">
            {date
              .toLocaleString("en-GB", {
                day: "2-digit",
                month: "long",
                year: "numeric",
              })
              .replace(/ /g, " ")
              .replace(",", "")}
          </Typography>
        );
      },
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 1,
      renderCell: (params) => {
        // Log the value to debug
        console.log("isActive value:", params.value, typeof params.value);

        return (
          <Box
            sx={{
              backgroundColor: params.value === true ? "#03C95A" : "#E70D0D",
              color: "#fff",
              borderRadius: "4px",
              display: "flex",
              textAlign: "center",
              maxWidth: "60px",
              justifyContent: "center",
              fontSize: "10px",
              fontWeight: 500,
              padding: "0px 25px",
              lineHeight: "18px",
              letterSpacing: "0.5px",
              alignItems: "center",
            }}
          >
            <Circle sx={{ fontSize: "6px", marginRight: "4px" }} />
            {params.value === true ? "Active" : "Inactive"}
          </Box>

          // <StatusToggle
          //   isActive={params.value}
          //   onChange={async (newStatus) => {
          //     try {
          //       await updatePerformanceAppraisal(params.row.id, {
          //         isActive: newStatus,
          //       });
          //       setRefresh(!refresh); // Trigger refresh after status change
          //       toast.success(
          //         `Performance appraisal status updated to ${
          //           newStatus ? "Active" : "Inactive"
          //         }`
          //       );
          //     } catch (error) {
          //       console.error("Failed to update status:", error);
          //       toast.error("Failed to update performance appraisal status");
          //     }
          //   }}
          //   title={`Change status to ${params.value ? "Inactive" : "Active"}`}
          // />
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 0.7,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEdit(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handleEdit = async (appraisalId: string) => {
    try {
      const response = await getPerformanceAppraisalById(appraisalId);
      if (response.success) {
        setSelectedAppraisalId(appraisalId);
        setEditMode(true);
        setOpenModal(true);
      }
    } catch (error) {
      console.error("Failed to fetch appraisal details:", error);
      // toast.error("Failed to fetch appraisal details");
    }
  };

  const handleDeleteClick = (appraisalId: string) => {
    setAppraisalToDelete(appraisalId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (appraisalToDelete) {
      try {
        await deletePerformanceAppraisal(appraisalToDelete);
        setDeleteDialogOpen(false);
        setAppraisalToDelete(null);
        setRefresh(!refresh);
        toast.success("Performance appraisal deleted successfully!");
      } catch (error) {
        console.error("Failed to delete appraisal:", error);
        // toast.error("Failed to delete performance appraisal");
      }
    }
  };

  const handleDialogSubmit = async (formData: FormData) => {
    try {
      // Extract form data
      const formDataEntries: Record<string, string> = {};
      formData.forEach((value, key) => {
        formDataEntries[key] = value as string;
      });

      // Parse competencies from JSON string
      const competencies = JSON.parse(formDataEntries.competencies);

      // Create performance appraisal object
      const performanceAppraisal = {
        empId: formDataEntries.empId,
        designationId: formDataEntries.designationId,
        appraisalDate: formDataEntries.appraisalDate,
        competencies: competencies.map((comp: any) => ({
          indicator: comp.competencyKey,
          indicatorName: comp.indicatorName,
          expectedValue: comp.expectedValue,
          setValue: comp.setValue,
        })),
        // Convert string "true"/"false" to boolean
        isActive: formDataEntries.isActive === "true",
      };

      // Log the data being sent to the API
      console.log("Sending to API:", performanceAppraisal);

      if (editMode && selectedAppraisalId) {
        await updatePerformanceAppraisal(
          selectedAppraisalId,
          performanceAppraisal
        );
        toast.success("Performance appraisal updated successfully!");
        setRefresh(!refresh); // Trigger refresh after update
      } else {
        await addPerformanceAppraisal(performanceAppraisal);
        setRefresh(!refresh); // Trigger refresh after add
        // toast.success("Performance appraisal added successfully!");
      }

      setOpenModal(false);
      setEditMode(false);
      setSelectedAppraisalId(null);
    } catch (error) {
      console.error(
        `Failed to ${editMode ? "update" : "add"} appraisal:`,
        error
      );
    }
  };

  return (
    <Box className="performance-appraisal-container">
      {loading && <Loader loading={loading} />}
      <Box className="content">
        <Box className="performance-appraisal-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Appraisal Process</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="report"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                color: "#FFF",
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => {
                setEditMode(false);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ width: "14px", height: "16px" }} />
              New Appraisal
            </Button>
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight:
                rows.length <= 2 ? "calc(70vh - 200px)" : "calc(100vh - 220px)",
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Performance Appraisal List</Typography>
              <PolicyFilters
            departments={[]}
            designations={[]}
            selectedDepartment="Department"
            setSelectedDepartment={() => {}}
            selectedDesignation="Designation"
            setSelectedDesignation={() => {}}
            selectedSortBy={selectedSortBy}
            setSelectedSortBy={setSelectedSortBy}
            selectedDateRange={selectedDateRange}
            setSelectedDateRange={setSelectedDateRange}
            setStartDate={setStartDate}
            setEndDate={setEndDate}
            selectedStatus="Select Status"
            setSelectedStatus={() => {}}
            selectedLeaveType="Select Type"
            setSelectedLeaveType={() => {}}
            setPage={setPage}
            currentDate={currentDate}
            showDateRangeFilter={true}
            showDepartmentFilter={false}
            showDesignationFilter={false}
            showStatusFilter={false}
            showSortByFilter={true}
            showLeaveTypeFilter={false}
            selectedPriority=""
            setSelectedPriority={() => {}}
          />
            </Box>
            <Divider />
            <CustomDataGrid
              apiRef={apiRef}
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={data?.appraisals.total || 0}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                setPage(model.page + 1);
                setPageSize(model.pageSize);
                setLimit(model.pageSize);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                  
toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this appraisal? This action cannot be undone."
      />

      <AddAppraisal
        open={openModal}
        onClose={() => {
          setOpenModal(false);
          setEditMode(false);
          setSelectedAppraisalId(null);
        }}
        onSubmit={handleDialogSubmit}
        mode={editMode ? "edit" : "add"}
        selectedAppraisalId={selectedAppraisalId}
      />
    </Box>
  );
}

export default PerformanceAppraisal;

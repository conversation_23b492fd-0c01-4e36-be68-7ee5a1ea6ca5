"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>utt<PERSON>,
  <PERSON><PERSON>,
  Divider,
  Typo<PERSON>,
  Paper,
} from "@mui/material";
import { useState, useEffect, useCallback } from "react";
import "./performance-indicator.scss";
import {
  HomeOutlined,
  EditNote,
  Delete,
  ControlPoint,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import AddNewIndicator from "@/app/performance/performance-indicator/AddNewIndicator";
import {
  GridRowsProp,
  GridColDef,
  useGridApiRef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import {
  addPerformanceIndicator,
  updatePerformanceIndicator,
  deletePerformanceIndicator,
} from "@/app/services/performance/performance.service";
import useFetchPerformanceSectionData from "@/app/hooks/performance/useFetchPerformanceSectionData";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { getAllDesignations } from "@/app/services/designation.service";
import { getDepartments } from "@/app/services/department.service";
import useAuthStore from "@/store/authStore";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

// Interface for adding a new performance indicator (POST)
interface PerformanceIndicator {
  //empId: string;
  designationId: string;
  customerExperience: string;
  marketing: string;
  management: string;
  administration: string;
  presentationSkill: string;
  qualityWork: string;
  efficiency: string;
  integrity: string;
  professionalism: string;
  teamWork: string;
  criticalThinking: string;
  conflictManagement: string;
  attendence: string;
  meetDedline: string;
  isActive: boolean;
}

// Interface for existing records (includes _id)
interface PerformanceRecord
  extends Omit<PerformanceIndicator, "designationId"> {
  createdAt: any;
  _id: string;
  designationId:
    | {
        _id: string;
        designationName: string;
        departmentId?: {
          _id: string;
          departmentName: string;
        };
        isActive: boolean;
      }
    | string;
}

function PerformanceIndicator() {
  // Get employee ID from auth store
  const { employeeId } = useAuthStore();

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Performance Management", href: "" },
    { label: "KPI Metrics" },
  ];

  // State for loading and refresh
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);

  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Pagination states
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);

  // Modal states
  const [openModal, setOpenModal] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedIndicatorId, setSelectedIndicatorId] = useState<string | null>(
    null
  );

  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedStatus, setSelectedStatus] = useState<string>("Status");

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [indicatorToDelete, setIndicatorToDelete] = useState<string | null>(
    null
  );

  const apiRef = useGridApiRef();

  // Restrict access to Employee roles only (Admin, SuperAdmin, Manager, HR, Employee)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // Fetch performance data using the custom hook
  const [performanceData, total] = useFetchPerformanceSectionData({
    setIsLoading,
    refresh,
    limit,
    page,
    departmentName: undefined,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate, // Pass startDate
    endDate, // Pass endDate
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  const [designations, setDesignations] = useState<{ [key: string]: string }>(
    {}
  );
  const [departments, setDepartments] = useState<{ [key: string]: string }>({});

  // Effect to fetch designations and departments
  useEffect(() => {
    const fetchDesignationsAndDepartments = async () => {
      try {
        // Fetch designations
        const designationResponse = await getAllDesignations();
        if (
          designationResponse &&
          designationResponse.designations &&
          designationResponse.designations.results
        ) {
          const designationMap: { [key: string]: string } = {};
          designationResponse.designations.results.forEach(
            (designation: any) => {
              designationMap[designation._id] = designation.designationName;
            }
          );
          setDesignations(designationMap);
          console.log("Designation map:", designationMap);
        }

        // Fetch departments
        const departmentResponse = await getDepartments();
        if (
          departmentResponse &&
          departmentResponse.departments &&
          departmentResponse.departments.results
        ) {
          const departmentMap: { [key: string]: string } = {};
          departmentResponse.departments.results.forEach((department: any) => {
            departmentMap[department._id] = department.departmentName;
          });
          setDepartments(departmentMap);
          console.log("Department map:", departmentMap);
        }
      } catch (error) {
        console.error("Failed to fetch designations or departments:", error);
      }
    };

    fetchDesignationsAndDepartments();
  }, [refresh]);

  // Map performance data to GridRowsProp
  const rows: GridRowsProp = performanceData
    ? performanceData.map((record: PerformanceRecord) => {
        // Handle nested designationId and departmentId objects
        let designationId = "";
        let designationName = "-";
        let departmentId = "";
        let departmentName = "-";

        if (typeof record.designationId === "object" && record.designationId) {
          designationId = record.designationId._id || "";
          designationName = record.designationId.designationName || "-";

          // Extract department info from the nested structure
          if (
            record.designationId.departmentId &&
            typeof record.designationId.departmentId === "object"
          ) {
            departmentId = record.designationId.departmentId._id || "";
            departmentName =
              record.designationId.departmentId.departmentName || "-";
          }
        }

        return {
          id: record._id,
          designation: designationName,
          department: departmentName,
          designationId: designationId,
          departmentId: departmentId,
          createdAt: record.createdAt
            ? new Date(record.createdAt).toLocaleDateString("en-GB", {
                day: "numeric",
                month: "long",
                year: "numeric",
              })
            : "N/A",
          isActive: record.isActive ? "Active" : "Inactive",
        };
      })
    : [];

  const columns: GridColDef[] = [
    {
      field: "designation",
      headerName: "Designation",
      editable: false,
      flex: 1,
      minWidth: 234.969,
      renderCell: (params) => {
        return (
          <Typography
            sx={{ color: "#111827", fontSize: "14px", fontWeight: 500 }}
          >
            {designations[params.row.designationId]}
          </Typography>
        );
      },
    },
    {
      field: "department",
      headerName: "Department",
      editable: false,
      flex: 1,
      minWidth: 152.906,
      renderCell: (params) => {
        return (
          <Typography
            sx={{ color: "#6B7280", fontSize: "14px", fontWeight: 400 }}
          >
            {departments[params.row.departmentId]}
          </Typography>
        );
      },
    },
    {
      field: "createdAt",
      headerName: "Created Date",
      editable: false,
      flex: 1,
      minWidth: 165.125,
      renderCell: (params) => {
        return (
          <Typography
            sx={{ color: "#6B7280", fontSize: "14px", fontWeight: 400 }}
          >
            {params.value}
          </Typography>
        );
      },
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 1,
      minWidth: 120.844,
      renderCell: (params) => (
        // <Box
        //   sx={{
        //     backgroundColor: params.value === "Active" ? "#03C95A" : "#E70D0D",
        //     color: "#fff",
        //     borderRadius: "4px",
        //     display: "flex",
        //     textAlign: "center",
        //     maxWidth: "48px",
        //     justifyContent: "center",
        //     fontSize: "10px",
        //     fontWeight: 500,
        //     padding: "0px 25px",
        //     lineHeight: "18px",
        //     letterSpacing: "0.5px",
        //   }}
        // >
        //   {params.value}
        // </Box>

        <StatusToggle
          isActive={params.value === "Active"}
          onChange={async (newStatus) => {
            try {
              const updatedIndicator: PerformanceIndicator = {
                //empId: params.row.empId,
                designationId: params.row.designationId,
                customerExperience: "",
                marketing: "",
                management: "",
                administration: "",
                presentationSkill: "",
                qualityWork: "",
                efficiency: "",
                integrity: "",
                professionalism: "",
                teamWork: "",
                criticalThinking: "",
                conflictManagement: "",
                attendence: "",
                meetDedline: "",
                isActive: newStatus,
              };
              await updatePerformanceIndicator(params.row.id, updatedIndicator);
              setRefresh(!refresh);
              toast.success(
                `Performance indicator status updated to ${newStatus ? "Active" : "Inactive"}`
              );
            } catch (error) {
              console.error("Failed to update status:", error);
              toast.error("Failed to update performance indicator status");
            }
          }}
          title={`Change status to ${params.value === "Active" ? "Inactive" : "Active"}`}
        />
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEdit(params.row.id)}
          >
            <EditNote sx={{ width: "16px", height: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ width: "16px", height: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handleEdit = (indicatorId: string) => {
    setSelectedIndicatorId(indicatorId);
    setEditMode(true);
    setOpenModal(true);
  };

  const handleDeleteClick = (indicatorId: string) => {
    setIndicatorToDelete(indicatorId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (indicatorToDelete) {
      try {
        await deletePerformanceIndicator(indicatorToDelete);
        setDeleteDialogOpen(false);
        setIndicatorToDelete(null);
        setRefresh(!refresh);
        toast.success("Performance indicator deleted successfully!");
      } catch (error) {
        console.error("Failed to delete indicator:", error);
        // toast.error("Failed to delete performance indicator");
      }
    }
  };

  const handleDialogSubmit = async (formData: FormData) => {
    try {
      const formDataEntries = Object.fromEntries(formData) as {
        [k: string]: FormDataEntryValue;
      };

      // Log the formDataEntries to verify empId is included
      console.log("Form data entries in submit handler:", formDataEntries);

      const performanceIndicator: PerformanceIndicator = {
        //empId: formDataEntries.empId ? String(formDataEntries.empId) : "",
        designationId: formDataEntries.designationId as string,
        customerExperience: formDataEntries.customerExperience as string,
        marketing: formDataEntries.marketing as string,
        management: formDataEntries.management as string,
        administration: formDataEntries.administration as string,
        presentationSkill: formDataEntries.presentationSkill as string,
        qualityWork: formDataEntries.qualityWork as string,
        efficiency: formDataEntries.efficiency as string,
        integrity: formDataEntries.integrity as string,
        professionalism: formDataEntries.professionalism as string,
        teamWork: formDataEntries.teamWork as string,
        criticalThinking: formDataEntries.criticalThinking as string,
        conflictManagement: formDataEntries.conflictManagement as string,
        attendence: formDataEntries.attendence as string,
        meetDedline: formDataEntries.meetDedline as string,
        isActive: formDataEntries.isActive === "true",
      };

      // Log the final object being sent to the API
      console.log("Performance indicator object:", performanceIndicator);

      if (editMode && selectedIndicatorId) {
        await updatePerformanceIndicator(
          selectedIndicatorId,
          performanceIndicator
        );
        toast.success("Performance indicator updated successfully!");
      } else {
        await addPerformanceIndicator(performanceIndicator);
        toast.success("Performance indicator added successfully!");
      }

      setRefresh(!refresh);
      setOpenModal(false);
      setEditMode(false);
      setSelectedIndicatorId(null);
    } catch (error) {
      console.error(
        `Failed to ${editMode ? "update" : "add"} indicator:`,
        error
      );
      // toast.error(
      //   `Failed to ${editMode ? "update" : "add"} performance indicator`
      // );
    }
  };

  return (
    <Box className="performance-indicator-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="performance-indicator-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">KPI Metrics</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="report"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                color: "#FFF",
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => {
                setEditMode(false);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ width: "14px", height: "16px" }} />
              Add KPI
            </Button>
          </Box>
        </Box>
        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight:
                rows.length <= 2 ? "calc(70vh - 200px)" : "calc(100vh - 220px)",
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Key Performance Indicators</Typography>
              <PolicyFilters
                departments={[]}
                selectedDepartment=""
                setSelectedDepartment={() => {}}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={false}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              apiRef={apiRef}
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              // checkboxSelection
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                setPage(model.page + 1);
                setPageSize(model.pageSize);
                setLimit(model.pageSize);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this KPI? This action cannot be undone."
      />

      <AddNewIndicator
        open={openModal}
        onClose={() => {
          setOpenModal(false);
          setEditMode(false);
          setSelectedIndicatorId(null);
        }}
        onSubmit={handleDialogSubmit}
        mode={editMode ? "edit" : "add"}
        selectedIndicatorId={selectedIndicatorId}
      />
    </Box>
  );
}

export default PerformanceIndicator;

"use client";

import "./AddNewIndicator.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  MenuItem,
  Select,
  FormControl,
  Grid,
  Typography,
  Box,
} from "@mui/material";
import { Formik, Form, Field } from "formik";
import { useState, useEffect } from "react";
import { getAllDesignations } from "@/app/services/designation.service";
import { getPerformanceIndicatorById } from "@/app/services/performance/performance.service";
import { Cancel } from "@mui/icons-material";

interface Designation {
  _id: string;
  designationName: string;
  departmentId: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface PerformanceIndicatorData {
  designationId: string;
  customerExperience: string;
  marketing: string;
  management: string;
  administration: string;
  presentationSkill: string;
  qualityWork: string;
  efficiency: string;
  integrity: string;
  professionalism: string;
  teamWork: string;
  criticalThinking: string;
  conflictManagement: string;
  attendence: string;
  meetDedline: string;
  isActive: string;
}

const performanceOptions = {
  customerExperience: [
    "Select",
    "Advanced",
    "Intermediate",
    "Average",
  ] as const,
  marketing: ["Select", "Advanced", "Intermediate", "Average"] as const,
  management: ["Select", "Advanced", "Intermediate", "Average"] as const,
  administration: ["Select", "Advanced", "Intermediate", "Average"] as const,
  presentationSkill: ["Select", "None", "Intermediate", "Average"] as const,
  qualityWork: ["Select", "None", "Intermediate", "Average"] as const,
  efficiency: ["Select", "None", "Intermediate", "Average"] as const,
  integrity: ["Select", "None", "Intermediate", "Average"] as const,
  professionalism: ["Select", "None", "Intermediate", "Average"] as const,
  teamWork: ["Select", "None", "Intermediate", "Average"] as const,
  criticalThinking: ["Select", "None", "Intermediate", "Average"] as const,
  conflictManagement: ["Select", "None", "Intermediate", "Average"] as const,
  attendence: ["Select", "None", "Intermediate", "Average"] as const,
  meetDedline: ["Select", "Advanced", "Intermediate", "Average"] as const,
};

type PerformanceField = keyof typeof performanceOptions;

// Mapping for user-friendly labels
const fieldLabels: Record<PerformanceField, string> = {
  customerExperience: "Customer Experience",
  marketing: "Marketing",
  management: "Management",
  administration: "Administration",
  presentationSkill: "Presentation Skills",
  qualityWork: "Quality of Work",
  efficiency: "Efficiency",
  integrity: "Integrity",
  professionalism: "Professionalism",
  teamWork: "Team Work",
  criticalThinking: "Critical Thinking",
  conflictManagement: "Conflict Management",
  attendence: "Attendance",
  meetDedline: "Ability To Meet Deadline",
};

// Add this style object at the top of your component
const selectStyles = {
  "& .MuiSelect-select": {
    display: "flex",
    alignItems: "center",
    minHeight: "43px", // Adjust this value as needed
  },
  "& .MuiMenuItem-root": {
    display: "flex",
    alignItems: "center",
    minHeight: "43px", // Keep the same height for consistency
  },
};

const AddNewIndicator = ({
  open,
  onClose,
  onSubmit,
  mode = "add",
  selectedIndicatorId,
}: {
  open: boolean;
  onClose: () => void;
  onSubmit: (formData: FormData) => void;
  mode?: "add" | "edit";
  selectedIndicatorId?: string | null;
}) => {
  const [designations, setDesignations] = useState<Designation[]>([]);
  const [loading, setLoading] = useState(false);
  const [initialData, setInitialData] =
    useState<PerformanceIndicatorData | null>(null);

  const initialValues = {
    designationId: "",
    customerExperience: "Select",
    marketing: "Select",
    management: "Select",
    administration: "Select",
    presentationSkill: "Select",
    qualityWork: "Select",
    efficiency: "Select",
    integrity: "Select",
    professionalism: "Select",
    teamWork: "Select",
    criticalThinking: "Select",
    conflictManagement: "Select",
    attendence: "Select",
    meetDedline: "Select",
    isActive: "",
  };

  // Fetch designations on mount
  useEffect(() => {
    const fetchDesignations = async () => {
      try {
        setLoading(true);
        const response = await getAllDesignations();
        const activeDesignations = response.designations.results.filter(
          (d: Designation) => d.isActive && !d.isDeleted
        );
        setDesignations(activeDesignations);
      } catch (error) {
        console.error("Failed to fetch designations:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchDesignations();
  }, []);

  // Fetch performance indicator data when in edit mode
  useEffect(() => {
    const fetchPerformanceIndicator = async () => {
      if (mode === "edit" && selectedIndicatorId) {
        try {
          setLoading(true);
          const response =
            await getPerformanceIndicatorById(selectedIndicatorId);
          const performance = response.performance;

          // Extract designationId correctly from the response
          let designationId = "";
          if (performance.designationId) {
            // If designationId is an object with _id property
            if (
              typeof performance.designationId === "object" &&
              performance.designationId._id
            ) {
              designationId = performance.designationId._id;
            }
            // If designationId is already a string
            else if (typeof performance.designationId === "string") {
              designationId = performance.designationId;
            }
          }

          console.log("Setting designationId in edit mode:", designationId);

          setInitialData({
            designationId: designationId,
            customerExperience: performance.customerExperience,
            marketing: performance.marketing,
            management: performance.management,
            administration: performance.administration,
            presentationSkill: performance.presentationSkill,
            qualityWork: performance.qualityWork,
            efficiency: performance.efficiency,
            integrity: performance.integrity,
            professionalism: performance.professionalism,
            teamWork: performance.teamWork,
            criticalThinking: performance.criticalThinking,
            conflictManagement: performance.conflictManagement,
            attendence: performance.attendence,
            meetDedline: performance.meetDedline,
            isActive: performance.isActive.toString(),
          });
          console.log("Initial data in edit form", {
            designationId: designationId,
            // other fields...
          });
        } catch (error) {
          console.error("Failed to fetch performance indicator:", error);
        } finally {
          setLoading(false);
        }
      }
    };
    fetchPerformanceIndicator();
  }, [mode, selectedIndicatorId]);

  return (
    <Dialog
      className="add-indicator-dialog"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth="md"
      sx={{ "& .MuiDialog-paper": { maxWidth: "800px" } }}
    >
      <DialogTitle className="dialog-title">
        <Typography>
          {mode === "add" ? "Add New Indicator" : "Edit New Indicator"}
        </Typography>

        <Cancel
          sx={{
            color: "#6B7280",
            cursor: "pointer",
            fontSize: "20px",
            ":hover": { color: "#D93D3D" },
          }}
          onClick={onClose}
        />
      </DialogTitle>
      <DialogContent className="dialog-content">
        <Formik
          initialValues={
            mode === "edit" && initialData ? initialData : initialValues
          }
          enableReinitialize={true}
          onSubmit={(values) => {
            const formData = new FormData();
            Object.entries(values).forEach(([key, value]) => {
              formData.append(key, value as string);
            });
            onSubmit(formData);
          }}
        >
          {({ values, handleChange }) => (
            <Form>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                {/* Designation Section */}
                <Box sx={{ width: "100%" }}>
                  <FormControl fullWidth>
                    <label>Designation</label>
                    <Field
                      as={Select}
                      name="designationId"
                      value={values.designationId}
                      onChange={handleChange}
                      disabled={loading}
                      sx={selectStyles}
                    >
                      <MenuItem value="">Select Designation</MenuItem>
                      {loading ? (
                        <MenuItem value="">Loading...</MenuItem>
                      ) : (
                        designations.map((designation) => (
                          <MenuItem
                            key={designation._id}
                            value={designation._id}
                          >
                            {designation.designationName}
                          </MenuItem>
                        ))
                      )}
                    </Field>
                  </FormControl>
                </Box>

                {/* Technical Section */}
                <Box sx={{ width: "100%" }}>
                  <Typography variant="h6">Technical</Typography>
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    flexWrap: "wrap",
                    gap: "24px",
                    "& > *": {
                      flex: "1 1 calc(33.33% - 16px)",
                      maxWidth: "173.5px",
                    },
                  }}
                >
                  {(
                    [
                      "customerExperience",
                      "marketing",
                      "management",
                      "administration",
                      "presentationSkill",
                      "qualityWork",
                      "efficiency",
                    ] as PerformanceField[]
                  ).map((field) => (
                    <FormControl key={field} fullWidth>
                      <label>{fieldLabels[field]}</label>
                      <Field
                        as={Select}
                        name={field}
                        value={values[field]}
                        onChange={handleChange}
                        disabled={loading}
                        sx={selectStyles}
                      >
                        {performanceOptions[field].map((option) => (
                          <MenuItem key={option} value={option}>
                            {option}
                          </MenuItem>
                        ))}
                      </Field>
                    </FormControl>
                  ))}
                </Box>

                {/* Organizational Section */}
                <Box sx={{ width: "100%" }}>
                  <Typography variant="h6">Organizational</Typography>
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    flexWrap: "wrap",
                    gap: "24px",
                    "& > *": {
                      flex: "1 1 calc(33.33% - 16px)",
                      maxWidth: "173.5px",
                    },
                  }}
                >
                  {(
                    [
                      "integrity",
                      "professionalism",
                      "teamWork",
                      "criticalThinking",
                      "conflictManagement",
                      "attendence",
                      "meetDedline",
                    ] as PerformanceField[]
                  ).map((field) => (
                    <FormControl key={field} fullWidth>
                      <label>{fieldLabels[field]}</label>
                      <Field
                        as={Select}
                        name={field}
                        value={values[field]}
                        onChange={handleChange}
                        disabled={loading}
                        sx={selectStyles}
                      >
                        {performanceOptions[field].map((option) => (
                          <MenuItem key={option} value={option}>
                            {option}
                          </MenuItem>
                        ))}
                      </Field>
                    </FormControl>
                  ))}
                </Box>

                {/* Status Section */}
                <Box sx={{ width: "100%" }}>
                  <FormControl fullWidth>
                    <label>Status</label>
                    <Field
                      as={Select}
                      name="isActive"
                      value={values.isActive}
                      onChange={handleChange}
                      disabled={loading}
                      sx={selectStyles}
                    >
                      <MenuItem value="">Select Status</MenuItem>
                      <MenuItem value="true">Active</MenuItem>
                      <MenuItem value="false">Inactive</MenuItem>
                    </Field>
                  </FormControl>
                </Box>
              </Box>

              {/* Actions */}
              <DialogActions
                sx={{
                  padding: "0px !important",
                  paddingTop: "1rem !important",
                }}
              >
                <Button
                  onClick={onClose}
                  sx={{
                    backgroundColor: "#f8f9fa",
                    border: "1px solid #f8f9fa",
                    color: "#111827",
                    textTransform: "none",
                    borderRadius: "5px",
                    fontSize: 14,
                    fontWeight: 500,
                    padding: ".5rem .85rem",
                    transition: "all .5s",
                  }}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  sx={{
                    backgroundColor: "#F26522",
                    color: "#FFF",
                    textTransform: "none",
                    "&:hover": {
                      backgroundColor: "#d55a1d",
                    },
                  }}
                  disabled={loading}
                >
                  {mode === "add" ? "Add Indicator" : "Save Changes"}
                </Button>
              </DialogActions>
            </Form>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  );
};

export default AddNewIndicator;

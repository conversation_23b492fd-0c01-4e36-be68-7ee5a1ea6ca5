"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogTit<PERSON>,
  DialogContent,
  <PERSON>alogActions,
  TextField,
  Button,
  FormControl,
  Select,
  MenuItem,
  Box,
  IconButton,
  Typography,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { useState, useEffect } from "react";
import { Formik, Form, Field, FieldProps } from "formik";
import * as Yup from "yup";
import JoditEditorWrapper from "@/components/JoditEditorWrapper";
import Loader from "@/components/Loader/Loader";
import useFetchAllGoalTypes from "@/app/hooks/goals/useFetchAllGoalTypes";
import useAuthStore from "@/store/authStore";
import { getGoalById } from "@/app/services/goals/goals.service";
import { toast } from "react-toastify";
import "./AddEditGoals.scss";

// Define Goal interface based on API response
interface Goal {
  _id: string;
  empId: string;
  goalTypeId: string; // Updated to string since API returns goalTypeId as a string
  subject: string;
  targetAchievement: string;
  startDate: string;
  endDate: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface GoalFormValues {
  goalTypeId: string;
  subject: string;
  targetAchievement: string;
  startDate: string;
  endDate: string;
  description: string;
  isActive: boolean;
  empId: string;
}

// Define props for AddEditGoal
interface AddEditGoalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: GoalFormValues) => Promise<void>;
  isEditMode: boolean;
  goalId: string | null; // Add goalId prop
}

// Validation schema using Yup
const validationSchema = Yup.object({
  goalTypeId: Yup.string().required("Goal Type is required"),
  subject: Yup.string().required("Subject is required"),
  targetAchievement: Yup.string().required("Target Achievement is required"),
  startDate: Yup.string().required("Start Date is required"),
  endDate: Yup.string().required("End Date is required"),
  description: Yup.string().required("Description is required"),
  isActive: Yup.boolean().required("Status is required"),
  empId: Yup.string().required("Employee ID is required"),
});

export default function AddEditGoal({
  open,
  onClose,
  onSubmit,
  isEditMode,
  goalId,
}: AddEditGoalProps) {
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(false); // For fetching goal data
  const [goalData, setGoalData] = useState<Goal | null>(null); // Store fetched goal data
  const {
    goalTypes,
    loading: goalTypesLoading,
    error: goalTypesError,
  } = useFetchAllGoalTypes();
  const { employeeId: authEmployeeId } = useAuthStore();

  // Fetch goal data when in edit mode and goalId is provided
  useEffect(() => {
    const fetchGoalData = async () => {
      if (isEditMode && goalId) {
        try {
          setFetchLoading(true);
          const response = await getGoalById(goalId);
          setGoalData(response.goal);
        } catch (error) {
          console.error("Failed to fetch goal data:", error);
          toast.error("Failed to load goal data. Please try again.");
          onClose(); // Close the dialog if fetching fails
        } finally {
          setFetchLoading(false);
        }
      }
    };

    fetchGoalData();
  }, [isEditMode, goalId, onClose]);

  const handleSubmitForm = async (values: GoalFormValues) => {
    try {
      setLoading(true);
      await onSubmit(values);
      onClose();
    } catch (error) {
      console.error("Form submission failed:", error);
    } finally {
      setLoading(false);
    }
  };

  // Format dates to YYYY-MM-DD for display in the date input
  const formatDateForInput = (date: string | undefined) => {
    if (!date) return "";
    const d = new Date(date);
    return d.toISOString().split("T")[0];
  };

  // Default initial values for adding a new goal
  const defaultInitialValues: GoalFormValues = {
    goalTypeId: "",
    subject: "",
    targetAchievement: "",
    startDate: "",
    endDate: "",
    description: "",
    isActive: true,
    empId: authEmployeeId || "",
  };

  // Set initial values based on fetched data when editing
  const initialValues: GoalFormValues =
    isEditMode && goalData
      ? {
          goalTypeId: goalData.goalTypeId || "",
          subject: goalData.subject || "",
          targetAchievement: goalData.targetAchievement || "",
          startDate: formatDateForInput(goalData.startDate),
          endDate: formatDateForInput(goalData.endDate),
          description: goalData.description || "",
          isActive: goalData.isActive ?? true,
          empId: goalData.empId || "",
        }
      : defaultInitialValues;

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
      className="dialog-container"
    >
      <DialogTitle className="dialog-title">
        <Typography variant="h6" className="title-text">
          {isEditMode ? "Edit Goal" : "Add Goal"}
        </Typography>
        <IconButton
          color="inherit"
          onClick={onClose}
          aria-label="close"
          sx={{
            backgroundColor: "#6b7280",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": { backgroundColor: "#d55a1d" },
            "& .MuiSvgIcon-root": { fontSize: "14px" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      {loading || goalTypesLoading || fetchLoading ? (
        <DialogContent className="dialog-content">
          <Box className="loader-container">
            <Loader loading={loading || goalTypesLoading || fetchLoading} />
          </Box>
        </DialogContent>
      ) : goalTypesError ? (
        <DialogContent className="dialog-content">
          <Box className="loader-container">
            <Typography color="error">{goalTypesError}</Typography>
          </Box>
        </DialogContent>
      ) : !authEmployeeId ? (
        <DialogContent className="dialog-content">
          <Box className="loader-container">
            <Typography color="error">
              Employee ID not found. Please log in again.
            </Typography>
          </Box>
        </DialogContent>
      ) : (
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmitForm}
          enableReinitialize // Reinitialize form when initialValues change
        >
          {(formikProps) => (
            <Form className="goal-type-form">
              <DialogContent className="dialog-content">
                <Box className="form-field">
                  <label className="form-label">
                    Goal Type <span className="required">*</span>
                  </label>
                  <Field name="goalTypeId">
                    {({ field, meta }: FieldProps<string, GoalFormValues>) => (
                      <FormControl
                        fullWidth
                        size="small"
                        className="select-control"
                        error={meta.touched && Boolean(meta.error)}
                      >
                        <Select
                          {...field}
                          value={field.value}
                          onChange={(e) =>
                            formikProps.setFieldValue(
                              "goalTypeId",
                              e.target.value
                            )
                          }
                          className="select-field"
                          displayEmpty
                        >
                          <MenuItem value="" disabled>
                            Select
                          </MenuItem>
                          {goalTypes.map((type) => (
                            <MenuItem key={type._id} value={type._id}>
                              {type.goalType}
                            </MenuItem>
                          ))}
                        </Select>
                        {meta.touched && meta.error && (
                          <Typography variant="caption" color="error">
                            {meta.error}
                          </Typography>
                        )}
                      </FormControl>
                    )}
                  </Field>
                </Box>

                <Box className="form-field">
                  <label className="form-label">
                    Subject <span className="required">*</span>
                  </label>
                  <Field name="subject">
                    {({ field, meta }: FieldProps<string, GoalFormValues>) => (
                      <TextField
                        {...field}
                        fullWidth
                        variant="outlined"
                        error={meta.touched && Boolean(meta.error)}
                        helperText={meta.touched && meta.error}
                        size="small"
                        className="text-field"
                      />
                    )}
                  </Field>
                </Box>

                <Box className="form-field">
                  <label className="form-label">
                    Target Achievement <span className="required">*</span>
                  </label>
                  <Field name="targetAchievement">
                    {({ field, meta }: FieldProps<string, GoalFormValues>) => (
                      <TextField
                        {...field}
                        fullWidth
                        variant="outlined"
                        error={meta.touched && Boolean(meta.error)}
                        helperText={meta.touched && meta.error}
                        size="small"
                        className="text-field"
                      />
                    )}
                  </Field>
                </Box>

                <Box className="form-field" sx={{ display: "flex", gap: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <label className="form-label">
                      Start Date <span className="required">*</span>
                    </label>
                    <Field name="startDate">
                      {({
                        field,
                        meta,
                      }: FieldProps<string, GoalFormValues>) => (
                        <TextField
                          {...field}
                          type="date"
                          fullWidth
                          variant="outlined"
                          error={meta.touched && Boolean(meta.error)}
                          helperText={meta.touched && meta.error}
                          size="small"
                          className="text-field"
                          InputLabelProps={{ shrink: true }}
                        />
                      )}
                    </Field>
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label className="form-label">
                      End Date <span className="required">*</span>
                    </label>
                    <Field name="endDate">
                      {({
                        field,
                        meta,
                      }: FieldProps<string, GoalFormValues>) => (
                        <TextField
                          {...field}
                          type="date"
                          fullWidth
                          variant="outlined"
                          error={meta.touched && Boolean(meta.error)}
                          helperText={meta.touched && meta.error}
                          size="small"
                          className="text-field"
                          InputLabelProps={{ shrink: true }}
                        />
                      )}
                    </Field>
                  </Box>
                </Box>

                <Box className="form-field">
                  <label className="form-label">
                    Description <span className="required">*</span>
                  </label>
                  <JoditEditorWrapper
                    value={formikProps.values.description}
                    onChange={(content) =>
                      formikProps.setFieldValue("description", content)
                    }
                    height={200}
                  />
                </Box>

                <Box className="form-field">
                  <label className="form-label">
                    Status <span className="required">*</span>
                  </label>
                  <Field name="isActive">
                    {({ field }: FieldProps<boolean, GoalFormValues>) => (
                      <FormControl
                        fullWidth
                        size="small"
                        className="select-control"
                      >
                        <Select
                          {...field}
                          value={field.value ? "active" : "inactive"}
                          onChange={(e) => {
                            formikProps.setFieldValue(
                              "isActive",
                              e.target.value === "active"
                            );
                          }}
                          className="select-field"
                          displayEmpty
                        >
                          <MenuItem value="" disabled>
                            Select
                          </MenuItem>
                          <MenuItem value="active">Active</MenuItem>
                          <MenuItem value="inactive">Inactive</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  </Field>
                </Box>

                <Field name="empId">
                  {({ field }: FieldProps<string, GoalFormValues>) => (
                    <input type="hidden" {...field} />
                  )}
                </Field>

                <DialogActions className="dialog-actions">
                  <Button
                    onClick={onClose}
                    color="inherit"
                    className="cancel-button"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    className="submit-button"
                  >
                    {isEditMode ? "Save Changes" : "Add Goal"}
                  </Button>
                </DialogActions>
              </DialogContent>
            </Form>
          )}
        </Formik>
      )}
    </Dialog>
  );
}

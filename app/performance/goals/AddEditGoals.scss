.dialog-container {
    
    .MuiDialog-paper {
        overflow: hidden;
    }

    .Mui-selected {
        color: #F26522 !important;
    }

    .MuiButtonBase-root {
        text-transform: none;
    }

    .MuiTabs-indicator {
        background-color: #F26522;
    }

    .dialog-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding:16px !important;

        p {
            margin-right: .5rem;
            font-size: 20px;
            font-weight: 600;
        }

        span {
            font-size: 14px;
            color: #6B7280;
            line-height: 1.5;
        }
    }

    .dialog-content {
        padding: 16px ;

        label {
            font-size: 14px;
            font-weight: 500;
            color: #202C4B;
            // margin-bottom: .5rem;
            margin: 0px !important;
        }

        input {
            padding: 5.5px 14px;
        }

        .MuiSelect-select {
            padding: 5.5px 14px;
        }

        .MuiAutocomplete-inputRoot {
            padding: 0px;
        }



    }
    .dialog-actions {
        padding: 16px 0 0 0 !important;
    }

}

@media (max-width: 768px) {
    .mobile-alignment{
        flex-direction: column;
    }
}
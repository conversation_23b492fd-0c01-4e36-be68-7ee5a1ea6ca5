"use client";

import {
  <PERSON>,
  I<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Divide<PERSON>,
  Typo<PERSON>,
  Paper,
} from "@mui/material";
import { useCallback, useState } from "react";
import "./Goals.scss";
import {
  HomeOutlined,
  ControlPoint,
  EditNote,
  Delete,
  Circle,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import Loader from "@/components/Loader/Loader";
import ReadMore from "@/components/ReadMore/ReadMore";
import useFetchGoal from "@/app/hooks/goals/useFetchGoal";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { toast } from "react-toastify";
import {
  deleteGoal,
  updateGoal,
  addGoal,
} from "@/app/services/goals/goals.service";
import AddEditGoal from "./AddEditGoals";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

const stripHtmlTags = (html: string) => {
  const div = document.createElement("div");
  div.innerHTML = html;
  return div.textContent || div.innerText || "";
};

// Define Goal interface based on API response
interface Goal {
  _id: string;
  empId: string;
  goalTypeId: {
    _id: string;
    goalType: string;
    isActive: boolean;
    isDeleted: boolean;
  };
  targetAchievement: string;
  startDate: string;
  endDate: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  subject: string;
}

interface GoalFormValues {
  goalTypeId: string;
  subject: string;
  targetAchievement: string;
  startDate: string;
  endDate: string;
  description: string;
  isActive: boolean;
  empId: string;
}

export default function GoalsListContent() {
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Performance", href: "" },
    { label: "Goals List" },
  ];

  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);
  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedGoalId, setSelectedGoalId] = useState<string | null>(null);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [goalToDelete, setGoalToDelete] = useState<string | number | null>(
    null
  );

  // Restrict access to USER_ROLES
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const [goalsData, total] = useFetchGoal({
    setIsLoading,
    refresh,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate: startDate || undefined,
    endDate: endDate || undefined,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  }) as [Goal[], number];

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  const rows: GridRowsProp = goalsData
    ? goalsData.map((goal: Goal) => ({
        id: goal._id,
        goalType: goal.goalTypeId?.goalType || "-",
        subject: goal.subject || "-",
        targetAchievement: goal.targetAchievement || "-",
        startDate: goal.startDate
          ? new Date(goal.startDate).toLocaleDateString()
          : "-",
        endDate: goal.endDate
          ? new Date(goal.endDate).toLocaleDateString()
          : "-",
        description: stripHtmlTags(goal.description) || "-",
        isActive: goal.isActive || false,
        progress: "-",
      }))
    : [];

  const columns: GridColDef[] = [
    {
      field: "goalType",
      headerName: "Goal Type",
      minWidth: 150,
      flex: 1,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ color: "#202C4B", fontWeight: "500 !important" }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "subject",
      headerName: "Subject",
      minWidth: 150,
      flex: 1,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ color: "#202C4B", fontWeight: "500 !important" }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "targetAchievement",
      headerName: "Target Achievement",
      minWidth: 180,
      flex: 1,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ color: "#202C4B", fontWeight: "500 !important" }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "startDate",
      headerName: "Start Date",
      minWidth: 150,
      flex: 1,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ color: "#202C4B", fontWeight: "500 !important" }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "endDate",
      headerName: "End Date",
      minWidth: 150,
      flex: 1,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ color: "#202C4B", fontWeight: "500 !important" }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "description",
      headerName: "Description",
      flex: 1,
      minWidth: 200,
      renderCell: (params) => (
        <Box
          sx={{
            width: "100%",
            maxWidth: "100%",
            padding: "8px 0",
            color: "#6B7280",
          }}
        >
          <ReadMore
            text={params.value}
            maxChars={50}
            sx={{
              width: "100%",
              "& .MuiTypography-root": {
                width: "100%",
                whiteSpace: "normal",
                overflow: "visible",
              },
            }}
          />
        </Box>
      ),
    },
    {
      field: "isActive",
      headerName: "Status",
      minWidth: 84.4375,
      flex: 1,
      renderCell: (params) => (
        // <Box
        //   sx={{
        //     backgroundColor: params.value ? "#03C95A" : "#E70D0D",
        //     color: "#fff",
        //     borderRadius: "4px",
        //     display: "flex",
        //     textAlign: "center",
        //     maxWidth: "60px",
        //     justifyContent: "center",
        //     fontSize: "10px",
        //     fontWeight: 500,
        //     padding: "0px 25px",
        //     lineHeight: "18px",
        //     letterSpacing: "0.5px",
        //     alignItems: "center",
        //   }}
        // >
        //   <Circle sx={{ fontSize: "6px", marginRight: "4px" }} />
        //   {params.value ? "Active" : "Inactive"}
        // </Box>

        <StatusToggle
          isActive={params.value}
          onChange={async (newStatus) => {
            try {
              await updateGoal(params.row.id, {
                isActive: newStatus,
              });
              setRefresh(!refresh);
              toast.success(
                `Goal status updated to ${newStatus ? "Active" : "Inactive"}!`
              );
            } catch (error) {
              console.error("Failed to update goal status:", error);
              toast.error("Failed to update goal status. Please try again.");
            }
          }}
          title="Change Goal Status"
        />
      ),
    },
    {
      field: "progress",
      headerName: "Progress",
      minWidth: 100,
      flex: 1,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ color: "#202C4B", fontWeight: "500 !important" }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  const handleEditClick = (goalId: string | number) => {
    setSelectedGoalId(goalId as string);
    setIsEditMode(true);
    setOpenModal(true);
  };

  const handleDeleteClick = (goalId: string | number) => {
    setGoalToDelete(goalId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (goalToDelete !== null) {
      try {
        await deleteGoal(goalToDelete as string);
        setDeleteDialogOpen(false);
        setGoalToDelete(null);
        setRefresh(!refresh);
        toast.success("Goal deleted successfully!");
      } catch (error) {
        console.error("Failed to delete goal:", error);
        toast.error("Failed to delete goal. Please try again.");
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setGoalToDelete(null);
  };

  const handleDialogSubmit = async (values: GoalFormValues) => {
    try {
      const goalBody = {
        empId: values.empId,
        goalTypeId: values.goalTypeId,
        subject: values.subject,
        targetAchievement: values.targetAchievement,
        startDate: values.startDate,
        endDate: values.endDate,
        description: values.description,
        isActive: values.isActive,
      };

      if (isEditMode && selectedGoalId) {
        await updateGoal(selectedGoalId, goalBody);
        toast.success("Goal updated successfully!");
      } else {
        await addGoal(goalBody);
        toast.success("Goal added successfully!");
      }
      setRefresh(!refresh);
    } catch (error) {
      console.error(`Failed to ${isEditMode ? "update" : "add"} goal:`, error);
      toast.error(
        `Failed to ${isEditMode ? "update" : "add"} goal. Please try again.`
      );
      throw error;
    }
    handleDialogClose();
  };

  const handleDialogClose = () => {
    setOpenModal(false);
    setIsEditMode(false);
    setSelectedGoalId(null);
  };

  return (
    <Box className="goals-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="goals-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Goals List</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="add-goal-type"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => {
                setIsEditMode(false);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ width: "14px", height: "14px" }} />
              Add Goal
            </Button>
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 550,
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Goals List</Typography>
              <PolicyFilters
                departments={[]}
                selectedDepartment={""}
                setSelectedDepartment={() => {}}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                setPage(model.page + 1);
                setPageSize(model.pageSize);
                setLimit(model.pageSize);
                setRefresh((prev) => !prev);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,

                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
              getRowHeight={() => "auto"}
              sx={{
                "& .MuiDataGrid-cell": {
                  maxHeight: "none !important",
                  overflow: "visible !important",
                  whiteSpace: "normal !important",
                  lineHeight: "1.5 !important",
                  display: "flex !important",
                  alignItems: "center !important",
                  padding: "8px 16px !important",
                },
                "& .MuiDataGrid-row": {
                  maxHeight: "none !important",
                },
              }}
            />
          </Box>
        </Paper>
      </Box>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this goal? This action cannot be undone."
      />

      <AddEditGoal
        open={openModal}
        onClose={handleDialogClose}
        onSubmit={handleDialogSubmit}
        isEditMode={isEditMode}
        goalId={selectedGoalId} // Pass goalId instead of initialValues
      />
    </Box>
  );
}

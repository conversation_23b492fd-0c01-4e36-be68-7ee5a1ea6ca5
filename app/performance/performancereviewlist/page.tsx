"use client";

import {
  <PERSON>,
  Divider,
  Typography,
  Paper,
  Tooltip,
  IconButton,
} from "@mui/material";
import React, { useState, useEffect, Suspense } from "react";
import { useRouter } from "next/navigation";
import { HomeOutlined, Visibility } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import {
  GridRowsProp,
  GridColDef,
  GridPagination,
  useGridApiRef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import Link from "next/link";
import { getDepartment } from "@/app/services/department.service";
import { getAllPerformanceReview } from "@/app/services/performance/performance.service";
import Loader from "@/components/Loader/Loader";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import "./PerformanceReviewList.scss";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";

interface Department {
  _id: string;
  departmentName: string;
}

// Interface for the empId object in the performance review response
interface Employee {
  _id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  email: string;
  phone: string;
  joiningDate: string;
  departmentId: string;
  designationId: string;
  isActive: boolean;
  isDeleted: boolean;
  employeeId: string;
}

// Interface for the performance review record
interface PerformanceReview {
  _id: string;
  empId: Employee;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  professionalExcellence: Array<{
    keyResultArea: string;
    keyPerformanceIndicator: string;
    weightage: number;
    percentAchievSelf: number;
    pointScoredSelf: number;
    percentAchievRo: number;
    pointScoredRo: number;
    _id: string;
  }>;
}

// Interface for the API response
interface PerformanceReviewResponse {
  success: boolean;
  statusCode: number;
  message: string;
  professionalExcellence: {
    results: PerformanceReview[];
    total: number;
    activeCount: number;
    inactiveCount: number;
    page: string;
    limit: string;
    totalPages: number;
  };
  meta: {
    version: string;
    forceUpdate: boolean;
    maintenance: boolean;
    hasUpdate: boolean;
  };
}

function QuickSearchToolbar() {
  return (
    <Box
      sx={{
        p: 0.5,
        pb: 0,
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        padding: "10px 20px",
      }}
    >
      <GridToolbarQuickFilter
        className="grid-search"
        sx={{ textDecoration: "none" }}
        placeholder="Search"
      />
      <Box
        className="grid-export"
        sx={{ display: "flex", alignItems: "center" }}
      >
        <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
      </Box>
    </Box>
  );
}

const formatDateForApi = (dateStr: string) => {
  if (!dateStr) return undefined;

  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return undefined;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return undefined;
  }
};

function EmployeesListContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [performanceReviews, setPerformanceReviews] = useState<
    PerformanceReview[]
  >([]);
  const [total, setTotal] = useState(0);

  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");

  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // Fetch departments
  useEffect(() => {
    let isMounted = true;
    const fetchDepartments = async () => {
      setIsLoading(true);
      try {
        const response = await getDepartment(
          undefined,
          undefined,
          "",
          "asc",
          undefined,
          undefined,
          "true"
        );
        if (isMounted && response?.departments.results) {
          const sortedDepartments = [...response.departments.results].sort(
            (a, b) => a.departmentName.localeCompare(b.departmentName)
          );
          setDepartments(sortedDepartments);
        }
      } catch (error) {
        console.error("Failed to fetch departments:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchDepartments();
    return () => {
      isMounted = false;
    };
  }, []);

  // Fetch performance reviews
  useEffect(() => {
    const fetchPerformanceReviews = async () => {
      setIsLoading(true);
      try {
        const response: PerformanceReviewResponse =
          await getAllPerformanceReview(
            limit,
            page,
            selectedDepartment,
            selectedSortBy,
            formatDateForApi(startDate),
            formatDateForApi(endDate),
            selectedStatus === "Active"
              ? "true"
              : selectedStatus === "Inactive"
                ? "false"
                : undefined
          );
        setPerformanceReviews(response.professionalExcellence.results);
        setTotal(response.professionalExcellence.total);
      } catch (error) {
        console.error("Failed to fetch performance reviews:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchPerformanceReviews();
  }, [
    limit,
    page,
    selectedDepartment,
    selectedSortBy,
    startDate,
    endDate,
    selectedStatus,
  ]);

  // Map performance review data to GridRowsProp
  const rows: GridRowsProp =
    performanceReviews?.length > 0
      ? performanceReviews
          .filter((review) => review?.empId) // Filter out invalid entries first
          .map((review, index) => ({
            id: review?.empId?._id || index, // Use empId._id for the row ID
            empId: review?.empId?.employeeId, // Display employeeId (e.g., "P25D005")
            name: `${review?.empId?.firstName} ${review?.empId?.lastName}`,
            avatar: review?.empId?.avatar,
            department:
              departments.find(
                (dept) => dept?._id === review?.empId?.departmentId
              )?.departmentName || "-",
            email: review?.empId?.email,
            phone: review?.empId?.phone,
            designationName: "-",
            joiningDate: review?.empId?.joiningDate
              ? new Date(review?.empId?.joiningDate).toLocaleDateString()
              : "-",
            isActive: review?.isActive ? "Active" : "Inactive",
          }))
      : [];

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Performance", href: "" },
    { label: "Performance Review List" },
  ];

  const apiRef = useGridApiRef();
  const router = useRouter();

  const columns: GridColDef[] = [
    {
      field: "empId",
      headerName: "Emp ID",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#111827" }}>
          {params.row.empId}
        </Typography>
      ),
    },
    {
      field: "name",
      headerName: "Name",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            <Typography sx={{ fontSize: "14px", color: "#111827" }}>
              <Link href="#">{params.row.name}</Link>
            </Typography>
          </Box>
        </Box>
      ),
    },
    // {
    //   field: "department",
    //   headerName: "Department",
    //   editable: false,
    //   flex: 1.5,
    //   renderCell: (params) => (
    //     <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
    //       {params.row.department}
    //     </Typography>
    //   ),
    // },
    // {
    //   field: "designationName",
    //   headerName: "Designation",
    //   editable: false,
    //   flex: 1,
    //   renderCell: (params) => (
    //     <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
    //       {params.row.designationName}
    //     </Typography>
    //   ),
    // },
    {
      field: "joiningDate",
      headerName: "Joining Date",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.joiningDate}
        </Typography>
      ),
    },
    {
      field: "isActive",
      headerName: "Status",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: "3px",
            backgroundColor: params.value === "Active" ? "#03C95A" : "#E70D0D",
            color: "#fff",
            borderRadius: "5px",
            textAlign: "center",
            minWidth: "66px",
            justifyContent: "center",
            fontSize: "10px",
            fontWeight: 500,
            padding: "0px 5px",
            lineHeight: "18px",
          }}
        >
          <Box
            sx={{
              width: "5px",
              height: "5px",
              borderRadius: "100%",
              backgroundColor: params.value === "Active" ? "#fff" : "#fff",
            }}
          />
          {params.value}
        </Box>

        // <StatusToggle
        //   isActive={params.row.isActive === "Active"}
        //   onChange={async (isActive) => {
        //     setIsLoading(true);
        //     try {
        //       // Call API to update status here if needed
        //       // await updatePerformanceReviewStatus(params.row.id, isActive);
        //       // For now, just log the change
        //       console.log(
        //         `Changing status for ${params.row.empId} to ${isActive ? "Active" : "Inactive"}`
        //       );
        //     } catch (error) {
        //       console.error("Failed to update status:", error);
        //     } finally {
        //       setIsLoading(false);
        //     }
        //   }}
        //   title={`Change status for ${params.row.empId} to ${
        //     params.row.isActive === "Active" ? "Inactive" : "Active"
        //   }`}
        // />
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <Tooltip title="View Performance Review">
            <IconButton
              onClick={() => {
                if (params.row.id) {
                  router.push(
                    `/performance/performance-review?employeeId=${params.row.id}`
                  );
                }
              }}
              sx={{ color: "#6B7280" }}
            >
              <Visibility />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  return (
    <Box className="employee-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="employee-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Performance Review List</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
        </Box>
        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 400,
              maxHeight: "calc(100vh - 200px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Performance Review</Typography>
              <PolicyFilters
                departments={departments.map((dept) => dept.departmentName)}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                showDesignationFilter={false}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => {}}
                selectedLeaveType={""}
                setSelectedLeaveType={() => {}}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              apiRef={apiRef}
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              initialState={{
                pagination: { paginationModel: { pageSize: pageSize } },
              }}
              paginationModel={{ page: page - 1, pageSize: pageSize }}
              onPaginationModelChange={(model) => {
                if (model.pageSize !== pageSize) {
                  setPageSize(model.pageSize);
                  setLimit(model.pageSize);
                  setPage(1);
                } else {
                  setPage(model.page + 1);
                }
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: QuickSearchToolbar,
              }}
            />
          </Box>
        </Paper>
      </Box>
    </Box>
  );
}

function EmployeesListPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <EmployeesListContent />
    </Suspense>
  );
}

export default EmployeesListPage;

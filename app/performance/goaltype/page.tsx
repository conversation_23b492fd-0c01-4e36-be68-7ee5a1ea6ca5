"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Di<PERSON><PERSON>,
  Typo<PERSON>,
  Paper,
} from "@mui/material";
import { useCallback, useState } from "react";
import "./GoalType.scss";
import {
  HomeOutlined,
  ControlPoint,
  EditNote,
  Delete,
  Circle,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import PolicyFilters from "@/components/policyFilter/PolicyFilters";
import Loader from "@/components/Loader/Loader";
import ReadMore from "@/components/ReadMore/ReadMore";
import useFetchGoalsType from "@/app/hooks/goals/useFetchGoalsType";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { toast } from "react-toastify";
import {
  addGoalType,
  updateGoalType,
  deleteGoalType,
  getGoalTypeById,
} from "@/app/services/goals/goals.service";
import AddEditGoalType from "./AddEditGoalType";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";

// Utility to strip HTML tags
const stripHtmlTags = (html: string) => {
  const div = document.createElement("div");
  div.innerHTML = html;
  return div.textContent || div.innerText || "";
};

// Define GoalType interface
interface GoalType {
  _id: string;
  goalType: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface GoalTypeFormValues {
  goalType: string;
  description: string;
  isActive: boolean;
}

// QuickSearchToolbar component


export default function GoalsContent() {
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Performance", href: "" },
    { label: "Goals" },
  ];
  
  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);
  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedGoalTypeId, setSelectedGoalTypeId] = useState<string | null>(
    null
  );
  const [selectedGoalTypeData, setSelectedGoalTypeData] =
    useState<GoalType | null>(null);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>("Date Range");
  const [, setStartDate] = useState<string>("");
  const [, setEndDate] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const currentDate = new Date();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [goalTypeToDelete, setGoalTypeToDelete] = useState<
    string | number | null
  >(null);

  // Restrict access to USER_ROLES
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const [goalTypesData, total] = useFetchGoalsType({
    setIsLoading,
    refresh,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,      
  }) as [GoalType[], number];

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  const rows: GridRowsProp = goalTypesData
    ? goalTypesData.map((goalType: GoalType) => ({
        id: goalType._id,
        goalType: goalType.goalType || "-",
        description: stripHtmlTags(goalType.description) || "-",
        isActive: goalType.isActive || false,
        createdAt: goalType.createdAt
          ? new Date(goalType.createdAt).toLocaleDateString()
          : "-",
      }))
    : [];

  const columns: GridColDef[] = [
    {
      field: "goalType",
      headerName: "Goal Type",
      minWidth: 191.969,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{ color: "#202C4B", fontWeight: "500 !important" }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "description",
      headerName: "Description",
      flex: 3,
      minWidth: 300,
      renderCell: (params) => (
        <Box
          sx={{
            width: "100%",
            maxWidth: "100%",
            padding: "8px 0",
            color: "#6B7280",
          }}
        >
          <ReadMore
            text={params.value}
            maxChars={50}
            sx={{
              width: "100%",
              "& .MuiTypography-root": {
                width: "100%",
                whiteSpace: "normal",
                overflow: "visible",
              },
            }}
          />
        </Box>
      ),
    },
    {
      field: "isActive",
      headerName: "Status",
      minWidth: 84.4375,
      flex: 1,
      renderCell: (params) => (
        // <Box
        //   sx={{
        //     backgroundColor: params.value ? "#03C95A" : "#E70D0D",
        //     color: "#fff",
        //     borderRadius: "4px",
        //     display: "flex",
        //     textAlign: "center",
        //     maxWidth: "60px",
        //     justifyContent: "center",
        //     fontSize: "10px",
        //     fontWeight: 500,
        //     padding: "0px 25px",
        //     lineHeight: "18px",
        //     letterSpacing: "0.5px",
        //     alignItems: "center",
        //   }}
        // >
        //   <Circle sx={{ fontSize: "6px", marginRight: "4px" }} />
        //   {params.value ? "Active" : "Inactive"}
        // </Box>

        <StatusToggle
          isActive={params.value}
          onChange={async (newStatus) => {
            try {
              await updateGoalType(params.row.id, {
                isActive: newStatus,
              });
              setRefresh(!refresh);
              toast.success(
                `Goal type status updated to ${newStatus ? "Active" : "Inactive"}!`
              );
            } catch (error) {
              console.error("Failed to update goal type status:", error);
              toast.error(
                "Failed to update goal type status. Please try again."
              );
            }
          }}
          title={`Change status to ${params.value ? "Inactive" : "Active"}`}
        />
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  const handleEditClick = async (goalTypeId: string | number) => {
    setSelectedGoalTypeId(goalTypeId as string);
    setIsEditMode(true);
    try {
      const response = await getGoalTypeById(goalTypeId as string);
      const goalType = response.goalType; // Access the nested goalType object
      setSelectedGoalTypeData({
        _id: goalType._id,
        goalType: goalType.goalType || "",
        description: goalType.description || "", // Keep HTML content for JoditEditor
        isActive: goalType.isActive ?? false,
        createdAt: goalType.createdAt || "",
        updatedAt: goalType.updatedAt || "",
      });
      setOpenModal(true);
    } catch (error) {
      console.error("Failed to fetch goal type data:", error);
      toast.error("Failed to load goal type data. Please try again.");
    }
  };

  const handleDeleteClick = (goalTypeId: string | number) => {
    setGoalTypeToDelete(goalTypeId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (goalTypeToDelete !== null) {
      try {
        await deleteGoalType(goalTypeToDelete as string);
        setDeleteDialogOpen(false);
        setGoalTypeToDelete(null);
        setRefresh(!refresh);
        toast.success("Goal type deleted successfully!");
      } catch (error) {
        console.error("Failed to delete goal type:", error);
        toast.error("Failed to delete goal type. Please try again.");
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setGoalTypeToDelete(null);
  };

  const handleDialogSubmit = async (values: GoalTypeFormValues) => {
    try {
      const goalTypeBody = {
        goalType: values.goalType,
        description: values.description,
        isActive: values.isActive,
      };

      if (isEditMode && selectedGoalTypeId) {
        await updateGoalType(selectedGoalTypeId, goalTypeBody);
        toast.success("Goal type updated successfully!");
      } else {
        await addGoalType(goalTypeBody);
        toast.success("Goal type added successfully!");
      }
      setRefresh(!refresh);
    } catch (error) {
      console.error(
        `Failed to ${isEditMode ? "update" : "add"} goal type:`,
        error
      );
      toast.error(
        `Failed to ${isEditMode ? "update" : "add"} goal type. Please try again.`
      );
      throw error;
    }
    handleDialogClose();
  };

  const handleDialogClose = () => {
    setOpenModal(false);
    setIsEditMode(false);
    setSelectedGoalTypeId(null);
    setSelectedGoalTypeData(null);
  };

  return (
    <Box className="goals-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="goals-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Goal Type</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="add-goal-type"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => {
                setIsEditMode(false);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ width: "14px", height: "14px" }} />
              Add Goal Type
            </Button>
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 550,
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Goal Type List</Typography>
              <PolicyFilters
                departments={[]}
                selectedDepartment={""}
                setSelectedDepartment={() => {}}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={false}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                setPage(model.page + 1);
                setPageSize(model.pageSize);
                setLimit(model.pageSize);
                setRefresh((prev) => !prev);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
              getRowHeight={() => "auto"}
              sx={{
                "& .MuiDataGrid-cell": {
                  maxHeight: "none !important",
                  overflow: "visible !important",
                  whiteSpace: "normal !important",
                  lineHeight: "1.5 !important",
                  display: "flex !important",
                  alignItems: "center !important",
                  padding: "8px 16px !important",
                },
                "& .MuiDataGrid-row": {
                  maxHeight: "none !important",
                },
              }}
            />
          </Box>
        </Paper>
      </Box>

      <AddEditGoalType
        open={openModal}
        onClose={handleDialogClose}
        onSubmit={handleDialogSubmit}
        initialValues={selectedGoalTypeData}
        isEditMode={isEditMode}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this goal type? This action cannot be undone."
      />
    </Box>
  );
}

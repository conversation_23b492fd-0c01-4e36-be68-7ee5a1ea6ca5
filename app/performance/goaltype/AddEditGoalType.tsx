"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  Select,
  MenuItem,
  Box,
  IconButton,
  Typography,
  InputLabel,
} from "@mui/material";
import { Cancel, Close } from "@mui/icons-material";
import { useState } from "react";
import { Formik, Form, Field, FieldProps } from "formik";
import * as Yup from "yup";
import JoditEditorWrapper from "@/components/JoditEditorWrapper";
import Loader from "@/components/Loader/Loader";
import "./AddEditGoalType.scss";

// Define GoalType interface
interface GoalType {
  _id: string;
  goalType: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface GoalTypeFormValues {
  goalType: string;
  description: string;
  isActive: boolean;
}

// Define props for AddEditGoalTypes
interface AddEditGoalTypesProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: GoalTypeFormValues) => Promise<void>;
  initialValues: GoalType | null;
  isEditMode: boolean;
}

// Validation schema using Yup
const validationSchema = Yup.object({
  goalType: Yup.string().required("Goal Type is required"),
  description: Yup.string().required("Description is required"),
});

export default function AddEditGoalType({
  open,
  onClose,
  onSubmit,
  initialValues,
  isEditMode,
}: AddEditGoalTypesProps) {
  const [loading, setLoading] = useState(false);

  const handleSubmitForm = async (values: GoalTypeFormValues) => {
    try {
      await onSubmit(values);
      onClose();
    } catch (error) {
      console.error("Form submission failed:", error);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
      className="dialog-container"
    >
      <DialogTitle className="dialog-title">
        <Typography variant="h6" className="title-text">
          {isEditMode ? "Edit Goal Type" : "Add Goal Type"}
        </Typography>
        <IconButton
          color="inherit"
          onClick={onClose}
          aria-label="close"
          sx={{
            backgroundColor: "#6b7280",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": { backgroundColor: "#d55a1d" },
            "& .MuiSvgIcon-root": { fontSize: "14px" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      {loading ? (
        <DialogContent className="dialog-content">
          <Box className="loader-container">
            <Loader loading={loading} />
          </Box>
        </DialogContent>
      ) : (
        <Formik
          initialValues={{
            goalType: initialValues?.goalType || "",
            description: initialValues?.description || "",
            isActive: initialValues?.isActive ?? true,
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmitForm}
          enableReinitialize
        >
          {(formikProps) => (
            <Form className="goal-type-form">
              <DialogContent className="dialog-content">
                <Box className="form-field">
                  <label className="form-label">
                    Goal Type <span className="required">*</span>
                  </label>
                  <Field name="goalType">
                    {({
                      field,
                      meta,
                    }: FieldProps<string, GoalTypeFormValues>) => (
                      <TextField
                        {...field}
                        fullWidth
                        variant="outlined"
                        error={meta.touched && Boolean(meta.error)}
                        helperText={meta.touched && meta.error}
                        size="small"
                        className="text-field"
                      />
                    )}
                  </Field>
                </Box>

                <Box className="form-field">
                  <label className="form-label">
                    Description <span className="required">*</span>
                  </label>
                  <JoditEditorWrapper
                    value={formikProps.values.description}
                    onChange={(content) =>
                      formikProps.setFieldValue("description", content)
                    }
                    height={200}
                  />
                </Box>

                <Box className="form-field">
                  <label className="form-label">
                    Status <span className="required">*</span>
                  </label>
                  <Field name="isActive">
                    {({ field }: FieldProps<boolean, GoalTypeFormValues>) => (
                      <FormControl
                        fullWidth
                        size="small"
                        className="select-control"
                      >
                        <Select
                          {...field}
                          value={field.value ? "active" : "inactive"}
                          onChange={(e) => {
                            formikProps.setFieldValue(
                              "isActive",
                              e.target.value === "active"
                            );
                          }}
                          className="select-field"
                        >
                          <MenuItem value="active">Active</MenuItem>
                          <MenuItem value="inactive">Inactive</MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  </Field>
                </Box>

                <DialogActions className="dialog-actions">
                  <Button
                    onClick={onClose}
                    color="inherit"
                    className="cancel-button"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    className="submit-button"
                  >
                    {isEditMode ? "Save Changes" : "Add Goal Type"}
                  </Button>
                </DialogActions>
              </DialogContent>
            </Form>
          )}
        </Formik>
      )}
    </Dialog>
  );
}

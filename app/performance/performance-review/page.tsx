"use client";

import { useEffect, useState } from "react";
import { Box, Typography } from "@mui/material";
import "./performance-review.scss";
import { HomeOutlined } from "@mui/icons-material";
import Loader from "@/components/Loader/Loader";
import { useSearchParams } from "next/navigation";

import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import EmployeeBasicInformationTable from "./ReviewTable/EmployeeBasicInformationTable/EmployeeBasicInformationTable";
import ProfessionalExcellence from "./ReviewTable/ProfessionalExcellence/ProfessionalExcellence";
import PersonalExcellence from "./ReviewTable/PersonalExcellence/PersonalExcellence";
import SpecialInitiatives from "./ReviewTable/SpecialInitiatives/SpecialInitiatives";
import CommentsTable from "./ReviewTable/CommentsTable/Comments";
import StrengthTable from "./ReviewTable/StrengthTable/StrengthTable";
import ByReportingOfficer from "./ReviewTable/ByReportingOfficer/ByReportingOfficer";
import ByHeadDepartment from "./ReviewTable/ByHeadDepartment/ByHeadDepartment";
import PersonalGoals from "./ReviewTable/PersonalGoals/PersonalGoals";
import ProfessionalGoalLastYear from "./ReviewTable/ProfessionalGoalLastYear/ProfessionalGoalLastYear";
import TrainingRequirements from "./ReviewTable/TrainingRequirements/TrainingRequirements";
import OtherGeneralComments from "./ReviewTable/OtherGeneralComments/OtherGeneralComments";
import PersonalUpdates from "./ReviewTable/PersonalUpdates/PersonalUpdates";
import ROUseOnlyForm from "./ReviewTable/ROUseOnlyForm/ROUseOnlyForm";
import HRDForm from "./ReviewTable/HRDForm/HRDForm";
import LastForm from "./ReviewTable/LastForm/LastForm";
import useAuthStore from "@/store/authStore";
import { getPerformanceReviewByEmployeeId } from "@/app/services/performance/performance.service";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";

function PerformanceReview() {
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Performance", href: "" },
    { label: "Performance Review" },
  ];

  // Get employee ID from URL query parameter
  const searchParams = useSearchParams();
  const queryEmployeeId = searchParams.get("employeeId");

  // Get auth store data for role check
  const { roles, employeeId: authEmployeeId } = useAuthStore();
  const employeeId = queryEmployeeId || authEmployeeId;
  const isEmployee = roles.includes("Employee");
  const [performanceData, setPerformanceData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Access control hook
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  useEffect(() => {
    const fetchPerformanceReview = async () => {
      if (!employeeId) return;

      try {
        const response = await getPerformanceReviewByEmployeeId(employeeId);
        setPerformanceData(response.performance);
      } catch (error) {
        console.error("Error fetching performance review:", error);
        setPerformanceData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchPerformanceReview();
  }, [employeeId]);

  if (loading) {
    return <Loader loading={loading} />;
  }

  return (
    <Box className="performance-review-container">
      <Box className="content">
        <Box className="performance-review-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Performance Appraisal</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
        </Box>

        <EmployeeBasicInformationTable empId={employeeId || ""} />
        <ProfessionalExcellence
          empId={employeeId || ""}
          performanceData={performanceData}
        />
        <PersonalExcellence
          empId={employeeId || ""}
          performanceData={performanceData}
        />
        <SpecialInitiatives
          empId={employeeId || ""}
          performanceData={performanceData}
        />
        <CommentsTable
          empId={employeeId || ""}
          performanceData={performanceData}
        />
        <StrengthTable
          empId={employeeId || ""}
          performanceData={performanceData}
        />
        {!isEmployee && (
          <ByReportingOfficer
            empId={employeeId || ""}
            performanceData={performanceData}
          />
        )}
        {!isEmployee && (
          <ByHeadDepartment
            empId={employeeId || ""}
            performanceData={performanceData}
          />
        )}
        <PersonalGoals
          empId={employeeId || ""}
          performanceData={performanceData}
        />
        <PersonalUpdates
          empId={employeeId || ""}
          performanceData={performanceData}
        />
        <ProfessionalGoalLastYear
          empId={employeeId || ""}
          performanceData={performanceData}
        />
        <TrainingRequirements
          empId={employeeId || ""}
          performanceData={performanceData}
        />
        <OtherGeneralComments
          empId={employeeId || ""}
          performanceData={performanceData}
        />
        {!isEmployee && (
          <ROUseOnlyForm
            empId={employeeId || ""}
            performanceData={performanceData}
          />
        )}
        {!isEmployee && (
          <HRDForm empId={employeeId || ""} performanceData={performanceData} />
        )}
        <LastForm empId={employeeId || ""} performanceData={performanceData} />
      </Box>
    </Box>
  );
}

export default PerformanceReview;

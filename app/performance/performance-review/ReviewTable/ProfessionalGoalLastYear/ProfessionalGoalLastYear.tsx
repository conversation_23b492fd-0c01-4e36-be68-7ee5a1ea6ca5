"use client";

import React, { useEffect, useState } from "react";
import ReusableTableForm from "../ReusableTable/ReusableTable";
import { Box } from "@mui/material";
import { postProfessionalGoals, ProfessionalGoalsPayload } from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

interface ProfessionalGoalLastYearProps {
    empId: string;
    performanceData: any;
}

function ProfessionalGoalLastYear({ empId, performanceData }: ProfessionalGoalLastYearProps) {
    const [initialData, setInitialData] = useState<any[]>([]);

    // Use performanceData prop to set initial data
    useEffect(() => {
        if (performanceData?.professionalGoals) {
            setInitialData(performanceData.professionalGoals);
        } else {
            setInitialData([]); // Default to empty array if no data
        }
    }, [performanceData]);

    const handleSubmit = async (values: any) => {
        try {
            const formattedData: ProfessionalGoalsPayload = {
                empId,
                professionalGoals: values.rows.map((row: any) => ({
                    professionalGoalsAcheiveLastYearBySelf: row.byself,
                    professionalGoalsAcheiveLastYearByRo: row.roComment,
                    professionalGoalsAcheiveLastYearByHod: row.hodComment,
                    professionalGoalsAcheiveForthComingYearBySelf: "",
                    professionalGoalsAcheiveForthComingYearByRo: "", 
                    professionalGoalsAcheiveForthComingYearByHod: "", 
                })),
            };
            await postProfessionalGoals(formattedData);
            toast.success("Professional goals submitted successfully!");
        } catch (error) {
            console.error("Failed to submit professional goals:", error);
            // toast.error("Failed to submit professional goals. Please try again.");
        }
    };

    const initialRows = initialData.map((item) => ({
        byself: item.professionalGoalsAcheiveLastYearBySelf || "",
        roComment: item.professionalGoalsAcheiveLastYearByRo || "",
        hodComment: item.professionalGoalsAcheiveLastYearByHod || "",
    }));

    return (
        <Box>
            <ReusableTableForm
                heading="Professional Goals Achieved for last year"
                subHeading=""
                columns={[
                    { name: "By Self", key: "byself" },
                    { name: "RO's Comment", key: "roComment" },
                    { name: "HOD's Comment", key: "hodComment" },
                ]}
                onSubmit={handleSubmit}
                initialRows={initialRows.length > 0 ? initialRows : undefined}
            />
        </Box>
    );
}

export default ProfessionalGoalLastYear;
import React, { useEffect, useState } from "react";
import { Formik, Form, Field } from "formik";
import {
  <PERSON><PERSON>ield,
  Button,
  Box,
  Typography,
  CardContent,
  Card,
  CircularProgress,
} from "@mui/material";
import {
  postPerformanceApproval,
  PerformanceApprovalPayload,
} from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

const parameters = ["Employee", "Reporting Officer", "HOD", "HRD"];

interface LastFormProps {
  empId: string;
  performanceData?: any; // Optional prop for pre-fetched performance data
}

const LastForm = ({ empId, performanceData }: LastFormProps) => {
  // Default initial values
  const defaultInitialValues = parameters.reduce(
    (acc, param) => {
      acc[param] = { name: "", signature: "", date: "" };
      return acc;
    },
    {} as Record<string, { name: string; signature: string; date: string }>
  );

  const [initialValues, setInitialValues] = useState(defaultInitialValues);

  // Move useEffect to the top level of the component
  useEffect(() => {
    if (performanceData && performanceData.performanceApprovals) {
      const updatedValues = parameters.reduce(
        (acc, param) => {
          const matchingApproval = performanceData.performanceApprovals.find(
            (item: any) => item.userRole === param
          );
          acc[param] = {
            name: matchingApproval?.userName || "",
            signature: matchingApproval?.userSignature || "",
            date: matchingApproval?.userDate
              ? new Date(matchingApproval.userDate).toISOString().split("T")[0] // Format to YYYY-MM-DD
              : "",
          };
          return acc;
        },
        {} as Record<string, { name: string; signature: string; date: string }>
      );

      setInitialValues(updatedValues);
    }
  }, [performanceData]);

  return (
    <Card
      sx={{
        marginBottom: "1.5rem",
        boxShadow: 3,
        borderRadius: "5px",
        "& .MuiCardContent-root": {
          padding: "0px !important",
        },
        "& .MuiFormControl-root": {
          margin: "0px !important",
        },
      }}
    >
      <CardContent>
        <Box
          sx={{
            borderColor: "#E5E7EB",
            position: "relative",
            background: "transparent",
            padding: "1rem 1.25rem 1rem",
          }}
        >
          <Typography
            variant="h3"
            align="center"
            sx={{
              fontSize: "20px",
              fontWeight: "600",
              marginBottom: "0.5rem",
            }}
          >
            Performance Approval Signatures
          </Typography>
          <Typography
            align="center"
            sx={{ fontSize: "14px", color: "#677788" }}
          >
            Final approval signatures from all parties
          </Typography>
        </Box>

        <Formik
          initialValues={initialValues}
          enableReinitialize={true} // Allow reinitialization when initialValues change
          onSubmit={async (values, { setSubmitting }) => {
            try {
              setSubmitting(true);
              const payload: PerformanceApprovalPayload = {
                empId,
                performanceApprovals: parameters.map((param) => ({
                  userRole: param,
                  userName: values[param].name,
                  userSignature: values[param].signature,
                  userDate: values[param].date,
                })),
              };
              await postPerformanceApproval(payload);
              toast.success("Performance approval submitted successfully!");
            } catch (error) {
              console.error("Failed to submit performance approval:", error);
              // toast.error(
              //   "Failed to submit performance approval. Please try again."
              // );
            } finally {
              setSubmitting(false);
            }
          }}
        >
          {({ values, handleChange, isSubmitting }) => (
            <Form>
              <Box border="1px solid #ccc" borderRadius="8px">
                <Box
                  p={2}
                  bgcolor="#F3F4F6"
                  fontWeight="bold"
                  sx={{ display: "flex", width: "100%" }}
                >
                  <Box sx={{ width: "22%" }}>
                    <Typography>Role</Typography>
                  </Box>
                  <Box sx={{ width: "22%" }}>
                    <Typography>Name</Typography>
                  </Box>
                  <Box sx={{ width: "22%" }}>
                    <Typography>Signature</Typography>
                  </Box>
                  <Box sx={{ width: "22%" }}>
                    <Typography>Date</Typography>
                  </Box>
                </Box>

                {parameters.map((param, index) => (
                  <Box
                    key={index}
                    display="flex"
                    alignItems="center"
                    p={2}
                    borderBottom="1px solid #ddd"
                  >
                    <Box sx={{ width: "20%" }}>
                      <Typography flex={2}>{param}</Typography>
                    </Box>
                    <Box sx={{ width: "20%", margin: "0px 10px" }}>
                      <Field
                        fullWidth
                        as={TextField}
                        name={`${param}.name`}
                        onChange={handleChange}
                        value={values[param].name}
                        sx={{ mx: 2 }}
                        disabled={isSubmitting}
                        placeholder="Full Name"
                      />
                    </Box>
                    <Box sx={{ width: "20%", margin: "0px 10px" }}>
                      <Field
                        fullWidth
                        as={TextField}
                        name={`${param}.signature`}
                        onChange={handleChange}
                        value={values[param].signature}
                        sx={{ mx: 2 }}
                        disabled={isSubmitting}
                        placeholder="Digital Signature"
                      />
                    </Box>
                    <Box sx={{ width: "20%", margin: "0px 10px" }}>
                      <Field
                        fullWidth
                        as={TextField}
                        name={`${param}.date`}
                        onChange={handleChange}
                        value={values[param].date}
                        sx={{ flex: 1 }}
                        disabled={isSubmitting}
                        type="date"
                        InputLabelProps={{ shrink: true }}
                      />
                    </Box>
                  </Box>
                ))}

                <Box
                  textAlign="center"
                  mt={3}
                  display="flex"
                  justifyContent="flex-end"
                  p={2}
                >
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={isSubmitting}
                    sx={{ background: "#f26522", textTransform: "none" }}
                    startIcon={
                      isSubmitting ? <CircularProgress size={20} /> : null
                    }
                  >
                    {isSubmitting ? "Saving..." : "Save"}
                  </Button>
                </Box>
              </Box>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  );
};

export default LastForm;

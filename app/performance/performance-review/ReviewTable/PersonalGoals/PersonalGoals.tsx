"use client";

import React, { useEffect, useState } from "react";
import { Box } from "@mui/material";
import ReusableTableForm from "../ReusableTable/ReusableTable";
import { postPersonalGoals, PersonalGoalsPayload } from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

interface PersonalGoalsProps {
    empId: string;
    performanceData: any; // Data passed from PerformanceReview
}

interface PersonalGoal {
    personalGoalsAchievedLastYear: string;
    personalGoalsSetCurrentYear: string;
    _id: string;
}

const PersonalGoals = ({ empId, performanceData }: PersonalGoalsProps) => {
    const [initialData, setInitialData] = useState<PersonalGoal[]>([]);

    // Use performanceData prop to set initial data
    useEffect(() => {
        if (performanceData?.personalGoals) {
            setInitialData(performanceData.personalGoals);
        } else {
            setInitialData([]); // Default to empty array if no data
        }
    }, [performanceData]);

    const columns = [
        { name: "Goal Achieved during last year", key: "goalAchieved" },
        { name: "Goal set for current year", key: "goalSet" },
    ];

    const handleSubmit = async (values: { rows: { goalAchieved: string; goalSet: string }[] }) => {
        try {
            const payload: PersonalGoalsPayload = {
                empId,
                personalGoals: values.rows.map((row) => ({
                    personalGoalsAchievedLastYear: row.goalAchieved,
                    personalGoalsSetCurrentYear: row.goalSet,
                })),
            };
            await postPersonalGoals(payload);
            toast.success("Personal goals submitted successfully!");
        } catch (error) {
            console.error("Failed to submit personal goals:", error);
            // toast.error("Failed to submit personal goals. Please try again.");
            throw error;
        }
    };

    // Transform initial data to match ReusableTableForm expected format
    const initialRows = initialData.map((item) => ({
        goalAchieved: item.personalGoalsAchievedLastYear || "",
        goalSet: item.personalGoalsSetCurrentYear || "",
    }));

    return (
        <Box>
            <ReusableTableForm
                heading="Personal Goals"
                subHeading=""
                columns={columns}
                onSubmit={handleSubmit}
                initialRows={initialRows.length > 0 ? initialRows : undefined}
            />
        </Box>
    );
};

export default PersonalGoals;
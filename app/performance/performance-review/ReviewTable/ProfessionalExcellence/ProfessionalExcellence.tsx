"use client";

import React, { useEffect, useState } from "react";
import ExcellenceTable from "../ExcellenceTable/ExcellenceTable";
import { postProfessionalExcellence, ProfessionalExcellencePayload } from "@/app/services/performance/performance.service";
import { Typography } from "@mui/material";
import useAuthStore from "@/store/authStore";
import { toast } from "react-toastify";

const ProfessionalExcellence = ({ empId, performanceData }: { empId: string; performanceData: any }) => {
    const data = [
        { area: "Production", indicators: ["Quality", "TAT (Turn Around Time)"] },
        { area: "Process Improvement", indicators: ["PMS, New Ideas"] },
        { area: "Team Management", indicators: ["Team Productivity, Dynamics, Attendance, Attrition"] },
        { area: "Knowledge Sharing", indicators: ["Sharing knowledge for team productivity"] },
        { area: "Reporting and Communication", indicators: ["Emails/Calls/Reports and Other Communication"] },
    ];

    const { roles } = useAuthStore(); 
    const isEmployee = roles.includes("Employee");

    const [open, setOpen] = useState(false);
    const [initialValues, setInitialValues] = useState<any>(null);

    useEffect(() => {
        if (performanceData && performanceData.professionalExcellence) {
            const profExcellence = performanceData.professionalExcellence;
            const fetchedValues = {
                weightage: profExcellence.map((item: any) => item.weightage || 0),
                percentageSelf: profExcellence.map((item: any) => item.percentAchievSelf || ""),
                pointsSelf: profExcellence.map((item: any) => item.pointScoredSelf || 0),
                percentageRO: profExcellence.map((item: any) => item.percentAchievRo || ""),
                pointsRO: profExcellence.map((item: any) => item.pointScoredRo || 0),
            };
            setInitialValues(fetchedValues);
        } else {
            setInitialValues({
                weightage: Array(data.length).fill(0),
                percentageSelf: Array(data.length).fill(""),
                pointsSelf: Array(data.length).fill(0),
                percentageRO: Array(data.length).fill(""),
                pointsRO: Array(data.length).fill(0),
            });
        }
    }, [performanceData]);

    const handleSave = async (values: any) => {
        const payload: ProfessionalExcellencePayload = {
            empId,
            professionalExcellence: data.map((item, index) => ({
                keyResultArea: item.area,
                keyPerformanceIndicator: item.indicators.join(", "),
                weightage: Number(values.weightage[index]) || 0,
                percentAchievSelf: Number(values.percentageSelf[index]) || 0,
                percentAchievRo: Number(values.percentageRO[index]) || 0,
            })),
        };

        try {
            await postProfessionalExcellence(payload);
            toast.success("Professional Excellence saved successfully");
            setOpen(true);
            console.log("Professional Excellence data posted successfully");
        } catch (error) {
            console.error("Failed to post Professional Excellence data:", error);
        }
    };

    if (!initialValues) {
        return <Typography>Loading...</Typography>;
    }

    return (
        <div>
            <ExcellenceTable 
                title="Professional Excellence" 
                data={data} 
                onSave={handleSave}
                initialValues={initialValues}
                // disabled={isEmployee} // Disable the table for Employee role
            />
        </div>
    );
};

export default ProfessionalExcellence;
import React, { useEffect } from "react";
import { Formik, Form, Field } from "formik";
import {
  TextField,
  Button,
  Box,
  Typography,
  CardContent,
  Card,
  CircularProgress,
} from "@mui/material";
import {
  postAssessmentByHod,
  AssessmentByHodPayload,
} from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

const parameters = [
  "KRAs Target Achievement Points (will be considered from the overall score specified in this document by the Reporting officer)",
  "Professional Skills Scores (RO’s Points furnished in the skill & attitude assessment sheet will be considered)",
  "Personal Skills Scores (RO’s Points furnished in the skill & attitude assessment sheet will be considered)",
  "Special Achievements Score (HOD to furnish)",
  "Overall Total Score",
];

interface HRDFormProps {
  empId: string;
  performanceData?: any; // Optional prop for pre-fetched performance data
}

const HRDForm = ({ empId, performanceData }: HRDFormProps) => {
  // Default initial values
  const defaultInitialValues = parameters.reduce(
    (acc, param) => {
      acc[param] = { availablePoints: "", pointsScored: "", comment: "" };
      return acc;
    },
    {} as Record<
      string,
      { availablePoints: string; pointsScored: string; comment: string }
    >
  );

  const [initialValues, setInitialValues] =
    React.useState(defaultInitialValues);

  // Move useEffect to the top level of the component
  useEffect(() => {
    if (performanceData && performanceData.assessmentByHod) {
      const updatedValues = parameters.reduce(
        (acc, param) => {
          const matchingAssessment = performanceData.assessmentByHod.find(
            (item: any) => item.overallParameterByHod === param
          );
          acc[param] = {
            availablePoints: matchingAssessment?.availablePointByHod || "",
            pointsScored: matchingAssessment?.pointScoredByHod || "",
            comment: matchingAssessment?.roComment || "",
          };
          return acc;
        },
        {} as Record<
          string,
          { availablePoints: string; pointsScored: string; comment: string }
        >
      );

      setInitialValues(updatedValues);
    }
  }, [performanceData]); // Dependency array includes performanceData

  return (
    <Card
      sx={{
        marginBottom: "1.5rem",
        boxShadow: 3,
        borderRadius: "5px",
        "& .MuiCardContent-root": {
          padding: "0px !important",
        },
        "& .MuiFormControl-root": {
          margin: "0px !important",
        },
      }}
    >
      <CardContent>
        <Box
          sx={{
            borderColor: "#E5E7EB",
            position: "relative",
            background: "transparent",
            padding: "1rem 1.25rem 1rem",
          }}
        >
          <Typography
            variant="h3"
            align="center"
            sx={{
              fontSize: "20px",
              fontWeight: "600",
              marginBottom: "0.5rem",
            }}
          >
            For HRD's Use Only
          </Typography>
          <Typography
            align="center"
            sx={{ fontSize: "14px", color: "#677788" }}
          >
            
          </Typography>
        </Box>

        <Formik
          initialValues={initialValues}
          enableReinitialize={true} // Allow reinitialization when initialValues change
          onSubmit={async (values, { setSubmitting }) => {
            try {
              setSubmitting(true);
              const payload: AssessmentByHodPayload = {
                empId,
                assessmentByHod: parameters.map((param) => ({
                  overallParameterByHod: param,
                  availablePointByHod: values[param].availablePoints,
                  pointScoredByHod: values[param].pointsScored,
                  roComment: values[param].comment,
                })),
              };
              await postAssessmentByHod(payload);
              toast.success("HRD assessment submitted successfully!");
            } catch (error) {
              console.error("Failed to submit HRD assessment:", error);
              // toast.error("Failed to submit HRD assessment. Please try again.");
            } finally {
              setSubmitting(false);
            }
          }}
        >
          {({ values, handleChange, isSubmitting }) => (
            <Form>
              <Box p={3} border="1px solid #ccc" borderRadius="8px">
                <Box
                  p={2}
                  bgcolor="#F3F4F6"
                  fontWeight="bold"
                  sx={{ display: "flex", width: "100%" }}
                >
                  <Box sx={{ width: "60%" }}>
                    <Typography>Overall Parameters</Typography>
                  </Box>
                  <Box sx={{ width: "20%" }}>
                    <Typography>Available Points</Typography>
                  </Box>
                  <Box sx={{ width: "20%" }}>
                    <Typography>Points Scored</Typography>
                  </Box>
                  <Box sx={{ width: "20%" }}>
                    <Typography>RO's Comment</Typography>
                  </Box>
                </Box>

                {parameters.map((param, index) => (
                  <Box
                    key={index}
                    display="flex"
                    alignItems="center"
                    p={2}
                    borderBottom="1px solid #ddd"
                  >
                    <Box sx={{ width: "60%" }}>
                      <Typography flex={2}>{param}</Typography>
                    </Box>
                    <Box sx={{ width: "20%" }}>
                      <Field
                        fullWidth
                        as={TextField}
                        name={`${param}.availablePoints`}
                        onChange={handleChange}
                        value={values[param].availablePoints}
                        sx={{ mx: 2, width: "200px" }}
                        disabled={isSubmitting}
                        type="number"
                      />
                    </Box>
                    <Box sx={{ width: "20%" }}>
                      <Field
                        fullWidth
                        as={TextField}
                        name={`${param}.pointsScored`}
                        onChange={handleChange}
                        value={values[param].pointsScored}
                        sx={{ mx: 2, width: "200px" }}
                        disabled={isSubmitting}
                        type="number"
                      />
                    </Box>
                    <Box sx={{ width: "20%" }}>
                      <Field
                        as={TextField}
                        fullWidth
                        name={`${param}.comment`}
                        onChange={handleChange}
                        value={values[param].comment}
                        sx={{ flex: 1 }}
                        disabled={isSubmitting}
                      />
                    </Box>
                  </Box>
                ))}

                <Box
                  textAlign="center"
                  mt={3}
                  display="flex"
                  justifyContent="flex-end"
                >
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={isSubmitting}
                    sx={{ background: "#f26522", textTransform: "none" }}
                    startIcon={
                      isSubmitting ? <CircularProgress size={20} /> : null
                    }
                  >
                    {isSubmitting ? "Saving..." : "Save"}
                  </Button>
                </Box>
              </Box>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  );
};

export default HRDForm;

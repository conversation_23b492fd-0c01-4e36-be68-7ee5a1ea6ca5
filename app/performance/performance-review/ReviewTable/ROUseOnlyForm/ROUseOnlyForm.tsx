import React, { useEffect, useState } from "react";
import { Formik, Form, Field } from "formik";
import {
  MenuItem,
  Select,
  TextField,
  Button,
  Box,
  Typography,
  CardContent,
  Divider,
  Card,
  CircularProgress,
} from "@mui/material";
import {
  postAssessmentByRo,
  AssessmentByRoPayload,
} from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

const teamMemberIssues = [
  "The Team member has Work related Issues",
  "The Team member has Leave Issues",
  "The team member has Stability Issues",
  "The Team member exhibits non-supportive attitude",
  "Any other points in specific to note about the team member",
  "Overall Comment /Performance of the team member",
];

interface ROUseOnlyFormProps {
  empId: string;
  performanceData?: any; // Optional prop for pre-fetched performance data
}

const ROUseOnlyForm = ({ empId, performanceData }: ROUseOnlyFormProps) => {
  // Default initial values
  const defaultInitialValues = teamMemberIssues.reduce(
    (acc, issue) => {
      acc[issue] = { status: "", details: "" };
      return acc;
    },
    {} as Record<string, { status: string; details: string }>
  );

  const [initialValues, setInitialValues] = useState(defaultInitialValues);

  // Move useEffect to the top level of the component
  useEffect(() => {
    if (performanceData && performanceData.assessmentByRo) {
      const updatedValues = teamMemberIssues.reduce(
        (acc, issue) => {
          const matchingAssessment = performanceData.assessmentByRo.find(
            (item: any) => item.assessmentByRo === issue
          );
          acc[issue] = {
            status: matchingAssessment?.assessmentByRoAnswer || "",
            details: matchingAssessment?.assessmentByRoDetails || "",
          };
          return acc;
        },
        {} as Record<string, { status: string; details: string }>
      );

      setInitialValues(updatedValues);
    }
  }, [performanceData]);

  return (
    <Card
      sx={{
        marginBottom: "1.5rem",
        boxShadow: 3,
        borderRadius: "5px",
        "& .MuiCardContent-root": {
          padding: "0px !important",
        },
        "& .MuiFormControl-root": {
          margin: "0px !important",
        },
      }}
    >
      <CardContent>
        <Box
          sx={{
            borderColor: "#E5E7EB",
            position: "relative",
            background: "transparent",
            padding: "1rem 1.25rem 1rem",
          }}
        >
          <Typography
            variant="h3"
            align="center"
            sx={{
              fontSize: "20px",
              fontWeight: "600",
              marginBottom: "0.5rem",
            }}
          >
            For RO's Use Only
          </Typography>
          <Typography
            align="center"
            sx={{ fontSize: "14px", color: "#677788" }}
          >
            
          </Typography>
        </Box>

        <Divider />
        <Formik
          initialValues={initialValues}
          enableReinitialize={true} // Allow reinitialization when initialValues change
          onSubmit={async (values, { setSubmitting }) => {
            try {
              setSubmitting(true);
              const payload: AssessmentByRoPayload = {
                empId,
                assessmentByRo: teamMemberIssues.map((issue) => ({
                  assessmentByRo: issue,
                  assessmentByRoAnswer: values[issue].status,
                  assessmentByRoDetails: values[issue].details,
                })),
              };
              await postAssessmentByRo(payload);
              toast.success("Assessment submitted successfully!");
            } catch (error) {
              console.error("Failed to submit assessment:", error);
              // toast.error("Failed to submit assessment. Please try again.");
            } finally {
              setSubmitting(false);
            }
          }}
        >
          {({ values, handleChange, isSubmitting }) => (
            <Form>
              <Box>
                <Box
                  mt={3}
                  p={2}
                  bgcolor="#F3F4F6"
                  fontWeight="bold"
                  sx={{ display: "flex", width: "100%" }}
                >
                  <Box sx={{ width: "60%" }}>
                    <Typography>Issue</Typography>
                  </Box>
                  <Box sx={{ width: "12%" }}>
                    <Typography>Yes/No</Typography>
                  </Box>
                  <Box>
                    <Typography>If Yes-Details</Typography>
                  </Box>
                </Box>

                {teamMemberIssues.map((issue, index) => (
                  <Box
                    key={index}
                    display="flex"
                    alignItems="center"
                    p={1}
                    borderBottom="1px solid #ddd"
                  >
                    <Box sx={{ width: "60%" }}>
                      <Typography flex={1}>{issue}</Typography>
                    </Box>

                    <Box>
                      <Field
                        as={Select}
                        name={`${issue}.status`}
                        onChange={handleChange}
                        value={values[issue].status}
                        displayEmpty
                        sx={{ mx: 2, width: "150px" }}
                        disabled={isSubmitting}
                      >
                        <MenuItem value="">Select</MenuItem>
                        <MenuItem value="yes">Yes</MenuItem>
                        <MenuItem value="no">No</MenuItem>
                      </Field>
                    </Box>

                    <Box sx={{ width: "30%" }}>
                      <Field
                        fullWidth
                        as={TextField}
                        name={`${issue}.details`}
                        placeholder="If Yes - Details"
                        disabled={
                          values[issue].status !== "yes" || isSubmitting
                        }
                        sx={{ flex: 2 }}
                      />
                    </Box>
                  </Box>
                ))}

                <Box sx={{ padding: "1rem", textAlign: "right" }}>
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={isSubmitting}
                    startIcon={
                      isSubmitting ? <CircularProgress size={20} /> : null
                    }
                    sx={{ background: "#f26522", textTransform: "none" }}
                  >
                    {isSubmitting ? "Saving..." : "Save"}
                  </Button>
                </Box>
              </Box>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  );
};

export default ROUseOnlyForm;

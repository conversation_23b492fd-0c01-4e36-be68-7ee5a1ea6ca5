"use client";

import React, { useEffect, useState } from "react";
import ReusableTableForm from "../ReusableTable/ReusableTable";
import { Box } from "@mui/material";
import { postReviewOnRole } from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

interface StrengthTableProps {
    empId: string;
    performanceData: any; // Data passed from PerformanceReview
}

interface ReviewOnRole {
    commentOnRolestrength: string;
    commentOnRoleAreaForImprove: string;
    _id: string;
}

function StrengthTable({ empId, performanceData }: StrengthTableProps) {
    const [initialData, setInitialData] = useState<ReviewOnRole[]>([]);

    // Use performanceData prop to set initial data
    useEffect(() => {
        if (performanceData?.reviewOnRole) {
            setInitialData(performanceData.reviewOnRole);
        } else {
            setInitialData([]); // Default to empty array if no data
        }
    }, [performanceData]);

    const handleSubmit = async (values: any) => {
        try {
            const formattedData = {
                empId,
                reviewOnRole: values.rows.map((row: any) => ({
                    commentOnRolestrength: row.strengths,
                    commentOnRoleAreaForImprove: row.areaforimprovement,
                })),
            };
            await postReviewOnRole(formattedData);
            toast.success("Strength comments submitted successfully!");
        } catch (error) {
            console.error("Failed to submit strength comments:", error);
            // toast.error("Failed to submit strength comments. Please try again.");
        }
    };

    // Transform initial data to match ReusableTableForm expected format
    const initialRows = initialData.map((item) => ({
        strengths: item.commentOnRolestrength || "",
        areaforimprovement: item.commentOnRoleAreaForImprove || "",
    }));

    return (
        <Box>
            <ReusableTableForm
                heading="Comments on the role"
                subHeading="Alterations if any required like addition/deletion of responsibilities"
                columns={[
                    { name: "Strengths", key: "strengths" },
                    { name: "Area's for Improvement", key: "areaforimprovement" },
                ]}
                onSubmit={handleSubmit}
                initialRows={initialRows.length > 0 ? initialRows : undefined}
            />
        </Box>
    );
}

export default StrengthTable;
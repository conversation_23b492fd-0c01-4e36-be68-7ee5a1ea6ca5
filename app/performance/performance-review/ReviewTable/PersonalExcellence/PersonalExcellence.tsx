"use client";

import React, { useEffect, useState } from "react";
import ExcellenceTable from "../ExcellenceTable/ExcellenceTable";
import { postPersonalExcellence } from "@/app/services/performance/performance.service";
import type { PersonalExcellence } from "@/app/services/performance/performance.service";
import { Typography } from "@mui/material";

interface PersonalExcellenceProps {
    empId: string;
    performanceData: any; // Data passed from PerformanceReview
}

interface InitialValues {
    weightage: number[];
    percentageSelf: number[];
    pointsSelf: number[];
    percentageRO: number[];
    pointsRO: number[];
}

const PersonalExcellence: React.FC<PersonalExcellenceProps> = ({ empId, performanceData }) => {
    const data = [
        { area: "Personal Growth", indicators: ["Skill Development", "Certifications"] },
        { area: "Work-Life Balance", indicators: ["Time Management", "Stress Management"] },
        { area: "Community Engagement", indicators: ["Volunteering", "Social Responsibility"] },
    ];

    const [initialValues, setInitialValues] = useState<InitialValues | null>(null);

    useEffect(() => {
        if (performanceData?.personalExcellence) {
            const personalExcellence = performanceData.personalExcellence;

            // Map API data to match the static `data` array order
            const mappedData = data.map((staticItem) => {
                const apiItem = personalExcellence.find(
                    (item: any) => item.personalKeyResultArea === staticItem.area
                ) || {
                    personalWeightage: 0,
                    personalPercentAchievSelf: 0,
                    personalPointScoredSelf: 0,
                    personalPercentAchievRo: 0,
                    personalPointScoredRo: 0,
                };
                return apiItem;
            });

            const fetchedValues: InitialValues = {
                weightage: mappedData.map((item) => item.personalWeightage || 0),
                percentageSelf: mappedData.map((item) => item.personalPercentAchievSelf || 0),
                pointsSelf: mappedData.map((item) => item.personalPointScoredSelf || 0),
                percentageRO: mappedData.map((item) => item.personalPercentAchievRo || 0),
                pointsRO: mappedData.map((item) => item.personalPointScoredRo || 0),
            };

            setInitialValues(fetchedValues);
        } else {
            // Set default values if no personalExcellence data is found
            setInitialValues({
                weightage: Array(data.length).fill(0),
                percentageSelf: Array(data.length).fill(0),
                pointsSelf: Array(data.length).fill(0),
                percentageRO: Array(data.length).fill(0),
                pointsRO: Array(data.length).fill(0),
            });
        }
    }, [performanceData]);

    const handleSave = async (values: any) => {
        const payload: PersonalExcellence = {
            empId,
            personalExcellence: data.map((item, index) => ({
                personalKeyResultArea: item.area,
                personalKeyPerformanceIndicator: item.indicators.join(", "),
                personalWeightage: Number(values.weightage[index]) || 0,
                personalPercentAchievSelf: Number(values.percentageSelf[index]) || 0,
                personalPercentAchievRo: Number(values.percentageRO[index]) || 0,
                // personalPointScoredSelf: Number(values.pointsSelf[index]) || 0,
                // personalPointScoredRo: Number(values.pointsRO[index]) || 0,
            })),
        };

        try {
            await postPersonalExcellence(payload);
            console.log("Personal Excellence data posted successfully:", payload);
        } catch (error) {
            console.error("Failed to post Personal Excellence data:", error);
        }
    };

    if (!initialValues) {
        return <Typography>Loading...</Typography>;
    }

    return (
        <ExcellenceTable
            title="Personal Excellence"
            data={data}
            showTotalPercentageAndGrade={true}
            onSave={handleSave}
            initialValues={{
                ...initialValues,
                percentageSelf: initialValues.percentageSelf.map(String),
                percentageRO: initialValues.percentageRO.map(String),
            }}
        />
    );
};

export default PersonalExcellence;
"use client";

import React, { useEffect } from "react";
import { useFormik } from "formik";
import {
    Table,
    TableHead,
    TableRow,
    TableCell,
    TableBody,
    Select,
    MenuItem,
    TextField,
    Button,
    CardContent,
    Typography,
    Box,
    Divider,
    Card,
    FormControl,
    CircularProgress,
} from "@mui/material";
import { postPersonalUpdates, PersonalUpdatesPayload } from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

interface PersonalUpdatesProps {
    empId: string;
    performanceData: any; // Data passed from PerformanceReview
}

const PersonalUpdates = ({ empId, performanceData }: PersonalUpdatesProps) => {
    const formik = useFormik({
        initialValues: {
            updates: [
                { lastYear: "Married/Engaged?", currentYear: "Marriage Plans", yesNoLastYear: "", detailsLastYear: "", yesNoCurrentYear: "", detailsCurrentYear: "" },
                { lastYear: "Higher Studies/Certifications?", currentYear: "Plans For Higher Study", yesNoLastYear: "", detailsLastYear: "", yesNoCurrentYear: "", detailsCurrentYear: "" },
                { lastYear: "Health Issues?", currentYear: "Certification Plans", yesNoLastYear: "", detailsLastYear: "", yesNoCurrentYear: "", detailsCurrentYear: "" },
                { lastYear: "Others", currentYear: "Others", yesNoLastYear: "", detailsLastYear: "", yesNoCurrentYear: "", detailsCurrentYear: "" },
            ],
        },
        onSubmit: async (values, { setSubmitting }) => {
            try {
                setSubmitting(true);
                const payload: PersonalUpdatesPayload = {
                    empId,
                    personalUpdates: values.updates.map((update) => ({
                        personalUpdateslastYear: update.lastYear,
                        personalUpdateslastYearStatus: update.yesNoLastYear,
                        personalUpdateslastYearDetails: update.detailsLastYear,
                        personalUpdatesCurrentYear: update.currentYear,
                        personalUpdatesCurrentYearStatus: update.yesNoCurrentYear,
                        personalUpdatesCurrentYearDetails: update.detailsCurrentYear,
                    })),
                };
                await postPersonalUpdates(payload);
                toast.success("Personal updates submitted successfully!");
            } catch (error) {
                console.error("Error submitting personal updates:", error);
                // toast.error("Failed to submit personal updates. Please try again.");
            } finally {
                setSubmitting(false);
            }
        },
    });

    // Use performanceData prop to set initial data
    useEffect(() => {
        if (performanceData?.personalUpdates) {
            const personalUpdates = performanceData.personalUpdates;

            // Map the API response to the formik initial structure
            const updatedValues = formik.values.updates.map((defaultUpdate, index) => {
                const apiUpdate = personalUpdates[index] || {};
                return {
                    lastYear: defaultUpdate.lastYear,
                    currentYear: defaultUpdate.currentYear,
                    yesNoLastYear: apiUpdate.personalUpdateslastYearStatus || "",
                    detailsLastYear: apiUpdate.personalUpdateslastYearDetails || "",
                    yesNoCurrentYear: apiUpdate.personalUpdatesCurrentYearStatus || "",
                    detailsCurrentYear: apiUpdate.personalUpdatesCurrentYearDetails || "",
                };
            });

            // Update Formik values
            formik.setValues({ updates: updatedValues });
        }
    }, [performanceData]); // Dependency array includes performanceData to update when it changes

    return (
        <Card
            sx={{
                marginBottom: "1.5rem",
                boxShadow: 3,
                borderRadius: "5px",
                "& .MuiCardContent-root": {
                    padding: "0px !important",
                },
                "& .MuiFormControl-root": {
                    margin: "0px !important",
                },
            }}
        >
            <CardContent>
                <Box
                    sx={{
                        borderColor: "#E5E7EB",
                        position: "relative",
                        background: "transparent",
                        padding: "1rem 1.25rem 1rem",
                    }}
                >
                    <Typography
                        variant="h3"
                        align="center"
                        sx={{
                            fontSize: "20px",
                            fontWeight: "600",
                            marginBottom: "0.5rem",
                        }}
                    >
                        Personal Updates
                    </Typography>
                    <Typography align="center" sx={{ fontSize: "14px", color: "#677788" }}>
                        
                    </Typography>
                </Box>

                <Divider />
                <form onSubmit={formik.handleSubmit}>
                    <Table>
                        <TableHead>
                            <TableRow
                                sx={{
                                    background: "#E5E7EB",
                                    fontSize: "14px",
                                    color: "#111827",
                                    fontWeight: "600",
                                }}
                            >
                                <TableCell>#</TableCell>
                                <TableCell>Last Year</TableCell>
                                <TableCell>Yes/No</TableCell>
                                <TableCell>Details</TableCell>
                                <TableCell>Current Year</TableCell>
                                <TableCell>Yes/No</TableCell>
                                <TableCell>Details</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {formik.values.updates.map((update, index) => (
                                <TableRow key={index}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>{update.lastYear}</TableCell>
                                    <TableCell>
                                        <Box sx={{ minWidth: 120 }}>
                                            <FormControl fullWidth>
                                                <Select
                                                    name={`updates[${index}].yesNoLastYear`}
                                                    value={formik.values.updates[index].yesNoLastYear}
                                                    onChange={formik.handleChange}
                                                    disabled={formik.isSubmitting}
                                                    renderValue={(selected) => (selected ? selected : "Select")}
                                                >
                                                    <MenuItem value="">Select</MenuItem>
                                                    <MenuItem value="Yes">Yes</MenuItem>
                                                    <MenuItem value="No">No</MenuItem>
                                                </Select>
                                            </FormControl>
                                        </Box>
                                    </TableCell>
                                    <TableCell>
                                        <TextField
                                            name={`updates[${index}].detailsLastYear`}
                                            value={formik.values.updates[index].detailsLastYear}
                                            onChange={formik.handleChange}
                                            disabled={formik.isSubmitting || formik.values.updates[index].yesNoLastYear !== "Yes"}
                                            fullWidth
                                        />
                                    </TableCell>
                                    <TableCell>{update.currentYear}</TableCell>
                                    <TableCell>
                                        <Box sx={{ minWidth: 120 }}>
                                            <FormControl fullWidth>
                                                <Select
                                                    name={`updates[${index}].yesNoCurrentYear`}
                                                    value={formik.values.updates[index].yesNoCurrentYear}
                                                    onChange={formik.handleChange}
                                                    disabled={formik.isSubmitting}
                                                    renderValue={(selected) => (selected ? selected : "Select")}
                                                >
                                                    <MenuItem value="">Select</MenuItem>
                                                    <MenuItem value="Yes">Yes</MenuItem>
                                                    <MenuItem value="No">No</MenuItem>
                                                </Select>
                                            </FormControl>
                                        </Box>
                                    </TableCell>
                                    <TableCell>
                                        <TextField
                                            name={`updates[${index}].detailsCurrentYear`}
                                            value={formik.values.updates[index].detailsCurrentYear}
                                            onChange={formik.handleChange}
                                            disabled={formik.isSubmitting || formik.values.updates[index].yesNoCurrentYear !== "Yes"}
                                            fullWidth
                                        />
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                    <Box sx={{ display: "flex", justifyContent: "end", p: 2 }}>
                        <Button
                            type="submit"
                            variant="contained"
                            color="primary"
                            disabled={formik.isSubmitting}
                            startIcon={formik.isSubmitting ? <CircularProgress size={20} /> : null}
                            sx={{ background: "#f26522", textTransform: "none" }}
                        >
                            {formik.isSubmitting ? "Saving..." : "Save"}
                        </Button>
                    </Box>
                </form>
            </CardContent>
        </Card>
    );
};

export default PersonalUpdates;
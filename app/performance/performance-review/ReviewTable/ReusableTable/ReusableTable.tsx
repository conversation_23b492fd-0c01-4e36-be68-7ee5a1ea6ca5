"use client";

import React from "react";
import { Formik, Form, Field, FieldArray } from "formik";
import { 
    TextField, 
    Table, 
    TableBody, 
    TableCell, 
    TableContainer, 
    TableHead, 
    TableRow, 
    Paper, 
    Box, 
    Typography, 
    Button, 
    CardContent, 
    Divider, 
    Card 
} from "@mui/material";
import SaveIcon from "@mui/icons-material/Save";
import useAuthStore from "@/store/authStore";

interface ReusableTableFormProps {
    heading: string;
    subHeading: string;
    columns: { name: string; key: string }[];
    onSubmit: (values: any) => Promise<void>;
    initialRows?: Array<{ [key: string]: string }>;
}

const ReusableTableForm = ({ heading, subHeading, columns, onSubmit, initialRows }: ReusableTableFormProps) => {
    const { roles } = useAuthStore();
    const isEmployee = roles.includes("Employee");

    // Filter columns to exclude RO and HOD comments for employees
    const filteredColumns = isEmployee
        ? columns.filter(col => col.key !== "roComment" && col.key !== "hodComment" && col.key !== "ro" && col.key !== "hod")
        : columns;

    const initialValues = {
        rows: initialRows || Array(4).fill(Object.fromEntries(columns.map(col => [col.key, ""])))
    };

    return (
        <Card
            sx={{
                marginBottom: "1.5rem",
                boxShadow: 3,
                borderRadius: "5px",
                "& .MuiCardContent-root": {
                    padding: "0px !important",
                },
                "& .MuiFormControl-root": {
                    margin: "0px !important",
                },
            }}
        >
            <CardContent>
                <Box
                    sx={{
                        borderColor: "#E5E7EB",
                        position: "relative",
                        background: "transparent",
                        padding: "1rem 1.25rem 1rem",
                    }}
                >
                    <Typography
                        variant="h3"
                        align="center"
                        sx={{
                            fontSize: "20px",
                            fontWeight: "600",
                            marginBottom: "0.5rem",
                        }}
                    >
                        {heading}
                    </Typography>
                    <Typography align="center" sx={{ fontSize: "14px", color: "#677788" }}>
                        {subHeading}
                    </Typography>
                </Box>

                <Divider />
                <Formik
                    initialValues={initialValues}
                    enableReinitialize={true}
                    onSubmit={async (values, { setSubmitting }) => {
                        await onSubmit(values);
                        setSubmitting(false);
                    }}
                >
                    {({ values, handleChange, handleSubmit, isSubmitting }) => (
                        <Form>
                            <TableContainer component={Paper}>
                                <Table>
                                    <TableHead>
                                        <TableRow sx={{ backgroundColor: "#F3F4F6" }}>
                                            <TableCell>#</TableCell>
                                            {filteredColumns.map((col, index) => (
                                                <TableCell key={index}>{col.name}</TableCell>
                                            ))}
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        <FieldArray name="rows">
                                            {() => (
                                                <>
                                                    {values.rows.map((_, index) => (
                                                        <TableRow key={index}>
                                                            <TableCell>{index + 1}</TableCell>
                                                            {filteredColumns.map((col, colIndex) => (
                                                                <TableCell key={colIndex}>
                                                                    <Field
                                                                        as={TextField}
                                                                        name={`rows[${index}][${col.key}]`}
                                                                        fullWidth
                                                                        size="small"
                                                                        onChange={handleChange}
                                                                    />
                                                                </TableCell>
                                                            ))}
                                                        </TableRow>
                                                    ))}
                                                </>
                                            )}
                                        </FieldArray>
                                    </TableBody>
                                </Table>
                            </TableContainer>
                            <Box sx={{ padding: "1rem", textAlign: "right" }}>
                                <Button 
                                    type="submit" 
                                    variant="contained" 
                                    disabled={isSubmitting}
                                    sx={{background:"#f26522",textTransform:"none"}}
                                >
                                    {isSubmitting ? 'Saving...' : 'Save'}
                                </Button>
                            </Box>
                        </Form>
                    )}
                </Formik>
            </CardContent>
        </Card>
    );
};

export default ReusableTableForm;
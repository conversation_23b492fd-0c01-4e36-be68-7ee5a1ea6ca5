"use client";

import React, { useEffect, useState } from "react";
import ReusableTableForm from "../ReusableTable/ReusableTable";
import { Box } from "@mui/material";
import { postCommentOnRoleByHod } from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

interface ByHeadDepartmentProps {
    empId: string;
    performanceData: any; // Data passed from PerformanceReview
}

// Interface for the comment on role by HOD data structure
interface CommentOnRoleByHod {
    commentOnRolestrengthHod: string;
    commentOnRoleAreaForImproveHod: string;
    _id: string;
}

function ByHeadDepartment({ empId, performanceData }: ByHeadDepartmentProps) {
    const [initialData, setInitialData] = useState<CommentOnRoleByHod[]>([]);

    // Use performanceData prop to set initial data
    useEffect(() => {
        if (performanceData?.commentOnRoleByHod) {
            setInitialData(performanceData.commentOnRoleByHod);
        } else {
            setInitialData([]); // Default to empty array if no data
        }
    }, [performanceData]);

    const handleSubmit = async (values: any) => {
        try {
            const formattedData = {
                empId,
                commentOnRoleByHod: values.rows.map((row: any) => ({
                    commentOnRolestrengthHod: row.strengths,
                    commentOnRoleAreaForImproveHod: row.areaforimprovement,
                })),
            };
            await postCommentOnRoleByHod(formattedData);
            toast.success("Strength comments submitted successfully!");
        } catch (error) {
            console.error("Failed to submit strength comments:", error);
            // toast.error("Failed to submit strength comments. Please try again.");
        }
    };

    // Transform initial data to match ReusableTableForm expected format
    const initialRows = initialData.map((item) => ({
        strengths: item.commentOnRolestrengthHod || "",
        areaforimprovement: item.commentOnRoleAreaForImproveHod || "",
    }));

    return (
        <Box>
            <ReusableTableForm
                heading="Appraisee's Strengths and Areas for Improvement perceived by the Head of the Department"
                subHeading=""
                columns={[
                    { name: "Strengths", key: "strengths" },
                    { name: "Area's for Improvement", key: "areaforimprovement" },
                ]}
                onSubmit={handleSubmit}
                initialRows={initialRows.length > 0 ? initialRows : undefined}
            />
        </Box>
    );
}

export default ByHeadDepartment;
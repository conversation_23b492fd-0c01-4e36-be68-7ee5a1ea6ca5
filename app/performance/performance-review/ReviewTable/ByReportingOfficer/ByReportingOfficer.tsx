"use client";

import React, { useEffect, useState } from "react";
import ReusableTableForm from "../ReusableTable/ReusableTable";
import { Box } from "@mui/material";
import { postCommentOnRoleByRo } from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

interface ByReportingOfficerProps {
    empId: string;
    performanceData: any; // Data passed from PerformanceReview
}

// Interface for the comment on role by RO data structure
interface CommentOnRoleByRo {
    commentOnRolestrengthRo: string;
    commentOnRoleAreaForImproveRo: string;
    _id: string;
}

function ByReportingOfficer({ empId, performanceData }: ByReportingOfficerProps) {
    const [initialData, setInitialData] = useState<CommentOnRoleByRo[]>([]);

    // Fetch existing comments when component mounts
    useEffect(() => {
        if (performanceData?.commentOnRoleByRo) {
            setInitialData(performanceData.commentOnRoleByRo);
        } else {
            setInitialData([]); // Default to empty array if no data
        }
    }, [performanceData]);

    const handleSubmit = async (values: any) => {
        try {
            const formattedData = {
                empId,
                commentOnRoleByRo: values.rows.map((row: any) => ({
                    commentOnRolestrengthRo: row.strengths,
                    commentOnRoleAreaForImproveRo: row.areaforimprovement,
                })),
            };
            await postCommentOnRoleByRo(formattedData);
            toast.success("Strength comments submitted successfully!");
        } catch (error) {
            console.error("Failed to submit strength comments:", error);
            // toast.error("Failed to submit strength comments. Please try again.");
        }
    };

    // Transform initial data to match ReusableTableForm expected format
    const initialRows = initialData.map((item) => ({
        strengths: item.commentOnRolestrengthRo || "",
        areaforimprovement: item.commentOnRoleAreaForImproveRo || "",
    }));

    return (
        <Box>
            <ReusableTableForm
                heading="Appraisee's Strengths and Areas for Improvement perceived by the Reporting officer"
                subHeading=""
                columns={[
                    { name: "Strengths", key: "strengths" },
                    { name: "Area's for Improvement", key: "areaforimprovement" },
                ]}
                onSubmit={handleSubmit}
                initialRows={initialRows.length > 0 ? initialRows : undefined}
            />
        </Box>
    );
}

export default ByReportingOfficer;
"use client";

import React, { useEffect, useState } from "react";
import ReusableTableForm from "../ReusableTable/ReusableTable";
import { Box } from "@mui/material";
import { postSpecialAchievement } from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

interface SpecialInitiativesProps {
    empId: string;
    performanceData: any; // Data passed from PerformanceReview
}

interface SpecialAchievement {
    specialAcheivmentCommentBySelf: string;
    specialAcheivmentCommentByRo: string;
    specialAcheivmentCommentByHod: string;
    _id: string;
}

function SpecialInitiatives({ empId, performanceData }: SpecialInitiativesProps) {
    const [initialData, setInitialData] = useState<SpecialAchievement[]>([]);

    // Use performanceData prop to set initial data
    useEffect(() => {
        if (performanceData?.specialAchievements) {
            setInitialData(performanceData.specialAchievements);
        } else {
            setInitialData([]); // Default to empty array if no data
        }
    }, [performanceData]);

    const handleSubmit = async (values: any) => {
        try {
            const formattedData = {
                empId,
                specialAchievements: values.rows.map((row: any) => ({
                    specialAcheivmentCommentBySelf: row.bySelf,
                    specialAcheivmentCommentByRo: row.roComment,
                    specialAcheivmentCommentByHod: row.hodComment,
                })),
            };
            await postSpecialAchievement(formattedData);
            toast.success("Special achievements submitted successfully!");
        } catch (error) {
            console.error("Failed to submit special achievements:", error);
            // toast.error("Failed to submit special achievements. Please try again.");
        }
    };

    // Transform initial data to match ReusableTableForm expected format
    const initialRows = initialData.map((item) => ({
        bySelf: item.specialAcheivmentCommentBySelf || "",
        roComment: item.specialAcheivmentCommentByRo || "",
        hodComment: item.specialAcheivmentCommentByHod || "",
    }));

    return (
        <Box>
            <ReusableTableForm
                heading="Special Initiatives, Achievements, contributions if any"
                subHeading=""
                columns={[
                    { name: "By Self", key: "bySelf" },
                    { name: "RO's Comment", key: "roComment" },
                    { name: "HOD's Comment", key: "hodComment" },
                ]}
                onSubmit={handleSubmit}
                initialRows={initialRows.length > 0 ? initialRows : undefined}
            />
        </Box>
    );
}

export default SpecialInitiatives;
import React from 'react'
import ReusableTableForm from '../ReusableTable/ReusableTable';
import { Box } from '@mui/material';


function ProfessionalGoalForthComingYear() {
    return (
        <Box>
            <ReusableTableForm
                heading="Professional Goals for the forthcoming year"
                subHeading=""
                columns={[
                    { name: "By Self", key: "byself" },
                    { name: "RO's Comment", key: "roComment" },
                    { name: "HOD's Comment", key: "hodComment" },
                ]}
                onSubmit={async (values) => {
                    console.log('Submitted values:', values);
                }}
            />
        </Box>
    )
}

export default ProfessionalGoalForthComingYear
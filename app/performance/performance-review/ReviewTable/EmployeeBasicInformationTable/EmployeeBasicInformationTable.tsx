import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>po<PERSON>, <PERSON>, <PERSON><PERSON>ontent, Divider, Box } from "@mui/material";
import { getUserById } from "@/app/services/users.service";
import './EmployeeBasicInformationTable.scss';
import Loader from "@/components/Loader/Loader";

interface EmployeeBasicInformationTableProps {
    empId: string;
}

interface UserData {
    firstName: string;
    lastName: string;
    departmentName: string;
    designationName: string;
    qualification?: string;
    username: string;
    joiningDate: string;
    dateOfConfirmation?: string;
    previousYearsExp?: number;
    roName?: string;
    roDesignation?: string;
}

const EmployeeBasicInformationTable: React.FC<EmployeeBasicInformationTableProps> = ({ empId }) => {
    const [userData, setUserData] = useState<UserData | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const fetchUserData = async () => {
            setIsLoading(true);
            try {
                const data = await getUserById(empId);
                setUserData({
                    firstName: data.user.firstName || "",
                    lastName: data.user.lastName || "",
                    departmentName: data.user.departmentName || "",
                    designationName: data.user.designationName || "",
                    qualification: data.user.education?.length > 0 ? data.user.education[0].course : "",
                    username: data.user.employeeId || "",
                    joiningDate: data.user.joiningDate || "",
                    dateOfConfirmation: data.user.dateOfConfirmation || "",
                    previousYearsExp: data.user.experience?.length > 0 ? data.user.experience[0].years : undefined,
                    roName: data.user.roName || "Vikas",
                    roDesignation: data.user.roDesignation || "Manager",
                });
            } catch (error) {
                console.error("Error fetching user data:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchUserData();
    }, [empId]);

    if (isLoading || !userData) {
        return <Loader loading={true} />;
    }

    return (
        <Card
            sx={{
                marginBottom: "1.5rem",
                boxShadow: 3,
                borderRadius: "5px",
                "& .MuiCardContent-root": {
                    padding: "0px !important",
                },
            }}
        >
            <CardContent>
                <Box
                    sx={{
                        borderColor: "#E5E7EB",
                        position: "relative",
                        background: "transparent",
                        padding: "1rem 1.25rem 1rem",
                    }}
                >
                    <Typography
                        variant="h3"
                        align="center"
                        sx={{
                            fontSize: "20px",
                            fontWeight: "600",
                            marginBottom: "0.5rem",
                        }}
                    >
                        Employee Basic Information
                    </Typography>
                    <Typography
                        align="center"
                        sx={{ fontSize: "14px", color: "#677788" }}
                    >
                        Employee details
                    </Typography>
                </Box>

                <Divider />

                <Box sx={{ display: "flex" }}>
                    {/* Left Column */}
                    <Box
                        sx={{
                            color: "#6B7280",
                            fontSize: "14px",
                            padding: "8px 20px",
                            verticalAlign: "middle",
                            width: "100%",
                            borderRight: "1px solid #E5E7EB",
                        }}
                    >
                        <Box sx={{ marginBottom: "1rem" }}>
                            <label>Name</label>
                            <TextField
                                fullWidth
                                value={`${userData.firstName} ${userData.lastName}`.trim()}
                                disabled
                            />
                        </Box>
                        <Box sx={{ marginBottom: "1rem" }}>
                            <label>Department</label>
                            <TextField
                                fullWidth
                                value={userData.departmentName}
                                disabled
                            />
                        </Box>
                        <Box sx={{ marginBottom: "1rem" }}>
                            <label>Designation</label>
                            <TextField
                                fullWidth
                                value={userData.designationName}
                                disabled
                            />
                        </Box>
                        <Box sx={{ marginBottom: "1rem" }}>
                            <label>Qualification</label>
                            <TextField
                                fullWidth
                                value={userData.qualification || ""}
                                disabled
                            />
                        </Box>
                    </Box>

                    <Divider orientation="vertical" />

                    {/* Middle Column */}
                    <Box
                        sx={{
                            color: "#6B7280",
                            fontSize: "14px",
                            padding: "8px 20px",
                            verticalAlign: "middle",
                            width: "100%",
                            borderRight: "1px solid #E5E7EB",
                        }}
                    >
                        <Box sx={{ marginBottom: "1rem" }}>
                            <label>Emp ID</label>
                            <TextField
                                fullWidth
                                value={userData.username}
                                disabled
                            />
                        </Box>
                        <Box sx={{ marginBottom: "1rem" }}>
                            <label>Date Of Join</label>
                            <TextField
                                fullWidth
                                type="date"
                                value={userData.joiningDate.split("T")[0]}
                                disabled
                                InputLabelProps={{ shrink: true }}
                            />
                        </Box>
                        <Box sx={{ marginBottom: "1rem" }}>
                            <label>Date of Confirmation</label>
                            <TextField
                                fullWidth
                                type="date"
                                value={userData.dateOfConfirmation || ""}
                                disabled
                                InputLabelProps={{ shrink: true }}
                            />
                        </Box>
                        <Box sx={{ marginBottom: "1rem" }}>
                            <label>Previous Years of Exp</label>
                            <TextField
                                fullWidth
                                type="number"
                                value={userData.previousYearsExp || ""}
                                disabled
                            />
                        </Box>
                    </Box>

                    <Divider orientation="vertical" />

                    {/* Right Column */}
                    <Box
                        sx={{
                            color: "#6B7280",
                            fontSize: "14px",
                            padding: "8px 20px",
                            verticalAlign: "middle",
                            width: "100%",
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "center",
                        }}
                    >
                        <Box sx={{ marginBottom: "1rem" }}>
                            <label>RO's Name</label>
                            <TextField
                                fullWidth
                                value={userData.roName}
                                disabled
                            />
                        </Box>
                        <Box sx={{ marginBottom: "1rem" }}>
                            <label>RO Designation</label>
                            <TextField
                                fullWidth
                                value={userData.roDesignation}
                                disabled
                            />
                        </Box>
                    </Box>
                </Box>
            </CardContent>
        </Card>
    );
};

export default EmployeeBasicInformationTable;
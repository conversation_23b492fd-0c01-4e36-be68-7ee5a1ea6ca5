import React, { useEffect, useState } from "react";
import { Box } from "@mui/material";
import ReusableTableForm from "../ReusableTable/ReusableTable";
import { postPerformanceOthers, PerformanceOthersPayload } from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

interface OtherGeneralCommentsProps {
  empId: string;
  performanceData?: any; // Optional prop for pre-fetched performance data
}

const OtherGeneralComments = ({ empId, performanceData }: OtherGeneralCommentsProps) => {
  const columns = [
    { name: "Self", key: "self" },
    { name: "RO", key: "ro" },
    { name: "HOD", key: "hod" },
  ];

  const [initialRows, setInitialRows] = useState<Array<{ [key: string]: string }>>([]);

  // Use performanceData prop to set initial rows if provided
  useEffect(() => {
    if (performanceData && performanceData.performanceOthers) {
      const mappedRows = performanceData.performanceOthers.map((comment: any) => ({
        self: comment.otherCommentsBySelf || "",
        ro: comment.otherCommentsByRo || "",
        hod: comment.otherCommentsByHod || "",
      }));
      setInitialRows(mappedRows);
    }
  }, [performanceData]); // Dependency array includes performanceData

  const handleSubmit = async (values: { rows: { self: string; ro: string; hod: string }[] }) => {
    try {
      const payload: PerformanceOthersPayload = {
        empId,
        performanceOthers: values.rows.map((row) => ({
          otherCommentsBySelf: row.self,
          otherCommentsByRo: row.ro,
          otherCommentsByHod: row.hod,
        })),
      };
      await postPerformanceOthers(payload);
      toast.success("Other general comments submitted successfully!");
    } catch (error) {
      console.error("Failed to submit other general comments:", error);
      // toast.error("Failed to submit other general comments. Please try again.");
      throw error; // Re-throw to let Formik handle the submitting state
    }
  };

  return (
    <Box>
      <ReusableTableForm
        heading="Any other general comments, observations, suggestions etc."
        subHeading=""
        columns={columns}
        onSubmit={handleSubmit}
        initialRows={initialRows.length > 0 ? initialRows : undefined} // Pass initialRows only if data exists
      />
    </Box>
  );
};

export default OtherGeneralComments;
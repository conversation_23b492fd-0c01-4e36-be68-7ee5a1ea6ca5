"use client";

import React from "react";
import { Formik, Form, Field } from "formik";
import {
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Typography,
  Divider,
  CardContent,
  Card,
  Button,
  CircularProgress,
} from "@mui/material";
import useAuthStore from "@/store/authStore";
import "./ExcellenceTable.scss";

interface ExcellenceTableProps {
  title: string;
  data: { area: string; indicators: string[] }[];
  showTotalPercentageAndGrade?: boolean;
  onSave?: (values: any) => void;
  initialValues?: {
    weightage: number[];
    percentageSelf: string[];
    pointsSelf: number[];
    percentageRO: string[];
    pointsRO: number[];
  };
}

const ExcellenceTable = ({
  title,
  data,
  showTotalPercentageAndGrade = false,
  onSave,
  initialValues,
}: ExcellenceTableProps) => {
  const { roles } = useAuthStore();
  const isEmployee = roles.includes("Employee");

  const defaultInitialValues = {
    weightage: Array(data.length).fill(0),
    percentageSelf: Array(data.length).fill(""),
    pointsSelf: Array(data.length).fill(0),
    percentageRO: Array(data.length).fill(""),
    pointsRO: Array(data.length).fill(0),
  };

  // Calculate sum for weightage
  const calculateTotal = (values: number[]) => values.reduce((acc, curr) => acc + curr, 0);

  // Calculate average for percentages
  const calculateAverage = (values: string[]) => {
    const numbers = values
      .map(Number)
      .filter(value => !isNaN(value));
    
    if (numbers.length === 0) return 0;
    
    const sum = numbers.reduce((acc, curr) => acc + curr, 0);
    return (sum / numbers.length).toFixed(2);
  };

  return (
    <Card
      sx={{
        marginBottom: "1.5rem",
        boxShadow: 3,
        borderRadius: "5px",
        "& .MuiCardContent-root": {
          padding: "0px !important",
        },
        "& .MuiFormControl-root": {
          margin: "0px !important",
        },
      }}
    >
      <CardContent>
        <Box
          sx={{
            borderColor: "#E5E7EB",
            position: "relative",
            background: "transparent",
            padding: "1rem 1.25rem 1rem",
          }}
        >
          <Typography
            variant="h3"
            align="center"
            sx={{
              fontSize: "20px",
              fontWeight: "600",
              marginBottom: "0.5rem",
            }}
          >
            {title}
          </Typography>
          <Typography align="center" sx={{ fontSize: "14px", color: "#677788" }}>
            
          </Typography>
        </Box>

        <Divider />

        <Formik
          initialValues={initialValues || defaultInitialValues}
          enableReinitialize // Allow reinitialization when initialValues change
          onSubmit={async (values, { setSubmitting }) => {
            if (onSave) {
              await onSave(values);
            }
            setSubmitting(false);
          }}
        >
          {({ values, handleChange, isSubmitting }) => (
            <Form>
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow
                      sx={{
                        background: "#E5E7EB",
                        fontSize: "14px",
                        color: "#111827",
                        fontWeight: "600",
                      }}
                    >
                      <TableCell>#</TableCell>
                      <TableCell>Key Result Area</TableCell>
                      <TableCell>Key Performance Indicators</TableCell>
                      <TableCell>Weightage</TableCell>
                      <TableCell>Percentage Achieved (Self Score)</TableCell>
                      <TableCell>Points Scored (Self)</TableCell>
                      {!isEmployee && <TableCell>Percentage Achieved (RO's Score)</TableCell>}
                      {!isEmployee && <TableCell>Points Scored (RO)</TableCell>}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {data.map((row, index) => (
                      <TableRow key={index}>
                        <TableCell>{index + 1}</TableCell>
                        <TableCell>{row.area}</TableCell>
                        <TableCell>
                          {row.indicators.map((indicator, i) => (
                            <Typography key={i} variant="body2">
                              {indicator}
                            </Typography>
                          ))}
                        </TableCell>
                        <TableCell>
                          <Field
                            as={TextField}
                            name={`weightage[${index}]`}
                            value={values.weightage[index]}
                            onChange={handleChange}
                            disabled={isSubmitting || isEmployee}
                            type="number"
                          />
                        </TableCell>
                        <TableCell>
                          <Field
                            as={TextField}
                            name={`percentageSelf[${index}]`}
                            value={values.percentageSelf[index]}
                            onChange={handleChange}
                            disabled={isSubmitting}
                            type="number"
                          />
                        </TableCell>
                        <TableCell>
                          <Field
                            as={TextField}
                            name={`pointsSelf[${index}]`}
                            value={values.pointsSelf[index]}
                            onChange={handleChange}
                            disabled={true}
                            type="number"
                          />
                        </TableCell>
                        {!isEmployee && (
                          <TableCell>
                            <Field
                              as={TextField}
                              name={`percentageRO[${index}]`}
                              value={values.percentageRO[index]}
                              onChange={handleChange}
                              disabled={isSubmitting}
                              type="number"
                            />
                          </TableCell>
                        )}
                        {!isEmployee && (
                          <TableCell>
                            <Field
                              as={TextField}
                              name={`pointsRO[${index}]`}
                              value={values.pointsRO[index]}
                              onChange={handleChange}
                              disabled={true}
                              type="number"
                            />
                          </TableCell>
                        )}
                      </TableRow>
                    ))}
                    <TableRow>
                      <TableCell colSpan={3} align="center">
                        Total
                      </TableCell>
                      <TableCell>{calculateTotal(values.weightage)}</TableCell>
                      <TableCell>
                        {calculateAverage(values.percentageSelf)}%
                      </TableCell>
                      <TableCell>{calculateTotal(values.pointsSelf)}</TableCell>
                      {!isEmployee && (
                        <TableCell>
                          {calculateAverage(values.percentageRO)}%
                        </TableCell>
                      )}
                      {!isEmployee && (
                        <TableCell>{calculateTotal(values.pointsRO)}</TableCell>
                      )}
                    </TableRow>
                    {showTotalPercentageAndGrade && (
                      <>
                        <TableRow>
                          <TableCell
                            colSpan={3}
                            align="center"
                            sx={{ fontWeight: "bold" }}
                          >
                            Total Percentage(%)
                          </TableCell>
                          <TableCell colSpan={isEmployee ? 3 : 5}>0</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell
                            colSpan={isEmployee ? 6 : 8}
                            align="center"
                            sx={{ fontSize: "18px", fontWeight: "600" }}
                          >
                            Grade
                          </TableCell>
                        </TableRow>
                      </>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              <Box sx={{ padding: "1rem", textAlign: "right" }}>
                <Button 
                  type="submit" 
                  variant="contained" 
                  disabled={isSubmitting}
                  startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
                  sx={{background:"#f26522",textTransform:"none"}}
                >
                  {isSubmitting ? "Saving..." : "Save"}
                </Button>
              </Box>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  );
};

export default ExcellenceTable;
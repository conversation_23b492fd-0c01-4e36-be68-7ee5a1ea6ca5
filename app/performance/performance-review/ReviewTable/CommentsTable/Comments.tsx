"use client";

import React, { useEffect, useState } from "react";
import ReusableTableForm from "../ReusableTable/ReusableTable";
import { Box } from "@mui/material";
import { postCommentOnRole } from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

interface CommentsTableProps {
    empId: string;
    performanceData: any; // Data passed from PerformanceReview
}

// Interface for the comment on role data structure
interface CommentOnRole {
    commentOnRoleBySelf: string;
    commentOnRoleByRo: string;
    commentOnRoleByHod: string;
    commentOnRoleAreaForImprove: string;
    _id: string;
}

function CommentsTable({ empId, performanceData }: CommentsTableProps) {
    const [initialData, setInitialData] = useState<CommentOnRole[]>([]);

    // Fetch existing comments when component mounts
    useEffect(() => {
        if (performanceData?.commentOnRole) {
            setInitialData(performanceData.commentOnRole);
        } else {
            setInitialData([]); // Default to empty array if no data
        }
    }, [performanceData]);

    const handleSubmit = async (values: any) => {
        try {
            const formattedData = {
                empId,
                commentOnRole: values.rows.map((row: any) => ({
                    commentOnRoleBySelf: row.bySelf,
                    commentOnRoleByRo: row.roComment,
                    commentOnRoleByHod: row.hodComment,
                    // Note: The API response includes commentOnRoleAreaForImprove,
                    // but it's not in the form. Adding it as an empty string for now.
                    commentOnRoleAreaForImprove: "",
                })),
            };
            await postCommentOnRole(formattedData);
            toast.success("Comments submitted successfully!");
        } catch (error) {
            console.error("Failed to submit comments on role:", error);
            // toast.error("Failed to submit comments. Please try again.");
        }
    };

    // Transform initial data to match ReusableTableForm expected format
    const initialRows = initialData.map((item) => ({
        bySelf: item.commentOnRoleBySelf || "",
        roComment: item.commentOnRoleByRo || "",
        hodComment: item.commentOnRoleByHod || "",
    }));

    return (
        <Box>
            <ReusableTableForm
                heading="Comments on the role"
                subHeading="Alterations if any required like addition/deletion of responsibilities"
                columns={[
                    { name: "By Self", key: "bySelf" },
                    { name: "RO's Comment", key: "roComment" },
                    { name: "HOD's Comment", key: "hodComment" },
                ]}
                onSubmit={handleSubmit}
                initialRows={initialRows.length > 0 ? initialRows : undefined}
            />
        </Box>
    );
}

export default CommentsTable;
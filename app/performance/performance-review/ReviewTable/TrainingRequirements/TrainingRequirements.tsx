import React, { useEffect, useState } from "react";
import { Box } from "@mui/material";
import ReusableTableForm from "../ReusableTable/ReusableTable";
import { postPerformanceTraining, PerformanceTrainingPayload } from "@/app/services/performance/performance.service";
import { toast } from "react-toastify";

interface TrainingRequirementsProps {
  empId: string;
  performanceData?: any; // Optional prop for pre-fetched performance data
}

const TrainingRequirements = ({ empId, performanceData }: TrainingRequirementsProps) => {
  const columns = [
    { name: "By Self", key: "byself" },
    { name: "RO's Comment", key: "roComment" },
    { name: "HOD's Comment", key: "hodComment" },
  ];

  const [initialRows, setInitialRows] = useState<Array<{ [key: string]: string }>>([]);

  // Use performanceData prop to set initial rows if provided
  useEffect(() => {
    if (performanceData && performanceData.performanceTraining) {
      const mappedRows = performanceData.performanceTraining.map((training: any) => ({
        byself: training.performanceTrainingBySelf || "",
        roComment: training.performanceTrainingByRo || "",
        hodComment: training.performanceTrainingByHod || "",
      }));
      setInitialRows(mappedRows);
    }
  }, [performanceData]); // Dependency array includes performanceData

  const handleSubmit = async (values: { rows: { byself: string; roComment: string; hodComment: string }[] }) => {
    try {
      const payload: PerformanceTrainingPayload = {
        empId,
        performanceTraining: values.rows.map((row) => ({
          performanceTrainingBySelf: row.byself,
          performanceTrainingByRo: row.roComment,
          performanceTrainingByHod: row.hodComment,
        })),
      };
      await postPerformanceTraining(payload);
      toast.success("Training requirements submitted successfully!");
    } catch (error) {
      console.error("Failed to submit training requirements:", error);
      // toast.error("Failed to submit training requirements. Please try again.");
      throw error; // Re-throw to let Formik handle the submitting state
    }
  };

  return (
    <Box>
      <ReusableTableForm
        heading="Training Requirements"
        subHeading=""
        columns={columns}
        onSubmit={handleSubmit}
        initialRows={initialRows.length > 0 ? initialRows : undefined} // Pass initialRows only if data exists
      />
    </Box>
  );
};

export default TrainingRequirements;
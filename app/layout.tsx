import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import "./theme.scss";
import AuthGuard from "./services/authguard/AuthGuard";
import ConditionalLayout from "./ConditionalLayout"; // New client component
import ThemeProvider from "./theme-provider";

const roboto = Roboto({
  weight: ["400", "500", "700"],
  subsets: ["latin"],
  variable: "--font-roboto",
});

export const metadata: Metadata = {
  title: "HRMS",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={roboto.variable} suppressHydrationWarning>
      <body suppressHydrationWarning>
        <ThemeProvider>
          <AuthGuard>
            <ConditionalLayout>{children}</ConditionalLayout>
          </AuthGuard>
        </ThemeProvider>
      </body>
    </html>
  );
}

export interface ApprovedBy {
    firstName: string;
    designationName: string;
}

export interface LeaveEmployee {
    _id?: string;
    id?: string;
    employeeId?: string;
    employee?: string;
    leaveType: string;
    from: string;
    to: string;
    days: number;
    leaveTime: string;
    remainingDays: number;
    status: string;
    reason: string;
    department?: string;
    approvedBy?: ApprovedBy | null;
    avatar?: string;
}

export interface LeaveBody {
    empId: string;
    leaveType: string;
    leaveFrom: string;
    leaveTo: string;
    leaveTime: string;
    remainingDays: number;
    leaveStatus: string;
    reason: string;
}

export interface TimesheetEntry {
  _id?: string;
  empId: string;
  title: string;
  date: string; // ISO date string
  projectId: string;
  taskId: string;
  hours: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface TimesheetCreateRequest {
  empId: string;
  title: string;
  date: string;
  projectId: string;
  taskId: string;
  hours: number;
}

export interface TimesheetUpdateRequest {
  empId?: string;
  title?: string;
  date?: string;
  projectId?: string;
  taskId?: string;
  hours?: number;
}

export interface TimesheetResponse {
  success: boolean;
  data: TimesheetEntry | TimesheetEntry[];
  message?: string;
}

export interface TimesheetStats {
  drafts: number;
  finalizedWorkdays: number;
  leaves: number;
  totalEffort: string;
}

export interface ProjectOption {
  id: string;
  name: string;
}

export interface TaskOption {
  id: string;
  name: string;
}

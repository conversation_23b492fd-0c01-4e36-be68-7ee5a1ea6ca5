"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>graphy,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Card,
  IconButton,
  TextField,
  Chip,
  Box,
  CircularProgress,
} from "@mui/material";
import {
  Add,
  Refresh,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  Info,
} from "@mui/icons-material";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import "./timesheet.scss";
import { getAllProjects } from "../services/projects/project.service";
import useAuthStore from "@/store/authStore";
import {
  createTimesheet,
  updateTimesheet,
  getTimesheetsByDateRange
} from "@/app/services/timesheet/timesheet.service";
import { TimesheetEntry, TimesheetCreateRequest, TimesheetUpdateRequest } from "@/app/types/timesheet";
import { toast } from "react-toastify";

interface TimesheetStats {
  drafts: number;
  finalizedWorkdays: number;
  leaves: number;
  totalEffort: string;
}

interface TimeEntry {
  _id?: string;
  description: string;
  hours: number;
  projectId: string;
  taskId: string;
}

interface Project {
  _id: string;
  projectName: string;
  tasks: Task[];
}

interface Task {
  _id: string;
  title: string;
}

const TimesheetPage = () => {
  const { employeeId } = useAuthStore();
  const [currentDate, setCurrentDate] = useState<Dayjs>(dayjs());
  const [selectedWorkspace, setSelectedWorkspace] = useState("All Workspaces");
  const [timesheetData, setTimesheetData] = useState<{ [key: number]: TimeEntry }>({});
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [timesheetEntries, setTimesheetEntries] = useState<TimesheetEntry[]>([]);
  const [saving, setSaving] = useState<boolean>(false);

  // Fetch projects and timesheet data when component mounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch projects
        const projectResponse = await getAllProjects();
        setProjects(projectResponse.projects.results);

        // Fetch timesheet entries for current month if employeeId is available
        if (employeeId) {
          const startDate = currentDate.startOf('month').format('YYYY-MM-DD');
          const endDate = currentDate.endOf('month').format('YYYY-MM-DD');

          const timesheetResponse = await getTimesheetsByDateRange(employeeId, startDate, endDate);
          setTimesheetEntries(timesheetResponse);

          // Convert API data to local timesheet format
          const timesheetMap: { [key: number]: TimeEntry } = {};
          timesheetResponse.forEach((entry) => {
            const date = dayjs(entry.date).date();
            timesheetMap[date] = {
              _id: entry._id,
              description: entry.title,
              hours: entry.hours,
              projectId: entry.projectId,
              taskId: entry.taskId,
            };
          });
          setTimesheetData(timesheetMap);
        }

        setLoading(false);
      } catch (err) {
        setError("Failed to load data. Please try again later.");
        setLoading(false);
      }
    };

    fetchData();
  }, [currentDate, employeeId]);

  // Calculate stats dynamically
  const calculateStats = (): TimesheetStats => {
    const entries = Object.values(timesheetData);
    const drafts = entries.filter((entry) => entry.description.toLowerCase().includes("draft")).length;
    const finalizedWorkdays = Object.keys(timesheetData).length;
    const leaves = entries.filter((entry) => entry.description.toLowerCase().includes("leave")).length;
    const totalEffort = entries.reduce((total, entry) => total + entry.hours, 0);

    return {
      drafts,
      finalizedWorkdays,
      leaves,
      totalEffort: `${totalEffort}h 0m`,
    };
  };

  const stats = calculateStats();

  // Generate calendar days for current month
  const generateCalendarDays = () => {
    const year = currentDate.year();
    const month = currentDate.month();
    const daysInMonth = currentDate.daysInMonth();
    const days = [];

    for (let day = 1; day <= daysInMonth; day++) {
      const date = dayjs(new Date(year, month, day));
      const dayName = date.format("ddd");
      days.push({
        date: day,
        dayName,
        fullDate: date,
        isWeekend: date.day() === 0 || date.day() === 6,
      });
    }
    return days;
  };

  const calendarDays = generateCalendarDays();

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = direction === "prev" ? currentDate.subtract(1, "month") : currentDate.add(1, "month");
    setCurrentDate(newDate);
  };

  const getRowStatus = (date: Dayjs) => {
    const isWeekend = date.day() === 0 || date.day() === 6;
    const today = dayjs();
    const isToday = date.isSame(today, "day");
    const workingDays = Object.keys(timesheetData).map(Number);
    const isWorkingDay = workingDays.includes(date.date());

    if (isWeekend) return "weekend";
    if (isToday) return "today";
    if (isWorkingDay) return "working-day";
    return "workday";
  };

  const formatMonthYear = () => {
    return currentDate.format("MMMM YYYY");
  };

  // Handle input changes and save to API
  const handleInputChange = async (date: number, field: keyof TimeEntry, value: string | number) => {
    if (!employeeId) {
      toast.error("Employee ID not found. Please login again.");
      return;
    }

    // Update local state immediately for better UX
    const updatedEntry = {
      ...timesheetData[date] || { description: "", hours: 0, projectId: "", taskId: "" },
      [field]: field === "hours" ? parseFloat(value as string) || 0 : value,
    };

    setTimesheetData((prev) => ({
      ...prev,
      [date]: updatedEntry,
    }));

    // Debounce API calls to avoid too many requests
    try {
      setSaving(true);
      const entryDate = currentDate.date(date).format('YYYY-MM-DD');

      const apiData: TimesheetCreateRequest | TimesheetUpdateRequest = {
        empId: employeeId,
        title: updatedEntry.description,
        date: entryDate,
        projectId: updatedEntry.projectId,
        taskId: updatedEntry.taskId,
        hours: updatedEntry.hours,
      };

      if (updatedEntry._id) {
        // Update existing entry
        await updateTimesheet(updatedEntry._id, apiData);
      } else {
        // Create new entry only if there's meaningful data
        if (updatedEntry.description || updatedEntry.hours > 0 || updatedEntry.projectId || updatedEntry.taskId) {
          const response = await createTimesheet(apiData as TimesheetCreateRequest);
          // Update local state with the new ID from API
          const newEntry = Array.isArray(response.data) ? response.data[0] : response.data;
          setTimesheetData((prev) => ({
            ...prev,
            [date]: {
              ...prev[date],
              _id: newEntry._id,
            },
          }));
        }
      }
    } catch (error) {
      console.error("Error saving timesheet:", error);
      // Revert local state on error
      setTimesheetData((prev) => ({
        ...prev,
        [date]: timesheetData[date] || { description: "", hours: 0, projectId: "", taskId: "" },
      }));
    } finally {
      setSaving(false);
    }
  };

  const handleRefresh = async () => {
    if (!employeeId) return;

    try {
      setLoading(true);
      const startDate = currentDate.startOf('month').format('YYYY-MM-DD');
      const endDate = currentDate.endOf('month').format('YYYY-MM-DD');

      const timesheetResponse = await getTimesheetsByDateRange(employeeId, startDate, endDate);
      setTimesheetEntries(timesheetResponse);

      // Convert API data to local timesheet format
      const timesheetMap: { [key: number]: TimeEntry } = {};
      timesheetResponse.forEach((entry) => {
        const date = dayjs(entry.date).date();
        timesheetMap[date] = {
          _id: entry._id,
          description: entry.title,
          hours: entry.hours,
          projectId: entry.projectId,
          taskId: entry.taskId,
        };
      });
      setTimesheetData(timesheetMap);
      toast.success("Timesheet refreshed successfully");
    } catch (error) {
      toast.error("Failed to refresh timesheet");
    } finally {
      setLoading(false);
    }
  };

  // Get tasks for the selected project
  const getTaskOptions = (projectId: string) => {
    const project = projects.find((p) => p._id === projectId);
    return project ? project.tasks : [];
  };

  if (!employeeId) {
    return (
      <Box className="timesheet-container">
        <Box className="content">
          <Typography variant="h6" color="error">
            Employee ID not found. Please login again.
          </Typography>
        </Box>
      </Box>
    );
  }

  if (loading) {
    return (
      <Box className="timesheet-container">
        <Box className="content">
          <Box display="flex" alignItems="center" gap={2}>
            <CircularProgress size={24} />
            <Typography>Loading timesheet data...</Typography>
          </Box>
        </Box>
      </Box>
    );
  }

  if (error) {
    return (
      <Box className="timesheet-container">
        <Box className="content">
          <Typography variant="h6" color="error">
            {error}
          </Typography>
          <Button onClick={handleRefresh} variant="contained" sx={{ mt: 2 }}>
            Retry
          </Button>
        </Box>
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box className="timesheet-container">
        <Box className="content">
          {/* Header Section */}
          <Box className="timesheet-header">
            <Box className="breadcrumbs-box">
              <Typography variant="h4" component="h1">
                Timesheet
              </Typography>
            </Box>
            <Box className="header-controls">
              <IconButton
                onClick={handleRefresh}
                disabled={loading || saving}
                title="Refresh timesheet data"
              >
                <Refresh />
              </IconButton>
              {saving && <CircularProgress size={20} />}
            </Box>
          </Box>

          {/* Month Navigation and Workspace Selector */}
          <Box className="timesheet-controls">
            <Box className="month-navigation">
              <IconButton onClick={() => navigateMonth("prev")}>
                <KeyboardArrowLeft />
              </IconButton>
              <Typography variant="h6" className="month-year">
                {formatMonthYear()}
              </Typography>
              <IconButton onClick={() => navigateMonth("next")}>
                <KeyboardArrowRight />
              </IconButton>
            </Box>
            <FormControl className="workspace-selector">
              <InputLabel>Workspace</InputLabel>
              <Select
                value={selectedWorkspace}
                onChange={(e) => setSelectedWorkspace(e.target.value)}
                label="Workspace"
              >
                <MenuItem value="All Workspaces">All Workspaces</MenuItem>
                <MenuItem value="Development">Development</MenuItem>
                <MenuItem value="Design">Design</MenuItem>
                <MenuItem value="Marketing">Marketing</MenuItem>
              </Select>
            </FormControl>
          </Box>

          {/* Stats Cards */}
          <Box className="stats-section">
            <Box className="stats-card drafts">
              <Typography variant="h6">{stats.drafts}</Typography>
              <Typography variant="body2">Drafts</Typography>
            </Box>
            <Box className="stats-card finalized">
              <Typography variant="h6">{stats.finalizedWorkdays}</Typography>
              <Typography variant="body2">Finalized Workdays</Typography>
            </Box>
            <Box className="stats-card leaves">
              <Typography variant="h6">{stats.leaves} Days</Typography>
              <Typography variant="body2">Leaves</Typography>
            </Box>
            <Box className="stats-card effort">
              <Typography variant="h6">{stats.totalEffort}</Typography>
              <Typography variant="body2">Effort</Typography>
            </Box>
          </Box>

          {/* Timesheet Grid */}
          <Card className="timesheet-grid-card">
            <Box className="timesheet-grid">
              {/* Header Row */}
              <Box className="grid-header">
                <Box className="header-cell date-header">Date</Box>
                <Box className="header-cell description-header">Description</Box>
                <Box className="header-cell hours-header">Hours</Box>
                <Box className="header-cell project-header">Project</Box>
                <Box className="header-cell task-header">Task</Box>
                <Box className="header-cell total-header">Total</Box>
              </Box>

              {/* Data Rows */}
              {calendarDays.map((day) => {
                const rowStatus = getRowStatus(day.fullDate);
                const data = timesheetData[day.date] || { description: "", hours: 0, projectId: "", taskId: "" };
                const taskOptions = getTaskOptions(data.projectId);
                return (
                  <Box key={day.date} className={`grid-row ${rowStatus}`}>
                    <Box className="grid-cell date-cell">
                      <Box className="date-number">{day.date}</Box>
                      <Box className="date-day">({day.dayName})</Box>
                    </Box>
                    <Box className="grid-cell description-cell">
                      <TextField
                        placeholder="Description"
                        variant="outlined"
                        size="small"
                        fullWidth
                        value={data.description}
                        onChange={(e) => handleInputChange(day.date, "description", e.target.value)}
                        multiline
                      />
                    </Box>
                    <Box className="grid-cell hours-cell">
                      <TextField
                        placeholder="0"
                        variant="outlined"
                        size="small"
                        type="number"
                        value={data.hours}
                        onChange={(e) => handleInputChange(day.date, "hours", e.target.value)}
                      />
                    </Box>
                    <Box className="grid-cell project-cell">
                      <FormControl fullWidth size="small">
                        <Select
                          value={data.projectId}
                          onChange={(e) => handleInputChange(day.date, "projectId", e.target.value)}
                          displayEmpty
                        >
                          <MenuItem value="" disabled>
                            Select Project
                          </MenuItem>
                          {projects.map((project) => (
                            <MenuItem key={project._id} value={project._id}>
                              {project.projectName}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                    <Box className="grid-cell task-cell">
                      <FormControl fullWidth size="small">
                        <Select
                          value={data.taskId}
                          onChange={(e) => handleInputChange(day.date, "taskId", e.target.value)}
                          displayEmpty
                          disabled={!data.projectId} // Disable task dropdown if no project is selected
                        >
                          <MenuItem value="" disabled>
                            Select Task
                          </MenuItem>
                          {taskOptions.map((task) => (
                            <MenuItem key={task._id} value={task._id}>
                              {task.title}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                    <Box className="grid-cell total-cell">
                      <Chip label={`${data.hours || 0}h 0m`} size="small" className="total-chip" />
                    </Box>
                  </Box>
                );
              })}
            </Box>
          </Card>
        </Box>
      </Box>
    </LocalizationProvider>
  );
};

export default TimesheetPage;
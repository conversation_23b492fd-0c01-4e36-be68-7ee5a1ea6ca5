"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Card,
  IconButton,
  TextField,
  Chip,
  Box,
} from "@mui/material";
import {
  Add,
  Refresh,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  Info,
} from "@mui/icons-material";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import "./timesheet.scss";

interface TimesheetStats {
  drafts: number;
  finalizedWorkdays: number;
  leaves: number;
  totalEffort: string;
}

interface TimeEntry {
  description: string; // Maps to title
  hours: number; // Maps to hours
  projectId: string; // Maps to projectId
  taskId: string; // Maps to taskId
}

const TimesheetPage = () => {
  const [currentDate, setCurrentDate] = useState<Dayjs>(dayjs());
  const [selectedWorkspace, setSelectedWorkspace] = useState("All Workspaces");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [timesheetData, setTimesheetData] = useState<{ [key: number]: TimeEntry }>({});

  // Hardcoded options for project and task dropdowns
  const projectOptions = [
    { id: "604c0b0b1b3c3f1a88f889df", name: "Project A" },
    { id: "704c0b0b1b3c3f1a88f889ef", name: "Project B" },
    { id: "804c0b0b1b3c3f1a88f889ff", name: "Project C" },
  ];

  const taskOptions = [
    { id: "604c0b0b1b3c3f1a88f889df", name: "Task 1" },
    { id: "704c0b0b1b3c3f1a88f889ef", name: "Task 2" },
    { id: "804c0b0b1b3c3f1a88f889ff", name: "Task 3" },
  ];

  // Calculate stats dynamically
  const calculateStats = (): TimesheetStats => {
    const entries = Object.values(timesheetData);
    const drafts = entries.filter((entry) => entry.description.toLowerCase().includes("draft")).length;
    const finalizedWorkdays = Object.keys(timesheetData).length;
    const leaves = entries.filter((entry) => entry.description.toLowerCase().includes("leave")).length;
    const totalEffort = entries.reduce((total, entry) => total + entry.hours, 0);

    return {
      drafts,
      finalizedWorkdays,
      leaves,
      totalEffort: `${totalEffort}h 0m`,
    };
  };

  const stats = calculateStats();

  // Generate calendar days for current month
  const generateCalendarDays = () => {
    const year = currentDate.year();
    const month = currentDate.month();
    const daysInMonth = currentDate.daysInMonth();
    const days = [];

    for (let day = 1; day <= daysInMonth; day++) {
      const date = dayjs(new Date(year, month, day));
      const dayName = date.format("ddd");
      days.push({
        date: day,
        dayName,
        fullDate: date,
        isWeekend: date.day() === 0 || date.day() === 6,
      });
    }
    return days;
  };

  const calendarDays = generateCalendarDays();

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = direction === "prev" ? currentDate.subtract(1, "month") : currentDate.add(1, "month");
    setCurrentDate(newDate);
  };

  const getRowStatus = (date: Dayjs) => {
    const isWeekend = date.day() === 0 || date.day() === 6;
    const today = dayjs();
    const isToday = date.isSame(today, "day");
    const workingDays = Object.keys(timesheetData).map(Number);
    const isWorkingDay = workingDays.includes(date.date());

    if (isWeekend) return "weekend";
    if (isToday) return "today";
    if (isWorkingDay) return "working-day";
    return "workday";
  };

  const formatMonthYear = () => {
    return currentDate.format("MMMM YYYY");
  };

  const handleAddEntry = () => {
    setIsAddDialogOpen(true);
  };

  const handleSaveEntry = (entryData: any) => {
    setTimesheetData((prev) => ({
      ...prev,
      [entryData.date]: {
        description: entryData.description,
        hours: parseFloat(entryData.hours) || 0,
        projectId: entryData.projectId,
        taskId: entryData.taskId,
      },
    }));
    setIsAddDialogOpen(false);
  };

  const handleInputChange = (date: number, field: keyof TimeEntry, value: string | number) => {
    setTimesheetData((prev) => ({
      ...prev,
      [date]: {
        ...prev[date] || { description: "", hours: 0, projectId: "", taskId: "" },
        [field]: field === "hours" ? parseFloat(value as string) || 0 : value,
      },
    }));
  };

  const handleRefresh = () => {
    setTimesheetData({});
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box className="timesheet-container">
        <Box className="content">
          {/* Header Section */}
          <Box className="timesheet-header">
            <Box className="breadcrumbs-box">
              <Typography variant="h4" component="h1">
                Timesheet
              </Typography>
            </Box>
            
          </Box>

          {/* Month Navigation and Workspace Selector */}
          <Box className="timesheet-controls">
            <Box className="month-navigation">
              <IconButton onClick={() => navigateMonth("prev")}>
                <KeyboardArrowLeft />
              </IconButton>
              <Typography variant="h6" className="month-year">
                {formatMonthYear()}
              </Typography>
              <IconButton onClick={() => navigateMonth("next")}>
                <KeyboardArrowRight />
              </IconButton>
            </Box>
            <FormControl className="workspace-selector">
              <InputLabel>Workspace</InputLabel>
              <Select
                value={selectedWorkspace}
                onChange={(e) => setSelectedWorkspace(e.target.value)}
                label="Workspace"
              >
                <MenuItem value="All Workspaces">All Workspaces</MenuItem>
                <MenuItem value="Development">Development</MenuItem>
                <MenuItem value="Design">Design</MenuItem>
                <MenuItem value="Marketing">Marketing</MenuItem>
              </Select>
            </FormControl>
          </Box>

          {/* Stats Cards */}
          <Box className="stats-section">
            <Box className="stats-card drafts">
              <Typography variant="h6">{stats.drafts}</Typography>
              <Typography variant="body2">Drafts</Typography>
            </Box>
            <Box className="stats-card finalized">
              <Typography variant="h6">{stats.finalizedWorkdays}</Typography>
              <Typography variant="body2">Finalized Workdays</Typography>
            </Box>
            <Box className="stats-card leaves">
              <Typography variant="h6">{stats.leaves} Days</Typography>
              <Typography variant="body2">Leaves</Typography>
            </Box>
            <Box className="stats-card effort">
              <Typography variant="h6">{stats.totalEffort}</Typography>
              <Typography variant="body2">Effort</Typography>
            </Box>
          </Box>

          {/* Timesheet Grid */}
          <Card className="timesheet-grid-card">
            <Box className="timesheet-grid">
              {/* Header Row */}
              <Box className="grid-header">
                <Box className="header-cell date-header">Date</Box>
                <Box className="header-cell description-header">Description</Box>
                <Box className="header-cell hours-header">Hours</Box>
                <Box className="header-cell project-header">Project</Box>
                <Box className="header-cell task-header">Task</Box>
                <Box className="header-cell total-header">Total</Box>
              </Box>

              {/* Data Rows */}
              {calendarDays.map((day) => {
                const rowStatus = getRowStatus(day.fullDate);
                const data = timesheetData[day.date] || { description: "", hours: 0, projectId: "", taskId: "" };
                return (
                  <Box key={day.date} className={`grid-row ${rowStatus}`}>
                    <Box className="grid-cell date-cell">
                      <Box className="date-number">{day.date}</Box>
                      <Box className="date-day">({day.dayName})</Box>
                    </Box>
                    <Box className="grid-cell description-cell">
                      <TextField
                        placeholder="Description"
                        variant="outlined"
                        size="small"
                        fullWidth
                        value={data.description}
                        onChange={(e) => handleInputChange(day.date, "description", e.target.value)}
                      />
                    </Box>
                    <Box className="grid-cell hours-cell">
                      <TextField
                        placeholder="0"
                        variant="outlined"
                        size="small"
                        type="number"
                        value={data.hours}
                        onChange={(e) => handleInputChange(day.date, "hours", e.target.value)}
                      />
                    </Box>
                    <Box className="grid-cell project-cell">
                      <FormControl fullWidth size="small">
                        <Select
                          value={data.projectId}
                          onChange={(e) => handleInputChange(day.date, "projectId", e.target.value)}
                          displayEmpty
                        >
                          <MenuItem value="" disabled>Select Project</MenuItem>
                          {projectOptions.map((project) => (
                            <MenuItem key={project.id} value={project.id}>
                              {project.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                    <Box className="grid-cell task-cell">
                      <FormControl fullWidth size="small">
                        <Select
                          value={data.taskId}
                          onChange={(e) => handleInputChange(day.date, "taskId", e.target.value)}
                          displayEmpty
                        >
                          <MenuItem value="" disabled>Select Task</MenuItem>
                          {taskOptions.map((task) => (
                            <MenuItem key={task.id} value={task.id}>
                              {task.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Box>
                    <Box className="grid-cell total-cell">
                      <Chip label={`${data.hours || 0}h 0m`} size="small" className="total-chip" />
                    </Box>
                  </Box>
                );
              })}
            </Box>
          </Card>
        </Box>
      </Box>
    </LocalizationProvider>
  );
};

export default TimesheetPage;
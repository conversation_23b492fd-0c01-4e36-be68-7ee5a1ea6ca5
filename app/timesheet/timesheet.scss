.timesheet-container {
  .content {
    padding: 24px;

    .timesheet-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .breadcrumbs-box {
        h1 {
          font-size: 24px;
          font-weight: 700;
          color: var(--text-dark);
          margin: 0;
        }
      }

      .header-controls {
        display: flex;
        gap: 12px;
        align-items: center;

        .add-entry-btn {
          background-color: var(--primary-color);
          color: white;
          text-transform: none;
          font-weight: 500;
          border-radius: 6px;
          padding: 8px 16px;

          &:hover {
            background-color: var(--primary-dark);
          }
        }
      }
    }

    .timesheet-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding: 16px 0;

      .month-navigation {
        display: flex;
        align-items: center;
        gap: 16px;

        .month-year {
          font-weight: 600;
          color: var(--text-dark);
          min-width: 180px;
          text-align: center;
        }
      }

      .workspace-selector {
        min-width: 200px;
      }
    }

    .stats-section {
      display: flex;
      gap: 16px;
      margin-bottom: 24px;

      .stats-card {
        flex: 1;
        padding: 16px;
        border-radius: 8px;
        text-align: center;
        border: 1px solid var(--border-color);
        background: white;

        h6 {
          font-size: 20px;
          font-weight: 600;
          margin: 0 0 4px 0;
        }

        .MuiTypography-body2 {
          color: var(--text-secondary);
          font-size: 12px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        &.drafts {
          border-left: 4px solid #2196f3;
          
          h6 {
            color: #2196f3;
          }
        }

        &.finalized {
          border-left: 4px solid #4caf50;
          
          h6 {
            color: #4caf50;
          }
        }

        &.leaves {
          border-left: 4px solid #ff9800;
          
          h6 {
            color: #ff9800;
          }
        }

        &.effort {
          border-left: 4px solid var(--primary-color);
          
          h6 {
            color: var(--primary-color);
          }
        }
      }
    }

    .timesheet-grid-card {
      box-shadow: var(--shadow-sm);
      border-radius: 8px;
      overflow: hidden;

      .timesheet-grid {
        .grid-header {
          display: grid;
          grid-template-columns: 80px 1fr 120px 150px 150px 100px;
          background-color: #f8f9fa;
          border-bottom: 2px solid var(--border-color);

          .header-cell {
            padding: 16px 12px;
            font-weight: 600;
            color: var(--text-dark);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
            border-right: 1px solid var(--border-color);

            &:last-child {
              border-right: none;
            }
          }
        }

        .grid-row {
          display: grid;
          grid-template-columns: 80px 1fr 120px 150px 150px 100px;
          border-bottom: 1px solid #e5e7eb;
          transition: background-color 0.2s ease;

          &:hover {
            background-color: #f8f9fa;
          }

          &.workday {
            background-color: white;
          }

          &.weekend {
            background-color: #fff8f0;
          }

          &.today {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
          }

          // Special styling for working days (like in the image)
          &.working-day {
            background-color: #e8f8e8;

            .date-cell {
              background-color: #c8e6c9;
              border-radius: 4px;
              margin: 4px;
            }
          }

          .grid-cell {
            padding: 12px;
            display: flex;
            align-items: center;
            border-right: 1px solid #e5e7eb;

            &:last-child {
              border-right: none;
            }

            &.date-cell {
              flex-direction: column;
              align-items: center;
              justify-content: center;
              text-align: center;

              .date-number {
                font-size: 16px;
                font-weight: 600;
                color: var(--text-dark);
              }

              .date-day {
                font-size: 12px;
                color: var(--text-secondary);
              }
            }

            .MuiTextField-root {
              .MuiOutlinedInput-root {
                border-radius: 4px;
                
                &.MuiInputBase-sizeSmall {
                  font-size: 14px;
                }

                .MuiOutlinedInput-notchedOutline {
                  border-color: #e5e7eb;
                }

                &:hover .MuiOutlinedInput-notchedOutline {
                  border-color: var(--primary-color);
                }

                &.Mui-focused .MuiOutlinedInput-notchedOutline {
                  border-color: var(--primary-color);
                  border-width: 1px;
                }
              }
            }

            &.total-cell {
              justify-content: center;

              .total-chip {
                background-color: #f0f0f0;
                color: var(--text-secondary);
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .timesheet-container {
    .content {
      .timesheet-grid-card {
        .timesheet-grid {
          .grid-header,
          .grid-row {
            grid-template-columns: 70px 1fr 100px 120px 120px 80px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .timesheet-container {
    .content {
      padding: 16px;

      .timesheet-controls {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;

        .month-navigation {
          justify-content: center;
        }
      }

      .stats-section {
        flex-direction: column;
        gap: 12px;
      }

      .timesheet-grid-card {
        overflow-x: auto;

        .timesheet-grid {
          min-width: 800px;
        }
      }
    }
  }
}

"use client";
import React from "react";
import { Box, Typography, Button } from "@mui/material";
import { useRouter } from "next/navigation";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";

const UnauthorizedPage = () => {
  const router = useRouter();

  React.useEffect(() => {
    localStorage.removeItem("token");
    localStorage.removeItem("userEmail");
  }, []);

  return (
    <Box
      sx={{
        height: "100vh",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        textAlign: "center",
        bgcolor: "#f5f5f5",
        padding: 4,
      }}
    >
      <WarningAmberIcon sx={{ fontSize: 60, color: "#f26522", mb: 2 }} />

      <Typography variant="h4" sx={{ mb: 1 }}>
        403 - Unauthorized
      </Typography>

      <Typography variant="body1" sx={{ mb: 3, maxWidth: "400px" }}>
        You do not have permission to access this page. Please contact your
        administrator if you believe this is an error.
      </Typography>

      <Button
        variant="contained"
        sx={{
          backgroundColor: "#f26522",
          textTransform: "none",
          "&:hover": {
            backgroundColor: "#d54d0d"
          }
        }}
        onClick={() => router.push("/login")}
      >
        Go Back
      </Button>
    </Box>
  );
};

export default UnauthorizedPage;
"use client";

import { Box, Typography } from "@mui/material";
import React, { useState, useEffect, Suspense } from "react";
import "./employeesGrid.scss";
import { HomeOutlined, ControlPoint } from "@mui/icons-material";
import { Button } from "@mui/material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { PeopleOutline } from "@mui/icons-material";
import AddEmployeeForm from "@/components/AddEmployeeForm/AddEmployeeForm";
import CustomHeaderCard from "@/components/CustomHeaderCard/CustomHeaderCard";
import ViewToggle from "@/components/ViewToggleButton/ViewToggle";
import ProfileCard from "@/components/ProfileCard/ProfileCard";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import { getDepartments } from "@/app/services/department.service";
import { deleteEmployee } from "@/app/services/users.service";
import useFetchUsersData from "@/app/hooks/users/useFetchUsersData";
import useFetchDesignationData from "@/app/hooks/designation/useFetchDesignationSectionData";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";

// Define the type for EmployeesList
interface EmployeesList {
  id: number | string;
  name: string;
  role: string;
  projects: number;
  done: number;
  progress: number;
  productivity: number;
  avatar: string | undefined;
  roleColor: string;
  progressColor: string;
}

interface Department {
  _id: string;
  departmentName: string;
}

function EmployeesGridContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [limit] = useState(10);
  const [departments, setDepartments] = useState<Department[]>([]);

  // Filter states
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const [selectedDesignation, setSelectedDesignation] =
    useState<string>("Designation");

  // Delete dialog states
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deletingEmployeeId, setDeletingEmployeeId] = useState<string | null>(
    null
  );

  // Restrict access to admin roles only (Admin, SuperAdmin, Manager, HR)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // Fetch departments for the filter
  useEffect(() => {
    let isMounted = true;
    const fetchDepartments = async () => {
      setIsLoading(true);
      try {
        const response = await getDepartments();
        if (isMounted && response?.departments.results) {
          setDepartments(response.departments.results);
        }
      } catch (error) {
        console.error("Failed to fetch departments:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchDepartments();
    return () => {
      isMounted = false;
    };
  }, []);

  // Fetch designation data using the custom hook
  const [designationData] = useFetchDesignationData({
    setIsLoading,
    refresh,
    limit: 1000, // Set a high limit to fetch all designations
    page: 1, // Keep on first page since we want all
    departmentName:
      selectedDepartment === "Department" ? "" : selectedDepartment,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate,
    endDate,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
  });

  // Map all designations for the filter dropdown without any limit
  const designations = designationData
    ? designationData.map(
        (designation: { designationName?: string }) =>
          designation.designationName
      )
    : [];

  // Fetch user data using the custom hook
  const [userData, total, activeCount, inactiveCount, newJoiners] =
    useFetchUsersData({
      setIsLoading,
      refresh,
      limit,
      page,
      departmentName:
        selectedDepartment === "Department" ? "" : selectedDepartment,
      designationName:
        selectedDesignation === "Designation" ? "" : selectedDesignation, // Add designationName filter
      order:
        selectedSortBy === "Ascending"
          ? "asc"
          : selectedSortBy === "Descending"
            ? "desc"
            : "",
      startDate,
      endDate,
      isActive:
        selectedStatus === "Active"
          ? "true"
          : selectedStatus === "Inactive"
            ? "false"
            : undefined,
    });

  interface UserRecord {
    _id: string;
    firstName?: string;
    lastName?: string;
    departmentName?: string;
    designationName?: string;
    avatar?: string | undefined;
    isActive?: boolean;
  }

  const profiles: EmployeesList[] = userData
    ? userData.map((user: UserRecord) => ({
        id: user._id,
        name: `${user.firstName} ${user.lastName}`,
        role: user.designationName || "Employee",
        projects: 20,
        done: 10,
        progress: 5,
        productivity: 50,
        avatar: user.avatar,
        roleColor: "#A020F0",
        progressColor: user.isActive ? "green" : "red",
      }))
    : [];

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Employee", href: "" },
    { label: "EmployeesGrid" },
  ];

  const [openModal, setOpenModal] = useState(false);
  // const [editModal, setEditModal] = useState(false);

  const refreshList = () => {
    setRefresh(!refresh);
  };

  // Handle delete action with confirmation
  const handleDeleteClick = (id: string) => {
    setDeletingEmployeeId(id);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (deletingEmployeeId) {
      try {
        await deleteEmployee(deletingEmployeeId);
        toast.success("Employee deleted successfully");
        refreshList();
      } catch (error) {
        // toast.error("Failed to delete employee");
        console.error("Error deleting employee:", error);
      }
    }
    setDeleteModalOpen(false);
    setDeletingEmployeeId(null);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteModalOpen(false);
    setDeletingEmployeeId(null);
  };

  return (
    <Box className="employee-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="employee-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Employees</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>

          <Box
            className="add-policy"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <ViewToggle />
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => setOpenModal(true)}
            >
              <ControlPoint />
              Add Employee
            </Button>
          </Box>
        </Box>

        {/* Header Cards with Dynamic Data */}
        <Box
          className="header-cards"
          sx={{ display: "flex", width: "100%", gap: "25px" }}
        >
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Total Employees"
            value={total || 0}
            percentage="+19.01%"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Active"
            value={activeCount || 0}
            percentage="+12.5%"
            iconBgColor="#03C95A"
            badgeBgColor="rgba(242, 101, 34, 0.1)"
            badgeTextColor="#F26522"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Inactive"
            value={inactiveCount || 0}
            percentage="+19.01%"
            iconBgColor="#E70D0D"
            badgeBgColor="rgba(242, 101, 34, 0.1)"
            badgeTextColor="#F26522"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="New Joiners"
            value={newJoiners || 0}
            percentage="+12.5%"
            iconBgColor="#1B84FF"
            badgeBgColor="rgba(59, 112, 128, 0.1)"
            badgeTextColor="#3B7080"
            showIcon={true}
          />
        </Box>

        {/* DataGrid Container with Filters */}
        <Box
          className="DataGrid-container"
          sx={{
            display: "flex",
            flexDirection: "column",
            maxHeight: "calc(100vh - 200px)",
            backgroundColor: "#fff",
          }}
        >
          <Box className="DataGrid-header">
            <Typography variant="h5">Employees Grid</Typography>
            <PolicyFilters
              departments={departments.map((dept) => dept.departmentName)}
              designations={designations.filter(
                (designation): designation is string =>
                  designation !== undefined
              )}
              selectedDepartment={selectedDepartment}
              setSelectedDepartment={setSelectedDepartment}
              selectedDesignation={selectedDesignation}
              setSelectedDesignation={setSelectedDesignation}
              selectedSortBy={selectedSortBy}
              setSelectedSortBy={setSelectedSortBy}
              selectedDateRange={selectedDateRange}
              setSelectedDateRange={setSelectedDateRange}
              setStartDate={setStartDate}
              setEndDate={setEndDate}
              selectedStatus={selectedStatus}
              setSelectedStatus={setSelectedStatus}
              setPage={setPage}
              currentDate={currentDate}
              showDateRangeFilter={true}
              showDepartmentFilter={true}
              showStatusFilter={true}
              showSortByFilter={true}
              showDesignationFilter={true}
              selectedLeaveType="Leave Type"
              setSelectedLeaveType={() => {}}
              showLeaveTypeFilter={false}
              selectedPriority=""
              setSelectedPriority={() => {}}
            />
          </Box>
        </Box>

        {/* Profile Cards */}
        <Box className="Profile-Cards">
          {profiles.map((profile) => (
            <ProfileCard
              key={profile.id}
              id={profile.id.toString()}
              name={profile.name}
              role={profile.role}
              projects={profile.projects}
              done={profile.done}
              progress={profile.progress}
              productivity={profile.productivity}
              avatar={profile.avatar || ""}
              roleColor={profile.roleColor}
              progressColor={profile.progressColor}
              refreshList={refreshList}
              onDelete={handleDeleteClick}
            />
          ))}
        </Box>
      </Box>

      {/* Add Employee Dialog */}
      <AddEmployeeForm
        open={openModal}
        onClose={() => setOpenModal(false)}
        refreshList={refreshList}
      />

      <DeleteConfirmationDialog
        open={deleteModalOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Confirm Delete"
        message="You want to delete all the marked items, this can't be undone once you delete."
      />
    </Box>
  );
}

function EmployeesGridPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <EmployeesGridContent />
    </Suspense>
  );
}

export default EmployeesGridPage;

"use client";
import type React from "react";
import { useState, useEffect } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  IconButton,
  CircularProgress,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { getDepartmentById } from "@/app/services/department.service";
import { getUsers } from "@/app/services/users.service";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Loader from "@/components/Loader/Loader";

interface AddDepartmentDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (formData: FormData) => Promise<void>;
  isEditMode: boolean;
  departmentId: string | null;
}

interface DepartmentFormValues {
  departmentName: string;
  status: "active" | "inactive";
  departmentHead: string;
}

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
}

const validationSchema = Yup.object({
  departmentName: Yup.string().required("Department name is required"),
  status: Yup.string().required("Status is required"),
  departmentHead: Yup.string().required("Department head is required"),
});

const initialValues: DepartmentFormValues = {
  departmentName: "",
  status: "active",
  departmentHead: "",
};

const AddDepartmentDialog: React.FC<AddDepartmentDialogProps> = ({
  open,
  onClose,
  onSubmit,
  isEditMode,
  departmentId,
}) => {
  const [initialFormValues, setInitialFormValues] =
    useState<DepartmentFormValues>(initialValues);
  const [loadingDepartment, setLoadingDepartment] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoadingUsers(true);
        const response = await getUsers();
        if (response.success && response.users?.results) {
          setUsers(response.users.results);
        } else {
          toast.error("Failed to load users.");
        }
      } catch (error) {
        console.error("Error fetching users:", error);
        toast.error("Failed to load users.");
      } finally {
        setLoadingUsers(false);
      }
    };

    if (open) {
      fetchUsers();
    }
  }, [open]);

  useEffect(() => {
    if (isEditMode && departmentId) {
      const fetchDepartment = async () => {
        try {
          setLoadingDepartment(true);
          const response = await getDepartmentById(departmentId);
          console.log("Fetched department data:", response);
          const departmentData = response.department;
          setInitialFormValues({
            departmentName: departmentData.departmentName || "",
            status: departmentData.isActive ? "active" : "inactive",
            departmentHead: departmentData.departmentHead?._id || "", // Extract _id from departmentHead object
          });
        } catch (error) {
          console.error("Error fetching department:", error);
          toast.error("Failed to load department details.");
        } finally {
          setLoadingDepartment(false);
        }
      };
      fetchDepartment();
    } else {
      setInitialFormValues(initialValues);
    }
  }, [isEditMode, departmentId, open]);

  const handleSubmit = async (values: DepartmentFormValues) => {
    try {
      const formData = new FormData();
      formData.append("departmentName", values.departmentName);
      formData.append(
        "isActive",
        values.status === "active" ? "true" : "false"
      );
      formData.append("departmentHead", values.departmentHead);

      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error(
        `Error ${isEditMode ? "updating" : "adding"} department:`,
        error
      );
      toast.error(
        `Failed to ${isEditMode ? "update" : "add"} department. Please try again.`
      );
    }
  };

  return (
    <>
      <ToastContainer />
      {loadingDepartment || loadingUsers ? (
        <Loader loading={loadingDepartment || loadingUsers} />
      ) : (
        <Dialog
          open={open}
          onClose={(event, reason) => {
            if (reason !== "backdropClick") {
              onClose();
            }
          }}
          disableEscapeKeyDown
          sx={{
            "& .MuiDialog-paper": {
              maxWidth: "498px",
              width: "100%",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "16px !important",
            }}
          >
            <Typography
              sx={{ fontSize: "20px", fontWeight: 600, color: "#202c4b" }}
            >
              {isEditMode ? "Edit Department" : "Add Department"}
            </Typography>
            <IconButton
              onClick={onClose}
              aria-label="close"
              sx={{
                backgroundColor: "#6b7280",
                backgroundImage: "none",
                borderRadius: "50%",
                color: "#fff",
                height: "20px",
                width: "20px",
                margin: 0,
                padding: 0,
                "&:hover": {
                  backgroundColor: "#d55a1d",
                },
                "& .MuiSvgIcon-root": {
                  fontSize: "14px",
                },
              }}
            >
              <Close />
            </IconButton>
          </DialogTitle>

          <Formik
            initialValues={initialFormValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            enableReinitialize
          >
            {({ errors, touched }) => (
              <Form>
                <DialogContent sx={{ padding: "16px !important" }}>
                  <Box sx={{ mt: 0 }}>
                    {/* Department Name */}
                    <Box sx={{ mb: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{ mb: 0.5, fontSize: "14px" }}
                      >
                        Department Name <span className="required">*</span>
                      </Typography>
                      <Field
                        as={TextField}
                        fullWidth
                        name="departmentName"
                        variant="outlined"
                        error={
                          touched.departmentName &&
                          Boolean(errors.departmentName)
                        }
                        helperText={
                          touched.departmentName && errors.departmentName
                        }
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            height: "38px",
                            fontSize: "14px",
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          },
                          "& .MuiInputBase-input": {
                            padding: "8px 14px",
                          },
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#ccc",
                          },
                        }}
                      />
                    </Box>

                    {/* Department Head Select */}
                    <Box sx={{ mb: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{ mb: 0.5, fontSize: "14px" }}
                      >
                        Department Head <span className="required">*</span>
                      </Typography>
                      <FormControl fullWidth>
                        <Field
                          as={Select}
                          name="departmentHead"
                          labelId="department-head-label"
                          variant="outlined"
                          error={
                            touched.departmentHead &&
                            Boolean(errors.departmentHead)
                          }
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              height: "38px",
                              fontSize: "14px",
                            },
                            "& .MuiInputBase-input": {
                              padding: "8px 14px",
                            },
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          }}
                        >
                          {users.map((user) => (
                            <MenuItem key={user._id} value={user._id}>
                              {`${user.firstName} ${user.lastName} (${user.email})`}
                            </MenuItem>
                          ))}
                        </Field>
                        <ErrorMessage
                          name="departmentHead"
                          component="div"
                          render={(msg) => (
                            <div style={{ color: "red", fontSize: "12px" }}>
                              {msg}
                            </div>
                          )}
                        />
                      </FormControl>
                    </Box>

                    {/* Status Select */}
                    <Box sx={{ mb: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{ mb: 0.5, fontSize: "14px" }}
                      >
                        Status <span className="required">*</span>
                      </Typography>
                      <FormControl fullWidth>
                        <Field
                          as={Select}
                          name="status"
                          variant="outlined"
                          error={touched.status && Boolean(errors.status)}
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              height: "38px",
                              fontSize: "14px",
                            },
                            "& .MuiInputBase-input": {
                              padding: "8px 14px",
                            },
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          }}
                        >
                          <MenuItem value="active">Active</MenuItem>
                          <MenuItem value="inactive">Inactive</MenuItem>
                        </Field>
                        <ErrorMessage
                          name="status"
                          component="div"
                          render={(msg) => (
                            <div style={{ color: "red", fontSize: "12px" }}>
                              {msg}
                            </div>
                          )}
                        />
                      </FormControl>
                    </Box>
                  </Box>
                </DialogContent>

                <DialogActions sx={{ padding: "16px !important" }}>
                  <Button
                    onClick={onClose}
                    variant="outlined"
                    sx={{
                      textTransform: "none",
                      borderColor: "#ccc",
                      color: "#000",
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    sx={{
                      backgroundColor: "#F26522",
                      color: "#FFF",
                      textTransform: "none",
                      "&:hover": {
                        backgroundColor: "#d55a1d",
                      },
                    }}
                  >
                    {isEditMode ? "Update Department" : "Add Department"}
                  </Button>
                </DialogActions>
              </Form>
            )}
          </Formik>
        </Dialog>
      )}
    </>
  );
};

export default AddDepartmentDialog;

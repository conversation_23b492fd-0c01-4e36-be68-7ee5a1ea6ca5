"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Typography,
  Paper,
  useMediaQuery,
  TextField,
} from "@mui/material";
import React, { useState, useEffect, useRef, useCallback } from "react";
import "./department.scss";
import { HomeOutlined, ControlPoint, Circle } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import { GridRowsProp, GridColDef, GridToolbarExport } from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { EditNote, Delete } from "@mui/icons-material";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import useFetchDepartmentData from "@/app/hooks/department/useFetchDepartmentData";
import {
  addDepartment,
  updateDepartment,
  deleteDepartment,
} from "@/app/services/department.service";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import AddDepartmentDialog from "./AddDepartmentDialog";
import PolicyFilters from "@/components/policyFilter/PolicyFilters";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";
import { getDepartmentUserCount } from "@/app/services/users.service";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

// Interfaces
interface UserCountItem {
  numberOfEmployees: number;
  departmentId: string;
  departmentName: string;
}

interface DepartmentUserCountResponse {
  success: boolean;
  statusCode: number;
  message: string;
  userCount: UserCountItem[];
}

interface Department {
  _id: string;
  departmentName: string;
  numberOfEmployees?: string;
  createdAt?: string;
  isActive: boolean;
  departmentHead?: {
    firstName?: string;
    lastName?: string;
  };
}


export default function DepartmentContent() {
  // State for loading and refresh
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);

  // State for pagination
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);

  // State for search
  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const handlePageChange = (newPage: number): void => {
    setPage(newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  // State for the Add/Edit Department modal
  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedDepartmentId, setSelectedDepartmentId] = useState<
    string | null
  >(null);

  // State for filters
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>("Date Range");
  const [, setStartDate] = useState<string>("");
  const [, setEndDate] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const currentDate = new Date();

  // State for storing employee counts
  const [employeeCounts, setEmployeeCounts] = useState<Map<string, number>>(
    new Map()
  );

  // Handle search input change
  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  // Fetch department data
  const [departmentData, total] = useFetchDepartmentData({
    setIsLoading,
    refresh,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",

    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });

  // Restrict access to admin roles only (Admin, SuperAdmin, Manager, HR)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // Map department data to a list of department names for PolicyFilters
  const departments = departmentData
    ? departmentData.map(
        (dept: { departmentName: string }) => dept.departmentName
      )
    : [];

  useEffect(() => {
    const fetchEmployeeCounts = async () => {
      try {
        const response = await getDepartmentUserCount();
        if (response && response.userCount) {
          const countsMap = new Map<string, number>();

          response.userCount.forEach((item: UserCountItem) => {
            countsMap.set(item.departmentId, item.numberOfEmployees);
          });

          setEmployeeCounts(countsMap);
        }
      } catch (error) {
        console.error("Error fetching employee counts:", error);
      }
    };

    fetchEmployeeCounts();
  }, [refresh]); // Add refresh to dependencies to update when departments change

  const rows: GridRowsProp = departmentData
    ? departmentData.map((department: Department, index: number) => ({
        id: department._id || index + 1,
        departmentName: department.departmentName || "N/A",
        numberOfEmployees: employeeCounts.get(department._id) || 0,
        createdDate: department.createdAt
          ? new Date(department.createdAt).toISOString().split("T")[0]
          : "N/A",
        createdDateRaw: department.createdAt
          ? new Date(department.createdAt)
          : new Date(),
        isActive: department.isActive || false,
        departmentHead:
          `${department.departmentHead?.firstName || "-"} ${department.departmentHead?.lastName || ""}` ||
          "-",
      }))
    : [];

  // State for delete dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [departmentToDelete, setDepartmentToDelete] = useState<
    string | number | null
  >(null);

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Employee Management", href: "" },
    { label: "Departments" },
  ];

  const isMobile = useMediaQuery("(max-width:768px)");

  const columns: GridColDef[] = [
    {
      field: "departmentName",
      headerName: "Department",
      flex: 1,
    },
    {
      field: "numberOfEmployees",
      headerName: "No of Employees",
      flex: 1,
      renderCell: (params) => <Typography>{params.value}</Typography>,
    },
    {
      field: "departmentHead",
      headerName: "Department Head",
      flex: 1,
      renderCell: (params) => <Typography>{params.value || "N/A"}</Typography>,
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 1,
      renderCell: (params) => (
        // <Box
        //   sx={{
        //     display: "flex",
        //     alignItems: "center",
        //     justifyContent: "center",
        //   }}
        // >
        //   {isMobile ? (
        //     <Circle
        //       sx={{
        //         fontSize: 12,
        //         color: params.value ? "#03C95A" : "#E70D0D",
        //       }}
        //     />
        //   ) : (
        //     <Typography
        //       sx={{
        //         display: "flex",
        //         alignItems: "center",
        //         gap: "3px",
        //         backgroundColor: params.value ? "#03C95A" : "#E70D0D",
        //         color: "#fff",
        //         borderRadius: "5px",
        //         textAlign: "center",
        //         minWidth: "66px",
        //         justifyContent: "center",
        //         fontSize: "10px",
        //         fontWeight: 500,
        //         padding: "0px 5px",
        //         lineHeight: "18px",
        //       }}
        //     >
        //       <Box
        //         sx={{
        //           width: "5px",
        //           height: "5px",
        //           borderRadius: "100%",
        //           backgroundColor: params.value === "Active" ? "#fff" : "#fff",
        //         }}
        //       />

        //       {params.value ? "Active" : "Inactive"}
        //     </Typography>
        //   )}
        // </Box>
        <StatusToggle
          isActive={params.value}
          title="Confirm Department Status Change"
          onChange={async (newStatus) => {
            try {
              const departmentBody = {
                isActive: newStatus,
              };
              await updateDepartment(params.row.id, departmentBody);
              setRefresh(!refresh);
            } catch (error) {
              console.error("Error updating department status:", error);
            }
          }}
        />
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 0.5,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handleEditClick = async (departmentId: string | number) => {
    try {
      setSelectedDepartmentId(departmentId as string);
      setIsEditMode(true);
      setOpenModal(true);
    } catch (error) {
      console.error("Error preparing edit:", error);
    }
  };

  const handleDeleteClick = (departmentId: string | number) => {
    setDepartmentToDelete(departmentId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (departmentToDelete !== null) {
      try {
        await deleteDepartment(departmentToDelete as string);
        setDeleteDialogOpen(false);
        setDepartmentToDelete(null);
        setRefresh(!refresh);
        // toast.success("Department deleted successfully!", {
        //   position: "top-right",
        //   autoClose: 3000,
        //   hideProgressBar: false,
        //   closeOnClick: true,
        //   pauseOnHover: true,
        //   draggable: true,
        // });
      } catch (error) {
        console.error("Failed to delete department:", error);
        // toast.error("Failed to delete department. Please try again.", {
        //   position: "top-right",
        //   autoClose: 3000,
        //   hideProgressBar: false,
        //   closeOnClick: true,
        //   pauseOnHover: true,
        //   draggable: true,
        // });
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDepartmentToDelete(null);
  };

  const handleDialogSubmit = async (formData: FormData) => {
    try {
      // Create a department body without numberOfEmployees
      const departmentBody = {
        departmentName: formData.get("departmentName") as string,
        isActive: formData.get("isActive") === "true",
        departmentHead: formData.get("departmentHead") as string,
      };

      if (isEditMode && selectedDepartmentId) {
        await updateDepartment(selectedDepartmentId, departmentBody);
        // toast.success("Department updated successfully!", {
        //   position: "top-right",
        //   autoClose: 3000,
        //   hideProgressBar: false,
        //   closeOnClick: true,
        //   pauseOnHover: true,
        //   draggable: true,
        // });
      } else {
        await addDepartment(departmentBody);
        // toast.success("Department added successfully!", {
        //   position: "top-right",
        //   autoClose: 3000,
        //   hideProgressBar: false,
        //   closeOnClick: true,
        //   pauseOnHover: true,
        //   draggable: true,
        // });
      }
      setRefresh(!refresh);
      setOpenModal(false);
    } catch (error) {
      console.error("Error submitting department:", error);
      // toast.error("Failed to submit department. Please try again.", {
      //   position: "top-right",
      //   autoClose: 3000,
      //   hideProgressBar: false,
      //   closeOnClick: true,
      //   pauseOnHover: true,
      //   draggable: true,
      // });
    }
  };

  const handleDialogClose = () => {
    setOpenModal(false);
    setIsEditMode(false);
    setSelectedDepartmentId(null);
  };

  // const formatDateForApi = (dateStr: string) => {
  //   const [month, day, year] = dateStr.split("/");
  //   return `${year}-${month}-${day}`;
  // };

  return (
    <Box className="department-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="department-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Departments</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="add-department"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => {
                setIsEditMode(false);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ width: "16px", height: "16px" }} />
              Add Department
            </Button>
          </Box>
        </Box>

        {/* DataGrid */}
        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight:
                rows.length <= 5 ? "calc(70vh - 200px)" : "calc(100vh - 200px)",
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Department List</Typography>
              {/* Integrate PolicyFilters Component */}
              <PolicyFilters
                departments={departments}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={false}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              // checkboxSelection
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                setPage(model.page + 1); // Update to directly use model.page
                setPageSize(model.pageSize);
                setLimit(model.pageSize);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      {/* Department Dialog */}
      <AddDepartmentDialog
        open={openModal}
        onClose={handleDialogClose}
        onSubmit={handleDialogSubmit}
        isEditMode={isEditMode}
        departmentId={selectedDepartmentId}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this department? This action cannot be undone."
      />
    </Box>
  );
}

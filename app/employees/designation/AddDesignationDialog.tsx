"use client";
import type React from "react";
import { useState, useEffect } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  IconButton,
  Autocomplete,
  CircularProgress,
  MenuItem,
  Select,
  FormControl,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { getDesignationById } from "@/app/services/designation.service";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

interface Department {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface AddDesignationDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (formData: FormData) => Promise<void>;
  departments: string[];
  fullDepartments: Department[];
  departmentsLoading: boolean;
  departmentsError: string | null;
  isEditMode: boolean;
  designationId: string | null;
}

interface DesignationFormValues {
  designationName: string;
  department: string;
  status: "active" | "inactive";
}

const validationSchema = Yup.object({
  designationName: Yup.string().required("Designation name is required"),
  department: Yup.string().required("Department is required"),
  status: Yup.string().required("Status is required"),
});

const initialValues: DesignationFormValues = {
  designationName: "",
  department: "",
  status: "active" as const,
};

const AddDesignationDialog: React.FC<AddDesignationDialogProps> = ({
  open,
  onClose,
  onSubmit,
  departments,
  fullDepartments,
  departmentsLoading,
  departmentsError,
  isEditMode,
  designationId,
}) => {
  const [initialFormValues, setInitialFormValues] =
    useState<DesignationFormValues>(initialValues);
  const [loadingDesignation, setLoadingDesignation] = useState(false);

  useEffect(() => {
    if (isEditMode && designationId) {
      const fetchDesignation = async () => {
        try {
          setLoadingDesignation(true);
          const response = await getDesignationById(designationId);
          console.log("Fetched designation data:", response);

          // Check the structure of the response
          const designation = response.designation || response;

          // Use departmentName directly from the response
          const departmentName = designation.departmentName || "";

          setInitialFormValues({
            designationName: designation.designationName || "",
            department: departmentName,
            status: designation.isActive ? "active" : "inactive",
          });

          console.log("Setting form values:", {
            designationName: designation.designationName || "",
            department: departmentName,
            status: designation.isActive ? "active" : "inactive",
          });
        } catch (error) {
          console.error("Error fetching designation:", error);
          // toast.error("Failed to fetch designation details");
        } finally {
          setLoadingDesignation(false);
        }
      };

      fetchDesignation();
    } else {
      setInitialFormValues(initialValues);
    }
  }, [isEditMode, designationId]);

  const handleSubmit = async (values: DesignationFormValues) => {
    try {
      const selectedDepartment = fullDepartments.find(
        (dept) => dept.departmentName === values.department
      );
      if (!selectedDepartment) {
        throw new Error("Selected department not found");
      }

      const formData = new FormData();
      formData.append("designationName", values.designationName);
      formData.append("departmentId", selectedDepartment._id);
      formData.append(
        "isActive",
        values.status === "active" ? "true" : "false"
      );

      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error(
        `Error ${isEditMode ? "updating" : "adding"} designation:`,
        error
      );
      toast.error(
        `Failed to ${isEditMode ? "update" : "add"} designation. Please try again.`
      );
    }
  };

  return (
    <>
      {/* Add ToastContainer to render toasts */}
      <ToastContainer />
      <Dialog
        open={open}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            onClose();
          }
        }}
        disableEscapeKeyDown
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          sx={{
            padding: "16px !important",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {isEditMode ? "Edit Designation" : "Add Designation"}
          <IconButton
            aria-label="close"
            onClick={onClose}
            sx={{
              backgroundColor: "#6b7280",
              backgroundImage: "none",
              borderRadius: "50%",
              color: "#fff",
              height: "20px",
              width: "20px",
              margin: 0,
              padding: 0,
              "&:hover": {
                backgroundColor: "#d55a1d",
              },
              "& .MuiSvgIcon-root": {
                fontSize: "14px",
              },
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        {loadingDesignation ? (
          <DialogContent>
            <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
              <CircularProgress size={24} />
            </Box>
          </DialogContent>
        ) : (
          <Formik
            initialValues={initialFormValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            enableReinitialize
          >
            {({ errors, touched, setFieldValue, values }) => (
              <Form>
                <DialogContent sx={{ padding: "1rem", paddingTop: "0px" }}>
                  <Box sx={{ mt: 1 }}>
                    {/* Designation Name */}
                    <Box sx={{ mb: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{ mb: 0.5, fontSize: "14px" }}
                      >
                        Designation Name <span className="required">*</span>
                      </Typography>
                      <Field
                        as={TextField}
                        fullWidth
                        name="designationName"
                        variant="outlined"
                        error={
                          touched.designationName &&
                          Boolean(errors.designationName)
                        }
                        helperText={
                          touched.designationName && errors.designationName
                        }
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            height: "38px",
                            fontSize: "14px",
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          },
                          "& .MuiInputBase-input": {
                            padding: "8px 14px",
                          },
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#ccc",
                          },
                        }}
                      />
                    </Box>

                    {/* Department Dropdown */}
                    <Box sx={{ mb: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{ mb: 0, fontSize: "14px" }}
                      >
                        Department <span className="required">*</span>
                      </Typography>
                    </Box>
                    {departmentsLoading ? (
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          mb: 1,
                        }}
                      >
                        <CircularProgress size={24} />
                      </Box>
                    ) : departmentsError ? (
                      <Typography color="error" sx={{ mb: 2 }}>
                        {departmentsError}
                      </Typography>
                    ) : (
                      <Autocomplete
                        disablePortal
                        id="department-autocomplete"
                        options={departments}
                        sx={{
                          width: "100%",
                          mb: 2,
                          "& .MuiOutlinedInput-root": {
                            height: "38px",
                            fontSize: "14px",
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          },
                          "& .MuiInputBase-input": {
                            padding: "8px 14px !important",
                          },
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#ccc",
                          },
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            error={
                              touched.department && Boolean(errors.department)
                            }
                            helperText={touched.department && errors.department}
                          />
                        )}
                        onChange={(event, value) =>
                          setFieldValue("department", value || "")
                        }
                        value={values.department || ""}
                      />
                    )}

                    {/* Status Select */}
                    <Box sx={{ mb: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{ mb: 0.5, fontSize: "14px" }}
                      >
                        Status
                      </Typography>
                      <FormControl fullWidth>
                        <Field
                          as={Select}
                          name="status"
                          variant="outlined"
                          error={touched.status && Boolean(errors.status)}
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              height: "38px",
                              fontSize: "14px",
                            },
                            "& .MuiInputBase-input": {
                              padding: "8px 14px",
                            },
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          }}
                        >
                          <MenuItem value="active">Active</MenuItem>
                          <MenuItem value="inactive">Inactive</MenuItem>
                        </Field>
                        <ErrorMessage
                          name="status"
                          render={(msg) => (
                            <div style={{ color: "red", fontSize: "12px" }}>
                              {msg}
                            </div>
                          )}
                        />
                      </FormControl>
                    </Box>
                  </Box>

                  <DialogActions sx={{ padding: "16px 0 0 0 !important" }}>
                    <Button
                      onClick={onClose}
                      variant="outlined"
                      sx={{
                        textTransform: "none",
                        borderColor: "#ccc",
                        color: "#000",
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      sx={{
                        backgroundColor: "#F26522",
                        color: "#FFF",
                        textTransform: "none",
                        "&:hover": {
                          backgroundColor: "#d55a1d",
                        },
                      }}
                    >
                      {isEditMode ? "Update Designation" : "Add Designation"}
                    </Button>
                  </DialogActions>
                </DialogContent>
              </Form>
            )}
          </Formik>
        )}
      </Dialog>
    </>
  );
};

export default AddDesignationDialog;

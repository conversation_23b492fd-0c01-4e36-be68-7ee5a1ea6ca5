"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Typo<PERSON>,
  Paper,
  TextField,
  useMediaQuery,
} from "@mui/material";
import React, { useState, useEffect, useRef, useCallback } from "react";
import "./designation.scss";
import { HomeOutlined, ControlPoint, Circle } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { EditNote, Delete } from "@mui/icons-material";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import useFetchDesignationData from "@/app/hooks/designation/useFetchDesignationSectionData";
import {
  addDesignation,
  updateDesignation,
  deleteDesignation,
  DesignationBody,
} from "@/app/services/designation.service";
import { toast } from "react-toastify";
import { getDepartments } from "@/app/services/department.service";
import Loader from "@/components/Loader/Loader";
import AddDesignationDialog from "./AddDesignationDialog";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";
import { getDesignationUserCount } from "@/app/services/users.service";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";

// interface Designation {
//   id: number | string;
//   designationName: string;
//   departmentName: string;
//   numberOfEmployee: string;
//   createdDate: string;
//   createdDateRaw?: Date;
//   isActive: boolean;
// }

interface Department {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

// Quick Search Toolbar Component
const QuickSearchToolbar = React.memo(
  ({
    onSearchChange,
    searchQuery,
  }: {
    onSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
    searchQuery: string;
  }) => {
    const inputRef = useRef<HTMLInputElement>(null);

    // Restore focus after re-renders
    useEffect(() => {
      if (inputRef.current && document.activeElement !== inputRef.current) {
        inputRef.current.focus();
      }
    }, [searchQuery]);

    return (
      <Box
        sx={{
          p: 0.5,
          pb: 0,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "10px 20px",
        }}
      >
        <TextField
          inputRef={inputRef}
          variant="outlined"
          placeholder="Search"
          size="small"
          value={searchQuery}
          onChange={onSearchChange}
        />
        <Box
          className="grid-export"
          sx={{ display: "flex", alignItems: "center" }}
        >
          <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
        </Box>
      </Box>
    );
  }
);

// const formatDate = (date: Date) => {
//   return date.toLocaleDateString("en-US", {
//     month: "2-digit",
//     day: "2-digit",
//     year: "numeric",
//   });
// };

const formatDateForApi = (dateStr: string) => {
  if (!dateStr) return undefined;

  try {
    // Assuming dateStr is in format "MM/DD/YYYY"
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return undefined;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return undefined;
  }
};

export default function DesignationContent() {
  // State for loading and refresh
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);

  // State for pagination
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);

  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const handlePageChange = (newPage: number): void => {
    setPage(newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  // State for the Add/Edit Designation modal
  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedDesignationId, setSelectedDesignationId] = useState<
    string | null
  >(null);

  // State for departments fetching
  const [departments, setDepartments] = useState<Department[]>([]);
  const [departmentNames, setDepartmentNames] = useState<string[]>([]);
  const [departmentsLoading, setDepartmentsLoading] = useState(true);
  const [departmentsError, setDepartmentsError] = useState<string | null>(null);

  // State for filters
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Status");

  const [designationData, total] = useFetchDesignationData({
    setIsLoading,
    refresh,
    limit,
    page,
    departmentName:
      selectedDepartment === "Department" ? "" : selectedDepartment,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate: startDate ? formatDateForApi(startDate) : undefined,
    endDate: endDate ? formatDateForApi(endDate) : undefined,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,      
  });

  // State for employee counts
  const [employeeCounts, setEmployeeCounts] = useState<Map<string, number>>(
    new Map()
  );

  const handleSearchChange = useCallback(
      (event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(event.target.value);
      },
      []
    );

  // Restrict access to admin roles only (Admin, SuperAdmin, Manager, HR)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // Fetch departments when the component mounts
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setDepartmentsLoading(true);
        const response = await getDepartments();
        const deptData = response.departments.results.filter(
          (dept: Department) => dept.isActive && !dept.isDeleted
        );

        setDepartments(deptData);
        const departmentNames = deptData.map(
          (dept: Department) => dept.departmentName
        );
        setDepartmentNames(departmentNames);
      } catch (error) {
        setDepartmentsError("Failed to fetch departments");
        console.error("Error fetching departments:", error);
      } finally {
        setDepartmentsLoading(false);
      }
    };

    fetchDepartments();
  }, []);

  // Fetch employee counts when the component mounts or refreshes
  useEffect(() => {
    const fetchEmployeeCounts = async () => {
      try {
        const response = await getDesignationUserCount();
        if (response && response.userCount) {
          const countsMap = new Map<string, number>();

          response.userCount.forEach(
            (item: {
              departmentId: string;
              numberOfEmployees: number;
              departmentName: string;
            }) => {
              // Store counts by both ID and name to handle both matching cases
              countsMap.set(item.departmentId, item.numberOfEmployees);
              if (item.departmentName) {
                countsMap.set(item.departmentName, item.numberOfEmployees);
              }
            }
          );

          setEmployeeCounts(countsMap);
        }
      } catch (error) {
        console.error("Error fetching employee counts:", error);
      }
    };

    fetchEmployeeCounts();
  }, [refresh]); // Add refresh to dependencies to update when departments change

  interface DesignationRecord {
    _id: string;
    designationName?: string;
    departmentId?:
      | {
          _id: string;
          departmentName: string;
        }
      | string;
    createdAt?: string;
    isActive?: boolean;
    departmentName?: string;
  }

  const rows: GridRowsProp = designationData
    ? designationData.map((designation: DesignationRecord, index: number) => ({
        id: designation._id || index + 1,
        designationName: designation.designationName || "-",
        departmentName:
          designation.departmentId &&
          typeof designation.departmentId === "object"
            ? designation.departmentId.departmentName
            : "-",
        numberOfEmployee:
          employeeCounts.get(designation._id) ||
          employeeCounts.get(designation.designationName || "") ||
          "0",
        createdDate: designation.createdAt
          ? new Date(designation.createdAt).toISOString().split("T")[0]
          : "-",
        createdDateRaw: designation.createdAt
          ? new Date(designation.createdAt)
          : new Date(),
        isActive: designation.isActive || false,
      }))
    : [];

  // State for delete dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [designationToDelete, setDesignationToDelete] = useState<
    string | number | null
  >(null);

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Employee Management", href: "" },
    { label: "Designations" },
  ];

  const isMobile = useMediaQuery("(max-width:768px)");

  const columns: GridColDef[] = [
    {
      field: "designationName",
      headerName: "Designation",
      editable: false,
      flex: 1,
    },
    {
      field: "departmentName",
      headerName: "Department",
      editable: false,
      flex: 1,
    },
    {
      field: "numberOfEmployee",
      headerName: "No of Employees",
      editable: false,
      flex: 1,
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 1,
      renderCell: (params) => (
        // <Box
        //   sx={{
        //     display: "flex",
        //     alignItems: "center",
        //     justifyContent: "center",
        //   }}
        // >
        //   {isMobile ? (
        //     <Circle
        //       sx={{
        //         fontSize: 12,
        //         color: params.value ? "#03C95A" : "#E70D0D",
        //       }}
        //     />
        //   ) : (
        //     <Typography
        //       sx={{
        //         display: "flex",
        //         alignItems: "center",
        //         gap: "3px",
        //         backgroundColor: params.value ? "#03C95A" : "#E70D0D",
        //         color: "#fff",
        //         borderRadius: "5px",
        //         textAlign: "center",
        //         minWidth: "66px",
        //         justifyContent: "center",
        //         fontSize: "10px",
        //         fontWeight: 500,
        //         padding: "0px 5px",
        //         lineHeight: "18px",
        //       }}
        //     >
        //       <Box
        //         sx={{
        //           width: "5px",
        //           height: "5px",
        //           borderRadius: "100%",
        //           backgroundColor: params.value ? "#fff" : "#fff",
        //         }}
        //       />

        //       {params.value ? "Active" : "Inactive"}
        //     </Typography>
        //   )}
        // </Box>
        <StatusToggle
          isActive={params.value}
          title="Confirm Designation Status Change"
          onChange={async (isActive: boolean) => {
            try {
              await updateDesignation(params.row.id, {
                designationName: params.row.designationName,
                departmentId: params.row.departmentId,
                isActive,
              });
              setRefresh(!refresh);
            } catch (error) {
              console.error("Failed to update designation status:", error);
              toast.error(
                "Failed to update designation status. Please try again."
              );
            }
          }}
        />
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 0.5,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handleEditClick = async (designationId: string | number) => {
    try {
      setSelectedDesignationId(designationId as string);
      setIsEditMode(true);
      setOpenModal(true);
    } catch (error) {
      console.error("Error preparing edit:", error);
    }
  };

  const handleDeleteClick = (designationId: string | number) => {
    setDesignationToDelete(designationId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (designationToDelete !== null) {
      try {
        await deleteDesignation(designationToDelete as string);
        setDeleteDialogOpen(false);
        setDesignationToDelete(null);
        setRefresh(!refresh);
        // toast.success("Designation deleted successfully!");
      } catch (error) {
        console.error("Failed to delete designation:", error);
        // toast.error("Failed to delete designation. Please try again.");
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDesignationToDelete(null);
  };

  const handleDialogSubmit = async (formData: FormData) => {
    try {
      const designationBody: DesignationBody = {
        designationName: formData.get("designationName") as string,
        departmentId: formData.get("departmentId") as string,
        isActive: formData.get("isActive") === "true",
      };

      console.log("Submitting designation data:", designationBody);

      if (isEditMode && selectedDesignationId) {
        await updateDesignation(selectedDesignationId, designationBody);
        console.log("Designation updated successfully");
      } else {
        await addDesignation(designationBody);
        console.log("Designation added successfully");
      }
      setRefresh(!refresh);
    } catch (error) {
      console.error("Error submitting designation:", error);
      // toast.error("Failed to submit designation. Please try again.");
    }
  };

  const handleDialogClose = () => {
    setOpenModal(false);
    setIsEditMode(false);
    setSelectedDesignationId(null);
  };

  return (
    <Box className="designation-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="designation-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Designations</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="add-designation"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => {
                setIsEditMode(false);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ fontSize: "16px" }} />
              Add Designation
            </Button>
          </Box>
        </Box>

        {/* DataGrid */}
        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: rows.length <= 5 ? "60vh" : "80vh",
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Designations List</Typography>
              <PolicyFilters
                departments={departmentNames}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={true}
                showStatusFilter={true}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                setPage(model.page + 1);
                setPageSize(model.pageSize);
                setLimit(model.pageSize);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      {/* Add/Edit Designation Dialog */}
      <AddDesignationDialog
        open={openModal}
        onClose={handleDialogClose}
        onSubmit={handleDialogSubmit}
        departments={departmentNames}
        fullDepartments={departments}
        departmentsLoading={departmentsLoading}
        departmentsError={departmentsError}
        isEditMode={isEditMode}
        designationId={selectedDesignationId}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this designation? This action cannot be undone."
      />
    </Box>
  );
}

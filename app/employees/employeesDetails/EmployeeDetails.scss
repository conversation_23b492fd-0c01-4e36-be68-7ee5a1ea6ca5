.details-container {
  padding: var(--spacing-md);

  .details-header {
    h6 {
      a {
        color: var(--text-primary);
        cursor: pointer;
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 14px;

        &:hover {
          color: var(--primary-color);
        }
      }
    }
  }

  .details-content {
    display: flex;
    gap: var(--spacing-md);

    .projects-assets-tab {
      padding: var(--spacing-md);
      margin-bottom: var(--spacing-lg);
      background-color: #fff;
      transition: all 0.3s ease-in-out;
      position: relative;
      border-radius: var(--border-radius-md);
      border: 1px solid var(--border-color);
      box-shadow: var(--shadow-sm);
      color: inherit;
    }
  }

  .add-button {
    display: flex;
    gap: var(--spacing-sm);

    .MuiButton-contained {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      &.MuiButtonBase-root {
        &.MuiButton-containedPrimary {
          background-color: var(--primary-color);
          border-color: var(--primary-color);
          color: #fff;

          &:hover {
            background-color: var(--primary-dark);
          }
        }

        &.MuiButton-containedSecondary {
          background-color: #fff;
          color: var(--text-primary);
          border: 1px solid var(--border-color);
        }
      }
    }
  }
}

// Responsive styles
@media screen and (max-width: 1199px) {
  .details-content {
    flex-direction: column;

    .left-section {
      width: 100%;
      margin-bottom: var(--spacing-md);
    }

    .right-section {
      width: 100%;
    }
  }
}

@media screen and (max-width: 767px) {
  .EducationAndExperience {
    flex-direction: column;
    gap: var(--spacing-md);

    .EducationalDetails {
      width: 100%;
      margin-bottom: var(--spacing-md);
    }

    .ExperienceDetails {
      width: 100%;
    }
  }
}

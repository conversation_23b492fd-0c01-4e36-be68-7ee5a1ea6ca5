"use client";
import React, { useState, useEffect } from "react";
import "./EmployeeDetails.scss";
import { Box, Typography, Button } from "@mui/material";
import { ControlPoint } from "@mui/icons-material";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import EmployeeCard from "@/components/EmployeeDetailCard/EmployeeDetailCard";
import AboutEmpAccordion from "@/components/AboutEmpAccordion/AboutEmpAccordion";
import BankEmpAccordion from "@/components/BankEmpAccordion/BankEmpAccordion";
import FamilyInfoAccordion from "@/components/FamilyInfoAccordion/FamilyInfoAccordion";
import EducationDetailAccordion from "@/components/EducationDetailAccordion/EducationDetailAccordion";
import EmpExperienceAccordion from "@/components/EmpExperienceAccordion/EmpExperienceAccordion";
import EmergencyContact from "@/components/EmergencyContactCard/EmergencyContact";
import ProjectsAssetsTabCard from "@/components/Projects&AssetsTabCard/Projects&AssetsTabCard";
import BankStatutoryDialog from "@/components/BankStatutoryDialog/BankStatutoryDialog";
import useAuthStore from "@/store/authStore";
import { getUserById } from "@/app/services/users.service";
import Loader from "@/components/Loader/Loader";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";
import { MaritalStatus, RelationshipType } from "@/app/types/common";

interface FamilyMember {
  _id: string;
  userId: string;
  name: string;
  relationship: RelationshipType;
  phone: string;
  dob: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface PersonalInfo {
  _id: string;
  userId: string;
  passportNumber: string;
  passportExpiry: string;
  nationality: string;
  religion: string;
  maritalStatus: string;
  employementSpouse: string;
  childrens: number;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface Education {
  _id: string;
  userId: string;
  instituteName: string;
  course: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface Experience {
  _id: string;
  userId: string;
  previousCompany: string;
  designation: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface Bank {
  _id: string;
  userId: string;
  bankName: string;
  accountNumber: string;
  ifsc: string;
  address: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface Asset {
  _id: string;
  userId: string;
  type: string;
  brand: string;
  category: string;
  serialNumber: string;
  cost: string;
  vendor: string;
  assignedBy: string;
  assignedDate: string;
  warrentyTo: string;
  location: string;
  assetImage: string[]; // Changed to match the expected type
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface EmergencyContact {
  _id: string;
  userId: string;
  name: string;
  relationship: string;
  phone1: string;
  phone2: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface BankAndStatutory {
  _id: string;
  userId: string;
  salaryBasis: string;
  salaryCurrency: string;
  paymentType: string;
  pfContribution: string;
  pfNumber: string;
  pfRate: string;
  pfAdditionalRate: string;
  totalRate: string;
  esiContribution: string;
  esiNumber: string;
  esiRate: string;
  esiAdditionalRate: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface User {
  _id: string;
  roles: string[];
  firstName: string;
  lastName: string;
  avatar: string;
  email: string;
  password: string;
  phone: string;
  gender: string; // Added from API
  address: string; // Added from API
  company: string;
  about: string;
  joiningDate: string;
  confirmationDate: string; // Added from API
  previousExp: string; // Added from API
  roId: string; // Added from API
  roName: string; // Added from API
  roDesignation: string; // Added from API
  country: string; // Added from API
  city: string; // Added from API
  state: string; // Added from API
  postalCode: string; // Added from API
  departmentId: string;
  departmentName: string;
  designationId: string;
  designationName: string;
  birthDate: string;
  maritalStatus: string;
  weddingAnniversaryDate: string;
  leaveBalance: {
    Annual: number;
    Medical: number;
    Casual: number;
    Others: number;
  }; // Added from API
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
  education: Education[];
  family: FamilyMember;
  bank: Bank;
  emergencyContact: EmergencyContact[];
  bankAndStatutory: BankAndStatutory;
  assets: Asset[];
  personalInfo: PersonalInfo; // Changed from array to single object
  experience: Experience[];
}

interface Meta {
  version: string;
  forceUpdate: boolean;
  maintenance: boolean;
  hasUpdate: boolean;
}

interface ApiResponse {
  success: boolean;
  statusCode: number;
  message: string;
  user: User;
  meta: Meta;
}

const EmployeeDetails: React.FC = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [userData, setUserData] = useState<ApiResponse | null>(null);
  const { employeeId: authEmployeeId } = useAuthStore();
  const searchParams = useSearchParams();
  const employeeIdFromQuery = searchParams.get("id");

  const effectiveEmployeeId = employeeIdFromQuery || authEmployeeId;

  // Restrict access to Employee roles only (Admin, SuperAdmin, Manager, HR, Employee)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const fetchUserData = async () => {
    if (effectiveEmployeeId) {
      try {
        const data = (await getUserById(effectiveEmployeeId)) as ApiResponse;
        setUserData(data);
      } catch (error) {
        console.error("Failed to fetch user data:", error);
        setUserData(null);
      }
    } else {
      console.warn("No employee ID available to fetch user data.");
    }
  };

  useEffect(() => {
    fetchUserData();
  }, [effectiveEmployeeId]);

  if (!userData) {
    return <Loader loading={true} />;
  }

  return (
    <Box
      className="details-container"
      sx={{ display: "flex", flexDirection: "column" }}
    >
      <Box
        className="details-header"
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "20px 0px",
          borderBottom: "1px solid #E9EDF4",
        }}
      >
        <Typography variant="h6">
          <Link href="/employees/employeesList">← Employees Details</Link>
        </Typography>
        <Box>
          <Box
            className="add-button"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => setOpenDialog(true)}
            >
              <ControlPoint sx={{ fontSize: "14px" }} />
              Bank & Statutory
            </Button>
          </Box>
        </Box>
      </Box>

      <Box className="details-content">
        <Box className="left-section" sx={{ width: "35%" }}>
          <EmployeeCard employeeId={effectiveEmployeeId} />
          <EmergencyContact employeeId={effectiveEmployeeId} />
        </Box>
        <Box className="right-section" sx={{ width: "70%" }}>
          <AboutEmpAccordion userData={userData} />
          <BankEmpAccordion
            userData={{
              ...userData,
              user: { ...userData.user, bank: [userData.user.bank] },
            }}
            employeeId={effectiveEmployeeId}
          />
          <FamilyInfoAccordion
            userData={{
              ...userData,
              user: {
                ...userData.user,
                maritalStatus: userData.user.maritalStatus as MaritalStatus,
                family: [userData.user.family],
              },
            }}
            employeeId={effectiveEmployeeId}
          />
          <Box
            className="EducationAndExperience"
            sx={{ display: "flex", alignItems: "flex-start", gap: "24px" }}
          >
            <Box
              className="EducationalDetails"
              sx={{ flex: "1", minWidth: "0" }}
            >
              <EducationDetailAccordion
                userData={userData}
                employeeId={effectiveEmployeeId}
                refreshUserData={fetchUserData}
              />
            </Box>
            <Box
              className="ExperienceDetails"
              sx={{ flex: "1", minWidth: "0" }}
            >
              <EmpExperienceAccordion
                userData={userData}
                employeeId={effectiveEmployeeId}
                refreshUserData={fetchUserData}
              />
            </Box>
          </Box>
          <Box className="projects-assets-tab">
            <ProjectsAssetsTabCard
              userData={userData}
              refreshUserData={fetchUserData}
            />
          </Box>
        </Box>
      </Box>
      <BankStatutoryDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        userId={effectiveEmployeeId || ""}
        bankAndStatutory={userData.user.bankAndStatutory} // Updated to pass single object
      />
    </Box>
  );
};

export default EmployeeDetails;

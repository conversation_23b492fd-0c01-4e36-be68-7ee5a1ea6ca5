"use client";
import "./AddPolicyDialog.scss";
import type React from "react";
import { useState, useEffect } from "react";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  IconButton,
  Autocomplete,
} from "@mui/material";
import { Close, CloudUpload } from "@mui/icons-material";
import { getPolicyById } from "@/app/services/policies.service";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import useUploadMedia from "@/app/hooks/useUploadMedia";
import Loader from "@/components/Loader/Loader";

interface Department {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface AddPolicyDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (formData: FormData) => Promise<void>;
  departments: string[];
  fullDepartments: Department[];
  departmentsLoading: boolean;
  departmentsError: string | null;
  isEditMode: boolean;
  policyId: string | null;
}

interface PolicyFormValues {
  policyName: string;
  implementationDate: string;
  department: string;
  description: string;
  policyDocument: string;
}

const validationSchema = Yup.object({
  policyName: Yup.string().required("Policy name is required"),
  implementationDate: Yup.date()
    .required("Implementation date is required")
    .min(new Date(), "Implementation date cannot be in the past")
    .typeError("Please enter a valid date"),
  department: Yup.string().required("Department is required"),
});

const initialValues: PolicyFormValues = {
  policyName: "",
  implementationDate: "",
  department: "",
  description: "",
  policyDocument: "",
};

const AddPolicyDialog: React.FC<AddPolicyDialogProps> = ({
  open,
  onClose,
  onSubmit,
  departments,
  fullDepartments,
  departmentsLoading,
  departmentsError,
  isEditMode,
  policyId,
}) => {
  const [policyFile, setPolicyFile] = useState<File | null>(null);
  const [initialFormValues, setInitialFormValues] =
    useState<PolicyFormValues>(initialValues);
  const [loadingPolicy, setLoadingPolicy] = useState(false);
  const [uploadingFile, setUploadingFile] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const { uploadMedia } = useUploadMedia();

  // Fetch policy details when in edit mode and policyId changes
  useEffect(() => {
    if (isEditMode && policyId) {
      const fetchPolicy = async () => {
        try {
          setLoadingPolicy(true);
          const response = await getPolicyById(policyId);
          console.log("Fetched policy data:", response);
          const policyData = response.policy;

          // Extract department name from departmentId object if it exists
          let departmentName = "";
          if (policyData.departmentId) {
            if (
              typeof policyData.departmentId === "object" &&
              policyData.departmentId.departmentName
            ) {
              // If departmentId is an object with departmentName property
              departmentName = policyData.departmentId.departmentName;
            } else if (typeof policyData.departmentId === "string") {
              // If departmentId is just an ID, try to find the department in fullDepartments
              const department = fullDepartments.find(
                (dept) => dept._id === policyData.departmentId
              );
              if (department) {
                departmentName = department.departmentName;
              }
            }
          }

          console.log("Extracted department name:", departmentName);

          setInitialFormValues({
            policyName: policyData.policyName || "",
            implementationDate: policyData.implementationDate
              ? new Date(policyData.implementationDate)
                  .toISOString()
                  .split("T")[0]
              : "",
            department: departmentName,
            description: policyData.description || "",
            policyDocument: policyData.policyDocument || "",
          });

          // Set the preview URL for the existing policy document
          setPreviewUrl(policyData.policyDocument || null);
        } catch (error) {
          console.error("Error fetching policy:", error);
          // toast.error("Failed to load policy details.");
        } finally {
          setLoadingPolicy(false);
        }
      };
      fetchPolicy();
    } else {
      setInitialFormValues(initialValues);
      setPreviewUrl(null);
    }
  }, [isEditMode, policyId, fullDepartments]);

  // Handle file upload using useUploadMedia hook
  const handleFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    setFieldValue: (field: string, value: File | string) => void
  ) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setPolicyFile(file);
      setFieldValue("policyFile", file);

      try {
        setUploadingFile(true);
        const { preview } = await uploadMedia(
          file,
          "temp",
          1,
          setUploadingFile,
          file.name
        );
        setPreviewUrl(preview);
        setFieldValue("policyDocument", preview);
        toast.success("File uploaded successfully!");
      } catch (error) {
        console.error("File upload failed:", error);
        // toast.error("Failed to upload file. Please try again.");
      }
    }
  };

  const handleSubmit = async (values: PolicyFormValues) => {
    try {
      const selectedDepartment = fullDepartments.find(
        (dept) => dept.departmentName === values.department
      );
      if (!selectedDepartment) {
        throw new Error("Selected department not found");
      }

      const formData = new FormData();
      formData.append("policyName", values.policyName);
      formData.append("implementationDate", values.implementationDate);
      formData.append("departmentId", selectedDepartment._id);
      formData.append("description", values.description || "");

      // Use the previewUrl if available (new upload or existing in edit mode)
      if (previewUrl) {
        formData.append("policyDocument", previewUrl);
      } else if (!isEditMode) {
        formData.append("policyDocument", "");
      }

      await onSubmit(formData);
      onClose();
      setPolicyFile(null);
      setPreviewUrl(null);
    } catch (error) {
      console.error(
        `Error ${isEditMode ? "updating" : "adding"} policy:`,
        error
      );
      // toast.error(
      //   `Failed to ${isEditMode ? "update" : "add"} policy. Please try again.`
      // );
    }
  };

  return (
    <>
      <ToastContainer />
      <Dialog
        open={open}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            onClose();
          }
        }}
        disableEscapeKeyDown
        sx={{
          "& .MuiDialog-paper": {
            maxWidth: "800px",
            width: "100%",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "16px !important",
          }}
        >
          <Typography
            sx={{
              fontSize: "20px !important",
              fontWeight: 600,
              color: "#202c4b",
            }}
          >
            {isEditMode ? "Edit Policy" : "Add Policy"}
          </Typography>
          <IconButton
            onClick={onClose}
            aria-label="close"
            sx={{
              backgroundColor: "#6b7280",
              backgroundImage: "none",
              borderRadius: "50%",
              color: "#fff",
              height: "20px",
              width: "20px",
              margin: 0,
              padding: 0,
              "&:hover": {
                backgroundColor: "#D93D3D",
              },
              "& .MuiSvgIcon-root": {
                fontSize: "14px",
              },
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>

        {loadingPolicy ? (
          <DialogContent>
            <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
              <Loader loading={loadingPolicy} />
            </Box>
          </DialogContent>
        ) : (
          <Formik
            initialValues={initialFormValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            enableReinitialize
          >
            {({ errors, touched, setFieldValue, values }) => (
              <Form>
                <DialogContent sx={{ padding: "1rem" }}>
                  <Box sx={{ mt: 1 }}>
                    {/* Policy Name */}
                    <Box
                      sx={{
                        mb: 3,
                        "& label": { marginTop: "8px", display: "block" },
                      }}
                    >
                      <label>
                        Policy Name <span className="required">*</span>
                      </label>
                      <Field
                        as={TextField}
                        fullWidth
                        name="policyName"
                        variant="outlined"
                        error={touched.policyName && Boolean(errors.policyName)}
                        helperText={touched.policyName && errors.policyName}
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            height: "38px",
                            fontSize: "14px",
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          },
                          "& .MuiInputBase-input": {
                            padding: "8px 14px",
                          },
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#ccc",
                          },
                        }}
                      />
                    </Box>

                    {/* Implementation Date */}
                    <Box
                      sx={{
                        mb: 2,
                        "& label": { marginTop: "8px", display: "block" },
                      }}
                    >
                      <label>
                        Implementation Date <span className="required">*</span>
                      </label>
                      <Field
                        as={TextField}
                        fullWidth
                        name="implementationDate"
                        type="date"
                        variant="outlined"
                        InputLabelProps={{ shrink: true }}
                        error={
                          touched.implementationDate &&
                          Boolean(errors.implementationDate)
                        }
                        helperText={
                          touched.implementationDate &&
                          errors.implementationDate
                        }
                        inputProps={{
                          min: new Date().toISOString().split("T")[0], // Prevents selecting past dates
                        }}
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            height: "38px",
                            fontSize: "14px",
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          },
                          "& .MuiInputBase-input": {
                            padding: "8px 14px",
                          },
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#ccc",
                          },
                        }}
                      />
                    </Box>

                    {/* Department Dropdown */}
                    <Box sx={{ mb: 1 }}>
                      <label>
                        Department <span className="required">*</span>
                      </label>
                    </Box>
                    {departmentsLoading ? (
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "center",
                          mb: 2,
                        }}
                      >
                        <Loader loading={departmentsLoading} />
                      </Box>
                    ) : departmentsError ? (
                      <Typography color="error" sx={{ mb: 2 }}>
                        {departmentsError}
                      </Typography>
                    ) : (
                      <Autocomplete
                        disablePortal
                        id="department-autocomplete"
                        options={departments}
                        sx={{
                          width: "100%",
                          mb: 2,
                          "& .MuiOutlinedInput-root": {
                            height: "38px",
                            fontSize: "14px",
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          },
                          "& .MuiInputBase-input": {
                            padding: "8px 14px !important",
                          },
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#ccc",
                          },
                        }}
                        ListboxProps={{
                          style: {
                            maxHeight: "200px",
                          },
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            error={
                              touched.department && Boolean(errors.department)
                            }
                            helperText={touched.department && errors.department}
                          />
                        )}
                        onChange={(event, value) =>
                          setFieldValue("department", value)
                        }
                        value={values.department || ""}
                      />
                    )}

                    {/* Description field */}
                    <Box
                      sx={{
                        mb: 3,
                        "& label": { marginTop: "8px", display: "block" },
                      }}
                    >
                      <label>Description</label>
                      <Field
                        as={TextField}
                        fullWidth
                        name="description"
                        multiline
                        rows={2}
                        variant="outlined"
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            minHeight: "56px",
                            fontSize: "14px",
                            padding: 0,
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          },
                          "& .MuiInputBase-root": {
                            height: "auto",
                            alignItems: "flex-start",
                            padding: 0,
                          },
                          "& .MuiInputBase-inputMultiline": {
                            padding: "8px 14px",
                          },
                          "& textarea": {
                            height: "auto !important",
                          },
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#ccc",
                          },
                          // Target the specific class combination
                          "& .MuiInputBase-root.MuiOutlinedInput-root.MuiInputBase-multiline":
                            {
                              padding: 0,
                            },
                        }}
                      />
                    </Box>

                    {/* File Upload */}
                    <Box sx={{ mb: 3 }}>
                      <label>
                        Upload Policy <span className="required">*</span>
                      </label>
                      <Box
                        sx={{
                          border: "2px dashed #ccc",
                          borderRadius: "8px",
                          padding: "20px",
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          gap: "10px",
                          position: "relative",
                        }}
                      >
                        {uploadingFile && <Loader loading={uploadingFile} />}
                        {!uploadingFile && (
                          <>
                            <Box
                              sx={{
                                width: "50px",
                                height: "50px",
                                borderRadius: "50%",
                                backgroundColor: "#e3f2fd",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                mb: 1,
                              }}
                            >
                              <CloudUpload sx={{ color: "#F26522" }} />
                            </Box>
                            <Typography variant="body2" color="textSecondary">
                              Drag and drop your files
                            </Typography>
                            <Button
                              variant="contained"
                              component="label"
                              sx={{
                                backgroundColor:
                                  "rgb(226.95, 82.1538461538, 13.35)",
                                color: "#fff !important",
                                border:
                                  "1px solid rgb(226.95, 82.1538461538, 13.35)",
                                padding: "0.25rem 0.5rem",
                                fontSize: "0.75rem",
                                borderRadius: "5px",
                                fontWeight: "600",
                                textTransform: "none",
                              }}
                            >
                              <span>Upload</span>
                              <input
                                type="file"
                                hidden
                                onChange={(e) =>
                                  handleFileChange(e, setFieldValue)
                                }
                              />
                            </Button>
                            {policyFile && (
                              <Typography variant="body2" sx={{ mt: 1 }}>
                                Selected file: {policyFile.name}
                              </Typography>
                            )}
                            {previewUrl && (
                              <Typography variant="body2" sx={{ mt: 1 }}>
                                Preview URL:{" "}
                                <a
                                  href={previewUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  View File
                                </a>
                              </Typography>
                            )}
                          </>
                        )}
                      </Box>
                    </Box>
                  </Box>

                  <DialogActions
                    sx={{
                      padding: "0px !important",
                      paddingTop: "1rem !important",
                    }}
                  >
                    <Button
                      onClick={onClose}
                      variant="outlined"
                      sx={{
                        textTransform: "none",
                        borderColor: "#ccc",
                        color: "#000",
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      sx={{
                        backgroundColor: "#F26522",
                        color: "#FFF",
                        textTransform: "none",
                        "&:hover": {
                          backgroundColor: "#d55a1d",
                        },
                      }}
                      disabled={uploadingFile}
                    >
                      {isEditMode ? "Update Policy" : "Add Policy"}
                    </Button>
                  </DialogActions>
                </DialogContent>
              </Form>
            )}
          </Formik>
        )}
      </Dialog>
    </>
  );
};

export default AddPolicyDialog;

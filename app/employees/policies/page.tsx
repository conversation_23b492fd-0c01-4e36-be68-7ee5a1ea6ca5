"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Typography,
  Paper,
} from "@mui/material";
import React, { useState, useEffect, useCallback } from "react";
import "./policies.scss";
import { HomeOutlined, ControlPoint } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import { GridRowsProp, GridColDef } from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { EditNote, Delete } from "@mui/icons-material";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import useFetchPolicySectionData from "@/app/hooks/policy/useFetchPolicySectionData";
import AddPolicyDialog from "./AddPolicyDialog";
import PolicyFilters, {
  getDateR<PERSON><PERSON>,
} from "@/components/policyFilter/PolicyFilters";
import {
  addPolicy,
  updatePolicy,
  deletePolicy,
} from "@/app/services/policies.service";
import { getDepartments } from "@/app/services/department.service";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

interface Department {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

const formatDateForApi = (dateStr: string) => {
  if (!dateStr) return undefined;

  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return undefined;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return undefined;
  }
};

export default function PolicyContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);

  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedPolicyId, setSelectedPolicyId] = useState<string | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [departmentNames, setDepartmentNames] = useState<string[]>([]);
  const [departmentsLoading, setDepartmentsLoading] = useState(true);
  const [departmentsError, setDepartmentsError] = useState<string | null>(null);
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Status");

  const [policyData, total] = useFetchPolicySectionData({
    setIsLoading,
    refresh,
    limit,
    page,
    departmentName:
      selectedDepartment === "Department" ? "" : selectedDepartment,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate: startDate ? formatDateForApi(startDate) : undefined,
    endDate: endDate ? formatDateForApi(endDate) : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  const { roles } = useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const isEmployee = roles.includes("Employee");

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setDepartmentsLoading(true);
        const response = await getDepartments();
        const deptData: Department[] = response.departments.results.filter(
          (dept: Department) => dept.isActive && !dept.isDeleted
        );
        setDepartments(deptData);
        const departmentNames: string[] = deptData.map(
          (dept: Department) => dept.departmentName
        );
        setDepartmentNames(departmentNames);
      } catch (error) {
        setDepartmentsError("Failed to fetch departments");
        console.error("Error fetching departments:", error);
      } finally {
        setDepartmentsLoading(false);
      }
    };

    fetchDepartments();
  }, []);

  const rows: GridRowsProp = policyData
    ? policyData.map((policy, index: number) => ({
        id: policy._id || index + 1,
        policyName: policy.policyName || "-",
        department:
          policy.departmentId && typeof policy.departmentId === "object"
            ? (policy.departmentId as { departmentName: string }).departmentName
            : "-",
        description: policy.description || "-",
        implementationDate: policy.implementationDate
          ? new Date(policy.implementationDate).toLocaleDateString("en-GB", {
              day: "numeric",
              month: "long",
              year: "numeric",
            })
          : "-",
        createdDate: policy.createdAt
          ? new Date(policy.createdAt).toLocaleDateString("en-GB", {
              day: "numeric",
              month: "long",
              year: "numeric",
            })
          : "-",
        createdDateRaw: policy.createdAt
          ? new Date(policy.createdAt)
          : new Date(),
      }))
    : [];

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [policyToDelete, setPolicyToDelete] = useState<string | number | null>(
    null
  );

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "HR", href: "" },
    { label: "Policies" },
  ];

  interface PolicyRow {
    id: string | number;
    policyName: string;
    department: string;
    description: string;
    implementationDate: string;
    createdDate: string;
    createdDateRaw: Date;
  }

  interface ActionParams {
    row: PolicyRow;
  }

  const columns: GridColDef<PolicyRow>[] = [
    { field: "policyName", headerName: "Name", editable: false, flex: 1 },
    { field: "department", headerName: "Department", editable: false, flex: 1 },
    {
      field: "description",
      headerName: "Description",
      editable: false,
      flex: 1,
    },
    {
      field: "implementationDate",
      headerName: "Implementation Date",
      editable: false,
      flex: 1,
    },
    {
      field: "createdDate",
      headerName: "Created Date",
      editable: false,
      flex: 1,
    },
    ...(!isEmployee
      ? [
          {
            field: "actions",
            headerName: "",
            editable: false,
            disableExport: true,
            flex: 0.5,
            renderCell: (params: ActionParams) => (
              <Box sx={{ display: "flex", gap: 1 }}>
                <IconButton
                  sx={{ color: "#6B7280" }}
                  onClick={() => handleEditClick(params.row.id)}
                >
                  <EditNote sx={{ fontSize: "16px" }} />
                </IconButton>
                <IconButton
                  sx={{ color: "#6B7280" }}
                  onClick={() => handleDeleteClick(params.row.id)}
                >
                  <Delete sx={{ fontSize: "16px" }} />
                </IconButton>
              </Box>
            ),
          },
        ]
      : []),
  ];

  const handleEditClick = async (policyId: string | number) => {
    if (isEmployee) return;
    try {
      setSelectedPolicyId(policyId as string);
      setIsEditMode(true);
      setOpenModal(true);
    } catch (error) {
      console.error("Error preparing edit:", error);
    }
  };

  const handleDeleteClick = (policyId: string | number) => {
    if (isEmployee) return;
    setPolicyToDelete(policyId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (policyToDelete !== null) {
      try {
        await deletePolicy(policyToDelete as string);
        setDeleteDialogOpen(false);
        setPolicyToDelete(null);
        setRefresh(!refresh);
        toast.success("Policy deleted successfully!", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      } catch (error) {
        console.error("Failed to delete policy:", error);
        // toast.error("Failed to delete policy. Please try again.", {
        //   position: "top-right",
        //   autoClose: 3000,
        //   hideProgressBar: false,
        //   closeOnClick: true,
        //   pauseOnHover: true,
        //   draggable: true,
        // });
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setPolicyToDelete(null);
  };

  const handleDialogSubmit = async (formData: FormData) => {
    if (isEmployee) return;
    try {
      if (isEditMode && selectedPolicyId) {
        const policyBody = {
          policyName: formData.get("policyName") as string,
          implementationDate: formData.get("implementationDate") as string,
          departmentId: formData.get("departmentId") as string,
          description: formData.get("description") as string,
          policyDocument: formData.get("policyDocument") as string,
        };
        await updatePolicy(selectedPolicyId, policyBody);
        toast.success("Policy updated successfully!", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      } else {
        const policyBody = {
          policyName: formData.get("policyName") as string,
          implementationDate: formData.get("implementationDate") as string,
          departmentId: formData.get("departmentId") as string,
          description: formData.get("description") as string,
          policyDocument: formData.get("policyDocument") as string,
        };
        await addPolicy(policyBody);
        toast.success("Policy added successfully!", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }
      setRefresh(!refresh);
    } catch (error) {
      console.error(
        `Failed to ${isEditMode ? "update" : "add"} policy:`,
        error
      );
      toast.error(
        `Failed to ${isEditMode ? "update" : "add"} policy. Please try again.`,
        {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        }
      );
      throw error;
    }
  };

  const handleDialogClose = () => {
    setOpenModal(false);
    setIsEditMode(false);
    setSelectedPolicyId(null);
  };

  return (
    <Box className="policy-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="policy-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Policies</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          {!isEmployee && (
            <Box
              className="add-policy"
              sx={{ display: "flex", gap: 1, alignItems: "center" }}
            >
              <Button
                variant="contained"
                sx={{
                  display: "flex",
                  gap: "8px",
                  backgroundColor: "#F26522",
                  borderColor: "#F26522",
                  color: "#FFF",
                  fontWeight: 400,
                  fontSize: "14px",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                onClick={() => {
                  setIsEditMode(false);
                  setOpenModal(true);
                }}
              >
                <ControlPoint sx={{ fontSize: "16px" }} />
                Add Policy
              </Button>
            </Box>
          )}
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 550,
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Policies List</Typography>
              <PolicyFilters
                departments={departmentNames}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showStatusFilter={false}
                showDepartmentFilter={true}
                showSortByFilter={true}
                showDateRangeFilter={true}
                showDesignationFilter={false}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns as readonly GridColDef[]}
              rowCount={total}
              paginationMode="server"
              initialState={{ pagination: { paginationModel: { pageSize } } }}
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                handlePageChange(model.page);
                handlePageSizeChange(model.pageSize);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this policy? This action cannot be undone."
      />

      <AddPolicyDialog
        open={openModal}
        onClose={handleDialogClose}
        onSubmit={handleDialogSubmit}
        departments={departmentNames}
        fullDepartments={departments}
        departmentsLoading={departmentsLoading}
        departmentsError={departmentsError}
        isEditMode={isEditMode}
        policyId={selectedPolicyId}
      />
    </Box>
  );
}

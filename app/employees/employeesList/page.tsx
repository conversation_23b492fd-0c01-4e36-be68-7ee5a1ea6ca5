"use client";

import { Box, Divider, Typography, Paper, TextField } from "@mui/material";
import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  Suspense,
} from "react";
import { useRouter } from "next/navigation";
import ViewToggle from "@/components/ViewToggleButton/ViewToggle";
import "./employeesList.scss";

import { HomeOutlined, ControlPoint, Visibility } from "@mui/icons-material";
import { IconButton, Button } from "@mui/material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  useGridApiRef,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { EditNote, Delete, PeopleOutline } from "@mui/icons-material";
import AddEmployeeForm from "@/components/AddEmployeeForm/AddEmployeeForm";
// import EditEmployeeDialog from "@/components/EditEmployeeDialog/EditEmployeeDialog";
import CustomHeaderCard from "@/components/CustomHeaderCard/CustomHeaderCard";
import PolicyFilters, {
} from "@/components/policyFilter/PolicyFilters";
import Link from "next/link";
import { deleteEmployee, getUserById, editUser } from "@/app/services/users.service";
import {
  getDepartment,
  getDepartments,
} from "@/app/services/department.service";
import useFetchUsersData from "@/app/hooks/users/useFetchUsersData";
import Loader from "@/components/Loader/Loader";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import { toast } from "react-toastify";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { EmploymentType, Gender, WorkMode } from "@/app/types/EmployeeForm";
import FileUploadDownload from "@/components/FileUploadDownload/FileUploadDownload";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

interface EmployeesList {
  id: number | string;
  empId: string;
  name: string;
  email: string;
  phone: string;
  designationName: string;
  joiningDate: string;
  isActive: string;
  // col7: string;
}

interface EmployeeFormData {
  roles: string[];
  _id: string;
  firstName: string;
  lastName: string;
  employeeId?: string;
  joiningDate: string;
  email: string;
  password: string;
  confirmPassword?: string;
  phone: string;
  pan: string;
  company: string;
  departmentId: string;
  designationId: string;
  about: string;
  avatar: string | undefined;
  birthDate?: string;
  address?: string;
  employmentType?: EmploymentType;
  workMode?: WorkMode;
  gender?: Gender;
  maritalStatus?: string;
  onProbation?: boolean;
  weddingAnniversaryDate?: string;
}

interface Department {
  _id: string;
  departmentName: string;
}

interface UserRecord {
  _id: string;
  employeeId?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  departmentName?: string;
  departmentId?:
    | {
        _id: string;
        departmentName: string;
        isActive: boolean;
        isDeleted: boolean;
      }
    | string;
  designationId?:
    | {
        _id: string;
        designationName: string;
        isActive: boolean;
        isDeleted: boolean;
      }
    | string;
  email?: string;
  phone?: string;
  pan?: string;
  designationName?: string;
  joiningDate?: string;
  isActive?: boolean;
}

// Custom debounce hook
const useDebounce = (value: string, delay: number = 300) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
};


const formatDateForApi = (dateStr: string) => {
  if (!dateStr) return undefined;

  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return undefined;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return undefined;
  }
};

function EmployeesListContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  const currentDate = new Date();
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] = useState<string>("Date Range");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");

  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  useEffect(() => {
    let isMounted = true;
    const fetchDepartments = async () => {
      setIsLoading(true);
      try {
        const response = await getDepartment(
          undefined,
          undefined,
          "",
          "asc",
          undefined,
          undefined,
          "true"
        );
        if (isMounted && response?.departments.results) {
          const sortedDepartments = [...response.departments.results].sort(
            (a, b) => a.departmentName.localeCompare(b.departmentName)
          );
          setDepartments(sortedDepartments);
        }
      } catch (error) {
        console.error("Failed to fetch departments:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchDepartments();
    return () => {
      isMounted = false;
    };
  }, []);

  const [
    userData,
    totalEmployeesCount,
    activeEmployeesCount,
    inactiveEmployeesCount,
    newJoiners,
  ] = useFetchUsersData({
    setIsLoading,
    refresh,
    limit,
    page,
    departmentName:
      selectedDepartment === "Department" ? "" : selectedDepartment,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate: startDate ? formatDateForApi(startDate) : undefined,
    endDate: endDate ? formatDateForApi(endDate) : undefined,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });

  const rows: GridRowsProp = userData
    ? userData.map((user: UserRecord, index: number) => ({
        id: user._id || index + 1,
        empId: user.employeeId,
        name: `${user.firstName} ${user.lastName}`,
        avatar: user.avatar,
        department:
          user.departmentId && typeof user.departmentId === "object"
            ? user.departmentId.departmentName
            : user.departmentName || "-",
        email: user.email,
        phone: user.phone,
        designationName:
          user.designationId && typeof user.designationId === "object"
            ? user.designationId.designationName
            : user.designationName || "-",
        joiningDate: user.joiningDate
          ? new Date(user.joiningDate).toLocaleDateString()
          : "-",
        isActive: user.isActive ? "Active" : "Inactive",
      }))
    : [];

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Employee Management", href: "" },
    { label: "Employees" },
  ];

  const [openModal, setOpenModal] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const [deletingEmployeesList, setDeletingEmployeesList] =
    useState<EmployeesList | null>(null);
  const [editingEmployee, setEditingEmployee] =
    useState<EmployeeFormData | null>(null);

  const apiRef = useGridApiRef();

  const refreshList = () => {
    setRefresh(!refresh);
  };

  const router = useRouter();

  const handleEditClick = async (row: EmployeesList) => {
    try {
      const response = await getUserById(row.id.toString());
      if (response.success) {
        const userData = response.user;
        console.log("User data from API:", userData); // Debug log
        const mappedEmployee: EmployeeFormData = {
          roles: userData.roles || ["Employee"],
          _id: userData._id,
          firstName: userData.firstName || "",
          lastName: userData.lastName || "",
          employeeId: userData.employeeId || "",
          email: userData.email || "",
          phone: userData.phone || "",
          pan: userData.pan || "",
          designationId: userData.designationId || "",
          joiningDate: userData.joiningDate
            ? new Date(userData.joiningDate).toISOString().split("T")[0]
            : "",
          password: "",
          confirmPassword: "",
          company: userData.company || "",
          departmentId: userData.departmentId || "",
          birthDate: userData.birthDate
            ? new Date(userData.birthDate).toISOString().split("T")[0]
            : "",
          about: userData.about || "",
          avatar: userData.avatar || undefined,
          address: userData.address || "", // Add the address field
          employmentType: userData.employmentType || EmploymentType.FullTime,
          workMode: userData.workMode || WorkMode.Office,
          gender: userData.gender || Gender.Male,
          maritalStatus: userData.maritalStatus || "",
          onProbation: userData.onProbation || false,
          weddingAnniversaryDate: userData.weddingAnniversaryDate
            ? new Date(userData.weddingAnniversaryDate)
                .toISOString()
                .split("T")[0]
            : "",
        };
        console.log("Mapped employee data:", mappedEmployee); // Debug log
        setEditingEmployee(mappedEmployee);
        setEditModal(true);
      } else {
        throw new Error("Failed to fetch employee data");
      }
    } catch (error) {
      console.error("Error fetching employee data:", error);
    }
  };

  const handleDeleteClick = (row: EmployeesList) => {
    setDeletingEmployeesList(row);
    setDeleteModal(true);
  };

  const handleDelete = async () => {
    if (deletingEmployeesList) {
      try {
        await deleteEmployee(deletingEmployeesList.id.toString());
        // toast.success("Employee deleted successfully");
        setRefresh(!refresh);
        setDeleteModal(false);
      } catch (error) {
        console.error("Error deleting employee:", error);
        // toast.error("Failed to delete employee. Please try again.");
        // Keep the modal open when there's an error
        // setDeleteModal(false); - removed this
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteModal(false);
    setDeletingEmployeesList(null);
  };

  const columns: GridColDef[] = [
    {
      field: "empId",
      headerName: "Emp ID",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#111827" }}>
          {params.row.empId}
        </Typography>
      ),
    },
    {
      field: "name",
      headerName: "Name",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {/* <Avatar
            src={params.row.avatar}
            alt={params.row.name}
            sx={{ width: 32, height: 32 }}
          /> */}
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            <Typography sx={{ fontSize: "14px", color: "#111827" }}>
              <Link href="#">{params.row.name}</Link>
            </Typography>
            {/* <Typography component="span" sx={{ fontSize: "0.75rem" }}>
              {params.row.department}
            </Typography> */}
          </Box>
        </Box>
      ),
    },
    {
      field: "department",
      headerName: "Department",
      editable: false,
      flex: 1.5,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.department}
        </Typography>
      ),
    },
    {
      field: "email",
      headerName: "Email",
      editable: false,
      flex: 1.5,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.email}
        </Typography>
      ),
    },
    {
      field: "phone",
      headerName: "Phone",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.phone}
        </Typography>
      ),
    },
    {
      field: "designationName",
      headerName: "Designation",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.designationName}
        </Typography>
      ),
    },
    {
      field: "joiningDate",
      headerName: "Joining Date",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.joiningDate}
        </Typography>
      ),
    },
    {
      field: "isActive",
      headerName: "Status",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        // <Box
        //   sx={{
        //     display: "flex",
        //     alignItems: "center",
        //     gap: "3px",
        //     backgroundColor: params.value === "Active" ? "#03C95A" : "#E70D0D",
        //     color: "#fff",
        //     borderRadius: "5px",
        //     textAlign: "center",

        //     minWidth: "66px",

        //     justifyContent: "center",
        //     fontSize: "10px",
        //     fontWeight: 500,
        //     padding: "0px 5px",
        //     lineHeight: "18px",
        //   }}
        // >
        //   <Box
        //     sx={{
        //       width: "5px",
        //       height: "5px",
        //       borderRadius: "100%",
        //       backgroundColor: params.value === "Active" ? "#fff" : "#fff",
        //     }}
        //   />

        //   {params.value}
        // </Box>

      <StatusToggle
        isActive={params.row.isActive === "Active"}
        onChange={async (newStatus) => {
          try {
            await editUser(params.row.id.toString(), { isActive: newStatus });
            setRefresh(!refresh);
          } catch (error) {
            console.error("Error updating employee status:", error);
          }
        }}
        title={`Confirm ${params.row.name} Status Change`}
      />
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() =>
              router.push(`/employees/employeesDetails?id=${params.row.id}`)
            }
          >
            <Visibility sx={{ width: "16px", height: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row as EmployeesList)}
          >
            <EditNote sx={{ width: "16px", height: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row as EmployeesList)}
          >
            <Delete sx={{ width: "16px", height: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  // const router = useRouter();

  return (
    <Box className="employee-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="employee-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Employees</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            {/* <FileUploadDownload onUploadSuccess={refreshList} /> */}
            <ViewToggle />
            <Button
              className="add-employee"
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => setOpenModal(true)}
            >
              <ControlPoint sx={{ width: "16px", height: "16px" }} />
              Add Employee
            </Button>
          </Box>
        </Box>

        <Box
          className="header-cards"
          sx={{ display: "flex", width: "100%", gap: "24px" }}
        >
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Total Employees"
            value={totalEmployeesCount || 0}
            percentage="+19.01%"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Active"
            value={activeEmployeesCount || 0}
            percentage="+12.5%"
            iconBgColor="#03C95A"
            badgeBgColor="rgba(242, 101, 34, 0.1)"
            badgeTextColor="#F26522"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Ex Employee"
            value={inactiveEmployeesCount || 0}
            percentage="+19.01%"
            iconBgColor="#E70D0D"
            badgeBgColor="rgba(242, 101, 34, 0.1)"
            badgeTextColor="#F26522"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="New Joiners"
            value={newJoiners || 0}
            percentage="+12.5%"
            iconBgColor="#1B84FF"
            badgeBgColor="rgba(59, 112, 128, 0.1)"
            badgeTextColor="#3B7080"
            showIcon={true}
          />
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 400,
              maxHeight: "calc(100vh - 200px)",
              position: "relative", // For Loader positioning
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Employee List</Typography>
              <PolicyFilters
                departments={departments.map((dept) => dept.departmentName)}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={true}
                showStatusFilter={true}
                showSortByFilter={true}
                showDesignationFilter={false}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => {}}
                selectedLeaveType={""}
                setSelectedLeaveType={() => {}}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>

            <Divider />
            <CustomDataGrid
              apiRef={apiRef}
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              // checkboxSelection
              rows={rows}
              columns={columns}
              rowCount={totalEmployeesCount}
              paginationMode="server"
              initialState={{
                pagination: { paginationModel: { pageSize: pageSize } },
              }}
              paginationModel={{ page: page - 1, pageSize: pageSize }}
              onPaginationModelChange={(model) => {
                if (model.pageSize !== pageSize) {
                  // If page size changed
                  setPageSize(model.pageSize);
                  setLimit(model.pageSize);
                  setPage(1); // Reset to first page
                } else {
                  // If only page changed
                  setPage(model.page + 1);
                }
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      <AddEmployeeForm
        open={openModal}
        onClose={() => setOpenModal(false)}
        refreshList={refreshList}
      />
      {editingEmployee && (
        <AddEmployeeForm
          open={editModal}
          onClose={() => {
            setEditModal(false);
            setEditingEmployee(null); // Clear the editing employee data
          }}
          refreshList={refreshList}
          isEdit
          employeeData={{
            ...editingEmployee,
            // Ensure all required fields are present
            about: editingEmployee.about || "",
            // Make sure address is included
            address: editingEmployee.address || "",
          }}
        />
      )}
      <DeleteConfirmationDialog
        open={deleteModal}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleDelete}
        title="Confirm Delete"
        message={`Are you sure you want to delete the employee ${deletingEmployeesList?.empId}? This action cannot be undone.`}
      />
    </Box>
  );
}

function EmployeesListPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <EmployeesListContent />
    </Suspense>
  );
}

export default EmployeesListPage;
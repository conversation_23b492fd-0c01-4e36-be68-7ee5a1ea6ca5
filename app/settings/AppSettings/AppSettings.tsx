import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
  <PERSON>,
  Di<PERSON>r,
  Avatar,
} from "@mui/material";
import { updateCompany, getCompany } from "@/app/services/setting.service";
import useAuthStore from "@/store/authStore";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import AddIcon from "@mui/icons-material/Add";
import CancelIcon from "@mui/icons-material/Cancel";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import * as Yup from "yup";
import useUploadMedia from "@/app/hooks/useUploadMedia";
import Loader from "@/components/Loader/Loader";

interface CompanyData {
  companyName: string;
  allowedDomains?: string[];
  logoUrl?: string;
  companyAddress: string;
  companyEmail: string;
  companyPhone: string;
}

const initialValues = {
  companyName: "",
  allowedDomains: [] as string[],
  newDomain: "", // For the input field
  logoUrl: "",
  companyAddress: "",
  companyEmail: "",
  companyPhone: "",
};

const validationSchema = Yup.object({
  companyName: Yup.string().required("Company name is required"),
  newDomain: Yup.string().matches(
    /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/,
    "Please enter a valid domain (e.g., example.com)"
  ),
  companyLogo: Yup.string(),
  companyAddress: Yup.string().required("Company Address is required"),
  companyEmail: Yup.string()
    .email("Invalid email format")
    .required("Company Email is required"),
  companyPhone: Yup.string()
    .matches(/^\d{10}$/, "Please enter a 10-digit phone number")
    .required("Company Phone Number is required"),
});

export default function AppSettings() {
  const [isLoading, setIsLoading] = useState(false);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const { employeeId } = useAuthStore();
  const { uploadMedia } = useUploadMedia();

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
        console.log("employee Id", employeeId);
        console.log("form values", values);
      if (!employeeId) return;


      setIsLoading(true);
      try {
        // Ensure companyLogo is included in the request
        const companyData: CompanyData = {
          companyName: values.companyName,
          allowedDomains: values.allowedDomains,
          logoUrl: values.logoUrl || "", // Ensure it's never undefined
          companyAddress: values.companyAddress,
          companyEmail: values.companyEmail,
          companyPhone: values.companyPhone,
        };

        console.log("Submitting company data:", companyData);
        const response = await updateCompany(companyData);
        console.log("Update company response:", response);
        toast.success("Company settings updated successfully!");
      } catch (error) {
        console.error("Failed to update company settings:", error);
        toast.error("Failed to update company settings");
      } finally {
        setIsLoading(false);
      }
    },
  });

  // Handle file upload to S3
  const handleLogoUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.currentTarget.files;
    if (files && files.length > 0) {
      const file = files[0];
      try {
        setUploadingLogo(true);
        const { preview } = await uploadMedia(
          file,
          "company",
          1,
          setUploadingLogo,
          file.name
        );
        formik.setFieldValue("logoUrl", preview);
        toast.success("Logo uploaded successfully!");
      } catch (error) {
        console.error("Error uploading logo:", error);
        // toast.error("Failed to upload logo. Please try again.");
      } finally {
        setUploadingLogo(false);
      }
    }
  };

  // Handle adding a new domain
  const handleAddDomain = () => {
    if (!formik.values.newDomain || formik.errors.newDomain) return;

    const newDomains = [
      ...formik.values.allowedDomains,
      formik.values.newDomain,
    ];
    formik.setFieldValue("allowedDomains", newDomains);
    formik.setFieldValue("newDomain", "");
  };

  // Handle removing a domain
  const handleRemoveDomain = (domainToRemove: string) => {
    const newDomains = formik.values.allowedDomains.filter(
      (domain) => domain !== domainToRemove
    );
    formik.setFieldValue("allowedDomains", newDomains);
  };

  // Fetch company data on component mount
  useEffect(() => {
    const fetchCompanyData = async () => {
      try {
        const response = await getCompany();
        console.log("Company data from API:", response);

        if (response && response.company) {
          // Set form values directly from database
          formik.setValues({
            companyName: response.company.companyName || "",
            allowedDomains: response.company.allowedDomains || [],
            newDomain: "", // Keep this empty for new domain input
            logoUrl: response.company.logoUrl || "",
            companyAddress: response.company.companyAddress || "",
            companyEmail: response.company.companyEmail || "",
            companyPhone: response.company.companyPhone || "",
          });

          console.log(
            "Form values after setting from database:",
            formik.values
          );
        }
      } catch (error) {
        console.error("Failed to fetch company data:", error);
        // toast.error("Failed to load company data");
      }
    };

    fetchCompanyData();
  }, []);

  return (
    <Box
      className="settings-container"
      p={3}
      sx={{ bgcolor: "background.paper", borderRadius: 2, boxShadow: 2 }}
    >
      <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
        App Settings
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <form onSubmit={formik.handleSubmit}>
        {/* Company Logo Upload Section */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 500 }}>
            Company Logo
          </Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 3,
              p: 2,
              bgcolor: "#f8f9fa",
              borderRadius: 1,
            }}
          >
            <Box
              sx={{
                width: 100,
                height: 100,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                border: "1px dashed #ccc",
                borderRadius: 1,
                overflow: "hidden",
              }}
            >
              {formik.values.logoUrl ? (
                <Avatar
                  src={formik.values.logoUrl}
                  alt="Company Logo"
                  variant="square"
                  sx={{
                    width: "100%",
                    height: "100%",
                    objectFit: "contain",
                  }}
                />
              ) : (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  align="center"
                >
                  No logo uploaded
                </Typography>
              )}
            </Box>

            <Box>
              <Typography variant="body1" sx={{ mb: 1, fontWeight: 500 }}>
                Upload Company Logo
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Recommended size: 200x200 pixels. Max size: 2MB
              </Typography>

              <Button
                variant="contained"
                component="label"
                startIcon={<CloudUploadIcon />}
                disabled={uploadingLogo}
                sx={{
                  bgcolor: "#F26522",
                  "&:hover": { bgcolor: "#d55a1d" },
                  "&.Mui-disabled": { bgcolor: "#f8c8b3" },
                }}
              >
                {uploadingLogo ? (
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Loader loading={uploadingLogo} />
                    <Typography component="span" sx={{ ml: 1 }}>
                      Uploading...
                    </Typography>
                  </Box>
                ) : (
                  "Upload Logo"
                )}
                <input
                  type="file"
                  hidden
                  accept="image/*"
                  onChange={handleLogoUpload}
                />
              </Button>
            </Box>
          </Box>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 500 }}>
            Company Name
          </Typography>
          <TextField
            fullWidth
            id="companyName"
            name="companyName"
            placeholder="Enter company name"
            value={formik.values.companyName}
            onChange={formik.handleChange}
            error={
              formik.touched.companyName && Boolean(formik.errors.companyName)
            }
            helperText={formik.touched.companyName && formik.errors.companyName}
            size="small"
          />
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 500 }}>
            Company Email
          </Typography>
          <TextField
            fullWidth
            id="companyEmail"
            name="companyEmail"
            placeholder="Enter company Email"
            value={formik.values.companyEmail}
            onChange={formik.handleChange}
            error={
              formik.touched.companyEmail && Boolean(formik.errors.companyEmail)
            }
            helperText={
              formik.touched.companyEmail && formik.errors.companyEmail
            }
            size="small"
          />
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 500 }}>
            Company Address
          </Typography>
          <TextField
            fullWidth
            id="companyAddress"
            name="companyAddress"
            placeholder="Enter company Address"
            value={formik.values.companyAddress}
            onChange={formik.handleChange}
            error={
              formik.touched.companyAddress &&
              Boolean(formik.errors.companyAddress)
            }
            helperText={
              formik.touched.companyAddress && formik.errors.companyAddress
            }
            size="small"
          />
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 500 }}>
            Company Phone Number
          </Typography>
          <TextField
            fullWidth
            id="companyPhone"
            name="companyPhone"
            placeholder="Enter company Phone"
            value={formik.values.companyPhone}
            onChange={formik.handleChange}
            error={
              formik.touched.companyPhone &&
              Boolean(formik.errors.companyPhone)
            }
            helperText={
              formik.touched.companyPhone &&
              formik.errors.companyPhone
            }
            size="small"
          />
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 500 }}>
            Allowed Email Domains
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Only users with email addresses from these domains will be allowed
            to register
          </Typography>

          <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
            <TextField
              id="newDomain"
              name="newDomain"
              placeholder="example.com"
              value={formik.values.newDomain}
              onChange={formik.handleChange}
              error={
                formik.touched.newDomain && Boolean(formik.errors.newDomain)
              }
              helperText={formik.touched.newDomain && formik.errors.newDomain}
              size="small"
              sx={{ mr: 1, flexGrow: 1 }}
            />
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddDomain}
              disabled={
                !formik.values.newDomain || Boolean(formik.errors.newDomain)
              }
              startIcon={<AddIcon />}
              sx={{
                bgcolor: "#F26522",
                "&:hover": { bgcolor: "#d55a1d" },
              }}
            >
              Add
            </Button>
          </Box>

          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
            {formik.values.allowedDomains.map((domain, index) => (
              <Chip
                key={index}
                label={domain}
                onDelete={() => handleRemoveDomain(domain)}
                deleteIcon={<CancelIcon />}
                sx={{
                  bgcolor: "#f0f0f0",
                  "& .MuiChip-deleteIcon": {
                    color: "#F26522",
                    "&:hover": {
                      color: "#d55a1d",
                    },
                  },
                }}
              />
            ))}
            {formik.values.allowedDomains.length === 0 && (
              <Typography variant="body2" color="text.secondary">
                No domains added yet. Add domains to restrict user registration.
              </Typography>
            )}
          </Box>
        </Box>

        <Box sx={{ mt: 4 }}>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={isLoading}
            sx={{
              bgcolor: "#F26522",
              "&:hover": { bgcolor: "#d55a1d" },
              "&.Mui-disabled": { bgcolor: "#f8c8b3" },
            }}
          >
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </Box>
      </form>
    </Box>
  );
}

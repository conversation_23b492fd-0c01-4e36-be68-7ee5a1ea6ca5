import React, { useState } from "react";
import "./ChangePhoneNumberDialog.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  IconButton,
  InputAdornment,
  Box,
  Divider,
} from "@mui/material";
import { Cancel, Visibility, VisibilityOff } from "@mui/icons-material";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";

const validationSchema = Yup.object({
  currentPhoneNumber: Yup.string().required("Phone Number is required"),
  newPhoneNumber: Yup.string().required("New Phone Number is required"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("newPhoneNumber"), undefined], "Phone Number must match")
    .required("Confirm new number is required"),
});

interface ChangePhoneNumberDialogProps {
  open: boolean;
  handleClose: () => void;
}

const ChangePhoneNumberDialog: React.FC<ChangePhoneNumberDialogProps> = ({
  open,
  handleClose,
}) => {
  const [showPassword, setShowPassword] = useState({
    confirmPassword: false,
  });

  interface ShowPasswordState {
    confirmPassword: boolean;
  }

  const toggleShowPassword = (field: keyof ShowPasswordState): void => {
    setShowPassword((prev: ShowPasswordState) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          handleClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth="sm"
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          fontSize: "20px",
          fontWeight: "600",
        }}
      >
        Change Phone Number
        <IconButton onClick={handleClose}>
          <Cancel
            sx={{
              width: "20px",
              height: "20px",
              color: "#111827",
              cursor: "pointer",
              ":hover": { color: "#F26522 !important" },
            }}
          />
        </IconButton>
      </DialogTitle>

      <Divider />
      <Formik
        initialValues={{
          currentPhoneNumber: "",
          newPhoneNumber: "",
          confirmPassword: "",
        }}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          console.log("Phone Number Changed: ", values);
          handleClose();
        }}
      >
        {({ errors, touched, handleChange, values }) => (
          <Form>
            <DialogContent>
              <Box>
                <label>New Phone Number</label>
                <Field
                  as={TextField}
                  name="currentPhoneNumber"
                  placeholder="Enter Phone number"
                  //   type={showPassword.currentPassword ? "text" : "password"}
                  fullWidth
                  margin="dense"
                  error={
                    touched.currentPhoneNumber &&
                    Boolean(errors.currentPhoneNumber)
                  }
                  helperText={
                    touched.currentPhoneNumber && errors.currentPhoneNumber
                  }
                />
              </Box>
              <Box>
                <label>New Phone Number</label>
                <Field
                  as={TextField}
                  name="newPhoneNumber"
                  placeholder="New Phone Number"
                  fullWidth
                  margin="dense"
                  error={
                    touched.newPhoneNumber && Boolean(errors.newPhoneNumber)
                  }
                  helperText={touched.newPhoneNumber && errors.newPhoneNumber}
                />
              </Box>
              <Box>
                <label>Confirm New Password</label>
                <Field
                  as={TextField}
                  name="confirmPassword"
                  type={showPassword.confirmPassword ? "text" : "password"}
                  fullWidth
                  margin="dense"
                  error={
                    touched.confirmPassword && Boolean(errors.confirmPassword)
                  }
                  helperText={touched.confirmPassword && errors.confirmPassword}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => toggleShowPassword("confirmPassword")}
                          edge="end"
                        >
                          {showPassword.confirmPassword ? (
                            <VisibilityOff
                              sx={{ color: "#111827", fontSize: "18px" }}
                            />
                          ) : (
                            <Visibility
                              sx={{ color: "#111827", fontSize: "18px" }}
                            />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={handleClose}
                sx={{
                  borderRadius: "5px",
                  padding: "0.5rem 0.85rem",
                  fontSize: "14px",
                  transition: "all 0.5s",
                  fontWeight: "500",
                  backgroundColor: "#F8F9FA",
                  border: "1px solid #F8F9FA",
                  color: "#111827",
                  textTransform: "none",
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                sx={{
                  background: "#111827 !important",
                  border: "1px solid #212529",
                  borderColor: "#111827 !important",
                  boxShadow: "none",
                  color: "#FFF",
                  padding: "0.5rem 0.85rem",
                  fontSize: "14px",
                  textTransform: "none",
                  borderRadius: "5px",
                }}
              >
                Change Number
              </Button>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default ChangePhoneNumberDialog;

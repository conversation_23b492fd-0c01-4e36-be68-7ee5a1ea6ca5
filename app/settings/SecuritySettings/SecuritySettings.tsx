import React from "react";
import { useState } from "react";
import "./SecuritySettings.scss";
import {
  Box,
  Button,
  Typography,
  Switch,
  Divider,
  Chip,
  Stack,
} from "@mui/material";
import ChangePasswordDialog from "@/app/settings/SecuritySettings/ChangePasswordDialog/ChangePasswordDialog";
import ChangePhoneNumberDialog from "@/app/settings/SecuritySettings/ChangePhoneNumberDialog/ChangePhoneNumberDialog";
import { Verified } from "@mui/icons-material";
import DeviceManagementDialog from "@/app/settings/SecuritySettings/DeviceManagementDialog/DeviceManagementDialog";
import ChangeEmailDialog from "@/app/settings/SecuritySettings/ChangeEmailDialog/ChangeEmailDialog";
import AccountActivityDialog from "@/app/settings/SecuritySettings/AccountActivityDialog/AccountActivityDialog";
import DeleteAccountDialog from "@/app/settings/SecuritySettings/DeleteAccountDialog/DeleteAccountDialog";

const SecuritySettings = () => {
  const [openChangePasswordDialog, setOpenChangePasswordDialog] =
    useState(false);
  const [openChangePhoneNumberDialog, setOpenChangePhoneNumberDialog] =
    useState(false);
  const [openDeviceManagementDialog, setOpenDeviceManagementDialog] =
    useState(false);
  const [openChangeEmailDialog, setOpenChangeEmailDialog] = useState(false);
  const [openAccountActivityDialog, setOpenAccountActivityDialog] =
  useState(false);
  const [openDeleteAccountDialog, setOpenDeleteAccountDialog] = useState(false);

  type Device = {
    device: string;
    date: string;
    location: string;
    ip: string;
  };

  const [devices, setDevices] = React.useState<Device[]>([
    {
      device: "Chrome - Windows",
      date: "15 May 2025, 10:30 AM",
      location: "New York / USA",
      ip: "*************",
    },
    {
      device: "Safari MacOS",
      date: "10 Apr 2025, 05:15 PM",
      location: "New York / USA",
      ip: "*************",
    },
    {
      device: "Firefox Windows",
      date: "15 Mar 2025, 02:40 PM",
      location: "New York / USA",
      ip: "*************",
    },
    {
      device: "Safari MacOS",
      date: "15 May 2025, 10:30 AM",
      location: "New York / USA",
      ip: "333.555.10.54",
    },
  ]);

  const handleRemoveDevice = (ip: string) => {
    setDevices((prev) => prev.filter((device) => device.ip !== ip));
  };

  return (
    <Box
      className="settings-container"
      p={3}
      sx={{
        mx: "auto",
        bgcolor: "background.paper",
        borderRadius: 2,
        boxShadow: 2,
      }}
    >
      <Typography variant="h4" gutterBottom>
        Security Settings
      </Typography>
      <Divider sx={{ mb: "1rem" }} />
      <Stack spacing={2}>
        {/* Password */}
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Box>
            <Typography variant="h5">Password</Typography>
            <Typography>
              Set a unique password to protect the account | Last Changed 03 Jan
              2024, 09:00 AM
            </Typography>
          </Box>
          <Button
            className="black-button"
            variant="contained"
            size="small"
            sx={{ mt: 1 }}
            onClick={() => setOpenChangePasswordDialog(true)}
          >
            Change Password
          </Button>
        </Box>

        <Divider />

        {/* Two Factor Authentication */}
        {/* <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Box>
            <Typography variant="h5">Two Factor Authentication</Typography>
            <Typography>
              Receive codes via SMS or email every time you login
            </Typography>
          </Box>
          <Button
            className="black-button"
            variant="contained"
            size="small"
            sx={{ mt: 1 }}
          >
            Enable
          </Button>
        </Box>

        <Divider /> */}

        {/* Google Authentication */}
        <Box display="flex" alignItems="center" justifyContent="space-between">
          {/* <Box sx={{ display: "flex" }}>
            <Box>
              <Typography variant="h5">Google Authentication</Typography>
              <Typography>Connect to Google</Typography>
            </Box>
            <Chip
              className="chip"
              sx={{ marginLeft: ".5rem" }}
              label="Connected"
              size="small"
            />
          </Box> */}

          {/* place a switch here for toggle */}
          {/* <Switch
            defaultChecked={true}
            sx={{
              "& .MuiSwitch-switchBase": {
                color: "#9CA3AF",
                "&.Mui-checked": {
                  color: "#F97316",
                },
                "&.Mui-checked + .MuiSwitch-track": {
                  backgroundColor: "#F26522",
                  opacity: 1,
                },
              },
              "& .MuiSwitch-track": {
                backgroundColor: "#9CA3AF",
              },
              "& .MuiButtonBase-root": {
                padding: "13px",
              },
            }}
          /> */}
        </Box> 

       {/*  <Divider /> */}

        {/* Phone Number Verification */}
        {/* <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Typography variant="h5">Phone Number Verification</Typography>
              <Verified className="verified-icon" />
            </Box>
            <Typography>Verified Mobile Number: +99264710583</Typography>
          </Box>
          <Stack direction="row" spacing={1}>
            <Button className="white-button" variant="outlined" size="small">
              Remove
            </Button>
            <Button
              className="black-button"
              variant="contained"
              size="small"
              onClick={() => setOpenChangePhoneNumberDialog(true)}
            >
              Change
            </Button>
          </Stack>
        </Box> */}

        {/* <Divider /> */}

        {/* Email Verification */}
        {/* <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Typography variant="h5">Email Verification</Typography>
              <Verified className="verified-icon" />
            </Box>
            <Typography>Verified Email: <EMAIL></Typography>
          </Box>
          <Stack direction="row" spacing={1}>
            <Button className="white-button" variant="outlined" size="small">
              Remove
            </Button>
            <Button
              className="black-button"
              variant="contained"
              size="small"
              onClick={() => setOpenChangeEmailDialog(true)}
            >
              Change
            </Button>
          </Stack>
        </Box>

        <Divider /> */}

        {/* Device Management */}
        {/* <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h5">Device Management</Typography>
            <Typography>The devices associated with the account</Typography>
          </Box>
          <Button
            className="black-button"
            variant="contained"
            size="small"
            sx={{ mt: 1 }}
            onClick={() => setOpenDeviceManagementDialog(true)}
          >
            Manage
          </Button>
        </Box>

        <Divider /> */}

        {/* Account Activity */}
        {/* <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h5">Account Activity</Typography>
            <Typography>The activities of the account</Typography>
          </Box>
          <Button
            className="black-button"
            variant="contained"
            size="small"
            sx={{ mt: 1 }}
            onClick={() => setOpenAccountActivityDialog(true)}
          >
            View
          </Button>
        </Box>

        <Divider /> */}

        {/* Deactivate Account */}
        {/* <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h5">Deactivate Account</Typography>
            <Typography>
              This will shut down your account. Your account will be reactivated
              when you sign in again.
            </Typography>
          </Box>
          <Button
            className="black-button"
            variant="contained"
            color="warning"
            size="small"
            sx={{ mt: 1 }}
          >
            Deactivate
          </Button>
        </Box>

        <Divider />
 */}
        {/* Delete Account */}
        {/* <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h5">Delete Account</Typography>
            <Typography>Your account will be permanently deleted.</Typography>
          </Box>
          <Button
            className="black-button"
            variant="contained"
            color="error"
            size="small"
            sx={{ mt: 1 }}
            onClick={() => setOpenDeleteAccountDialog(true)}
          >
            Delete
          </Button>
        </Box> */}
      </Stack>
      <ChangePasswordDialog
        open={openChangePasswordDialog}
        handleClose={() => setOpenChangePasswordDialog(false)}
      />
      <ChangePhoneNumberDialog
        open={openChangePhoneNumberDialog}
        handleClose={() => setOpenChangePhoneNumberDialog(false)}
      />
      <DeviceManagementDialog
        open={openDeviceManagementDialog}
        onClose={() => setOpenDeviceManagementDialog(false)}
        devices={devices}
        onRemoveDevice={handleRemoveDevice}
      />

      <ChangeEmailDialog
        open={openChangeEmailDialog}
        handleClose={() => setOpenChangeEmailDialog(false)}
      />

      <AccountActivityDialog
        open={openAccountActivityDialog}
        onClose={() => setOpenAccountActivityDialog(false)}
        devices={devices}
      />

      <DeleteAccountDialog
        open={openDeleteAccountDialog}
        handleClose={() => setOpenDeleteAccountDialog(false)}
       />
    </Box>
  );
};

export default SecuritySettings;

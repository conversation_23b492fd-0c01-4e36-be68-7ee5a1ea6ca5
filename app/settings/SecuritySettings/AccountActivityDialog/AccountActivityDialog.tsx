import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Box,
  IconButton,
} from "@mui/material";
import "./AccountActivityDialog.scss";
import { Cancel } from "@mui/icons-material";

interface Device {
  device: string;
  date: string;
  location: string;
  ip: string;
}

interface AccountActivityDialogProps {
  open: boolean;
  onClose: () => void;
  devices: Device[];
}

const AccountActivityDialog: React.FC<AccountActivityDialogProps> = ({
  open,
  onClose,
  devices,
}) => {
  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="md"
      fullWidth
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          fontSize: "20px",
          fontWeight: "600",
        }}
      >
        Account Activity
        <IconButton onClick={onClose} sx={{ position: "absolute", right: 8 }}>
          <Cancel
            sx={{
              width: "20px",
              height: "20px",
              color: "#111827",
              cursor: "pointer",
              ":hover": { color: "#F26522!important" },
            }}
          />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <TableContainer>
          <Table>
            <TableHead className="table-head">
              <TableRow>
                <TableCell sx={{ fontWeight: "bold" }}>Device</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>Date</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>Location</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>IP Address</TableCell>
                <TableCell sx={{ fontWeight: "bold", textAlign: "center" }}>
                  Action
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {devices.map((device, index) => (
                <TableRow key={index}>
                  <TableCell>{device.device}</TableCell>
                  <TableCell>{device.date}</TableCell>
                  <TableCell>{device.location}</TableCell>
                  <TableCell>{device.ip}</TableCell>
                  <TableCell align="center">
                    <Button
                      variant="contained"
                      //   sx={{
                      //     backgroundColor: "green",
                      //     color: "white",
                      //     fontSize: "0.8rem",
                      //     fontWeight: "bold",
                      //     textTransform: "none",
                      //     padding: "4px 12px",
                      //     borderRadius: "20px",
                      //     "&:hover": { backgroundColor: "darkgreen" },
                      //   }}
                      className="connect-status"
                    >
                      ● Connect
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
    </Dialog>
  );
};

export default AccountActivityDialog;

import React, { useState } from "react";
import "./ChangePasswordDialog.scss";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  IconButton,
  InputAdornment,
  Box,
  Divider,
} from "@mui/material";
import { Cancel, Visibility, VisibilityOff } from "@mui/icons-material";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import { changePassword } from "@/app/services/changepassword/changepassword.service";
import { toast } from "react-toastify";

const validationSchema = Yup.object({
  newPassword: Yup.string()
    .min(8, "Password must be at least 8 characters")
    .max(64, "Max 64 chars allowed")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>]).*$/,
      "Need 1 lowercase, 1 uppercase, 1 special char"
    )
    .required("Password is required"),
  confirmNewPassword: Yup.string()
    .oneOf([Yup.ref("newPassword"), undefined], "Passwords must match")
    .required("Confirm new password is required"),
});

interface ChangePasswordDialogProps {
  open: boolean;
  handleClose: () => void;
}

const ChangePasswordDialog: React.FC<ChangePasswordDialogProps> = ({
  open,
  handleClose,
}) => {
  const [showPassword, setShowPassword] = useState({
    newPassword: false,
    confirmNewPassword: false,
  });

  const toggleShowPassword = (field: keyof typeof showPassword): void => {
    setShowPassword((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleSubmit = async (
    values: {
      newPassword: string;
      confirmNewPassword: string;
    },
    { setSubmitting }: { setSubmitting: (isSubmitting: boolean) => void }
  ) => {
    try {
      const body = {
        newPassword: values.newPassword,
        confirmNewPassword: values.confirmNewPassword,
      };
      const response = await changePassword(body);
      if (response.success) {
        // toast.success("Password changed successfully");
        handleClose();
      } else {
        //  toast.error(response.message || "Failed to change password");
      }
    } catch (error) {
      console.error("Error changing password:", error);
      // toast.error("An error occurred while changing the password");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          handleClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth="sm"
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          fontSize: "20px",
          fontWeight: "600",
        }}
      >
        Change Password
        <IconButton onClick={handleClose}>
          <Cancel
            sx={{
              width: "20px",
              height: "20px",
              color: "#111827",
              cursor: "pointer",
              ":hover": { color: "#F26522!important" },
            }}
          />
        </IconButton>
      </DialogTitle>

      <Divider />
      <Formik
        initialValues={{
          newPassword: "",
          confirmNewPassword: "",
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ errors, touched, handleChange, values, isSubmitting }) => (
          <Form>
            <DialogContent>
              <Box>
                <label>New Password</label>
                <Field
                  as={TextField}
                  name="newPassword"
                  type={showPassword.newPassword ? "text" : "password"}
                  fullWidth
                  margin="dense"
                  error={touched.newPassword && Boolean(errors.newPassword)}
                  helperText={touched.newPassword && errors.newPassword}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => toggleShowPassword("newPassword")}
                          edge="end"
                        >
                          {showPassword.newPassword ? (
                            <VisibilityOff
                              sx={{ color: "#111827", fontSize: "18px" }}
                            />
                          ) : (
                            <Visibility
                              sx={{ color: "#111827", fontSize: "18px" }}
                            />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
              <Box>
                <label>Confirm New Password</label>
                <Field
                  as={TextField}
                  name="confirmNewPassword"
                  type={showPassword.confirmNewPassword ? "text" : "password"}
                  fullWidth
                  margin="dense"
                  error={
                    touched.confirmNewPassword &&
                    Boolean(errors.confirmNewPassword)
                  }
                  helperText={
                    touched.confirmNewPassword && errors.confirmNewPassword
                  }
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() =>
                            toggleShowPassword("confirmNewPassword")
                          }
                          edge="end"
                        >
                          {showPassword.confirmNewPassword ? (
                            <VisibilityOff
                              sx={{ color: "#111827", fontSize: "18px" }}
                            />
                          ) : (
                            <Visibility
                              sx={{ color: "#111827", fontSize: "18px" }}
                            />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={handleClose}
                sx={{
                  textTransform: "none",
                  borderColor: "#ccc",
                  color: "#000",
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={isSubmitting}
                sx={{
                  backgroundColor: "#F26522",
                  color: "#FFF",
                  textTransform: "none",
                  "&:hover": {
                    backgroundColor: "#d55a1d",
                  },
                }}
              >
                Save
              </Button>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default ChangePasswordDialog;

import React from "react";
import "./DeviceManagementDialog.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Divider,
} from "@mui/material";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import { Cancel } from "@mui/icons-material";

interface Device {
  device: string;
  date: string;
  location: string;
  ip: string;
}

interface DeviceManagementDialogProps {
  open: boolean;
  onClose: () => void;
  devices: Device[];
  onRemoveDevice: (ip: string) => void;
}

const DeviceManagementDialog: React.FC<DeviceManagementDialogProps> = ({
  open,
  onClose,
  devices,
  onRemoveDevice,
}) => {
  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="md"
      fullWidth
    >
      <DialogTitle sx={{ fontSize: "20px", fontWeight: "600" }}>
        Device Management
        <IconButton onClick={onClose} sx={{ position: "absolute", right: 8 }}>
          <Cancel
            sx={{
              width: "20px",
              height: "20px",
              color: "#111827",
              cursor: "pointer",
              ":hover": { color: "#F26522!important" },
            }}
          />
        </IconButton>
      </DialogTitle>

      <Divider />
      <DialogContent>
        <TableContainer>
          <Table>
            <TableHead className="table-head">
              <TableRow>
                <TableCell sx={{ fontWeight: "bold" }}>Device</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>Date</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>Location</TableCell>
                <TableCell sx={{ fontWeight: "bold" }}>IP Address</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {devices.map((device) => (
                <TableRow key={device.ip}>
                  <TableCell>{device.device}</TableCell>
                  <TableCell>{device.date}</TableCell>
                  <TableCell>{device.location}</TableCell>
                  <TableCell>{device.ip}</TableCell>
                  <TableCell>
                    <IconButton onClick={() => onRemoveDevice(device.ip)}>
                      <DeleteOutlineIcon
                        sx={{ width: "14px", height: "14px" }}
                      />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
    </Dialog>
  );
};

export default DeviceManagementDialog;

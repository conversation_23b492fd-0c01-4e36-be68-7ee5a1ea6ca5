import React, { useState } from "react";
import "./DeleteAccountDialog.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  IconButton,
  InputAdornment,
  Box,
  Divider,
  Typography,
} from "@mui/material";
import { Cancel, Visibility, VisibilityOff } from "@mui/icons-material";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";

const validationSchema = Yup.object({
  currentPhoneNumber: Yup.string().required("Phone Number is required"),
  newPhoneNumber: Yup.string().required("New Phone Number is required"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("newPhoneNumber"), undefined], "Phone Number must match")
    .required("Confirm new number is required"),
});

interface DeleteAccountDialogProps {
  open: boolean;
  handleClose: () => void;
}

const DeleteAccountDialog: React.FC<DeleteAccountDialogProps> = ({
  open,
  handleClose,
}) => {
  const [showPassword, setShowPassword] = useState({
    confirmPassword: false,
  });

  interface ShowPasswordState {
    confirmPassword: boolean;
  }

  const toggleShowPassword = (field: keyof ShowPasswordState): void => {
    setShowPassword((prev: ShowPasswordState) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          handleClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth="sm"
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          fontSize: "20px",
          fontWeight: "600",
        }}
      >
        Delete Account
        <IconButton onClick={handleClose}>
          <Cancel
            sx={{
              width: "20px",
              height: "20px",
              color: "#111827",
              cursor: "pointer",
              ":hover": { color: "#F26522 !important" },
            }}
          />
        </IconButton>
      </DialogTitle>

      <Divider />
      <Formik
        initialValues={{
          confirmPassword: "",
        }}
        validationSchema={validationSchema}
        onSubmit={(values) => {
          console.log("Email Changed: ", values);
          handleClose();
        }}
      >
        {({ errors, touched, handleChange, values }) => (
          <Form>
            <DialogContent>
              <Box>
                <Typography sx={{ marginBottom: "1rem" }}>
                  Are you sure you want to delete This Account? To delete your
                  account, Type your password.
                </Typography>
              </Box>

              <Box>
                <label>Confirm New Password</label>
                <Field
                  as={TextField}
                  name="confirmPassword"
                  type={showPassword.confirmPassword ? "text" : "password"}
                  fullWidth
                  margin="dense"
                  error={
                    touched.confirmPassword && Boolean(errors.confirmPassword)
                  }
                  helperText={touched.confirmPassword && errors.confirmPassword}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => toggleShowPassword("confirmPassword")}
                          edge="end"
                        >
                          {showPassword.confirmPassword ? (
                            <VisibilityOff
                              sx={{ color: "#111827", fontSize: "18px" }}
                            />
                          ) : (
                            <Visibility
                              sx={{ color: "#111827", fontSize: "18px" }}
                            />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={handleClose}
                sx={{
                  borderRadius: "5px",
                  padding: "0.5rem 0.85rem",
                  fontSize: "14px",
                  transition: "all 0.5s",
                  fontWeight: "500",
                  backgroundColor: "#F8F9FA",
                  border: "1px solid #F8F9FA",
                  color: "#111827",
                  textTransform: "none",
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                sx={{
                  background: "#111827 !important",
                  border: "1px solid #212529",
                  borderColor: "#111827 !important",
                  boxShadow: "none",
                  color: "#FFF",
                  padding: "0.5rem 0.85rem",
                  fontSize: "14px",
                  textTransform: "none",
                  borderRadius: "5px",
                }}
              >
                Delete Account
              </Button>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default DeleteAccountDialog;

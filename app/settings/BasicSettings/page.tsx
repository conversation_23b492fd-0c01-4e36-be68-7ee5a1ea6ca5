"use client";
import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  CircularProgress,
  Alert,
  Divider
} from "@mui/material";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  updateNoticePeriodSettings,
  getNoticePeriod
} from "@/app/services/setting.service";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { HomeOutlined } from "@mui/icons-material";
import { toast } from "react-toastify";

interface NoticePeriodFormValues {
  noticePeriodDays: number;
}

export default function BasicSettings() {
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Administration", href: "" },
    { label: "Settings", href: "/settings" },
    { label: "Basic Settings" },
  ];

  // Validation schema
  const validationSchema = Yup.object({
    noticePeriodDays: Yup.number()
      .required("Notice period days is required")
      .min(0, "Notice period days must be a positive number")
      .integer("Notice period days must be a whole number"),
  });

  // Initialize formik
  const formik = useFormik<NoticePeriodFormValues>({
    initialValues: {
      noticePeriodDays: 0,
    },
    validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      setSuccessMessage("");
      setErrorMessage("");

      try {
        // Convert to number to ensure it's sent as a number, not a string
        const noticePeriodDays = Number(values.noticePeriodDays);
        
        await updateNoticePeriodSettings({
          noticePeriodDays: noticePeriodDays,
        });
        
        setSuccessMessage("Notice period settings updated successfully");
        toast.success("Notice period settings updated successfully");
      } catch (error) {
        console.error("Error updating notice period settings:", error);
        setErrorMessage("Failed to update notice period settings");
        // toast.error("Failed to update notice period settings");
      } finally {
        setLoading(false);
      }
    },
  });

  // Fetch current notice period settings
  useEffect(() => {
    const fetchNoticePeriod = async () => {
      setInitialLoading(true);
      try {
        const response = await getNoticePeriod();
        
        if (response && response.noticeSettings) {
          formik.setFieldValue(
            "noticePeriodDays",
            response.noticeSettings.noticePeriodDays || 0
          );
        }
      } catch (error) {
        console.error("Error fetching notice period:", error);
        setErrorMessage("Failed to fetch current notice period settings");
      } finally {
        setInitialLoading(false);
      }
    };

    fetchNoticePeriod();
  }, []);

  return (
    <Box className="settings-container">
      <Box className="content">
        <Box className="settings-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Basic Settings</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
        </Box>

        <Paper elevation={2} sx={{ p: 3, mt: 2 }}>
          <Typography variant="h5" gutterBottom>
            Notice Period Configuration
          </Typography>
          <Typography variant="body2" color="textSecondary" paragraph>
            Configure the notice period settings for your organization. This will affect how notice dates are calculated for resignations and terminations.
          </Typography>
          <Divider sx={{ my: 2 }} />
          
          {initialLoading ? (
            <Box display="flex" justifyContent="center" my={4}>
              <CircularProgress />
            </Box>
          ) : (
            <form onSubmit={formik.handleSubmit}>
              {successMessage && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {successMessage}
                </Alert>
              )}
              
              {errorMessage && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {errorMessage}
                </Alert>
              )}
              
              <Box mb={3}>
                <Typography variant="subtitle1" gutterBottom>
                  Notice Period Days
                </Typography>
                <TextField
                  fullWidth
                  id="noticePeriodDays"
                  name="noticePeriodDays"
                  type="number"
                  value={formik.values.noticePeriodDays}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.noticePeriodDays && 
                    Boolean(formik.errors.noticePeriodDays)
                  }
                  helperText={
                    formik.touched.noticePeriodDays && 
                    formik.errors.noticePeriodDays
                  }
                  InputProps={{
                    inputProps: { min: 0 }
                  }}
                  sx={{ maxWidth: 300 }}
                />
                <Typography variant="caption" color="textSecondary" display="block" mt={1}>
                  Set the number of days for the notice period. This will be used to calculate notice dates for resignations and terminations.
                </Typography>
              </Box>
              
              <Box display="flex" justifyContent="flex-end">
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading}
                  sx={{
                    backgroundColor: "#F26522",
                    color: "#FFF",
                    "&:hover": {
                      backgroundColor: "#d55a1d",
                    },
                    textTransform: "none",
                  }}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </Box>
            </form>
          )}
        </Paper>
      </Box>
    </Box>
  );
}

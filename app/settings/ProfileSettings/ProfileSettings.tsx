"use client";

import {
  <PERSON>,
  Divider,
  <PERSON><PERSON><PERSON>,
  Avatar,
  Button,
  TextField,
} from "@mui/material";
import { useState, useEffect } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
// import "./profile.scss";
import { HomeOutlined } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import Loader from "@/components/Loader/Loader";
import useAuthStore from "@/store/authStore";
import { getUserById, editUser } from "@/app/services/users.service"
import { toast } from "react-toastify";
import useUploadMedia from "@/app/hooks/useUploadMedia";

interface User {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company: string;
  joiningDate: string;
  address: string;
  country: string;
  state: string;
  city: string;
  postalCode: string;
  avatar?: string;
}

const initialValues = {
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  company: "",
  joiningDate: "",
  address: "",
  country: "",
  state: "",
  city: "",
  postalCode: "",
  currentPassword: "",
  newPassword: "",
  confirmNewPassword: "",
  selectedImage: "",
};

const validationSchema = Yup.object({
  firstName: Yup.string().required("First Name is required"),
  lastName: Yup.string().required("Last Name is required"),
  email: Yup.string()
    .email("Invalid email format")
    .required("Email is required"),
  phone: Yup.string().required("Phone is required"),
  company: Yup.string().required("Company is required"),
  joiningDate: Yup.date().required("Joining Date is required"),
  address: Yup.string().required("Address is required"),
  country: Yup.string().required("Country is required"),
  state: Yup.string().required("State is required"),
  city: Yup.string().required("City is required"),
  postalCode: Yup.string().required("Postal Code is required"),
  selectedImage: Yup.string(),
});

export default function ProfileSettings() {
  const [isLoading, setIsLoading] = useState(false);
  const { employeeId } = useAuthStore();
  const { uploadMedia } = useUploadMedia();

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      if (!employeeId) return;

      setIsLoading(true);
      try {
        const userData: User = {
          firstName: values.firstName,
          lastName: values.lastName,
          email: values.email,
          phone: values.phone,
          company: values.company,
          joiningDate: values.joiningDate,
          address: values.address,
          country: values.country,
          state: values.state,
          city: values.city,
          postalCode: values.postalCode,
          avatar: values.selectedImage,
        };

        await editUser(employeeId, userData);
        toast.success("Profile updated successfully!");
      } catch (error) {
        console.error("Failed to update profile:", error);
        // toast.error("Failed to update profile. Please try again.");
      } finally {
        setIsLoading(false);
      }
    },
  });

  useEffect(() => {
    const fetchUserData = async () => {
      if (employeeId) {
        setIsLoading(true);
        try {
          const response = await getUserById(employeeId);
          const userData = response.user;

          formik.setValues({
            firstName: userData.firstName || "",
            lastName: userData.lastName || "",
            email: userData.email || "",
            phone: userData.phone || "",
            company: userData.company || "",
            joiningDate: userData.joiningDate
              ? new Date(userData.joiningDate).toISOString().split("T")[0]
              : "",
            address: userData.address || "",
            country: userData.country || "",
            state: userData.state || "",
            city: userData.city || "",
            postalCode: userData.postalCode || "",
            currentPassword: "",
            newPassword: "",
            confirmNewPassword: "",
            selectedImage: userData.avatar || "",
          });
        } catch (error) {
          console.error("Failed to fetch user data:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchUserData();
  }, [employeeId]);

  // Handle file upload to S3
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.currentTarget.files;
    if (files && files.length > 0) {
      const file = files[0];
      try {
        setIsLoading(true);
        const { preview } = await uploadMedia(
          file,
          "temp",
          1,
          setIsLoading,
          file.name
        );
        formik.setFieldValue("selectedImage", preview);
        toast.success("Image uploaded successfully!");
      } catch (error) {
        console.error("Error uploading image:", error);
        // toast.error("Failed to upload image. Please try again.");
      }
    }
  };

  return (
    <Box className="profile-container">
      {isLoading && <Loader loading={isLoading} />}
        <Box
          className="profile-container"
          sx={{
            display: "flex",
            flexDirection: "column",
            width: "100%",
            gap: "24px",
            padding: "1.25rem",
            backgroundColor: "#FFF",
          }}
        >
          <Box className="profile-content">
            <Typography
              sx={{
                fontSize: "18px",
                paddingBottom: "1rem",
                fontWeight: "600",
              }}
              variant="h5"
            >
              Profile
            </Typography>

            <Divider />

            <form onSubmit={formik.handleSubmit}>
              <Box
                className="upload-avatar"
                sx={{
                  display: "flex",
                  padding: "16px",
                  backgroundColor: "#F8F9FA",
                  gap: "10px",
                }}
              >
                <Box
                  className="avatar-logo"
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    borderRadius: "50%",
                    width: "80px",
                    height: "80px",
                    border: "1px solid #E5E7EB",
                  }}
                >
                  <Avatar
                    src={formik.values.selectedImage}
                    alt="Employee Avatar"
                    sx={{ width: "100%", height: "100%" }}
                  />
                </Box>
                <Box>
                  <Typography sx={{ fontSize: "14px", fontWeight: 600 }}>
                    Upload Profile Image
                  </Typography>
                  <Typography
                    sx={{ fontSize: "0.75rem", marginBottom: ".5rem" }}
                  >
                    Image should be below 4 MB
                  </Typography>
                  <Button
                    variant="contained"
                    component="label"
                    sx={{
                      backgroundColor: "#F26522",
                      fontSize: "0.75rem",
                      padding: "0.25rem 0.5rem",
                    }}
                    disabled={isLoading}
                  >
                    {isLoading ? "Uploading..." : "Upload"}
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      onChange={handleFileUpload} // Use the new handler
                    />
                  </Button>
                </Box>
              </Box>

              <Box className="profile-details" sx={{ marginBottom: "1rem" }}>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: "1rem",
                      width: "50%",
                    }}
                  >
                    <label htmlFor="">First Name</label>
                    <TextField
                      sx={{
                        paddingRight: "1.5rem",
                        paddingLeft: "1.5rem",
                        marginTop: "0",
                      }}
                      fullWidth
                      margin="dense"
                      id="firstName"
                      name="firstName"
                      value={formik.values.firstName}
                      onChange={formik.handleChange}
                      error={
                        formik.touched.firstName &&
                        Boolean(formik.errors.firstName)
                      }
                      helperText={
                        formik.touched.firstName && formik.errors.firstName
                      }
                    />
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: "1rem",
                      width: "50%",
                    }}
                  >
                    <label htmlFor="">Last Name</label>
                    <TextField
                      sx={{
                        paddingRight: "1.5rem",
                        paddingLeft: "1.5rem",
                        marginTop: "0",
                      }}
                      fullWidth
                      margin="dense"
                      id="lastName"
                      name="lastName"
                      value={formik.values.lastName}
                      onChange={formik.handleChange}
                      error={
                        formik.touched.lastName &&
                        Boolean(formik.errors.lastName)
                      }
                      helperText={
                        formik.touched.lastName && formik.errors.lastName
                      }
                    />
                  </Box>
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: "1rem",
                      width: "50%",
                    }}
                  >
                    <label htmlFor="">Email</label>
                    <TextField
                      sx={{
                        paddingRight: "1.5rem",
                        paddingLeft: "1.5rem",
                        marginTop: "0",
                      }}
                      fullWidth
                      margin="dense"
                      id="email"
                      name="email"
                      value={formik.values.email}
                      onChange={formik.handleChange}
                      error={
                        formik.touched.email && Boolean(formik.errors.email)
                      }
                      helperText={formik.touched.email && formik.errors.email}
                    />
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: "1rem",
                      width: "50%",
                    }}
                  >
                    <label htmlFor="">Phone</label>
                    <TextField
                      sx={{
                        paddingRight: "1.5rem",
                        paddingLeft: "1.5rem",
                        marginTop: "0",
                      }}
                      fullWidth
                      margin="dense"
                      id="phone"
                      name="phone"
                      value={formik.values.phone}
                      onChange={formik.handleChange}
                      error={
                        formik.touched.phone && Boolean(formik.errors.phone)
                      }
                      helperText={formik.touched.phone && formik.errors.phone}
                    />
                  </Box>
                </Box>
              </Box>

              <Divider />

              <Box className="address-info" sx={{ marginTop: "1rem" }}>
                <Typography
                  sx={{
                    fontSize: "14px",
                    paddingBottom: "1rem",
                    fontWeight: "600",
                    marginBottom: "1rem ",
                  }}
                  variant="h6"
                >
                  Address Information
                </Typography>

                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: "1rem",
                    width: "100%",
                    marginBottom: "1rem",
                  }}
                >
                  <label style={{ width: "16.66666667%" }}>Address</label>
                  <TextField
                    sx={{
                      paddingRight: "1.5rem",
                      paddingLeft: "1.5rem",
                      marginTop: "0",
                    }}
                    fullWidth
                    margin="dense"
                    id="address"
                    name="address"
                    value={formik.values.address}
                    onChange={formik.handleChange}
                    error={
                      formik.touched.address && Boolean(formik.errors.address)
                    }
                    helperText={formik.touched.address && formik.errors.address}
                  />
                </Box>

                <Box className="profile-details" sx={{ marginBottom: "1rem" }}>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      marginBottom: "1rem",
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "1rem",
                        width: "50%",
                      }}
                    >
                      <label htmlFor="">Country</label>
                      <TextField
                        sx={{
                          paddingRight: "1.5rem",
                          paddingLeft: "1.5rem",
                          marginTop: "0",
                        }}
                        fullWidth
                        margin="dense"
                        id="country"
                        name="country"
                        value={formik.values.country}
                        onChange={formik.handleChange}
                        error={
                          formik.touched.country &&
                          Boolean(formik.errors.country)
                        }
                        helperText={
                          formik.touched.country && formik.errors.country
                        }
                      />
                    </Box>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "1rem",
                        width: "50%",
                      }}
                    >
                      <label htmlFor="">State</label>
                      <TextField
                        sx={{
                          paddingRight: "1.5rem",
                          paddingLeft: "1.5rem",
                          marginTop: "0",
                        }}
                        fullWidth
                        margin="dense"
                        id="state"
                        name="state"
                        value={formik.values.state}
                        onChange={formik.handleChange}
                        error={
                          formik.touched.state && Boolean(formik.errors.state)
                        }
                        helperText={formik.touched.state && formik.errors.state}
                      />
                    </Box>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "1rem",
                        width: "50%",
                      }}
                    >
                      <label htmlFor="">City</label>
                      <TextField
                        sx={{
                          paddingRight: "1.5rem",
                          paddingLeft: "1.5rem",
                          marginTop: "0",
                        }}
                        fullWidth
                        margin="dense"
                        id="city"
                        name="city"
                        value={formik.values.city}
                        onChange={formik.handleChange}
                        error={
                          formik.touched.city && Boolean(formik.errors.city)
                        }
                        helperText={formik.touched.city && formik.errors.city}
                      />
                    </Box>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: "1rem",
                        width: "50%",
                      }}
                    >
                      <label htmlFor="">Postal Code</label>
                      <TextField
                        sx={{
                          paddingRight: "1.5rem",
                          paddingLeft: "1.5rem",
                          marginTop: "0",
                        }}
                        fullWidth
                        margin="dense"
                        id="postalCode"
                        name="postalCode"
                        value={formik.values.postalCode}
                        onChange={formik.handleChange}
                        error={
                          formik.touched.postalCode &&
                          Boolean(formik.errors.postalCode)
                        }
                        helperText={
                          formik.touched.postalCode && formik.errors.postalCode
                        }
                      />
                    </Box>
                  </Box>
                </Box>
              </Box>

              <Divider />

              <Box
                className="save-button"
                sx={{
                  marginTop: "1rem",
                  display: "flex",
                  justifyContent: "end",
                }}
              >
                <Button
                  sx={{
                    fontWeight: "400",
                    fontSize: "14px",
                    color: "#111827",
                    backgroundColor: "#FFF",
                    border: "1px solid #E5E7EB",
                    borderRadius: "5px",
                    textTransform: "none",
                    padding: "8px 13.6px",
                    marginRight: "1rem",
                  }}
                  variant="contained"
                  onClick={() => formik.resetForm()}
                >
                  Cancel
                </Button>
                <Button
                  sx={{
                    display: "flex",
                    gap: "8px",
                    backgroundColor: "#F26522",
                    borderColor: "#F26522",
                    color: "#FFF",
                    fontWeight: 400,
                    fontSize: "14px",
                    borderRadius: "5px",
                    textTransform: "none",
                    padding: "8px 13.6px",
                  }}
                  variant="contained"
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? "Saving..." : "Save"}
                </Button>
              </Box>
            </form>
          </Box>
        </Box>   
    </Box>
  );
}

.settings-container {
    width: 100%;
    height: 100%;

    .MuiInputBase-root {
        padding: 5.5px 14px !important;
        height: 36px !important;
        box-sizing: border-box;

        &.MuiInputBase-multiline {
            height: auto !important;
            padding: 5.5px 14px !important;
        }

        .MuiInputBase-input {
            padding: 0 !important;
            height: 100% !important;
            box-sizing: border-box;
        }
    }

    .MuiAutocomplete-inputRoot {
        padding: 5.5px 14px !important;
        height: 36px !important;
        box-sizing: border-box;

        .MuiAutocomplete-input {
            padding: 0 !important;
            height: 100% !important;
        }
    }

    .MuiTextField-root,
    .MuiAutocomplete-root {
        margin-top: 8px !important;
        margin-bottom: 8px !important;
    }

    .profile-details {
        label {
            flex: 0 0 auto;
            width: 33.33333333%;
            font-size: 14px;
            font-weight: 500;
            color: rgb(32, 44, 75);
        }


    }

    .address-info {
        label {
            flex: 0 0 auto;
            width: 33.33333333%;
            font-size: 14px;
            font-weight: 500;
            color: rgb(32, 44, 75);
        }

    }

    .content {
        padding: 24px;

        .settings-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;

            .breadcrumbs-box {
                h2 {
                    font-size: 24px;
                    font-weight: 700;
                    color: #202C4B;
                }
            }


        }

        .horizontalTabs {
            button {
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .verticalTabs {
            background-color: #F8F9FA;


            .MuiTabs-vertical {
                margin-right: 10px;
                background-color: #FFF !important;
                width: 25%;
            }


            .MuiTabs-flexContainer {
                padding: 1.25rem;

                button {
                    min-width: max-content;
                    color: rgb(107, 114, 128);
                    font-weight: 500;
                    border-radius: 4px;

                }

                .Mui-selected {
                    background: rgb(254, 240, 233);
                    border-color: rgb(254, 240, 233);
                    color: rgb(242, 101, 34);
                    font-weight: 500;
                }
            }
        }

    }

    .MuiTabs-root {
        // margin-bottom: 1rem !important;

        .MuiTabs-indicator {
            display: none;
        }

        button {
            margin-bottom: -1px;
            background: 0 0;
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            text-transform: none;
            min-height: fit-content;
            display: flex;
            align-items: flex-start;
        }

        .Mui-selected {
            background-color: #F26522;
            border-color: #F26522;
            color: #FFF;
            border-radius: 0;
        }
    }
}
"use client";

import * as React from "react";
import { useState } from "react";
import "./Settings.scss";
import { Box, Typography, Tab, Tabs } from "@mui/material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import {
  HomeOutlined,
  Settings as SettingsIcon,
  LanguageOutlined,
} from "@mui/icons-material";
import ProfileSettings from "./ProfileSettings/ProfileSettings";
import SecuritySettings from "./SecuritySettings/SecuritySettings";
import AppSettings from "./AppSettings/AppSettings";
import { ROLE_GROUPS, ROLES } from "../constants/roles";
import useRoleAuth from "../hooks/useRoleAuth";
import useAuthStore from "@/store/authStore";
import BasicSettings from "./BasicSettings/page";


interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      sx={{ borderRadius: "5px", height: "90vh", width: "100%" }}
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

export default function Settings() {
  // State for horizontal and vertical tabs
  const [horizontalTabValue, setHorizontalTabValue] = useState(0);
  const [verticalTabValue, setVerticalTabValue] = useState(0);
  const { roles } = useAuthStore();
  const isSuperAdmin = roles.includes(ROLES.SuperAdmin);
  
  // Check if user has admin permissions for Basic Settings
  const canAccessBasicSettings = roles.some(
    role => role === ROLES.Admin || role === ROLES.SuperAdmin || role === ROLES.HR
  );

  // Restrict access to Employee roles only (Admin, SuperAdmin, Manager, HR, Employee)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const handleHorizontalTabChange = (
    event: React.SyntheticEvent,
    newValue: number
  ) => {
    setHorizontalTabValue(newValue);
  };

  const handleVerticalTabChange = (
    event: React.SyntheticEvent,
    newValue: number
  ) => {
    setVerticalTabValue(newValue);
  };

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Administration", href: "" },
    { label: "Settings" },
  ];

  return (
    <Box className="settings-container">
      <Box className="content">
        <Box className="settings-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Settings</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
        </Box>

        <Box sx={{ width: "100%" }}>
          {/* Horizontal Tabs */}
          <Box
            className="horizontalTabs"
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              marginBottom: "1rem",
            }}
          >
            <Tabs
              value={horizontalTabValue}
              onChange={handleHorizontalTabChange}
              aria-label="basic tabs example"
            >
              <Tab
                icon={<SettingsIcon sx={{ width: "14px", height: "14px" }} />}
                iconPosition="start"
                label="General Settings"
                {...a11yProps(0)}
              />
             {/*  <Tab
                icon={
                  <LanguageOutlined sx={{ width: "14px", height: "14px" }} />
                }
                iconPosition="start"
                label="Website Settings"
                {...a11yProps(1)}
              /> */}
              
             {/*  <Tab label="App Settings" {...a11yProps(2)} /> */}
            </Tabs>
          </Box>
          <CustomTabPanel value={horizontalTabValue} index={0}>
            {/* Vertical Tabs */}
            <Box
              className="verticalTabs"
              sx={{
                flexGrow: 1,
                bgcolor: "background.paper",
                display: "flex",
                height: 224,
              }}
            >
              <Tabs
                orientation="vertical"
                value={verticalTabValue}
                onChange={handleVerticalTabChange}
                aria-label="Vertical tabs example"
                sx={{ borderRight: 1, borderColor: "divider" }}
              >
                <Tab label="Profile Settings" {...a11yProps(0)} />
                <Tab label="Security Settings" {...a11yProps(1)} />
                {/* Only show App Settings tab for SuperAdmin */}
                {isSuperAdmin && <Tab label="App Settings" {...a11yProps(2)} />}
                {/* Only show Basic Settings tab for Admin, SuperAdmin, or HR */}
                {canAccessBasicSettings && (
                  <Tab 
                    label="Basic Settings" 
                    {...a11yProps(isSuperAdmin ? 3 : 2)} 
                  />
                )}
              </Tabs>
              <CustomTabPanel value={verticalTabValue} index={0}>
                {/* Profile Content */}
                <ProfileSettings />
              </CustomTabPanel>
              <CustomTabPanel value={verticalTabValue} index={1}>
                <SecuritySettings />
              </CustomTabPanel>
              {/* Only show App Settings panel for SuperAdmin */}
              {isSuperAdmin && (
                <CustomTabPanel value={verticalTabValue} index={2}>
                  <AppSettings />
                </CustomTabPanel>
              )}
              {/* Only show Basic Settings panel for Admin, SuperAdmin, or HR */}
              {canAccessBasicSettings && (
                <CustomTabPanel 
                  value={verticalTabValue} 
                  index={isSuperAdmin ? 3 : 2}
                >
                  <BasicSettings />
                </CustomTabPanel>
              )}
            </Box>
          </CustomTabPanel>
          <CustomTabPanel value={horizontalTabValue} index={1}>
            Website Settings Content
          </CustomTabPanel>
          {/* Only show App Settings panel for SuperAdmin */}
          {isSuperAdmin && (
            <CustomTabPanel value={horizontalTabValue} index={2}>
              App Settings Content
            </CustomTabPanel>
          )}
        </Box>
      </Box>
    </Box>
  );
}

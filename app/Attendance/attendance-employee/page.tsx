"use client";

import { Box, Divider, Typography, Paper, useMediaQuery } from "@mui/material";
import { useState, useMemo } from "react";
import "./attendanceEmployee.scss";
import {
  HomeOutlined,
  TimerOutlined,
  AccessTime,
  Circle,
} from "@mui/icons-material";

import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  useGridApiRef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import AttendancePunchCard from "@/components/AttendancePunchCard/AttendancePunchCard";
import CustomHoursCard from "@/components/CustomHoursCard/CustomHoursCard";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import useFetchAttendanceByEmpId from "@/app/hooks/attendance/useFetchAttendanceByEmpId";
import Loader from "@/components/Loader/Loader";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useAuthStore from "@/store/authStore";

// Quick Search Toolbar Component
function QuickSearchToolbar() {
  return (
    <Box
      sx={{
        p: 0.5,
        pb: 0,
        justifyContent: "space-between",
        padding: "10px 20px",
      }}
    >
      <GridToolbarQuickFilter
        placeholder="Search"
        className="grid-search"
        sx={{ textDecoration: "none" }}
      />
      <Box
        className="grid-export"
        sx={{ display: "flex", alignItems: "center" }}
      >
        <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
      </Box>
    </Box>
  );
}

// Utility function to format ISO date to DD MMM YYYY
const formatDate = (isoDate?: string) => {
  if (!isoDate || isoDate === "N/A") return "N/A";
  const date = Date.parse(isoDate);
  if (isNaN(date)) return "N/A";
  const parsedDate = new Date(date);
  const day = parsedDate.getDate();
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const month = monthNames[parsedDate.getMonth()];
  const year = parsedDate.getFullYear();
  return `${day} ${month} ${year}`;
};

const formatDateTime = (isoDate?: string) => {
  if (!isoDate || isoDate === "N/A") return "N/A";
  const date = new Date(isoDate);
  const hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const ampm = hours >= 12 ? "PM" : "AM";
  const formattedHours = hours % 12 || 12;
  return `${formattedHours}:${minutes} ${ampm}`;
};

const formatDateForApi = (dateStr: string) => {
  if (!dateStr) return undefined;

  try {
    // Assuming dateStr is in format "MM/DD/YYYY"
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return undefined;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return undefined;
  }
};

// Utility function to get today's date in YYYY-MM-DD format
const getTodayDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = (today.getMonth() + 1).toString().padStart(2, "0");
  const day = today.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const formatTimeLabel = (date: Date) => {
  const hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const ampm = hours >= 12 ? "PM" : "AM";
  const formattedHours = hours % 12 || 12;
  return `${formattedHours}:${minutes} ${ampm}`;
};

// Utility function to format hours to hours and minutes
const formatHoursToHM = (hours: number): string => {
  if (hours === 0) return "0";
  
  const wholeHours = Math.floor(hours);
  const minutes = Math.round((hours - wholeHours) * 60);
  
  if (wholeHours === 0) {
    return `${minutes}`;
  } else if (minutes === 0) {
    return wholeHours === 1 ? `${wholeHours}` : `${wholeHours}`;
  } else {
    return `${wholeHours}.${minutes}`;
  }
};

// Interface to match API response
interface AttendanceData {
  _id: string;
  break?: number;
  late?: number;
  productionHours?: number;
  avatar?: string;
  employeeName?: string;
  departmentName?: string;
  date?: string;
  checkIn?: string;
  checkOut?: string;
  status?: string;
  totalWorkingHours?: number;
}

export default function AttendanceEmployee() {
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Employee" },
    { label: "Employee Attendance" },
  ];

  const [isLoading, setIsLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);

  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");

  const { employeeId } = useAuthStore();

  // Handler for punch actions to trigger refetch
  const handlePunch = () => {
    setTimeout(() => {
      setRefreshKey((prev) => prev + 1);
    }, 1000);
  };

  const [attendanceData, attendanceTotal] = useFetchAttendanceByEmpId({
    empId: employeeId || "",
    setIsLoading,
    refresh: refreshKey,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : undefined,

    startDate: startDate ? formatDateForApi(startDate) : undefined,
    endDate: endDate ? formatDateForApi(endDate) : undefined,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
  });

  const apiRef = useGridApiRef();

  // Restrict access to Employee roles
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  // Add console.log to debug the response
  console.log("API Response:", attendanceData);

  // Memoized rows for DataGrid
  const rows: GridRowsProp = useMemo(() => {
    return attendanceData?.results
      ? attendanceData.results.map((item: AttendanceData) => ({
          id: item._id,
          date: formatDate(item.date),
          status: item.status || "Absent",
          checkIn: item.checkIn ? formatDateTime(item.checkIn) : "-",
          checkOut: item.checkOut ? formatDateTime(item.checkOut) : "-",
          break: item.break ? `${item.break} Min` : "-",
          late: item.late
            ? (() => {
                const hours = Math.floor(item.late / 60);
                const minutes = item.late % 60;
                return hours > 0 
                  ? `${hours} Hr${hours > 1 ? 's' : ''} ${minutes} Min` 
                  : `${minutes} Min`;
              })()
            : "-",
          productionHours: item.productionHours
            ? formatHoursToHM(item.productionHours)
            : "0 min",
        }))
      : [];
  }, [attendanceData]);

  const isMobile = useMediaQuery("(max-width:768px)");

  const columns: GridColDef[] = [
    { field: "date", headerName: "Date", flex: 1 },
    { field: "checkIn", headerName: "Check In", flex: 1 },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      renderCell: (params) => (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {isMobile ? (
            <Circle
              sx={{
                fontSize: 12,
                color: params.row.status === "Present" ? "#03C95A" : "#E70D0D",
              }}
            />
          ) : (
            <Box
              sx={{
                alignItems: "center",
                display: "flex",
                gap: "3px",
                backgroundColor:
                  params.row.status === "Present" ? "#D2F5E1" : "#FAE7E7",
                color: params.row.status === "Present" ? "#03C95A" : "#E70D0D",
                borderRadius: "4px",
                textAlign: "center",
                minWidth: "66px",
                minHeight: "18px",
                justifyContent: "center",
                fontSize: "10px",
                fontWeight: 500,
                padding: "0 0.45rem",
                lineHeight: "18px",
                letterSpacing: "0.5px",
              }}
            >
              <Box
                sx={{
                  width: "6px",
                  height: "6px",
                  borderRadius: "50%",
                  backgroundColor:
                    params.row.status === "Present" ? "#03C95A" : "#E70D0D",
                }}
              />
              {params.row.status}
            </Box>
          )}
        </Box>
      ),
    },
    { field: "checkOut", headerName: "Check Out", flex: 1 },
    { field: "break", headerName: "Break", flex: 1 },
    { field: "late", headerName: "Late", flex: 1 },
    {
      field: "productionHours",
      headerName: "Production Hours",
      flex: 1,
      renderCell: (params) => (
        <Box
          sx={{
            backgroundColor: "#03C95A",
            color: "#FFF",
            borderRadius: "4px",
            display: "flex",
            textAlign: "center",
            maxWidth: "fit-content",
            justifyContent: "center",
            // margin: "15px 200px 0px 21px",
            fontSize: "10px",
            fontWeight: 500,
            padding: "0px 8px",
            lineHeight: "18px",
            letterSpacing: "0.5px",
          }}
        >
          <TimerOutlined
            sx={{ width: "12px", height: "12px", margin: "2px 3px" }}
          />
          {params.row.productionHours}
        </Box>
      ),
    },
  ];

  const handlePaginationModelChange = (model: {
    page: number;
    pageSize: number;
  }) => {
    setPage(model.page + 1);
    setPageSize(model.pageSize);
    setLimit(model.pageSize);
  };

  // Filter today's attendance record
  const todayDate = getTodayDate();
  const todayAttendance = attendanceData?.results?.find(
    (item: AttendanceData) => item.date?.split("T")[0] === todayDate
  );

  // Use API-provided values directly, no conversion
  const totalWorkingHoursToday = todayAttendance?.totalWorkingHours || 0;
  const productionHoursToday = todayAttendance?.productionHours || 0;
  const breakHoursToday = todayAttendance?.break
    ? todayAttendance.break / 60
    : 0;

  // Calculate weekly and monthly totals from all records (optional)
  const totalWorkingHoursWeek =
    attendanceData?.results?.reduce(
      (total: number, item: AttendanceData): number =>
        total + (Number(item.totalWorkingHours) || 0),
      0
    ) || 0;
  const totalWorkingHoursMonth = totalWorkingHoursWeek;

  const visualizationWindowHours = 12;

  const startTime = todayAttendance?.checkIn
    ? new Date(todayAttendance.checkIn)
    : new Date();
  startTime.setMinutes(0, 0, 0);

  const timeLabels: string[] = [];
  for (let i = 0; i <= visualizationWindowHours; i++) {
    const time = new Date(startTime);
    time.setHours(startTime.getHours() + i);
    timeLabels.push(formatTimeLabel(time));
  }

  const totalVisualizationHours = visualizationWindowHours;
  const productiveWidth =
    (Math.abs(productionHoursToday) / totalVisualizationHours) * 100;
  const breakWidth = (breakHoursToday / totalVisualizationHours) * 100;
  const remainingWidth = Math.max(0, 100 - productiveWidth - breakWidth);

  // Handle error state
  if (!attendanceData && !isLoading) {
    return (
      <Box className="attendance-container">
        <Typography color="error">Error loading attendance data</Typography>
      </Box>
    );
  }

  return (
    <Box className="attendance-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="attendance-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Employee Attendance</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
        </Box>

        {/* Employee Punch In & Out Card */}
        <Box
          className="AttendanceCard-wrapper"
          sx={{ display: "flex", gap: 3, width: "100%" }}
        >
          <Box
            className="AttendancePunchCard-container"
            sx={{ flex: "1 1 30%", maxWidth: "30%" }}
          >
            <AttendancePunchCard onPunch={handlePunch} />
          </Box>
          <Box
            sx={{
              flex: "1 1 70%",
              display: "flex",
              flexDirection: "column",
              gap: 2,
            }}
          >
            <Box
              className="CustomHoursCard-container"
              sx={{
                display: "flex",
                justifyContent: "space-between",
                gap: 3,
                flexWrap: "wrap",
              }}
            >
              <CustomHoursCard
                icon={
                  <AccessTime style={{ color: "#fff", fontSize: "16px" }} />
                }
                value={formatHoursToHM(totalWorkingHoursToday)}
                total="9"
                title="Total Hours Today"
                subtitle="This Week"
                color="#F57C00"
                sx={{ flex: 1 }}
              />
              <CustomHoursCard
                icon={
                  <AccessTime style={{ color: "#fff", fontSize: "16px" }} />
                }
                value={formatHoursToHM(totalWorkingHoursWeek)}
                total="40"
                title="Total Hours Week"
                subtitle="Last Week"
                color="#2E2E2E"
                sx={{ flex: 1 }}
              />
              <CustomHoursCard
                icon={
                  <AccessTime style={{ color: "#fff", fontSize: "16px" }} />
                }
                value={formatHoursToHM(totalWorkingHoursMonth)}
                total="98"
                title="Total Hours Month"
                subtitle="Last Month"
                color="#1976D2"
                sx={{ flex: 1 }}
              />
            </Box>

            {/* Working Hours Summary */}
            <Box
              className="working-hours-summary"
              sx={{
                borderRadius: 2,
                backgroundColor: "#fff",
                padding: "37px",
                boxShadow: "0px 4px 8px rgba(0,0,0,0.1)",
                marginTop: "6px",
              }}
            >
              <Box
                className="working-hours-cards"
                sx={{ display: "flex", gap: 9 }}
              >
                <Box className="working-hours-card">
                  <Typography color="textSecondary">
                    <Circle
                      sx={{
                        color: "#E8E9EA",
                        width: "7px",
                        height: "7px",
                        marginRight: ".25rem",
                      }}
                    />
                    Total Working Hours
                  </Typography>
                  <Typography variant="h6">
                    {formatHoursToHM(totalWorkingHoursToday)}
                  </Typography>
                </Box>
                <Box className="working-hours-card">
                  <Typography color="textSecondary">
                    <Circle
                      sx={{
                        color: "#03C95A",
                        width: "7px",
                        height: "7px",
                        marginRight: ".25rem",
                      }}
                    />
                    Productive Hours
                  </Typography>
                  <Typography variant="h6">
                    {formatHoursToHM(productionHoursToday)}
                  </Typography>
                </Box>
                <Box className="working-hours-card">
                  <Typography color="textSecondary">
                    <Circle
                      sx={{
                        color: "#FFC107",
                        width: "7px",
                        height: "7px",
                        marginRight: ".25rem",
                      }}
                    />
                    Break Hours
                  </Typography>
                  <Typography variant="h6">
                    {formatHoursToHM(breakHoursToday)}
                  </Typography>
                </Box>
              </Box>

              {/* Time Distribution Visualization */}
              <Box
                sx={{
                  mt: 2,
                  display: "flex",
                  flexDirection: "column",
                  width: "100%",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Box
                  sx={{
                    width: "100%",
                    display: "flex",
                    justifyContent: "flex-start",
                    gap: 0,
                    mt: 1,
                  }}
                >
                  {totalWorkingHoursToday > 0 ? (
                    <>
                      <Box
                        sx={{
                          width: `${productiveWidth}%`,
                          height: 20,
                          bgcolor: "#03C95A",
                          borderTopLeftRadius: 4,
                          borderBottomLeftRadius: 4,
                          minWidth: productionHoursToday > 0 ? "5%" : "0%",
                        }}
                      />
                      <Box
                        sx={{
                          width: `${breakWidth}%`,
                          height: 20,
                          bgcolor: "#FFC107",
                          borderRadius: 0,
                          minWidth: breakHoursToday > 0 ? "3%" : "0%",
                        }}
                      />
                      <Box
                        sx={{
                          width: `${remainingWidth}%`,
                          height: 20,
                          bgcolor: "#E8E9EA",
                          borderTopRightRadius: 4,
                          borderBottomRightRadius: 4,
                          flexGrow: 1,
                        }}
                      />
                    </>
                  ) : (
                    <Box
                      sx={{
                        width: "100%",
                        height: 20,
                        bgcolor: "#E8E9EA",
                        borderRadius: 4,
                      }}
                    />
                  )}
                </Box>

                <Box
                  className="time-labels"
                  sx={{
                    display: "flex",
                    width: "100%",
                    justifyContent: "space-between",
                    gap: 0,
                    mt: 1,
                  }}
                >
                  {timeLabels.map((label, index) => (
                    <Typography
                      key={index}
                      sx={{ fontSize: "0.75rem", color: "#6B7280" }}
                    >
                      {label}
                    </Typography>
                  ))}
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>

        <Paper sx={{ marginTop: "24px" }}>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 400,
              maxHeight: "calc(100vh - 300px)",
              width: "100%",
            }}
          >
            <Box
              className="DataGrid-header"
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "16px 20px",
              }}
            >
              <Typography variant="h5">Employee Attendance</Typography>
              <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                <PolicyFilters
                  departments={[]}
                  designations={[]}
                  selectedDepartment="Department"
                  setSelectedDepartment={() => {}}
                  selectedDesignation=""
                  setSelectedDesignation={() => {}}
                  selectedSortBy={selectedSortBy}
                  setSelectedSortBy={setSelectedSortBy}
                  selectedDateRange={selectedDateRange}
                  setSelectedDateRange={setSelectedDateRange}
                  setStartDate={setStartDate}
                  setEndDate={setEndDate}
                  selectedStatus={selectedStatus}
                  setSelectedStatus={setSelectedStatus}
                  selectedLeaveType=""
                  setSelectedLeaveType={() => {}}
                  setPage={setPage}
                  currentDate={currentDate}
                  showDateRangeFilter={true}
                  showDepartmentFilter={false}
                  showDesignationFilter={false}
                  showStatusFilter={true}
                  showSortByFilter={true}
                  showLeaveTypeFilter={false}
                  selectedPriority=""
                  setSelectedPriority={() => {}}
                />
              </Box>
            </Box>
            <Divider />
            <CustomDataGrid
              apiRef={apiRef}
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={attendanceTotal || 0}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={handlePaginationModelChange}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: QuickSearchToolbar,
              }}
              // loading={isLoading} // Uncomment if CustomDataGrid supports loading prop
              sx={{ width: "100%" }}
            />
          </Box>
        </Paper>
      </Box>
    </Box>
  );
}

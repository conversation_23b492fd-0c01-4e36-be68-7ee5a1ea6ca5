.attendance-container {
    .content {
        padding: 16px;

        @media (min-width: 768px) {
            padding: 24px;
        }

        .attendance-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;

            .breadcrumbs-box {
                h2 {
                    font-size: 24px;
                    font-weight: 700;
                    color: #202C4B;
                }
            }
        }

        .header-cards {
            .avatarGroup {
                .MuiAvatarGroup-avatar {
                    width: 22px;
                    height: 22px;
                    font-size: 12px;
                    background-color: #F26522;
                    transition: transform 0.3s ease-in-out;

                    &:hover {
                        transform: translateY(-5px);
                    }
                }

                .avatar {
                    width: 22px;
                    height: 22px;
                    transition: transform 0.3s ease-in-out;

                    &:hover {
                        transform: translateY(-5px);
                    }
                }
            }
        }

        .DataGrid-container {
            border: 1px solid #E5E7EB;
            margin-top: 8px;

            .DataGrid-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 20px;

                h5 {
                    font-size: 18px;
                    font-weight: 600;
                }

                .filter-dropdown {
                    display: flex;
                    gap: 13px;

                    button {
                        font-weight: 400;
                        font-size: 14px;
                        color: #111827;
                        border: 1px solid #E5E7EB;
                        border-radius: 5px;
                        text-transform: none;
                        padding: 8px 13.6px;
                    }

                    .sort-dropdown {
                        background-color: #F26522;
                        border-color: #F26522;
                        color: #FFF;
                    }
                }
            }

            .MuiDataGrid-root {
                .MuiBox-root {
                    display: flex;

                    // SEARCH BAR FOR GRID-DATA
                    .grid-search {
                        .MuiInputBase-root {
                            border: 1px solid #E5E7EB;
                            border-radius: 5px;

                            .MuiSvgIcon-root {
                                display: none;
                            }
                        }
                    }

                    .grid-export {
                        button {
                            font-weight: 400;
                            font-size: 14px;
                            color: #111827;
                            border: 1px solid #E5E7EB;
                            border-radius: 5px;
                            text-transform: none;
                            padding: 8px 13.6px;
                        }
                    }
                }

                .MuiDataGrid-main {
                    margin-top: 10px;

                    .MuiDataGrid-virtualScroller {
                        .MuiDataGrid-topContainer {
                            .MuiDataGrid-columnHeaders {
                                .MuiDataGrid-row--borderBottom {
                                    background: #E5E7EB;

                                    .MuiDataGrid-columnHeader {
                                        .MuiDataGrid-columnHeaderDraggableContainer {
                                            .MuiDataGrid-columnHeaderTitleContainer {
                                                .MuiDataGrid-columnHeaderTitleContainerContent {
                                                    .MuiDataGrid-columnHeaderTitle {
                                                        color: #111827;
                                                        font-size: 14px;
                                                        font-weight: 600;
                                                        padding: 10px 20px;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        .MuiDataGrid-virtualScrollerContent {
                            .MuiDataGrid-virtualScrollerRenderZone {
                                .MuiDataGrid-row {
                                    .MuiDataGrid-cell {
                                        .MuiDataGrid-cell--textLeft {
                                            font-size: 14px;
                                            color: #111827;
                                            padding: 10px 20px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                .MuiDataGrid-footerContainer {
                    .MuiTablePagination-root {
                        .MuiToolbar-root {
                            display: flex;
                            justify-content: center;

                            .MuiTablePagination-spacer {
                                display: none;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media screen and (max-width: 1200px) {
    .content {
        .AttendanceCard-wrapper {
            display: flex;
            flex-direction: column;

            .AttendancePunchCard-container {
                max-width: 100%;

                .attendance-punch-card {
                    width: 100%;
                }
            }
        }

    }
}

@media (max-width: 768px) {
    .attendance-container {
        .content {
            padding: 16px;

            .AttendanceCard-wrapper {

                .CustomHoursCard-container {
                    flex-direction: column;
                }
            }

            .working-hours-summary {
                .working-hours-cards {
                    display: flex;
                    flex-direction: column !important;
                }

                .working-hours-card {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
            }


            .time-labels {
                p {
                    font-size: 0.2rem;
                }
            }


            .DataGrid-container {
                .DataGrid-header {
                    flex-direction: column;
                    gap: 10px;

                    .filters {
                        flex-wrap: wrap;
                    }
                }

                .MuiDataGrid-root {
                    .MuiBox-root {

                        display: flex;
                        align-items: center;
                    }

                    .grid-export {
                        padding: 0px;

                        button {
                            padding: 5px 13px !important;
                        }
                    }

                    .grid-search {
                        padding: 0px;
                    }
                }


            }



            .pageSelect-dropdown {
                margin: 20px 0px;
            }


        }
    }

}
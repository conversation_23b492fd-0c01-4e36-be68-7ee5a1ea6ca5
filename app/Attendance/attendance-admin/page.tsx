"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>on,
  Divider,
  <PERSON><PERSON><PERSON>,
  Avatar,
  AvatarGroup,
  Paper,
  useMediaQuery,
} from "@mui/material";
import { useState, useEffect } from "react";
import "./attendanceAdmin.scss";
import {
  HomeOutlined,
  EditNote,
  PeopleOutline,
  Info,
  Circle,
} from "@mui/icons-material";

import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import CustomHeaderCard from "@/components/CustomHeaderCard/CustomHeaderCard";
import EditAttendance from "@/components/EditAttendance/EditAttendance";
import AttendanceReport from "@/components/AttendanceReport/AttendanceReport";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import Loader from "@/components/Loader/Loader";
import useFetchAttendanceData from "@/app/hooks/attendance/useFetchAttendanceData";
import { updateAttendance } from "@/app/services/attendance.service";
import { getDepartments } from "@/app/services/department.service";
import { getAttendanceByDate } from "@/app/services/attendance.service";
import { toast } from "react-toastify";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";

// Quick Search Toolbar Component
function QuickSearchToolbar() {
  return (
    <Box
      sx={{
        p: 0.5,
        pb: 0,
        justifyContent: "space-between",
        padding: "10px 20px",
      }}
    >
      <GridToolbarQuickFilter
        placeholder="Search"
        className="grid-search"
        sx={{ textDecoration: "none" }}
      />
      <Box
        className="grid-export"
        sx={{ display: "flex", alignItems: "center" }}
      >
        <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
      </Box>
    </Box>
  );
}

// Define interfaces
interface AttendanceRecord {
  _id: string;
  empId:
    | string
    | { _id: string; firstName: string; email: string; departmentName: string };
  date?: string;
  status?: string;
  employeeName?: string;
  departmentName?: string;
  avatar?: string;
  checkIn?: string;
  checkOut?: string;
  break?: number;
  late?: number;
  productionHours?: number;
  isActive?: boolean;
}

interface AttendanceBody {
  empId?: string;
  date?: string;
  status?: string;
  checkIn?: string;
  checkOut?: string;
  break?: number;
  late?: number;
}

// Utility function to format ISO time to HH:MM AM/PM
const formatDateTime = (isoDate?: string) => {
  if (!isoDate || isoDate === "N/A") return "N/A";
  const date = new Date(isoDate);

  // Format time in 12-hour format
  const hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const ampm = hours >= 12 ? "PM" : "AM";
  const formattedHours = hours % 12 || 12; // Convert to 12-hour format

  return `${formattedHours}:${minutes} ${ampm}`;
};

const formatDateForApi = (dateStr: string) => {
  if (!dateStr) return undefined;

  try {
    // Assuming dateStr is in format "MM/DD/YYYY"
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return undefined;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return undefined;
  }
};

// Utility function to get today's date in YYYY-MM-DD format
const getTodayDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = (today.getMonth() + 1).toString().padStart(2, "0");
  const day = today.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const formatDateToReadable = (isoDate?: string) => {
  if (!isoDate || isoDate === "N/A") return "N/A";
  try {
    const date = new Date(isoDate);
    if (isNaN(date.getTime())) return "N/A";
    
    const day = date.getDate();
    const monthNames = [
      "Jan", "Feb", "Mar", "Apr", "May", "Jun",
      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    ];
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();
    
    return `${day} ${month} ${year}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return "N/A";
  }
};

// First, let's define the interface for the new response structure
interface AttendanceSummaryResponse {
  present: {
    count: number;
    percentage: string;
    change: string;
  };
  absent: {
    count: number;
    percentage: string;
    change: string;
    employees: Array<{
      id: string;
      firstName: string;
      avatar: string;
      departmentName: string;
      designationName: string;
    }>;
  };
  onLeave: {
    count: number;
    percentage: string;
    change: string;
  };
  late: {
    count: number;
    percentage: string;
    change: string;
    employees: Array<any>;
  };
  totalEmployees: number;
  totalAttendance: number;
  todaySummary : {
    present: number;
    absent: number;
    permission: number;
    late: number;
    uninformed: number;
  }
}

export default function AttendanceAdmin() {
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Attendance", href: "" },
    { label: "Attendance Admin" },
  ];

  // State for loading and refresh
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);

  // State for pagination
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);

  const handlePaginationModelChange = (model: {
    page: number;
    pageSize: number;
  }) => {
    setPage(model.page + 1);
    setPageSize(model.pageSize);
    setLimit(model.pageSize);
  };

  // State for modals
  const [openModal, setOpenModal] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [selectedRow, setSelectedRow] = useState<AttendanceRecord | null>(null);

  // State for filters
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const [departments, setDepartments] = useState<string[]>([]);

  // State for attendance summary
  const [attendanceSummary, setAttendanceSummary] =
    useState<AttendanceSummaryResponse | null>(null);

  // Restrict access to admin roles only (Admin, SuperAdmin, Manager, HR)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // Fetch departments using getDepartments service
  useEffect(() => {
    const fetchDepartments = async () => {
      setIsLoading(true);
      try {
        const response = await getDepartments();
        if (response.success && response.departments?.results) {
          const departmentNames = response.departments.results.map(
            (dept: { departmentName: string }) => dept.departmentName
          );
          setDepartments(departmentNames);
        }
      } catch (error) {
        console.error("Failed to fetch departments:", error);
        // toast.error("Failed to load departments");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDepartments();
  }, []);

  // Fetch attendance summary for current date dynamically
  useEffect(() => {
    const fetchAttendanceSummary = async () => {
      setIsLoading(true);
      try {
        const response = await getAttendanceByDate(startDate, endDate);
        if (response.success) {
          setAttendanceSummary(response.attendenceSummary);
        }
      } catch (error) {
        console.error("Failed to fetch attendance summary:", error);
        // toast.error("Failed to fetch attendance summary");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAttendanceSummary();
  }, [startDate, endDate]);

  // Fetch attendance data
  const [attendanceData, attendanceTotal] = useFetchAttendanceData({
    setIsLoading,
    refresh,
    limit,
    page,
    departmentName:
      selectedDepartment === "Department" ? undefined : selectedDepartment,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : undefined,
    startDate: startDate,
    endDate: endDate,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
  });

  const absentEmployees = attendanceData
    ? attendanceData.filter(
        (record: AttendanceRecord) => record.status === "Absent"
      )
    : [];

  // Map API-fetched attendance data to DataGrid rows
  const rows: GridRowsProp = attendanceData
    ? attendanceData.map((item: AttendanceRecord, index: number) => ({
        id: item?._id || `row-${index}`, // Fallback ID if _id is null
        _id: item?._id || `row-${index}`,
        empId: item?.empId || "",
        avatar: item?.avatar || "",
        employee: item?.employeeName || "N/A",
        departmentName: item?.departmentName || "N/A",
        status: item?.status || (item?.isActive ? "Present" : "Absent"),
        checkIn: item?.checkIn ? formatDateTime(item.checkIn) : "-",
        checkOut: item?.checkOut ? formatDateTime(item.checkOut) : "-",
        break: item?.break !== undefined ? `${item.break} min` : "-",
        late: item.late
          ? (() => {
              const hours = Math.floor(item.late / 60);
              const minutes = item.late % 60;
              return `${hours} Hrs ${minutes} Min`;
            })()
          : "-",
        productionHours: item?.productionHours || "0.00",
        date: item?.date ? formatDateToReadable(item.date) : "-",
      }))
    : [];

  const isMobile = useMediaQuery("(max-width:768px)");

  const columns: GridColDef[] = [
    {
      field: "employee",
      headerName: "Employee",
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={params.row.avatar}
            alt={params.row.employee}
            sx={{ width: 32, height: 32 }}
          />
          <Box display={"flex"} flexDirection={"column"}>
            <Typography variant="body2" color="textPrimary">
              {params.row.employee}
            </Typography>
            {/* <Typography
              sx={{ fontSize: "12px" }}
              variant="body2"
              color="textSecondary"
            >
              {params.row.departmentName}
            </Typography> */}
          </Box>
        </Box>
      ),
    },
    {
      field: "date",
      headerName: "Date",
      flex: 1,
      renderCell: (params) => (
        <Typography variant="body2" color="textPrimary">
          {params.row.date}
        </Typography>
      ),
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      renderCell: (params) => (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          {isMobile ? (
            <Circle
              sx={{
                fontSize: 12,
                color: params.row.status === "Present" ? "#03C95A" : "#E70D0D",
              }}
            />
          ) : (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: "3px",
                backgroundColor:
                  params.row.status === "Present" ? "#D2F5E1" : "#FAE7E7",
                color: params.row.status === "Present" ? "#03C95A" : "#E70D0D",
                borderRadius: "4px",
                textAlign: "center",
                minWidth: "66px",
                minHeight: "18px",
                justifyContent: "center",
                fontSize: "10.5px",
                fontWeight: 500,
                padding: "0 0.45rem",
                lineHeight: "18px",
                letterSpacing: "0.5px",
              }}
            >
              <Box
                sx={{
                  width: "5px",
                  height: "5px",
                  borderRadius: "100%",
                  backgroundColor:
                    params.row.status === "Present" ? "#03C95A" : "#E70D0D",
                }}
              />
              {params.row.status}
            </Box>
          )}
        </Box>
      ),
    },
    { field: "checkIn", headerName: "Check In", flex: 1 },
    { field: "checkOut", headerName: "Check Out", flex: 1 },
    { field: "break", headerName: "Break", flex: 1 },
    { field: "late", headerName: "Late", flex: 1 },
    {
      field: "productionHours",
      headerName: "Production Hours",
      flex: 1,
      renderCell: (params) => (
        <Box
          sx={{
            backgroundColor: "#03C95A",
            color: "#FFF",
            borderRadius: "4px",
            display: "flex",
            textAlign: "center",
            maxWidth: "48px",
            justifyContent: "center",
            // margin: "15px 200px 0px 21px",
            fontSize: "10px",
            fontWeight: 500,
            padding: "0px 25px",
            lineHeight: "18px",
            letterSpacing: "0.5px",
          }}
        >
          {params.row.productionHours}
        </Box>
      ),
    },
    {
      field: "actions",
      disableExport: true,
      headerName: "",
      flex: 0.5,
      renderCell: (params) => (
        <Box sx={{ display: "flex" }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row)}
          >
            <EditNote sx={{ width: "25px", height: "25px" }} />
          </IconButton>
          {/* <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleInfo(params.row)}
          >
            <Info sx={{ width: "20px", height: "20px" }} />
          </IconButton> */}
        </Box>
      ),
    },
  ];

  const handleEditClick = (row: AttendanceRecord) => {
    console.log("Selected Row for Edit:", row);
    setSelectedRow(row);
    setEditModal(true);
  };

  const handleInfo = (row: AttendanceRecord) => {
    setSelectedRow(row);
    setOpenModal(true);
  };

  const handleSave = async (updatedData: AttendanceRecord) => {
    try {
      // const empId =
      //   typeof updatedData.empId === "object"
      //     ? updatedData.empId._id
      //     : updatedData.empId;

      const payload: AttendanceBody = {
        // empId,
        date: updatedData.date || new Date().toISOString(),
        checkIn: updatedData.checkIn,
        checkOut: updatedData.checkOut,
        break: updatedData.break !== undefined ? updatedData.break : undefined,
        // late: updatedData.late !== undefined ? updatedData.late : undefined,
        status: updatedData.status,
      };

      console.log(
        "Saving Attendance with ID:",
        updatedData._id,
        "Payload:",
        payload
      );
      await updateAttendance(updatedData._id, payload);
      setRefresh(!refresh);
      // toast.success("Attendance updated successfully");
    } catch (error) {
      console.error("Failed to update attendance via service:", error);
      // toast.error("Failed to update attendance");
    }
  };

  return (
    <Box className="attendance-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="attendance-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Attendance Admin</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
        </Box>

        {/* Header Cards */}
        <Box className="header-cards">
          <Box
            className="Attendance-Details-Heading"
            sx={{ display: "flex", justifyContent: "space-between" }}
          >
            <Box>
              <Typography
                sx={{
                  fontSize: "1.125rem",
                  marginBottom: ".25rem",
                  fontWeight: "600",
                  color: "#202c4b",
                }}
                variant="h4"
              >
                Attendance Details Today
              </Typography>
              <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
                Data from the &nbsp;
                {attendanceSummary?.totalEmployees || 0} total no of employees
              </Typography>
            </Box>
            <Box sx={{ display: "flex", gap: "24px", alignItems: "center" }}>
              <Typography
                sx={{
                  fontSize: "14px",
                  fontWeight: "600 !important",
                  color: "#202C4B",
                }}
                variant="h5"
              >
                Total Absenties today
              </Typography>
              <AvatarGroup
                className="avatarGroup"
                max={6}
                sx={{ display: "flex", gap: "-10px" }}
              >
                {attendanceSummary?.absent?.employees &&
                attendanceSummary.absent.employees.length > 0 ? (
                  attendanceSummary.absent.employees.map((employee) => (
                    <Avatar
                      key={employee.id}
                      className="avatar"
                      alt={employee.firstName || "Unknown"}
                      src={employee.avatar}
                    />
                  ))
                ) : (
                  <Typography sx={{ fontSize: "12px", color: "#6B7280" }}>
                    No absentees today
                  </Typography>
                )}
              </AvatarGroup>
            </Box>
          </Box>
          <Box
            className="header-counter"
            sx={{ display: "flex", justifyContent: "space-between" }}
          >
            <CustomHeaderCard
              icon={PeopleOutline}
              title="Present"
              value={attendanceSummary?.todaySummary?.present || 0}
              percentage={attendanceSummary?.present?.change || "+0.00%"}
              badgeBgColor="#03C95A"
              badgeTextColor="#FFF"
            />
            <CustomHeaderCard
              icon={PeopleOutline}
              title="Late Login"
              value={attendanceSummary?.todaySummary?.late || 0}
              percentage={attendanceSummary?.late?.change || "+0.00%"}
              iconBgColor="#03C95A"
              badgeBgColor="#E70D0D"
              badgeTextColor="#FFF"
            />
            <CustomHeaderCard
              icon={PeopleOutline}
              title="Uninformed"
              value={attendanceSummary?.todaySummary?.uninformed || 0}
              percentage={attendanceSummary?.absent?.change || "+0.00%"}
              badgeBgColor="#E70D0D"
              badgeTextColor="#FFF"
            />
            <CustomHeaderCard
              icon={PeopleOutline}
              title="Permissions"
              value={attendanceSummary?.todaySummary?.permission || 0}
              percentage={attendanceSummary?.onLeave?.change || "+0.00%"}
              badgeBgColor="#03C95A"
              badgeTextColor="#FFF"
            />
            <CustomHeaderCard
              icon={PeopleOutline}
              title="Absent"
              value={attendanceSummary?.todaySummary?.absent || 0}
              percentage={attendanceSummary?.absent?.change || "+0.00%"}
              iconBgColor="#03C95A"
              badgeBgColor="#E70D0D"
              badgeTextColor="#FFF"
            />
          </Box>
        </Box>

        {/* DataGrid */}
        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 400,
              maxHeight: "calc(100vh - 300px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Admin Attendance</Typography>
              <PolicyFilters
                departments={departments}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => {}}
                selectedLeaveType={""}
                setSelectedLeaveType={() => {}}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              // checkboxSelection
              rows={rows}
              columns={columns}
              rowCount={attendanceTotal || 0}
              paginationMode="server"
              initialState={{
                pagination: { paginationModel: { pageSize: 10 } },
              }}
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={handlePaginationModelChange}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: QuickSearchToolbar,
              }}
            />
          </Box>
        </Paper>
      </Box>

      {selectedRow && (
        <AttendanceReport
          open={openModal}
          onClose={() => setOpenModal(false)}
          data={selectedRow as AttendanceRecord}
        />
      )}
      <EditAttendance
        open={editModal}
        onClose={() => setEditModal(false)}
        rowData={
          selectedRow
            ? {
                ...selectedRow,
                empId:
                  typeof selectedRow.empId === "object"
                    ? selectedRow.empId._id
                    : selectedRow.empId,
              }
            : null
        }
        onSave={(updatedData) => {
          const transformedData: AttendanceRecord = {
            ...updatedData,
            empId: updatedData.empId || "",
            break:
              typeof updatedData.break === "string"
                ? parseFloat(updatedData.break)
                : updatedData.break,
            late:
              typeof updatedData.late === "string"
                ? parseFloat(updatedData.late)
                : updatedData.late,
          };
          handleSave(transformedData);
        }}
      />
    </Box>
  );
}

"use client";

import { useEffect, useState } from "react";
import {
  Box,
  IconButton,
  Button,
  Typography,
  Avatar,
  Paper,
  Divider,
} from "@mui/material";
import {
  HomeOutlined,
  Description,
  EditNote,
  Delete,
  EventOutlined,
  MedicalServicesOutlined,
  TokenOutlined,
  WebhookOutlined,
  ControlPoint,
} from "@mui/icons-material";
import "./leaveEmployee.scss";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import LeavesCardEmployee from "@/components/LeavesCardEmployee/LeavesCardEmployee";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import Loader from "@/components/Loader/Loader";
import { getDepartments } from "@/app/services/department.service";
import {
  addEmployeeLeave,
  updateEmployeeLeave,
  deleteEmployeeLeave,
  getEmpLeaveById,
} from "@/app/services/leave/leave.service";
import { toast } from "react-toastify";
import useAuthStore from "@/store/authStore";
import AddEditLeaveEmployee from "./AddEditLeaveEmployee";
import useFetchLeaveData from "@/app/hooks/leaves/useFetchLeaveData";
import type { LeaveBody, ApprovedBy } from "@/app/types/leave";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";

import dayjs from "dayjs";

interface ApprovedByResponse {
  username: string;
  designationName: string;
}

interface LeaveEmployee {
  _id?: string;
  id?: string;
  employeeId?: string;
  employee?: string;
  leaveType: string;
  from: string;
  to: string;
  days: number;
  leaveTime: string;
  remainingDays: number;
  status: string;
  reason: string;
  department?: string;
  approvedBy?: ApprovedBy | null;
  avatar?: string;
}

interface LeaveSummaryResponse {
  type: string;
  total: number;
  taken: number;
  remaining: number;
  approved: number;
}

function QuickSearchToolbar() {
  return (
    <Box
      sx={{
        p: 0.5,
        pb: 0,
        justifyContent: "space-between",
        padding: "10px 20px",
      }}
    >
      <GridToolbarQuickFilter
        className="grid-search"
        sx={{ textDecoration: "none" }}
        placeholder="Search"
      />
      <Box
        className="grid-export"
        sx={{ display: "flex", alignItems: "center" }}
      >
        <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
      </Box>
    </Box>
  );
}

function LeaveEmployee() {
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Employee", href: "" },
    { label: "Leaves" },
  ];

  const employeeId = useAuthStore((state) => state.employeeId);
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);
  const [totalLeaves, setTotalLeaves] = useState(0);
  const [employeeLeaveSummary, setEmployeeLeaveSummary] = useState<{
    annual: LeaveSummaryResponse;
    medical: LeaveSummaryResponse;
    casual: LeaveSummaryResponse;
    others: LeaveSummaryResponse;
    totalTakenLeaves : number;
  }>({
    annual: { type: "Annual", total: 0, taken: 0, remaining: 0, approved: 0 },
    medical: { type: "Medical", total: 0, taken: 0, remaining: 0, approved: 0 },
    casual: { type: "Casual", total: 0, taken: 0, remaining: 0, approved: 0 },
    others: { type: "Others", total: 0, taken: 0, remaining: 0, approved: 0 },
    totalTakenLeaves: 0,
  });

  const handlePaginationModelChange = (model: {
    page: number;
    pageSize: number;
  }) => {
    setPage(model.page + 1);
    setPageSize(model.pageSize);
    setLimit(model.pageSize);
  };

  const [openModal, setOpenModal] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [editLeaveId, setEditLeaveId] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState<LeaveEmployee | null>(null);

  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const [selectedLeaveType, setSelectedLeaveType] =
    useState<string>("Leave Type");
  const [departments, setDepartments] = useState<string[]>([]);

  const result = useFetchLeaveData({
    setIsLoading,
    refresh,
    limit,
    page,
    departmentName: selectedDepartment,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "", // default case when "Sort By" is selected
    empId: employeeId || "",
    startDate,
    endDate,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
  });
  const [rows, total] = result;

  // Restrict access to Employee roles only (Admin, SuperAdmin, Manager, HR, Employee)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  useEffect(() => {
    const fetchDepartments = async () => {
      setIsLoading(true);
      try {
        const response = await getDepartments();
        if (response.success && response.departments?.results) {
          const departmentNames = response.departments.results.map(
            (dept: { departmentName: string }) => dept.departmentName
          );
          setDepartments(departmentNames);
        }
      } catch (error) {
        console.error("Failed to fetch departments:", error);
        // toast.error("Failed to load departments");
      } finally {
        setIsLoading(false);
      }
    };
    fetchDepartments();
  }, []);

  useEffect(() => {
    const fetchLeaveData = async () => {
      if (employeeId) {
        setIsLoading(true);
        try {
          const response = await getEmpLeaveById(employeeId);
          if (response.success) {
            const leaveSummaryData = response.leaves.leaveSummary;

            const summaryMap = leaveSummaryData.reduce(
              (acc: any, item: LeaveSummaryResponse) => {
                acc[item.type.toLowerCase()] = item;
                return acc;
              },
              {}
            );
            const totalTakenLeaves = response.leaves.totalTakenLeaves;

            setEmployeeLeaveSummary({
              annual: summaryMap.annual || {
                type: "Annual",
                total: 0,
                taken: 0,
                remaining: 0,
                approved: 0,
              },
              medical: summaryMap.medical || {
                type: "Medical",
                total: 0,
                taken: 0,
                remaining: 0,
                approved: 0,
              },
              casual: summaryMap.casual || {
                type: "Casual",
                total: 0,
                taken: 0,
                remaining: 0,
                approved: 0,
              },
              others: summaryMap.others || {
                type: "Others",
                total: 0,
                taken: 0,
                remaining: 0,
                approved: 0,
              },
              totalTakenLeaves: totalTakenLeaves,
            });
          }
        } catch (error) {
          console.error("Failed to fetch leave data:", error);
          // toast.error("Failed to load leave data");
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchLeaveData();
  }, [employeeId]); // Add employeeId as dependency

  useEffect(() => {
    setTotalLeaves(total);
  }, [total]);

  const handleEditClick = (row: LeaveEmployee) => {
    setSelectedRow(row);
    setEditLeaveId(row._id || null);
    setEditModal(true);
  };

  const handleFormSubmit = async (values: LeaveEmployee): Promise<void> => {
    if (!employeeId) {
      // toast.error("Employee ID not found. Please log in.");
      return;
    }

    try {
      const leaveBody: LeaveBody = {
        empId: employeeId,
        leaveType: values.leaveType,
        leaveFrom: values.from,
        leaveTo: values.to,
        leaveTime: values.leaveTime,
        remainingDays: values.remainingDays,
        leaveStatus: values.status,
        reason: values.reason,
      };

      if (values._id) {
        await updateEmployeeLeave(values._id, leaveBody);
        toast.success("Leave updated successfully");
      } else {
        await addEmployeeLeave(leaveBody);
        toast.success("Leave added successfully");
      }
      setRefresh(!refresh);
      setOpenModal(false);
      setEditModal(false);
      setEditLeaveId(null);
    } catch (error) {
      console.error("Failed to save leave:", error);
      // toast.error("Failed to save leave");
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteEmployeeLeave(id);
      setDeleteDialogOpen(false);
      toast.success("Leave deleted successfully");
      setRefresh(!refresh);
    } catch (error) {
      console.error("Failed to delete leave:", error);
      toast.error("Failed to delete leave");
    }
  };

  const handleDeleteClick = (row: LeaveEmployee) => {
    setSelectedRow(row);
    setDeleteDialogOpen(true);
  };

  const bgImages = {
    annual: "/assets/bg-black-le.svg",
    medical: "/assets/bg-blue-le.svg",
    casual: "/assets/bg-purple-le.svg",
    other: "/assets/bg-pink-le.svg",
  };
  const leaveEmpIcon = {
    annual: <EventOutlined sx={{ width: "32px", height: "32px" }} />,
    medical: <MedicalServicesOutlined sx={{ width: "32px", height: "32px" }} />,
    casual: <TokenOutlined sx={{ width: "32px", height: "32px" }} />,
    other: <WebhookOutlined sx={{ width: "32px", height: "32px" }} />,
  };

  const columns: GridColDef[] = [
    { field: "leaveType", headerName: "Leave Type", editable: true, flex: 1 },
    { field: "from", headerName: "From", editable: true, flex: 1 },
    {
      field: "approvedBy",
      headerName: "Approved By",
      flex: 1,
      renderCell: (params: { row: LeaveEmployee }) => {
        const approvedBy = params.row.approvedBy;
        if (!approvedBy) {
          return (
            <Typography variant="body2" color="textSecondary">
              Pending Approval
            </Typography>
          );
        }

        return (
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Avatar
              src={params.row.avatar}
              alt={approvedBy.firstName}
              sx={{ width: 32, height: 32 }}
            />
            <Box display="flex" flexDirection="column">
              <Typography variant="body2" color="textPrimary">
                {approvedBy.firstName}
              </Typography>
              {/* <Typography
                sx={{ fontSize: "12px" }}
                variant="body2"
                color="textSecondary"
              >
                {approvedBy.designationName}
              </Typography> */}
            </Box>
          </Box>
        );
      },
    },
    { field: "to", headerName: "To", flex: 1 },
    { field: "days", headerName: "No. of Days", flex: 1 },
    { field: "status", headerName: "Status", editable: false, flex: 1 },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 1,
      renderCell: (params: { row: LeaveEmployee }) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Box className="attendance-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="attendance-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Leaves</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="report"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => setOpenModal(true)}
            >
              <ControlPoint sx={{ fontSize: "14px" }} />
              Add Leave
            </Button>
          </Box>
        </Box>

        <Box sx={{ display: "flex", gap: 2 }}>
          <LeavesCardEmployee
            title="Annual Leaves"
            value={employeeLeaveSummary.annual.taken.toString()}
            bgImage={bgImages.annual}
            remainingLeaveValue={employeeLeaveSummary.annual.remaining.toString()}
            icon={leaveEmpIcon.annual}
            bgcolor="rgb(237, 242, 244)"
            textcolor="rgb(59, 112, 128)"
          />
          <LeavesCardEmployee
            title="Medical Leaves"
            value={employeeLeaveSummary.medical.taken.toString()}
            bgImage={bgImages.medical}
            remainingLeaveValue={employeeLeaveSummary.medical.remaining.toString()}
            icon={leaveEmpIcon.medical}
            bgcolor="rgb(214, 233, 255)"
            textcolor="rgb(27, 132, 255)"
          />
          <LeavesCardEmployee
            title="Casual Leaves"
            value={employeeLeaveSummary.casual.taken.toString()}
            bgImage={bgImages.casual}
            remainingLeaveValue={employeeLeaveSummary.casual.remaining.toString()}
            icon={leaveEmpIcon.casual}
            bgcolor="rgb(171, 71, 188)"
            textcolor="rgb(247, 238, 249)"
          />
          <LeavesCardEmployee
            title="Other Leaves"
            value={employeeLeaveSummary.others.taken.toString()}
            bgImage={bgImages.other}
            remainingLeaveValue={employeeLeaveSummary.others.remaining.toString()}
            icon={leaveEmpIcon.other}
            bgcolor="rgb(255, 219, 236)"
            textcolor="rgb(253, 57, 149)"
          />
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 400,
              maxHeight: "calc(100vh - 300px)",
            }}
          >
            <Box className="DataGrid-header">
              <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
                <Typography variant="h5">Leave List</Typography>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: "#FEF1EB",
                    color: "#F26522",
                    borderRadius: "4px",
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      fontSize: "10.5px",
                      fontWeight: 600,
                      letterSpacing: "0.5px",
                      padding: "0.25rem 0.45rem",
                    }}
                  >
                    Total Leave: {employeeLeaveSummary.totalTakenLeaves}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: "#EDF2F4",
                    color: "#3B7080",
                    borderRadius: "4px",
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      fontSize: "10.5px",
                      fontWeight: 600,
                      letterSpacing: "0.5px",
                      padding: "0.25rem 0.45rem",
                    }}
                  >
                    Total Remaining Leaves:{" "}
                    {employeeLeaveSummary.annual.remaining +
                      employeeLeaveSummary.medical.remaining +
                      employeeLeaveSummary.casual.remaining +
                      employeeLeaveSummary.others.remaining}
                  </Typography>
                </Box>
              </Box>
              <PolicyFilters
                departments={departments}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                selectedLeaveType={selectedLeaveType}
                setSelectedLeaveType={setSelectedLeaveType}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => {}}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              //checkboxSelection
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={handlePaginationModelChange}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: QuickSearchToolbar,
              }}
              getRowId={(row) => row._id}
            />
          </Box>
        </Paper>
      </Box>

      <AddEditLeaveEmployee
        open={openModal || editModal}
        onClose={() => {
          setOpenModal(false);
          setEditModal(false);
          setEditLeaveId(null);
        }}
        onSubmit={handleFormSubmit}
        leaveId={editLeaveId || undefined}
        employeeId={employeeId || ""}
        employeeLeaveSummary={employeeLeaveSummary}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={() => handleDelete(selectedRow?._id || "")}
        message="Are you sure you want to delete this leave? This action cannot be undone."
      />
    </Box>
  );
}

// Helper functions for colors
const getBackgroundColor = (leaveType: string): string => {
  const colors: { [key: string]: string } = {
    Annual: "rgb(237, 242, 244)",
    Medical: "rgb(214, 233, 255)",
    Casual: "rgb(171, 71, 188)",
    Others: "rgb(255, 219, 236)",
  };
  return colors[leaveType] || colors["Others"];
};

const getTextColor = (leaveType: string): string => {
  const colors: { [key: string]: string } = {
    Annual: "rgb(59, 112, 128)",
    Medical: "rgb(27, 132, 255)",
    Casual: "rgb(247, 238, 249)",
    Others: "rgb(253, 57, 149)",
  };
  return colors[leaveType] || colors["Others"];
};

export default LeaveEmployee;

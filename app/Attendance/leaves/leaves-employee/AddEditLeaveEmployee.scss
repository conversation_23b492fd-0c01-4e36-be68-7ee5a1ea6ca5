.dialog-content {

  padding: 16px !important;

  .input-field-box {
    margin-bottom: 1rem !important;
  }

  label {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #202C4B !important;
    display: block;
    margin: 0 0 0.5rem 0 !important;
  }

  .MuiInputBase-root {
    padding: 5.5px 14px !important;
    height: 36px !important;
    box-sizing: border-box;

    &.MuiInputBase-multiline {
      height: auto !important;
      padding: 5.5px 14px !important;
    }

    .MuiInputBase-input {
      padding: 0 !important;
      height: 100% !important;
      box-sizing: border-box;
    }
  }

  .MuiAutocomplete-inputRoot {
    padding: 5.5px 14px !important;
    height: 36px !important;
    box-sizing: border-box;

    .MuiAutocomplete-input {
      padding: 0 !important;
      height: 100% !important;
    }
  }



  .jodit-wrapper {
    .jodit-container {
      border: 1px solid rgba(0, 0, 0, 0.23);
      border-radius: 4px;

      &:hover {
        border-color: rgba(0, 0, 0, 0.87);
      }

      &:focus-within {
        border-color: #1976d2;
        border-width: 2px;
      }
    }

    .jodit-workplace {
      min-height: 200px;
      max-height: 400px;
    }

    .jodit-toolbar__box {
      background: #f5f5f5;
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }

    &.error {
      .jodit-container {
        border-color: #d32f2f;
      }
    }
  }
}
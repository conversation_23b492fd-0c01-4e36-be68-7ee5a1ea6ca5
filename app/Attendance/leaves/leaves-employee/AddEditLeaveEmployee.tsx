"use client";

import React, { useEffect, useState } from "react";
import "./AddEditLeaveEmployee.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  MenuItem,
  IconButton,
  Box,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { Formik, Form, Field, useFormikContext } from "formik";
import * as Yup from "yup";
import dayjs from "dayjs";
import { getLeaveById } from "@/app/services/leave/leave.service";
import type { LeaveEmployee } from "@/app/types/leave";
import JoditEditorWrapper from "@/components/JoditEditorWrapper";

interface LeaveSummaryResponse {
  type: string;
  total: number;
  taken: number;
  remaining: number;
  approved: number;
}

interface LeaveFormValues extends LeaveEmployee {
  employeeId: string;
  leaveType: string;
  from: string;
  to: string;
  days: number;
  leaveTime: string;
  remainingDays: number;
  status: string;
  reason: string;
  approvedBy?: {
    firstName: string;
    designationName: string;
  } | null;
}

interface LeaveDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: LeaveFormValues) => Promise<void>;
  leaveId?: string;
  employeeId?: string;
  employeeLeaveSummary?: {
    annual: LeaveSummaryResponse;
    medical: LeaveSummaryResponse;
    casual: LeaveSummaryResponse;
    others: LeaveSummaryResponse;
  };
}

const leaveTypes = ["Medical", "Casual", "Annual", "Others"];
const leaveTimeOptions = ["Fullday", "Halfday"];
const statusOptions = ["Pending"];

const validationSchema = Yup.object({
  leaveType: Yup.string().required("Leave Type is required"),
  from: Yup.date()
    .required("Start Date is required")
    .min(
      dayjs().startOf("day").toDate(),
      "Start date must be today or a future date"
    )
    .typeError("Please enter a valid date"),
  to: Yup.date()
    .required("End Date is required")
    .min(
      dayjs().startOf("day").toDate(),
      "End date must be today or a future date"
    )
    .test(
      "is-after-start",
      "End date must be equal to or after start date",
      function (value) {
        const { from } = this.parent;
        if (!from || !value) return true;
        return (
          dayjs(value).startOf("day").diff(dayjs(from).startOf("day")) >= 0
        );
      }
    )
    .typeError("Please enter a valid date"),
  leaveTime: Yup.string().required("Leave Time is required"),
  reason: Yup.string()
    .required("Reason is required")
    .test("not-empty-html", "Reason is required", (value) => {
      if (!value) return false;
      // Remove HTML tags and check if there's actual content
      const textContent = value.replace(/<[^>]*>/g, "").trim();
      return textContent.length > 0;
    }),
});

// Separate component to handle form content and access Formik context
const FormContent: React.FC<{
  loading: boolean;
  employeeLeaveSummary?: LeaveDialogProps["employeeLeaveSummary"];
}> = ({ loading, employeeLeaveSummary }) => {
  const { values, setFieldValue, errors, touched } =
    useFormikContext<LeaveFormValues>();

  // Update remainingDays when leaveType changes
  useEffect(() => {
    if (values.leaveType && employeeLeaveSummary) {
      const leaveTypeLower =
        values.leaveType.toLowerCase() as keyof typeof employeeLeaveSummary;
      const remainingDays =
        employeeLeaveSummary[leaveTypeLower]?.remaining || 0;
      setFieldValue("remainingDays", remainingDays);
    }
  }, [values.leaveType, employeeLeaveSummary, setFieldValue]);

  // Calculate days whenever from/to dates change
  useEffect(() => {
    if (values.from && values.to) {
      const start = dayjs(values.from);
      const end = dayjs(values.to);
      let days = end.diff(start, "day") + 1;
      if (values.leaveTime === "Halfday" && days === 1) {
        days = 0.5;
      }
      if (values.days !== (days >= 0 ? days : 0)) {
        setFieldValue("days", days >= 0 ? days : 0);
      }
    }
  }, [values.from, values.to, values.leaveTime, setFieldValue]);

  return (
    <DialogContent className="dialog-content">
      {loading ? (
        <Box>Loading...</Box>
      ) : (
        <Box sx={{ display: "flex", flexDirection: "column" }}>
          <Box className="input-field-box">
            <label>
              Leave Type <span className="required">*</span>
            </label>
            <Field
              as={TextField}
              select
              fullWidth
              name="leaveType"
              error={touched.leaveType && Boolean(errors.leaveType)}
              helperText={touched.leaveType && errors.leaveType}
            >
              {leaveTypes.map((type) => (
                <MenuItem key={type} value={type}>
                  {type}
                </MenuItem>
              ))}
            </Field>
          </Box>

          <Box sx={{ display: "flex", gap: 2, width: "100%" }}>
            <Box sx={{ flex: 1 }} className="input-field-box">
              <label>
                From <span className="required">*</span>
              </label>
              <Field
                as={TextField}
                type="date"
                name="from"
                value={values.from}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFieldValue("from", e.target.value)
                }
                fullWidth
                error={touched.from && Boolean(errors.from)}
                helperText={touched.from && errors.from}
              />
            </Box>
            <Box sx={{ flex: 1 }} className="input-field-box">
              <label>
                To <span className="required">*</span>
              </label>
              <Field
                as={TextField}
                type="date"
                name="to"
                value={values.to}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setFieldValue("to", e.target.value)
                }
                fullWidth
                error={touched.to && Boolean(errors.to)}
                helperText={touched.to && errors.to}
              />
            </Box>
          </Box>

          <Box className="input-field-box">
            <label>Leave Time</label>
            <Field
              as={TextField}
              select
              fullWidth
              name="leaveTime"
              error={touched.leaveTime && Boolean(errors.leaveTime)}
              helperText={touched.leaveTime && errors.leaveTime}
            >
              {leaveTimeOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Field>
          </Box>

          <Box sx={{ display: "flex", gap: 2, width: "100%" }}>
            <Box sx={{ flex: 1 }} className="input-field-box">
              <label>No of Days</label>
              <Field
                as={TextField}
                fullWidth
                name="days"
                value={values.days}
                InputProps={{ readOnly: true }}
              />
            </Box>
            <Box sx={{ flex: 1 }} className="input-field-box">
              <label>Remaining Days</label>
              <Field
                as={TextField}
                fullWidth
                name="remainingDays"
                value={values.remainingDays}
                InputProps={{ readOnly: true }}
                error={touched.remainingDays && Boolean(errors.remainingDays)}
                helperText={touched.remainingDays && errors.remainingDays}
              />
            </Box>
          </Box>

          <Box className="input-field-box">
            <label>Reason</label>
            <JoditEditorWrapper
              value={values.reason}
              onChange={(content) => setFieldValue("reason", content)}
              height={100}
            />
            {touched.reason && errors.reason && (
              <div className="error-message">{errors.reason}</div>
            )}
          </Box>
        </Box>
      )}
    </DialogContent>
  );
};

const AddEditLeaveEmployee: React.FC<LeaveDialogProps> = ({
  open,
  onClose,
  onSubmit,
  leaveId,
  employeeId,
  employeeLeaveSummary,
}) => {
  const [fetchedLeaveData, setFetchedLeaveData] =
    useState<LeaveEmployee | null>(null);
  const [loading, setLoading] = useState(false);
  const isEditing = Boolean(leaveId);

  // Fetch leave data when editing
  useEffect(() => {
    if (isEditing && leaveId && open) {
      const fetchLeaveData = async () => {
        setLoading(true);
        try {
          const response = await getLeaveById(leaveId);
          const leaveData = response.leaves;

          const transformedData: LeaveEmployee = {
            _id: leaveData._id,
            employeeId: leaveData.empId,
            leaveType: leaveData.leaveType,
            from: dayjs(leaveData.leaveFrom).format("YYYY-MM-DD"),
            to: dayjs(leaveData.leaveTo).format("YYYY-MM-DD"),
            days: leaveData.leaveDays,
            leaveTime: leaveData.leaveTime,
            remainingDays: 0,
            status: leaveData.leaveStatus,
            reason: leaveData.reason,
            approvedBy: leaveData.approvedBy,
          };

          setFetchedLeaveData(transformedData);
        } catch (error) {
          console.error("Failed to fetch leave data:", error);
        } finally {
          setLoading(false);
        }
      };

      fetchLeaveData();
    } else {
      setFetchedLeaveData(null);
    }
  }, [isEditing, leaveId, open]);

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth={false}
      sx={{ "& .MuiDialog-paper": { maxWidth: "800px" } }}
      fullWidth
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px",
        }}
      >
        {isEditing ? "Edit Leave" : "Add Leave"}
        <IconButton
          onClick={onClose}
          sx={{
            backgroundColor: "#6b7280",
            backgroundImage: "none",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": {
              backgroundColor: "#d55a1d",
            },
            "& .MuiSvgIcon-root": {
              fontSize: "14px",
            },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <Formik
        enableReinitialize
        initialValues={
          isEditing && fetchedLeaveData
            ? ({
                ...fetchedLeaveData,
                remainingDays:
                  employeeLeaveSummary?.[
                    fetchedLeaveData.leaveType.toLowerCase() as keyof typeof employeeLeaveSummary
                  ]?.remaining || 0,
              } as LeaveFormValues)
            : {
                employeeId: employeeId || "",
                leaveType: "",
                from: "",
                to: "",
                days: 0,
                leaveTime: "Fullday",
                remainingDays: 0,
                status: "Pending",
                reason: "",
                approvedBy: null,
              }
        }
        validationSchema={validationSchema}
        onSubmit={async (values) => {
          const days = dayjs(values.to).diff(dayjs(values.from), "day") + 1;
          await onSubmit({
            ...values,
            _id: isEditing ? leaveId : undefined,
            employeeId: employeeId || values.employeeId,
            days: days >= 0 ? days : 0,
          } as LeaveFormValues);
        }}
      >
        {() => (
          <Form>
            <FormContent
              loading={loading}
              employeeLeaveSummary={employeeLeaveSummary}
            />
            <DialogActions>
              <Button
                sx={{
                  fontWeight: "400",
                  fontSize: "14px",
                  color: "#111827",
                  border: "1px solid #E5E7EB",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                onClick={onClose}
                variant="outlined"
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                sx={{
                  display: "flex",
                  gap: "8px",
                  backgroundColor: "#F26522",
                  borderColor: "#F26522",
                  color: "#FFF",
                  fontWeight: 400,
                  fontSize: "14px",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                type="submit"
                variant="contained"
                disabled={loading}
              >
                {isEditing ? "Update Leave" : "Add Leave"}
              </Button>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default AddEditLeaveEmployee;

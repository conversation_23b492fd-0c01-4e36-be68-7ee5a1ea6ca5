"use client";
import React, { useState, useEffect } from "react";
import "./NewCustomPolicyDialog.scss";
import { Close } from "@mui/icons-material";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  MenuItem,
  Select,
  FormControl,
  IconButton,
  Box,
  Autocomplete,
  Checkbox,
  FormHelperText,
  Chip,
} from "@mui/material";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { addLeaveSetting } from "@/app/services/leave/leave.service";
import { getAllDesignations } from "@/app/services/designation.service";
import { toast } from "react-toastify";

const validationSchema = Yup.object().shape({
  leaveType: Yup.string().required("Leave Type is required"),
  policyName: Yup.string().required("Policy Name is required"),
  noOfDays: Yup.number()
    .required("Number of Days is required")
    .positive()
    .integer(),
  designations: Yup.array()
    .min(1, "At least one designation must be selected")
    .required("Required"),
});

interface NewCustomPolicyDialogProps {
  open: boolean;
  onClose: () => void;
  refreshLeaveSettings: () => void;
}

interface Designation {
  _id: string;
  designationName: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

const NewCustomPolicyDialog: React.FC<NewCustomPolicyDialogProps> = ({
  open,
  onClose,
  refreshLeaveSettings,
}) => {
  const [designations, setDesignations] = useState<Designation[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      const fetchDesignations = async () => {
        setLoading(true);
        try {
          const response = await getAllDesignations();
          if (response.designations && response.designations.results) {
            setDesignations(
              response.designations.results.filter(
                (designation: Designation) =>
                  designation.isActive && !designation.isDeleted
              )
            );
          }
        } catch (error) {
          console.error("Failed to fetch designations:", error);
          toast.error("Failed to load designations");
        } finally {
          setLoading(false);
        }
      };
      fetchDesignations();
    }
  }, [open]);

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth="sm"
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px",
        }}
      >
        New Custom Policy
        <IconButton
          onClick={onClose}
          sx={{
            backgroundColor: "#6b7280",
            backgroundImage: "none",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": {
              backgroundColor: "#d55a1d",
            },
            "& .MuiSvgIcon-root": {
              fontSize: "14px",
            },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <Formik
        initialValues={{
          leaveType: "",
          policyName: "",
          noOfDays: "",
          designations: [] as string[],
        }}
        validationSchema={validationSchema}
        onSubmit={async (values, { resetForm }) => {
          setLoading(true);
          const payload = {
            leaveType: values.leaveType,
            policyName: values.policyName,
            days: Number(values.noOfDays),
            designations: values.designations,
          };
          try {
            await addLeaveSetting(payload);
            toast.success("Leave policy added successfully");
            resetForm();
            onClose();
            refreshLeaveSettings();
          } catch (error) {
            console.error("Failed to add leave policy:", error);
            toast.error("Failed to add leave policy");
          } finally {
            setLoading(false);
          }
        }}
      >
        {({
          values,
          handleChange,
          handleSubmit,
          errors,
          touched,
          setFieldValue,
        }) => (
          <Form onSubmit={handleSubmit}>
            <DialogContent className="dialog-content">
              <FormControl
                fullWidth
                error={touched.leaveType && Boolean(errors.leaveType)}
              >
                <label htmlFor="leaveType">
                  Leave Type <span className="required">*</span>
                </label>
                <Select
                  name="leaveType"
                  value={values.leaveType}
                  onChange={handleChange}
                >
                  <MenuItem value="Medical">Medical Leave</MenuItem>
                  <MenuItem value="Casual">Casual Leave</MenuItem>
                  <MenuItem value="Annual">Annual Leave</MenuItem>
                  <MenuItem value="Others">Others Leave</MenuItem>
                </Select>
                {touched.leaveType && errors.leaveType && (
                  <FormHelperText>{errors.leaveType}</FormHelperText>
                )}
              </FormControl>

              <Box display="flex" gap={2} mt={2} width="100%">
                <Box flex={1}>
                  <label htmlFor="policyName">
                    Policy Name <span className="required">*</span>
                  </label>
                  <TextField
                    fullWidth
                    name="policyName"
                    value={values.policyName}
                    onChange={handleChange}
                    error={touched.policyName && Boolean(errors.policyName)}
                    helperText={touched.policyName && errors.policyName}
                  />
                </Box>
                <Box flex={1}>
                  <label htmlFor="noOfDays">
                    Number of Days <span className="required">*</span>
                  </label>
                  <TextField
                    fullWidth
                    type="number"
                    name="noOfDays"
                    value={values.noOfDays}
                    onChange={handleChange}
                    error={touched.noOfDays && Boolean(errors.noOfDays)}
                    helperText={touched.noOfDays && errors.noOfDays}
                  />
                </Box>
              </Box>

              <FormControl fullWidth margin="dense">
                <label htmlFor="designations">
                  Add Designation <span className="required">*</span>
                </label>
                <Autocomplete
                  multiple
                  id="designations-autocomplete"
                  options={[
                    {
                      _id: "select-all",
                      designationName: "Select All",
                      departmentName: "",
                      isActive: true,
                      isDeleted: false,
                    },
                    ...designations,
                  ]}
                  getOptionLabel={(option) => option.designationName}
                  value={designations.filter((designation) =>
                    values.designations.includes(designation._id)
                  )}
                  onChange={(event, newValue) => {
                    const selectAllClicked = newValue.some(
                      (item) => item._id === "select-all"
                    );
                    if (selectAllClicked) {
                      if (values.designations.length === designations.length) {
                        setFieldValue("designations", []);
                      } else {
                        setFieldValue(
                          "designations",
                          designations.map((d) => d._id)
                        );
                      }
                    } else {
                      setFieldValue(
                        "designations",
                        newValue.map((designation) => designation._id)
                      );
                    }
                  }}
                  disabled={loading}
                  renderOption={(props, option, { selected }) => {
                    if (option._id === "select-all") {
                      const allSelected =
                        values.designations.length === designations.length;
                      const someSelected =
                        values.designations.length > 0 &&
                        values.designations.length < designations.length;
                      return (
                        <li
                          {...props}
                          onClick={(e) => {
                            e.preventDefault();
                            if (allSelected) {
                              setFieldValue("designations", []);
                            } else {
                              setFieldValue(
                                "designations",
                                designations.map((d) => d._id)
                              );
                            }
                            const autocompleteInput = document.getElementById(
                              "designations-autocomplete"
                            );
                            if (autocompleteInput) {
                              (autocompleteInput as HTMLElement).blur();
                            }
                          }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            <Checkbox
                              checked={allSelected}
                              indeterminate={someSelected}
                            />
                            <span style={{ fontWeight: "bold" }}>
                              Select All Designations
                            </span>
                          </Box>
                        </li>
                      );
                    }
                    return (
                      <li {...props}>
                        <Box
                          sx={{ display: "flex", alignItems: "center", gap: 1 }}
                        >
                          <Checkbox checked={selected} />
                          <span>{option.designationName}</span>
                          <span
                            style={{ color: "#6b7280", fontSize: "0.8rem" }}
                          >
                            ({option.departmentName})
                          </span>
                        </Box>
                      </li>
                    );
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      error={
                        touched.designations && Boolean(errors.designations)
                      }
                      helperText={touched.designations && errors.designations}
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => {
                      const { key, onDelete, ...otherTagProps } = getTagProps({
                        index,
                      });
                      return (
                        <Chip
                          key={option._id}
                          label={option.designationName}
                          size="small"
                          sx={{
                            backgroundColor: "#F26522",
                            color: "#FFF",
                            margin: "2px",
                            borderRadius: "4px",
                            "& .MuiChip-label": {
                              fontSize: "12px",
                              fontWeight: 500,
                            },
                            "& .MuiChip-deleteIcon": {
                              color: "#FFF",
                              "&:hover": {
                                color: "#FFF",
                              },
                            },
                          }}
                          onDelete={() => {
                            const newDesignations = values.designations.filter(
                              (id) => id !== option._id
                            );
                            setFieldValue("designations", newDesignations);
                          }}
                          {...otherTagProps}
                        />
                      );
                    })
                  }
                />
              </FormControl>
            </DialogContent>

            <DialogActions>
              <Button
                onClick={onClose}
                color="secondary"
                sx={{
                  textTransform: "none",
                  borderColor: "#ccc",
                  color: "#000",
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={loading}
                sx={{
                  backgroundColor: "#F26522",
                  color: "#FFF",
                  textTransform: "none",
                  "&:hover": { backgroundColor: "#d55a1d" },
                }}
              >
                Add Leave
              </Button>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default NewCustomPolicyDialog;

.leave-settings-container {
  // Content container
  .content {
    padding: var(--spacing-md);

    // Header styling
    .settings-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-md);

      .breadcrumbs-box {
        h2 {
          font-size: 24px;
          font-weight: 700;
          color: var(--text-dark);
        }
      }
    }

    // Typography styling
    .MuiTypography-root {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-dark);
    }

    // Switch styling
    .MuiSwitch-root {
      .MuiSwitch-thumb {
        background-color: white;
        width: 11px;
        height: 11px;
      }

      .Mui-checked {
        color: var(--primary-color);

        + .MuiSwitch-track {
          background-color: var(--primary-color);
        }
      }
    }

    .MuiButtonBase-root {
      padding: 13px;
    }

    // Card grid layout
    .settings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: var(--spacing-md);
    }
  }
}

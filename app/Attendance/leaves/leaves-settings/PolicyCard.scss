.policy-card {
  // Avatar group styling
  .avatarGroup {
    padding: var(--spacing-sm) var(--spacing-md);

    .MuiAvatarGroup-avatar {
      width: var(--avatar-sm);
      height: var(--avatar-sm);
      font-size: 12px;
      background-color: var(--primary-color);
      transition: transform 0.3s ease-in-out;

      &:hover {
        transform: translateY(-5px);
        z-index: 2;
      }
    }

    .avatar {
      width: var(--avatar-sm);
      height: var(--avatar-sm);
      transition: transform 0.3s ease-in-out;

      &:hover {
        transform: translateY(-5px);
      }
    }
  }

  // Form styling
  label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
  }

  // Input styling
  input {
    padding: 8px 12px;
    border-color: var(--border-color);
  }

  .MuiSelect-select {
    padding: 8px 12px;
  }

  // Card content styling
  .MuiCardContent-root {
    padding: var(--spacing-md) !important;
  }
}

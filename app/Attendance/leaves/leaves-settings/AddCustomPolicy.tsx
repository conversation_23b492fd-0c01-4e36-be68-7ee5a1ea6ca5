"use client";
import React, { useState, useEffect } from "react";
import "./AddCustomPolicy.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  List,
  ListItem,
  ListItemText,
  Box,
  IconButton,
  Divider,
  Typography,
  Tooltip,
} from "@mui/material";
import {
  FastForward,
  FastRewind,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  Close,
} from "@mui/icons-material";
import { useFormik } from "formik";
import * as Yup from "yup";
import { getAllDesignations } from "@/app/services/designation.service";
import { constants } from "fs/promises";

interface AddCustomPolicyProps {
  open: boolean;
  onClose: () => void;
  initialData?: LeaveSetting;
  onSubmit: (payload: {
    leaveType: string;
    policyName: string;
    days: number;
    designations: string[];
  }) => void;
}

interface LeaveSetting {
  _id: string;
  leaveType: string;
  policyName: string;
  days: number;
  designations: string[];
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface Designation {
  _id: string;
  designationName: string;
}

const AddCustomPolicyDialog: React.FC<AddCustomPolicyProps> = ({
  open,
  onClose,
  initialData,
  onSubmit,
}) => {
  const [availableDesignations, setAvailableDesignations] = useState<
    Designation[]
  >([]);
  const [selectedDesignations, setSelectedDesignations] = useState<
    Designation[]
  >([]);
  const [selectedFromAvailable, setSelectedFromAvailable] = useState<string[]>(
    []
  );
  const [selectedFromSelected, setSelectedFromSelected] = useState<string[]>(
    []
  );
  const [selectedItem, setSelectedItem] = useState<string | null>(null);

  // Validation schema using Yup
  const validationSchema = Yup.object({
    leaveType: Yup.string().required("Leave Type is required"),
    policyName: Yup.string().required("Policy Name is required"),
    days: Yup.number()
      .required("Days is required")
      .positive("Days must be a positive number")
      .integer("Days must be an integer"),
  });

  // Initialize Formik
  const formik = useFormik({
    initialValues: {
      leaveType: initialData?.leaveType || "Medical",
      policyName: initialData?.policyName || "",
      days: initialData?.days.toString() || "",
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values) => {
      // Define the payload with designation IDs
      const payload = {
        leaveType: values.leaveType,
        policyName: values.policyName,
        days: parseInt(values.days) || 0,
        designations: selectedDesignations.map(
          (designation) => designation._id
        ),
      };

      // Log payload for debugging (remove in production)
      console.log("Submitting payload:", payload);

      // Pass the payload to the onSubmit prop
      onSubmit(payload);

      // Close the dialog
      onClose();
    },
  });

  // Fetch designations when dialog opens
  useEffect(() => {
    const fetchDesignations = async () => {
      try {
        const data = await getAllDesignations();
        const allDesignations = data.designations.results; // Array of { _id, designationName }

        // Initialize selected designations from initialData (if provided)
        const selectedDesignationIds: string[] =
          initialData?.designations || [];
        const selectedDesignations = allDesignations.filter(
          (designation: Designation) =>
            selectedDesignationIds.includes(designation._id)
        );

        setSelectedDesignations(selectedDesignations);
        setAvailableDesignations(
          allDesignations.filter(
            (designation: Designation) =>
              !selectedDesignationIds.includes(designation._id)
          )
        );
      } catch (error) {
        console.error("Failed to fetch designations:", error);
      }
    };

    if (open) {
      fetchDesignations();
    }
  }, [open, initialData]);

  const moveSelectedDesignations = (
    from: Designation[],
    to: Designation[],
    setFrom: React.Dispatch<React.SetStateAction<Designation[]>>,
    setTo: React.Dispatch<React.SetStateAction<Designation[]>>,
    selectedIds: string[]
  ) => {
    const selected = from.filter((designation) =>
      selectedIds.includes(designation._id)
    );
    setTo([...to, ...selected]);
    setFrom(
      from.filter((designation) => !selectedIds.includes(designation._id))
    );
    selectedIds.length = 0; // Clear the selection
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth="md"
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          padding: "16px !important",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            fontSize: "20px !important",
            fontWeight: "600 !important",
            color: "rgb(32, 44, 75)",
          }}
        >
          {initialData ? "Edit" : "Add"} Custom Policy
        </Typography>
        <Tooltip title="Close" arrow placement="top">
          <IconButton
            onClick={onClose}
            aria-label="close"
            sx={{
              backgroundColor: "#6b7280",
              backgroundImage: "none",
              borderRadius: "50%",
              color: "#fff",
              height: "20px",
              width: "20px",
              margin: 0,
              padding: 0,
              "&:hover": {
                backgroundColor: "#d55a1d",
              },
              "& .MuiSvgIcon-root": {
                fontSize: "14px",
              },
            }}
          >
            <Close />
          </IconButton>
        </Tooltip>
      </DialogTitle>

      <Divider />

      <DialogContent className="dialog-content">
        <form onSubmit={formik.handleSubmit}>
          <Box mb={2}>
            <label>Leave Type</label>
            <TextField
              fullWidth
              required
              name="leaveType"
              value={formik.values.leaveType}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.leaveType && Boolean(formik.errors.leaveType)
              }
              helperText={formik.touched.leaveType && formik.errors.leaveType}
            />
          </Box>
          <Box mb={2}>
            <label>Policy Name</label>
            <TextField
              fullWidth
              required
              name="policyName"
              value={formik.values.policyName}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.policyName && Boolean(formik.errors.policyName)
              }
              helperText={formik.touched.policyName && formik.errors.policyName}
            />
          </Box>
          <Box mb={2}>
            <label>Days</label>
            <TextField
              fullWidth
              required
              type="number"
              name="days"
              value={formik.values.days}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.days && Boolean(formik.errors.days)}
              helperText={formik.touched.days && formik.errors.days}
            />
          </Box>

          <Box>
            <label>Add Designation</label>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              mt={1}
            >
              {/* Available Designations */}
              <List
                sx={{
                  width: "40%",
                  border: "1px solid #ccc",
                  borderRadius: 1,
                  height: 200,
                  overflowY: "auto",
                  textTransform: "none",
                }}
              >
                {availableDesignations.map((designation) => (
                  <ListItem
                    component="div"
                    sx={{ cursor: "pointer", padding: "0" }}
                    key={designation._id}
                    onClick={() => {
                      setSelectedItem(designation.designationName);
                      setSelectedFromAvailable([designation._id]);
                    }}
                  >
                    <ListItemText
                      primary={designation.designationName}
                      sx={{
                        color:
                          selectedItem === designation.designationName
                            ? "#d55a1d"
                            : "#6B7280",
                        fontSize: "14px",
                        "& .MuiTypography-root": {
                          backgroundColor: "#FFF",
                          padding: "0.35rem 0.75rem",
                          borderRadius: "0.25rem",
                          marginBlockEnd: "0.25rem",
                        },
                        ":hover": {
                          color: "#d55a1d",
                        },
                      }}
                    />
                  </ListItem>
                ))}
              </List>

              {/* Controls */}
              <Box display="flex" flexDirection="column" alignItems="center">
                <Tooltip title="Move all to selected" arrow placement="right">
                  <IconButton
                    onClick={() =>
                      moveSelectedDesignations(
                        availableDesignations,
                        selectedDesignations,
                        setAvailableDesignations,
                        setSelectedDesignations,
                        availableDesignations.map((d) => d._id)
                      )
                    }
                  >
                    <FastForward sx={{ color: "#111827" }} />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Move selected to right" arrow placement="right">
                  <IconButton
                    onClick={() =>
                      moveSelectedDesignations(
                        availableDesignations,
                        selectedDesignations,
                        setAvailableDesignations,
                        setSelectedDesignations,
                        selectedFromAvailable
                      )
                    }
                  >
                    <KeyboardArrowRight sx={{ color: "#111827" }} />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Move selected to left" arrow placement="left">
                  <IconButton
                    onClick={() =>
                      moveSelectedDesignations(
                        selectedDesignations,
                        availableDesignations,
                        setSelectedDesignations,
                        setAvailableDesignations,
                        selectedFromSelected
                      )
                    }
                  >
                    <KeyboardArrowLeft sx={{ color: "#111827" }} />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Move all to available" arrow placement="left">
                  <IconButton
                    onClick={() =>
                      moveSelectedDesignations(
                        selectedDesignations,
                        availableDesignations,
                        setSelectedDesignations,
                        setAvailableDesignations,
                        selectedDesignations.map((d) => d._id)
                      )
                    }
                  >
                    <FastRewind sx={{ color: "#111827" }} />
                  </IconButton>
                </Tooltip>
              </Box>

              {/* Selected Designations */}
              <List
                sx={{
                  width: "40%",
                  border: "1px solid #ccc",
                  borderRadius: 1,
                  height: 200,
                  overflowY: "auto",
                }}
              >
                {selectedDesignations.map((designation) => (
                  <ListItem
                    component="div"
                    sx={{ cursor: "pointer", padding: "0" }}
                    key={designation._id}
                    onClick={() => {
                      setSelectedItem(designation.designationName);
                      setSelectedFromSelected([designation._id]);
                    }}
                  >
                    <ListItemText
                      primary={designation.designationName}
                      sx={{
                        color:
                          selectedItem === designation.designationName
                            ? "#d55a1d"
                            : "#6B7280",
                        fontSize: "14px",
                        "& .MuiTypography-root": {
                          backgroundColor: "#FFF",
                          padding: "0.35rem 0.75rem",
                          borderRadius: "0.25rem",
                          marginBlockEnd: "0.25rem",
                        },
                        ":hover": {
                          color: "#d55a1d",
                        },
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          </Box>

          <DialogActions
            sx={{ justifyContent: "flex-start", mt: 2, padding: 0 }}
          >
            <Button
              variant="contained"
              color="primary"
              type="submit"
              sx={{
                backgroundColor: "#F26522",
                color: "#FFF",
                textTransform: "none",
                "&:hover": {
                  backgroundColor: "#d55a1d",
                },
              }}
            >
              Submit
            </Button>
          </DialogActions>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddCustomPolicyDialog;

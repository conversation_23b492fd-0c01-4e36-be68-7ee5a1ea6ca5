import * as React from "react";
import "./AnnualLeaveSetting.scss";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import {
  Box,
  TextField,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Radio,
  RadioGroup,
  FormControlLabel,
  DialogActions,
  Button,
  Divider,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import PolicyCard from "./PolicyCard";

const employees = [
  { name: "<PERSON>", avatar: "/assets/users/user-01.jpg" },
  { name: "<PERSON>", avatar: "/assets/users/user-10.jpg" },
  { name: "<PERSON>", avatar: "/assets/users/user-11.jpg" },
  { name: "<PERSON>", avatar: "/assets/users/user-12.jpg" },
  { name: "<PERSON>", avatar: "/assets/users/user-13.jpg" },
];

interface AnnuallLeaveSettingProps {
  open: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  index?: number;
  value?: number;
}

function CustomTabPanel(props: AnnuallLeaveSettingProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

export default function AnnuallLeaveSetting({
  open,
  onClose,
}: AnnuallLeaveSettingProps) {
  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Dialog
      className="dialog"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle
        sx={{
          m: 0,
          p: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        Annual Leave Settings
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ color: "grey.500" }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ width: "100%" }}>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs
              value={value}
              onChange={handleChange}
              aria-label="basic tabs example"
            >
              <Tab label="Settings" {...a11yProps(0)} />
              <Tab label="View Custom Policy" {...a11yProps(1)} />
            </Tabs>
          </Box>
          <CustomTabPanel value={value} index={0} open={open} onClose={onClose}>
            <Box className="settings-tab">
              <label>No of Days</label>
              <TextField
                type="number"
                variant="outlined"
                sx={{ width: "100%", mt: 1, mb: 2 }}
              />

              <label>Carry Forward</label>
              <Box sx={{ mt: 1, mb: 2 }}>
                <RadioGroup
                  row
                  defaultValue="yes"
                  name="carry-forward-radio-group"
                >
                  <FormControlLabel
                    value="yes"
                    control={<Radio />}
                    label="Yes"
                  />
                  <FormControlLabel value="no" control={<Radio />} label="No" />
                </RadioGroup>
              </Box>

              <Box>
                <label>Maximum No of Days</label>
                <TextField
                  type="number"
                  variant="outlined"
                  sx={{ width: "100%", mt: 1, mb: 2 }}
                />
              </Box>

              <label>Earned Leave</label>
              <Box sx={{ mt: 1, mb: 2 }}>
                <RadioGroup
                  row
                  defaultValue="yes"
                  name="carry-forward-radio-group"
                >
                  <FormControlLabel
                    value="yes"
                    control={<Radio />}
                    label="Yes"
                  />
                  <FormControlLabel value="no" control={<Radio />} label="No" />
                </RadioGroup>
              </Box>
            </Box>
          </CustomTabPanel>

          <CustomTabPanel value={value} index={1} open={open} onClose={onClose}>
            <Box>
              <PolicyCard
                policyName="2 Days Leave"
                noOfDays={2}
                employees={employees}
              />
              <PolicyCard
                policyName="2 Days Leave"
                noOfDays={2}
                employees={employees}
              />

              <PolicyCard
                policyName="2 Days Leave"
                noOfDays={2}
                employees={employees}
              />
              <PolicyCard
                policyName="2 Days Leave"
                noOfDays={2}
                employees={employees}
              />
            </Box>
          </CustomTabPanel>
        </Box>
      </DialogContent>
      <Divider />
      <DialogActions>
        <Button
          onClick={onClose}
          color="secondary"
          sx={{
            textTransform: "none",
            borderColor: "#ccc",
            color: "#000",
          }}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          sx={{
            backgroundColor: "#F26522",
            color: "#FFF",
            textTransform: "none",
            "&:hover": {
              backgroundColor: "#d55a1d",
            },
          }}
        >
          Save Changes
        </Button>
      </DialogActions>
    </Dialog>
  );
}

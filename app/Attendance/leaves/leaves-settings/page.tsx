"use client";
import { <PERSON>, Typo<PERSON>, <PERSON><PERSON> } from "@mui/material";
import { useState, useEffect } from "react";
import "./LeaveSettings.scss";
import { ControlPoint, HomeOutlined } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import Loader from "@/components/Loader/Loader";
import LeaveSettingCard from "@/components/LeaveSettingCard/LeaveSettingCard";
import AddCustomPolicyDialog from "./AddCustomPolicy";
import NewCustomPolicyDialog from "./NewCustomPolicyDialog";
import { getAllLeaveSettings, getLeaveSettingById, updateLeaveSetting } from "@/app/services/leave/leave.service";
import { toast } from "react-toastify";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";

interface LeaveSetting {
  _id: string;
  leaveType: string;
  policyName: string;
  days: number;
  designations: string[]; // Changed from employees to designations (array of IDs)
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface LeaveSettingBody {
  leaveType: string;
  policyName: string;
  days: number;
  designations: string[]; // Changed from employees to designations
}

export default function LeaveSettings() {
  const [openDialog, setOpenDialog] = useState(false);
  const [open, setOpen] = useState(false);
  const [leaveSettings, setLeaveSettings] = useState<LeaveSetting[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedLeaveId, setSelectedLeaveId] = useState<string | null>(null);
  const [dialogData, setDialogData] = useState<LeaveSetting | null>(null);

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Employee", href: "" },
    { label: "Leave Settings" },
  ];

  // Restrict access to admin roles only (Admin, SuperAdmin, Manager, HR)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // Fetch leave settings on component mount
  useEffect(() => {
    const fetchLeaveSettings = async () => {
      setIsLoading(true);
      try {
        const response = await getAllLeaveSettings();
        if (response.success) {
          setLeaveSettings(response.leave.results);
        }
      } catch (error) {
        console.error("Failed to fetch leave settings:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchLeaveSettings();
  }, []);

  // Fetch leave setting by ID when custom policy is clicked
  const handleCustomPolicyClick = async (id: string) => {
    setIsLoading(true);
    try {
      // Fetch leave setting by ID
      const leaveResponse = await getLeaveSettingById(id);
      if (leaveResponse.success) {
        setDialogData(leaveResponse.leave);
        setSelectedLeaveId(id);
      }
    } catch (error) {
      console.error("Failed to fetch leave setting:", error);
    } finally {
      setIsLoading(false);
      setOpenDialog(true);
    }
  };

  // Handle form submission to update leave setting
  const handleUpdateLeaveSetting = async (payload: { leaveType: string; policyName: string; days: number; designations: string[] }) => {
    if (!selectedLeaveId) return;

    setIsLoading(true);
    try {
      const body: LeaveSettingBody = {
        leaveType: payload.leaveType,
        policyName: payload.policyName,
        days: payload.days,
        designations: payload.designations,
      };
      console.log("Updating with body:", body);
      await updateLeaveSetting(selectedLeaveId, body);
      toast.success("Leave setting updated successfully");
      const response = await getAllLeaveSettings();
      if (response.success) {
        setLeaveSettings(response.leave.results);
      }
      setOpenDialog(false);
    } catch (error) {
      console.error("Failed to update leave setting:", error);
      toast.error("Failed to update leave setting");
    } finally {
      setIsLoading(false);
    }
  };

  // Function to refresh leave settings
  const refreshLeaveSettings = async () => {
    setIsLoading(true);
    try {
      const response = await getAllLeaveSettings();
      if (response.success) {
        setLeaveSettings(response.leave.results);
      }
    } catch (error) {
      console.error("Failed to refresh leave settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box className="leave-settings-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="settings-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Leave Settings</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="add-custom-policy-btn"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "0.5rem 0.85rem !important",
                transition: "all 0.5s",
                fontWeight: "500",
              }}
              onClick={() => setOpen(true)}
            >
              <ControlPoint sx={{ width: "14px", height: "14px" }} />
              Add Custom Policy
            </Button>
          </Box>
        </Box>

        {/* Leave Settings card */}
        <Box sx={{ display: "flex", flexDirection: "column" }}>
          {leaveSettings.length > 0 ? (
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: "repeat(3, 1fr)",
                gap: 2,
                mb: 2,
                width: "100%",
              }}
            >
              {leaveSettings.map((setting) => (
                <LeaveSettingCard
                  key={setting._id}
                  _id={setting._id}
                  title={setting.leaveType}
                  linkUrl={`/leave-policy/${setting._id}`}
                  switchChecked={setting.isActive}
                  onCustomPolicyClick={handleCustomPolicyClick}
                  policyName={setting.policyName}
                />
              ))}
            </Box>
          ) : (
            !isLoading && (
              <Typography variant="body1" sx={{ mt: 2 }}>
                No leave settings found.
              </Typography>
            )
          )}
        </Box>
      </Box>

      <AddCustomPolicyDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        initialData={dialogData || undefined}
        onSubmit={handleUpdateLeaveSetting}
      />
      <NewCustomPolicyDialog
        open={open}
        onClose={() => setOpen(false)}
        refreshLeaveSettings={refreshLeaveSettings}
      />
    </Box>
  );
}
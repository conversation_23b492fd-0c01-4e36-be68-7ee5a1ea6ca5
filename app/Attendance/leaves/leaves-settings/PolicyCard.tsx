"use client";
import React, { useState } from "react";
import './PolicyCard.scss';
import {
    Card,
    CardContent,
    Typography,
    AvatarGroup,
    Avatar,
    IconButton,
    Box,
    TextField,
    MenuItem,
    Select,
} from "@mui/material";
import { EditNote, Delete } from "@mui/icons-material";

// Define prop types
interface Employee {
    name: string;
    avatar: string;
}

interface PolicyCardProps {
    policyName: string;
    noOfDays: number;
    employees: Employee[];
}

const PolicyCard: React.FC<PolicyCardProps> = ({ policyName, noOfDays, employees }) => {
    const [isEditing, setIsEditing] = useState(false);
    const [formData, setFormData] = useState({
        policyName,
        noOfDays,
        selectedEmployee: "",
    });

    // Toggle Edit Form
    const handleEditClick = () => {
        setIsEditing((prev) => !prev);
    };

    // Handle Input Change
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };

    return (
        <Card className="policy-card" sx={{ p: 2, mb: 2 }}>
            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                <CardContent sx={{ flex: 1 }}>
                    <Typography variant="body2" fontWeight="bold">
                        Policy Name
                    </Typography>
                    <Typography variant="subtitle2" color="text.secondary">
                        {policyName}
                    </Typography>
                </CardContent>

                <CardContent sx={{ flex: 1 }}>
                    <Typography variant="body2" fontWeight="bold">
                        No Of Days
                    </Typography>
                    <Typography variant="subtitle2" color="text.secondary">
                        {noOfDays}
                    </Typography>
                </CardContent>

                <Box sx={{ flex: 1, display: "flex", alignItems: "center" }}>
                    <AvatarGroup className="avatarGroup" max={5}>
                        {employees.map((emp, index) => (
                            <Avatar key={index} src={emp.avatar} alt={emp.name} sx={{ width: 32, height: 32 }} />
                        ))}
                    </AvatarGroup>
                </Box>

                <Box>
                    <IconButton onClick={handleEditClick} sx={{ color: "#6B7280" }}>
                        <EditNote  sx={{ fontSize: "16px" }}/>
                    </IconButton>
                    <IconButton sx={{ color: "#6B7280" }}>
                        <Delete sx={{ fontSize: "16px" }}/>
                    </IconButton>
                </Box>
            </Box>

            {/* Edit Form (Dropdown on Edit Click) */}
            {isEditing && (
                <Box sx={{ mt: 2, p: 2, border: "1px solid #E5E7EB", borderRadius: "8px" }}>
                    <Typography variant="subtitle1" fontWeight="bold">
                        Edit Policy
                    </Typography>

                    <Box sx={{ display: "flex", gap: 2, mt: 2 }}>
                        <Box sx={{ display: "flex", flexDirection: "column" }}>
                            <label>Policy Name</label>
                            <TextField
                                name="policyName"
                                value={formData.policyName}
                                onChange={handleChange}
                                fullWidth
                                required
                            />
                        </Box>

                        <Box sx={{display:"flex", flexDirection:"column"}}>
                            <label>No of Days</label>
                            <TextField
                                name="noOfDays"
                                type="number"
                                value={formData.noOfDays}
                                onChange={handleChange}
                                fullWidth
                                required
                            />
                        </Box>
                    </Box>

                    <Box sx={{ mt: 2 }}>
                        <label>Add Employee</label>
                        <Select
                            fullWidth
                            value={formData.selectedEmployee}
                            onChange={(e) => setFormData({ ...formData, selectedEmployee: e.target.value })}
                            displayEmpty
                        >
                            <MenuItem value="">Select</MenuItem>
                            {employees.map((emp, index) => (
                                <MenuItem key={index} value={emp.name}>
                                    {emp.name}
                                </MenuItem>
                            ))}
                        </Select>
                    </Box>

                    {/* <Button onClick={handleEditClick} variant="contained" sx={{ mt: 2 }}>
                        Save Changes
                    </Button> */}
                </Box>
            )}
        </Card>
    );
};

export default PolicyCard;

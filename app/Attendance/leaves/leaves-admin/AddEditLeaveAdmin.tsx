"use client";

import React, { useEffect, useState } from "react";
import "./AddEditLeavesAdmin.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  MenuItem,
  IconButton,
  Box,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import dayjs from "dayjs";
import { getEmpLeaveById } from "@/app/services/leave/leave.service";
import JoditEditorWrapper from "@/components/JoditEditorWrapper";

interface UserData {
  _id: string;
  firstName: string;
  lastName: string;
  designationId: string;
}

interface LeaveFormValues {
  empId: string;
  leaveType: string;
  fromDate: string | null;
  toDate: string | null;
  leaveTime: "Full Day" | "Half Day";
  noOfDays: number;
  remainingDays: number;
  reason: string;
}

interface LeaveDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: LeaveFormValues) => void;
  initialValues?: LeaveFormValues;
  userData?: UserData[];
}

interface LeaveSummaryResponse {
  type: string;
  total: number;
  taken: number;
  remaining: number;
  approved: number;
}

const leaveTypes = ["Medical", "Casual", "Annual", "Others"];
const leaveTimeOptions = ["Fullday", "Halfday"];

const validationSchema = Yup.object({
  empId: Yup.string().required("Employee Name is required"),
  leaveType: Yup.string().required("Leave Type is required"),
  fromDate: Yup.date()
    .nullable()
    .required("Start Date is required")
    .min(
      dayjs().startOf("day").toDate(),
      "Start date must be today or a future date"
    )
    .typeError("Please enter a valid date"),
  toDate: Yup.date()
    .nullable()
    .required("End Date is required")
    .min(
      dayjs().startOf("day").toDate(),
      "End date must be today or a future date"
    )
    .test(
      "is-after-start",
      "End date must be equal to or after start date",
      function (value) {
        const { fromDate } = this.parent;
        if (!fromDate || !value) return true;
        return (
          dayjs(value).startOf("day").diff(dayjs(fromDate).startOf("day")) >= 0
        );
      }
    )
    .typeError("Please enter a valid date"),
  leaveTime: Yup.string().required("Leave Time is required"),
  reason: Yup.string()
    .required("Reason is required")
    .test("not-empty-html", "Reason is required", (value) => {
      if (!value) return false;
      const strippedHtml = value
        .replace(/<[^>]*>/g, "")
        .replace(/&nbsp;/g, " ")
        .replace(/\s+/g, " ")
        .trim();
      return strippedHtml.length > 0;
    }),
});

const AddEditLeaveAdmin: React.FC<LeaveDialogProps> = ({
  open,
  onClose,
  onSubmit,
  initialValues,
  userData = [],
}) => {
  const isEditing = Boolean(initialValues);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string>("");
  const [selectedLeaveType, setSelectedLeaveType] = useState<string>("");
  const [formValues, setFormValues] = useState<LeaveFormValues | null>(null);
  const [employeeLeaveSummary, setEmployeeLeaveSummary] = useState<{
    annual: LeaveSummaryResponse;
    medical: LeaveSummaryResponse;
    casual: LeaveSummaryResponse;
    others: LeaveSummaryResponse;
  }>({
    annual: { type: "Annual", total: 0, taken: 0, remaining: 0, approved: 0 },
    medical: { type: "Medical", total: 0, taken: 0, remaining: 0, approved: 0 },
    casual: { type: "Casual", total: 0, taken: 0, remaining: 0, approved: 0 },
    others: { type: "Others", total: 0, taken: 0, remaining: 0, approved: 0 },
  });

  // Initialize with initialValues if provided
  useEffect(() => {
    if (initialValues) {
      setSelectedEmployeeId(initialValues.empId);
      setSelectedLeaveType(initialValues.leaveType);
      setFormValues(initialValues);
    }
  }, [initialValues]);

  // Fetch leave data when employee ID is selected
  useEffect(() => {
    const fetchLeaveData = async () => {
      if (selectedEmployeeId) {
        setIsLoading(true);
        try {
          const response = await getEmpLeaveById(selectedEmployeeId);
          if (response.success) {
            const leaveSummaryData = response.leaves.leaveSummary;

            // Create a simple map of leave types to their data
            const summaryMap = leaveSummaryData.reduce(
              (acc: any, item: LeaveSummaryResponse) => {
                acc[item.type.toLowerCase()] = item;
                return acc;
              },
              {}
            );

            // Set all leave types at once
            setEmployeeLeaveSummary({
              annual: summaryMap.annual || {
                type: "Annual",
                total: 0,
                taken: 0,
                remaining: 0,
                approved: 0,
              },
              medical: summaryMap.medical || {
                type: "Medical",
                total: 0,
                taken: 0,
                remaining: 0,
                approved: 0,
              },
              casual: summaryMap.casual || {
                type: "Casual",
                total: 0,
                taken: 0,
                remaining: 0,
                approved: 0,
              },
              others: summaryMap.others || {
                type: "Others",
                total: 0,
                taken: 0,
                remaining: 0,
                approved: 0,
              },
            });
          }
        } catch (error) {
          console.error("Failed to fetch leave data:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchLeaveData();
  }, [selectedEmployeeId]);

  // Update remaining days when leave type changes
  useEffect(() => {
    if (selectedLeaveType && selectedEmployeeId && formValues) {
      const leaveTypeKey =
        selectedLeaveType.toLowerCase() as keyof typeof employeeLeaveSummary;
      if (employeeLeaveSummary[leaveTypeKey]) {
        setFormValues((prev) => {
          if (!prev) return null;
          return {
            ...prev,
            remainingDays: employeeLeaveSummary[leaveTypeKey].total,
          };
        });
      }
    }
  }, [selectedLeaveType, employeeLeaveSummary, selectedEmployeeId]);

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth="sm"
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px",
        }}
      >
        {isEditing ? "Edit Leave" : "Add Leave"}
        <IconButton
          onClick={onClose}
          sx={{
            backgroundColor: "#6b7280",
            backgroundImage: "none",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": {
              backgroundColor: "#d55a1d",
            },
            "& .MuiSvgIcon-root": {
              fontSize: "14px",
            },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <Formik
        enableReinitialize
        initialValues={
          formValues || {
            empId: "",
            leaveType: "",
            fromDate: null,
            toDate: null,
            leaveTime: "Full Day" as "Full Day" | "Half Day",
            noOfDays: 0,
            remainingDays: 0,
            reason: "",
          }
        }
        validationSchema={validationSchema}
        onSubmit={(values, actions) => {
          // Debug logs
          console.log("Form values before submission:", values);
          console.log("Reason content:", values.reason);

          // Ensure the reason is properly formatted
          const formattedValues = {
            ...values,
            reason: values.reason.trim(),
          };

          console.log("Formatted values:", formattedValues);
          onSubmit(formattedValues);
        }}
      >
        {({ values, setFieldValue, errors, touched, setFieldTouched }) => {
          // Calculate days whenever the form values change
          if (values.fromDate && values.toDate) {
            const start = dayjs(values.fromDate);
            const end = dayjs(values.toDate);
            let days = end.diff(start, "day") + 1;
            if (values.leaveTime === "Half Day" && days === 1) {
              days = 0.5;
            }
            // Only update if days have changed
            if (values.noOfDays !== days) {
              setFieldValue("noOfDays", days);
            }
          }

          return (
            <Form>
              <DialogContent className="dialog-content">
                <Box sx={{ display: "flex", flexDirection: "column" }}>
                  {/* Employee Name */}
                  <Box className="field-box">
                    <label>
                      Employee Name <span className="required">*</span>
                    </label>
                    <Field
                      as={TextField}
                      select
                      fullWidth
                      name="empId"
                      error={touched.empId && Boolean(errors.empId)}
                      helperText={touched.empId && errors.empId}
                      disabled={
                        !Array.isArray(userData) || userData.length === 0
                      }
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        const newEmployeeId = e.target.value;
                        setFieldValue("empId", newEmployeeId);
                        setSelectedEmployeeId(newEmployeeId);
                        // Reset leave type when employee changes
                        setFieldValue("leaveType", "");
                        setFieldValue("remainingDays", 0);
                        setSelectedLeaveType("");
                      }}
                      SelectProps={{
                        MenuProps: {
                          PaperProps: {
                            sx: {
                              maxHeight: "200px",
                              "& .MuiMenuItem-root": {
                                height: "38px",
                                fontSize: "14px",
                                padding: "8px 14px",
                              },
                            },
                          },
                        },
                      }}
                      sx={{
                        "& .MuiSelect-select": {
                          height: "38px",
                          padding: "8px 14px",
                          fontSize: "14px",
                        },
                      }}
                    >
                      {Array.isArray(userData) && userData.length > 0 ? (
                        userData.map((user) => (
                          <MenuItem key={user._id} value={user._id}>
                            {`${user.firstName || ""} ${user.lastName || ""}`.trim() ||
                              user._id ||
                              "Unknown"}
                          </MenuItem>
                        ))
                      ) : (
                        <MenuItem value="" disabled>
                          No employees available
                        </MenuItem>
                      )}
                    </Field>
                  </Box>

                  {/* Leave Type */}
                  <Box sx={{ display: "flex", gap: 2, width: "100%" }}>
                    <Box sx={{ flex: 1 }} className="field-box">
                      <label>
                        Leave Type <span className="required">*</span>
                      </label>
                      <Field
                        as={TextField}
                        select
                        fullWidth
                        name="leaveType"
                        error={touched.leaveType && Boolean(errors.leaveType)}
                        helperText={touched.leaveType && errors.leaveType}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          const newLeaveType = e.target.value;
                          setFieldValue("leaveType", newLeaveType);
                          setSelectedLeaveType(newLeaveType);

                          // Update remaining days based on selected leave type
                          if (selectedEmployeeId) {
                            const leaveTypeKey =
                              newLeaveType.toLowerCase() as keyof typeof employeeLeaveSummary;
                            if (employeeLeaveSummary[leaveTypeKey]) {
                              setFieldValue(
                                "remainingDays",
                                employeeLeaveSummary[leaveTypeKey].total
                              );
                            }
                          }
                        }}
                      >
                        {leaveTypes.map((type) => (
                          <MenuItem key={type} value={type}>
                            {type}
                          </MenuItem>
                        ))}
                      </Field>
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <label>Remaining Leaves</label>
                      <Field
                        as={TextField}
                        fullWidth
                        name="remainingDays"
                        value={values.remainingDays}
                        InputProps={{ readOnly: true }}
                      />
                    </Box>
                  </Box>

                  {/* From & To Dates */}
                  <Box sx={{ display: "flex", gap: 2, width: "100%" }}>
                    <Box sx={{ flex: 1 }} className="field-box">
                      <label>
                        From <span className="required">*</span>
                      </label>
                      <Field
                        as={TextField}
                        type="date"
                        name="fromDate"
                        value={values.fromDate}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue("fromDate", e.target.value);
                          // If toDate is before the new fromDate, update toDate to match fromDate
                          if (
                            values.toDate &&
                            dayjs(values.toDate).isBefore(dayjs(e.target.value))
                          ) {
                            setFieldValue("toDate", e.target.value);
                          }
                        }}
                        fullWidth
                        error={touched.fromDate && Boolean(errors.fromDate)}
                        helperText={touched.fromDate && errors.fromDate}
                        InputProps={{
                          inputProps: {
                            min: dayjs().format("YYYY-MM-DD"), // Disable past dates
                          },
                        }}
                      />
                    </Box>
                    <Box sx={{ flex: 1 }} className="field-box">
                      <label>
                        To <span className="required">*</span>
                      </label>
                      <Field
                        as={TextField}
                        type="date"
                        name="toDate"
                        value={values.toDate}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          setFieldValue("toDate", e.target.value)
                        }
                        fullWidth
                        error={touched.toDate && Boolean(errors.toDate)}
                        helperText={touched.toDate && errors.toDate}
                        InputProps={{
                          inputProps: {
                            min:
                              values.fromDate || dayjs().format("YYYY-MM-DD"), // Disable dates before fromDate or today
                          },
                        }}
                      />
                    </Box>
                    <Box sx={{ flex: 1 }} className="field-box">
                      <label>No of Days</label>
                      <Field
                        as={TextField}
                        fullWidth
                        name="noOfDays"
                        value={values.noOfDays}
                        InputProps={{ readOnly: true }}
                      />
                    </Box>
                  </Box>

                  {/* Leave Time */}
                  <Box className="field-box">
                    <label>Leave Time</label>
                    <Field
                      as={TextField}
                      select
                      fullWidth
                      name="leaveTime"
                      error={touched.leaveTime && Boolean(errors.leaveTime)}
                      helperText={touched.leaveTime && errors.leaveTime}
                    >
                      {leaveTimeOptions.map((option) => (
                        <MenuItem key={option} value={option}>
                          {option}
                        </MenuItem>
                      ))}
                    </Field>
                  </Box>

                  {/* Reason */}
                  <Box className="field-box">
                    <label>
                      Reason <span className="required">*</span>
                    </label>
                    <JoditEditorWrapper
                      height={100}
                      value={values.reason}
                      onChange={(content: string) => {
                        console.log("Editor content changed:", content);
                        setFieldValue("reason", content);
                        setFieldTouched("reason", true);
                      }}
                    />
                  </Box>
                </Box>

                {/* Actions */}
                <DialogActions
                  sx={{
                    padding: "16px 0px 0px 16px !important",
                    marginTop: "1rem",
                  }}
                >
                  <Button
                    sx={{
                      fontWeight: "400",
                      fontSize: "14px",
                      color: "#111827",
                      border: "1px solid #E5E7EB",
                      borderRadius: "5px",
                      textTransform: "none",
                      padding: "8px 13.6px",
                    }}
                    onClick={onClose}
                    variant="outlined"
                  >
                    Cancel
                  </Button>
                  <Button
                    sx={{
                      display: "flex",
                      gap: "8px",
                      backgroundColor: "#F26522",
                      borderColor: "#F26522",
                      color: "#FFF",
                      fontWeight: 400,
                      fontSize: "14px",
                      borderRadius: "5px",
                      textTransform: "none",
                      padding: "8px 13.6px",
                    }}
                    type="submit"
                    variant="contained"
                  >
                    {isEditing ? "Update Leave" : "Add Leave"}
                  </Button>
                </DialogActions>
              </DialogContent>
            </Form>
          );
        }}
      </Formik>
    </Dialog>
  );
};

export default AddEditLeaveAdmin;

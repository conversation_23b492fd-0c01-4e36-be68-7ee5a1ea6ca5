"use client";

import {
  Box,
  IconButton,
  Button,
  Divider,
  Typography,
  Avatar,
  Paper,
  Select,
  MenuItem,
  SelectChangeEvent,
  Tooltip,
} from "@mui/material";
import { useState, useEffect, useMemo, useCallback } from "react";
import "./leaveAdmin.scss";
import {
  HomeOutlined,
  Description,
  EditNote,
  Delete,
  KeyboardArrowDown,
  InfoOutlined,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import dayjs from "dayjs";
import {
  GridRowsProp,
  GridColDef,
  useGridApiRef,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import LeavesCard from "@/components/LeavesCardAdmin/LeavesCard";
import AddEditLeaveAdmin from "./AddEditLeaveAdmin";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import Loader from "@/components/Loader/Loader";
import {
  addLeave,
  updateLeave,
  deleteLeave,
  getLeaveById,
  approveLeaveById,
} from "@/app/services/leave/leave.service";
import { getDepartments } from "@/app/services/department.service";
import useFetchLeaveData from "@/app/hooks/leaves/useFetchLeaveData";
import useFetchUsersData from "@/app/hooks/users/useFetchUsersData";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import StatusChangeConfirmationDialog from "@/components/StatusChangeConfirmation/StatusChangeConfirmation";
import { toast } from "react-toastify";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

interface ApprovedBy {
  firstName: string;
  designationName: string;
  avatar?: string;
}

interface LeaveAdminRecord {
  id: string;
  employee: string;
  leaveType: string;
  from: string;
  to: string;
  days: string;
  avatar: string;
  department: string;
  status: LeaveStatus;
  leaveTime?: string;
  reason?: string;
  approvedBy?: ApprovedBy | null;
}

interface LeaveFormValues {
  empId: string;
  leaveType: string;
  fromDate: string | null;
  toDate: string | null;
  leaveTime: "Full Day" | "Half Day";
  noOfDays: number;
  remainingDays: number;
  reason: string;
}

type LeaveStatus = "Pending" | "Approved" | "Declined";

const formatDateForApi = (dateStr: string) => {
  if (!dateStr) return undefined;

  try {
    // Assuming dateStr is in format "MM/DD/YYYY"
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return undefined;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return undefined;
  }
};

const leaveTypeDescriptions: { [key: string]: string } = {
  Medical:
    "Leave taken for medical reasons, such as illness or medical appointments.",
  Casual:
    "Leave for personal or unplanned reasons, typically for short durations.",
  Annual:
    "Pre-planned leave allocated annually for vacations or personal time off.",
  Others: "Leave for miscellaneous reasons not covered by other categories.",
};

export default function LeaveAdmin() {
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Admin", href: "" },
    { label: "Leaves" },
  ];

  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [limit, setLimit] = useState(10);
  const [openModal, setOpenModal] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [selectedRow, setSelectedRow] = useState<LeaveAdminRecord | null>(null);
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const [selectedLeaveType, setSelectedLeaveType] =
    useState<string>("Leave Type");
  const [departments, setDepartments] = useState<string[]>([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [pendingStatusChange, setPendingStatusChange] = useState<{
    leaveId: string;
    newStatus: LeaveStatus;
  } | null>(null);

  // Restrict access to admin roles only (Admin, SuperAdmin, Manager, HR)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  useEffect(() => {
    const fetchDepartments = async () => {
      setIsLoading(true);
      try {
        const response = await getDepartments();
        if (response.success && response.departments?.results) {
          const departmentNames = response.departments.results.map(
            (dept: { departmentName: string }) => dept.departmentName
          );
          setDepartments(departmentNames);
        }
      } catch (error) {
        console.error("Failed to fetch departments:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchDepartments();
  }, []);

  const [leaveData, leaveTotal, leaveSummary] = useFetchLeaveData({
    setIsLoading,
    refresh,
    limit,
    page,
    departmentName:
      selectedDepartment === "Department" ? "" : selectedDepartment,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate: startDate ? formatDateForApi(startDate) : undefined, // Make sure these are being passed
    endDate: endDate ? formatDateForApi(endDate) : undefined, // Make sure these are being passed
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    leaveType:
      selectedLeaveType === "Leave Type" ? undefined : selectedLeaveType,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  const [userData] = useFetchUsersData({
    setIsLoading,
    refresh,
    limit: 1000,
    page: 1,
    departmentName:
      selectedDepartment === "Department" ? "" : selectedDepartment,
    designationName: "",
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate: startDate || undefined,
    endDate: endDate || undefined,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
  });

  const apiRef = useGridApiRef();

  const handlePaginationModelChange = (model: {
    page: number;
    pageSize: number;
  }) => {
    setPage(model.page + 1);
    setPageSize(model.pageSize);
    setLimit(model.pageSize);
  };

  const rows: GridRowsProp = useMemo(() => {
    if (!leaveData || !userData) return [];

    return leaveData.map((leaveItem: any, index: number) => {
      console.log("Processing leave item:", leaveItem);

      const row = {
        id: leaveItem._id || `${leaveItem.employeeId}-${index}`,
        employee: leaveItem.employeeName,
        leaveType: leaveItem.leaveType,
        from: leaveItem.from,
        to: leaveItem.to,
        days: leaveItem.days?.toString() || "0",
        avatar: leaveItem.avatar,
        department: leaveItem.department,
        status: leaveItem.status,
        leaveTime: leaveItem.leaveTime,
        reason: leaveItem.reason,
        approvedBy: leaveItem.approvedBy,
      };

      console.log("Created row with approvedBy:", row.approvedBy);
      return row;
    });
  }, [leaveData, userData]);

  const handleStatusChange = (event: SelectChangeEvent, leaveId: string) => {
    const newStatus = event.target.value as LeaveStatus;
    setPendingStatusChange({ leaveId, newStatus });
    setStatusDialogOpen(true);
  };

  const handleStatusConfirm = async () => {
    if (pendingStatusChange) {
      const { leaveId, newStatus } = pendingStatusChange;
      setIsLoading(true);
      try {
        await approveLeaveById(leaveId, newStatus);
        // toast.success(`Leave status updated to ${newStatus}`);
        setRefresh(!refresh);
      } catch (error) {
        console.error("Failed to update leave status:", error);
      } finally {
        setIsLoading(false);
        setStatusDialogOpen(false);
        setPendingStatusChange(null);
      }
    }
  };

  const handleStatusCancel = () => {
    setStatusDialogOpen(false);
    setPendingStatusChange(null);
  };

  const columns: GridColDef[] = [
    {
      field: "employee",
      headerName: "Employee",
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar src={params.row.avatar} sx={{ width: 32, height: 32 }} />
          <Box display="flex" flexDirection="column">
            <Typography sx={{ fontSize: "14px", color: "#111827" }}>
              {params.row.employee}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: "department",
      headerName: "Department",
      flex: 1,
      editable: false,
    },
    {
      field: "leaveType",
      headerName: "Leave Type",
      flex: 1,
      renderCell: (params) => (
        <Box display="flex" alignItems="center" gap={1}>
          <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
            {params.value}
          </Typography>
          <Tooltip
            title={
              leaveTypeDescriptions[params.value] || "No description available"
            }
            placement="top"
            arrow
          >
            <InfoOutlined sx={{ fontSize: "14px", color: "#1b84ff" }} />
          </Tooltip>
        </Box>
      ),
    },
    {
      field: "from",
      headerName: "From",
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "to",
      headerName: "To",
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "days",
      headerName: "No of Days",
      flex: 1,
      renderCell: (params) => {
        const days = parseInt(params.value, 10);
        return (
          <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
            {days === 1 ? `${days} Day` : `${days} Days`}
          </Typography>
        );
      },
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      renderCell: (params) => (
        <Select
          value={params.row.status}
          onChange={(event: SelectChangeEvent) =>
            handleStatusChange(event, params.row.id)
          }
          size="small"
          disabled={isLoading}
          IconComponent={KeyboardArrowDown}
          sx={{
            height: "32px",
            width: "120px",
            borderRadius: "8px",
            border: "none",
            "& .MuiSelect-select": {
              padding: "4px 8px",
              paddingRight: "35px !important",
              display: "flex",
              alignItems: "center",
              fontSize: "14px",
              color: "#111827",
            },
            "& .MuiOutlinedInput-notchedOutline": {
              border: "1px solid #D1D5DB",
            },
            "&:hover .MuiOutlinedInput-notchedOutline": {
              border: "1px solid #D1D5DB",
            },
            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
              border: "1px solid #D1D5DB",
            },
          }}
        >
          <MenuItem value="Pending">
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Box
                sx={{
                  width: 10,
                  height: 10,
                  borderRadius: "50%",
                  backgroundColor: "#D3D3D3",
                }}
              />
              Pending
            </Box>
          </MenuItem>
          <MenuItem value="Approved">
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Box
                sx={{
                  width: 10,
                  height: 10,
                  borderRadius: "50%",
                  backgroundColor: "#4CAF50",
                }}
              />
              Approved
            </Box>
          </MenuItem>
          <MenuItem value="Declined">
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Box
                sx={{
                  width: 10,
                  height: 10,
                  borderRadius: "50%",
                  backgroundColor: "#F44336",
                }}
              />
              Declined
            </Box>
          </MenuItem>
        </Select>
      ),
    },
    {
      field: "approvedBy",
      headerName: "Approved By",
      flex: 1,
      renderCell: (params: { row: LeaveAdminRecord }) => {
        const approvedBy = params.row.approvedBy;

        if (!approvedBy) {
          return (
            <Typography variant="body2" color="textSecondary">
              Pending Approval
            </Typography>
          );
        }

        return (
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Avatar
              src={approvedBy.avatar || "/assets/users/default.jpg"}
              alt={approvedBy.firstName}
              sx={{ width: 32, height: 32 }}
            />
            <Box display="flex" flexDirection="column">
              <Typography variant="body2" color="textPrimary">
                {approvedBy.firstName}
              </Typography>
              <Typography
                sx={{ fontSize: "12px" }}
                variant="body2"
                color="textSecondary"
              >
                {approvedBy.designationName}
              </Typography>
            </Box>
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 0.8,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handleEditClick = async (row: LeaveAdminRecord) => {
    setIsLoading(true);
    try {
      const response = await getLeaveById(row.id);
      if (response.success && response.leaves) {
        const leave = response.leaves;
        const user = userData?.find((u) => u._id === leave.empId);
        const fullName = user
          ? `${user.firstName || ""} ${user.lastName || ""}`.trim()
          : "Unknown";
        const approver =
          leave.approvedBy && typeof leave.approvedBy === "object"
            ? leave.approvedBy.firstName
            : "Pending";

        setSelectedRow({
          id: leave._id,
          employee: fullName,
          leaveType: leave.leaveType,
          from: leave.leaveFrom,
          to: leave.leaveTo,
          days: leave.leaveDays.toString(),
          avatar: user?.avatar || "/default-avatar.png",
          department: user?.departmentName || "Unknown",
          status: leave.leaveStatus,
          leaveTime: leave.leaveTime === "-" ? "Full Day" : leave.leaveTime,
          reason: leave.reason || "",
          approvedBy: approver,
        });
        setEditModal(true);
      }
    } catch (error) {
      console.error("Failed to fetch leave by ID:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClick = (id: string) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (deleteId) {
      deleteLeave(deleteId)
        .then(() => {
          setRefresh(!refresh);
          setDeleteDialogOpen(false);
          setDeleteId(null);
          toast.success("Leave deleted successfully");
        })
        .catch((err) => console.error("Failed to delete leave:", err));
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  const handleFormSubmit = async (values: LeaveFormValues) => {
    const leaveBody = {
      empId: values.empId,
      leaveType: values.leaveType,
      leaveFrom: values.fromDate,
      leaveTo: values.toDate,
      leaveTime: values.leaveTime || "-",
      remainingDays: values.remainingDays,
      leaveStatus: "Pending",
      reason: values.reason,
    };

    try {
      if (editModal && selectedRow) {
        await updateLeave(selectedRow.id, {
          ...leaveBody,
          leaveFrom: leaveBody.leaveFrom || "",
          leaveTo: leaveBody.leaveTo || "",
        });
        toast.success("Leave updated successfully");
        setRefresh((prev) => !prev);
        setEditModal(false);
        setSelectedRow(null);
      } else {
        await addLeave({
          ...leaveBody,
          leaveFrom: leaveBody.leaveFrom || "",
          leaveTo: leaveBody.leaveTo || "",
        });
        toast.success("Leave added successfully");
        setRefresh((prev) => !prev);
        setOpenModal(false);
      }
    } catch (err) {
      console.error(`Failed to ${editModal ? "update" : "add"} leave:`, err);
    }
  };

  const bgImages = {
    present: "/assets/bg-green-01.svg",
    planned: "/assets/bg-pink-01.svg",
    unplanned: "/assets/bg-yellow-01.svg",
    pending: "/assets/bg-blue-01.svg",
  };

  return (
    <Box className="attendance-admin-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="attendance-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Leaves</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="report"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => setOpenModal(true)}
            >
              <Description sx={{ width: "14px" }} />
              Add Leave
            </Button>
          </Box>
        </Box>

        <Box sx={{ display: "flex", gap: 2 }}>
          <LeavesCard
            title="Total Leaves"
            value={leaveSummary.globalTotalLeaves}
            bgImage={bgImages.present}
          />
          <LeavesCard
            title="Planned Leaves"
            value={leaveSummary.globalPlannedLeaves}
            bgImage={bgImages.planned}
          />
          <LeavesCard
            title="Unplanned Leaves"
            value={leaveSummary.globalUnplannedLeaves}
            bgImage={bgImages.unplanned}
          />
          <LeavesCard
            title="Pending Requests"
            value={leaveSummary.globalPendingRequests}
            bgImage={bgImages.pending}
          />
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 400,
              maxHeight: "calc(100vh - 300px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Leave List</Typography>
              <PolicyFilters
                departments={departments}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                selectedLeaveType={selectedLeaveType}
                setSelectedLeaveType={setSelectedLeaveType}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={false}
                showSortByFilter={true}
                showLeaveTypeFilter={true}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => {}}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              apiRef={apiRef}
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={leaveTotal || 0}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={handlePaginationModelChange}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      <AddEditLeaveAdmin
        open={openModal || editModal}
        onClose={() => {
          setOpenModal(false);
          setEditModal(false);
          setSelectedRow(null);
        }}
        onSubmit={handleFormSubmit}
        initialValues={
          selectedRow
            ? {
                empId:
                  leaveData?.find((l) => l._id === selectedRow.id)
                    ?.employeeId || "",
                leaveType: selectedRow.leaveType,
                fromDate: selectedRow.from.split("T")[0] || selectedRow.from,
                toDate: selectedRow.to.split("T")[0] || selectedRow.to,
                leaveTime:
                  selectedRow.leaveTime === "-"
                    ? "Full Day"
                    : (selectedRow.leaveTime as "Full Day" | "Half Day"),
                noOfDays: parseInt(selectedRow.days, 10),
                remainingDays: 0,
                reason: selectedRow.reason || "",
              }
            : undefined
        }
        userData={(userData || []).map((user) => ({
          ...user,
          firstName: user.firstName || "",
          lastName: user.lastName || "",
          designationId: user.designationId || "",
        }))}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        title="Confirm Delete Leave"
        message="Are you sure you want to delete this leave record? This action cannot be undone."
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
      />

      <StatusChangeConfirmationDialog
        open={statusDialogOpen}
        title="Confirm Status Change"
        message={`Are you sure you want to change the status to ${pendingStatusChange?.newStatus}? This action cannot be undone.`}
        onClose={handleStatusCancel}
        onConfirm={handleStatusConfirm}
      />
    </Box>
  );
}

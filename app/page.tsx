"use client";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import useAuthStore from "@/store/authStore";
import { ROLES } from "@/app/constants/roles";
import Loader from "@/components/Loader/Loader";

function Page() {
  const router = useRouter();
  const { roles, isAuthenticated } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  const [redirectAttempted, setRedirectAttempted] = useState(false);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const checkAuthAndNavigate = async () => {
      // Don't proceed if we've already attempted a redirect
      if (redirectAttempted) return;

      try {
        // Wait for authentication state to be stable
        timeoutId = setTimeout(async () => {
          if (!isAuthenticated) {
            await router.push("/login");
          } else {
            const isAdmin = roles.some(
              (role) =>
                role === ROLES.Admin ||
                role === ROLES.SuperAdmin ||
                role === ROLES.Manager ||
                role === ROLES.HR
            );

            if (isAdmin) {
              await router.push("/Dashboard/Admin-Dashboard");
            } else {
              await router.push("/Dashboard/Employee-Dashboard");
            }
          }
          setRedirectAttempted(true);
        }, 2000); // Increased timeout to ensure stable auth state
      } catch (error) {
        console.error("Navigation error:", error);
        router.push("/login/");
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthAndNavigate();

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [router, roles, isAuthenticated, redirectAttempted]);

  if (isLoading) {
    return <Loader loading={isLoading} />;
  }

  return null;
}

export default Page;

"use client";

import React, { useEffect, useState } from "react";
import "./AddEditTraining.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  MenuItem,
  IconButton,
  Box,
  Autocomplete,
  InputLabel,
  Typography,
} from "@mui/material";
import { Cancel } from "@mui/icons-material";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import dayjs from "dayjs";
import useFetchTrainingTypeData from "../hooks/trainingType/useFetchTrainingTypeData";
import useFetchTrainerData from "../hooks/trainers/useFetchTrainerData";
import useFetchUsersData from "../hooks/users/useFetchUsersData";
import { getTrainingById } from "@/app/services/training/training.service";
import JoditEditorWrapper from "@/components/JoditEditorWrapper";

// Interface for Training Type
interface TrainingType {
  _id: string;
  type: string;
  description: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

// Interface for Trainer
interface Trainer {
  _id: string;
  firstName: string;
  lastName: string;
  role: string;
  phone: string;
  email: string;
  description: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

// Interface for Employee (User)
interface UserRecord {
  _id: string;
  firstName: string;
  lastName?: string;
  email: string;
  departmentName: string;
  designationName: string;
  isActive: boolean;
  joinDate?: string;
  createdAt?: string;
}

interface TrainingFormValues {
  trainingTypeId: string;
  trainerId: string;
  employeesId: string[];
  trainingCost: number;
  startDate: string;
  endDate: string;
  description: string;
  isActive: boolean;
}

interface TraingDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (formData: FormData) => void;
  isEditMode: boolean;
  trainingId?: string | null;
}

const validationSchema = Yup.object({
  trainingTypeId: Yup.string().required("Training Type is required"),
  trainerId: Yup.string().required("Trainer is required"),
  employeesId: Yup.array()
    .of(Yup.string())
    .min(1, "At least one employee must be selected")
    .required("Employees are required"),
  trainingCost: Yup.number()
    .required("Training Cost is required")
    .min(0, "Training Cost cannot be negative"),
  startDate: Yup.date()
    .required("Start Date is required")
    .typeError("Invalid date format"),
  endDate: Yup.date()
    .required("End Date is required")
    .typeError("Invalid date format")
    .test("is-after", "End date must be after start date", function (value) {
      const { startDate } = this.parent;
      return dayjs(value).isAfter(dayjs(startDate));
    }),
  description: Yup.string().required("Description is required"),
  isActive: Yup.boolean().required("Status is required"),
});

const labelStyles = {
  "& .MuiFormLabel-asterisk": {
    color: "red",
  },
};

const AddEditTraining: React.FC<TraingDialogProps> = ({
  open,
  onClose,
  onSubmit,
  isEditMode,
  trainingId,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [refresh] = useState(false);
  const [initialData, setInitialData] = useState<TrainingFormValues | null>(
    null
  );

  // Fetch all training types (only active ones)
  const [trainingTypesUnsorted] = useFetchTrainingTypeData({
    setIsLoading,
    refresh,
    limit: 9999,
    page: 1,
    isActive: "true", // Only fetch active training types
  }) as [TrainingType[] | null, number];

  // Sort training types alphabetically by type (A to Z)
  const trainingTypes = trainingTypesUnsorted
    ? [...trainingTypesUnsorted].sort((a, b) => a.type.localeCompare(b.type))
    : null;

  // Fetch all trainers (only active ones)
  const [trainersUnsorted] = useFetchTrainerData({
    setIsLoading,
    refresh,
    limit: 9999,
    page: 1,
    isActive: "true", // Only fetch active trainers
  }) as [Trainer[] | null, number];

  // Sort trainers alphabetically by firstName (A to Z)
  const trainers = trainersUnsorted
    ? [...trainersUnsorted].sort((a, b) =>
        a.firstName.localeCompare(b.firstName)
      )
    : null;

  // Fetch all employees - Destructure all 5 elements
  const [employees, , , ,] = useFetchUsersData({
    setIsLoading,
    refresh,
    limit: 9999,
    page: 1,
  }) as [UserRecord[] | null, number, number, number, number];

  // Fetch training data by ID when in edit mode
  useEffect(() => {
    if (isEditMode && trainingId) {
      const fetchTrainingData = async () => {
        try {
          setIsLoading(true);
          const response = await getTrainingById(trainingId);
          const trainingData = response.training;

          // Safely extract IDs with fallbacks for each field
          // For training type
          let trainingTypeId = "";
          if (trainingData.trainingTypeId) {
            trainingTypeId =
              typeof trainingData.trainingTypeId === "object"
                ? trainingData.trainingTypeId._id || ""
                : trainingData.trainingTypeId;
          }

          // For trainer
          let trainerId = "";
          if (trainingData.trainerId) {
            trainerId =
              typeof trainingData.trainerId === "object"
                ? trainingData.trainerId._id || ""
                : trainingData.trainerId;
          }

          // For employees
          let employeesId: string[] = [];
          if (
            trainingData.employeesId &&
            Array.isArray(trainingData.employeesId)
          ) {
            employeesId = trainingData.employeesId
              .map((emp: any) => {
                if (emp && typeof emp === "object") {
                  return emp._id || "";
                }
                return emp || "";
              })
              .filter((id: string) => id); // Remove any empty strings
          }

          // Create initial data with fallbacks for all fields
          const initialFormData = {
            trainingTypeId: trainingTypeId || "",
            trainerId: trainerId || "",
            employeesId: employeesId || [],
            trainingCost: trainingData.trainingCost || 0,
            startDate: trainingData.startDate
              ? dayjs(trainingData.startDate).format("YYYY-MM-DD")
              : "",
            endDate: trainingData.endDate
              ? dayjs(trainingData.endDate).format("YYYY-MM-DD")
              : "",
            description: trainingData.description || "",
            isActive:
              typeof trainingData.isActive === "boolean"
                ? trainingData.isActive
                : false,
          };

          console.log("Setting initial form data:", initialFormData);
          setInitialData(initialFormData);
        } catch (error) {
          console.error("Failed to fetch training data:", error);
          // Set default values if fetch fails
          setInitialData({
            trainingTypeId: "",
            trainerId: "",
            employeesId: [],
            trainingCost: 0,
            startDate: "",
            endDate: "",
            description: "",
            isActive: true,
          });
        } finally {
          setIsLoading(false);
        }
      };
      fetchTrainingData();
    } else {
      setInitialData(null);
    }
  }, [isEditMode, trainingId]);

  const initialValues: TrainingFormValues = initialData || {
    trainingTypeId: "",
    trainerId: "",
    employeesId: [],
    trainingCost: 0,
    startDate: "",
    endDate: "",
    description: "",
    isActive: true,
  };

  return (
    <Dialog
      className="AddEditTraining"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle className="dialog-title">
        <Typography
          sx={{
            fontSize: "20px",
            fontWeight: 600,
            color: "#202C4B",
          }}
        >
          {isEditMode ? "Edit Training" : "Add Training"}
        </Typography>
        <IconButton onClick={onClose} sx={{ position: "absolute", right: 8 }}>
          <Cancel />
        </IconButton>
      </DialogTitle>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        enableReinitialize
        onSubmit={(values) => {
          const formData = new FormData();
          formData.append("trainingTypeId", values.trainingTypeId);
          formData.append("trainerId", values.trainerId);
          formData.append("employeesId", JSON.stringify(values.employeesId));
          formData.append("trainingCost", values.trainingCost.toString());
          formData.append("startDate", values.startDate);
          formData.append("endDate", values.endDate);
          formData.append("description", values.description);
          formData.append("isActive", values.isActive.toString());
          onSubmit(formData);
        }}
      >
        {({ values, setFieldValue, errors, touched }) => (
          <Form>
            <DialogContent className="dialog-content">
              {!isLoading && (
                <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                  {/* Training Type */}
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Box sx={{ width: "50%" }}>
                      <InputLabel required sx={labelStyles}>
                        Training Type
                      </InputLabel>
                      <Field
                        as={TextField}
                        fullWidth
                        select
                        name="trainingTypeId"
                        error={
                          touched.trainingTypeId &&
                          Boolean(errors.trainingTypeId)
                        }
                        helperText={
                          touched.trainingTypeId && errors.trainingTypeId
                        }
                      >
                        <MenuItem value="">Select Training Type</MenuItem>
                        {trainingTypes?.map((type) => (
                          <MenuItem key={type._id} value={type._id}>
                            {type.type}
                          </MenuItem>
                        ))}
                      </Field>
                    </Box>
                    <Box sx={{ width: "50%" }}>
                      <InputLabel required sx={labelStyles}>
                        Trainer
                      </InputLabel>
                      <Field
                        as={TextField}
                        fullWidth
                        select
                        name="trainerId"
                        error={touched.trainerId && Boolean(errors.trainerId)}
                        helperText={touched.trainerId && errors.trainerId}
                      >
                        <MenuItem value="">Select Trainer</MenuItem>
                        {trainers?.map((trainer) => (
                          <MenuItem key={trainer._id} value={trainer._id}>
                            {`${trainer.firstName} ${trainer.lastName}`}
                          </MenuItem>
                        ))}
                      </Field>
                    </Box>
                  </Box>

                  {/* Employees (Autocomplete) */}
                  <Box>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        mb: 1,
                      }}
                    >
                      <InputLabel required sx={labelStyles}>
                        Employees
                      </InputLabel>
                      <Button
                        size="small"
                        onClick={() => {
                          const allEmployeeIds = (employees || []).map(
                            (emp) => emp._id
                          );
                          const isAllSelected =
                            values.employeesId.length === allEmployeeIds.length;
                          setFieldValue(
                            "employeesId",
                            isAllSelected ? [] : allEmployeeIds
                          );
                        }}
                        sx={{
                          textTransform: "none",
                          fontSize: "12px",
                          color: "#F26522",
                          "&:hover": {
                            backgroundColor: "transparent",
                            textDecoration: "underline",
                          },
                        }}
                      >
                        {values.employeesId.length === (employees || []).length
                          ? "Unselect All"
                          : "Select All"}
                      </Button>
                    </Box>
                    <Autocomplete
                      multiple
                      options={employees || []}
                      getOptionLabel={(option: UserRecord) =>
                        `${option.firstName} ${option.lastName || ""}`
                      }
                      value={
                        employees?.filter((emp) =>
                          values.employeesId.includes(emp._id)
                        ) || []
                      }
                      onChange={(_, newValue) => {
                        setFieldValue(
                          "employeesId",
                          newValue.map((emp) => emp._id)
                        );
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          error={
                            touched.employeesId && Boolean(errors.employeesId)
                          }
                          helperText={touched.employeesId && errors.employeesId}
                          placeholder="Select employees"
                        />
                      )}
                      renderTags={() => null}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          padding: "8px",
                          minHeight: "56px",
                        },
                      }}
                    />
                    {/* Chips container below the input */}
                    <Box
                      sx={{
                        display: "flex",
                        flexWrap: "wrap",
                        gap: "8px",
                        padding: "8px 0",
                        minHeight: "40px",
                      }}
                    >
                      {employees
                        ?.filter((emp) => values.employeesId.includes(emp._id))
                        .map((employee) => (
                          <Box
                            key={employee._id}
                            sx={{
                              backgroundColor: "#E5E7EB",
                              borderRadius: "16px",
                              padding: "4px 12px",
                              display: "flex",
                              alignItems: "center",
                              gap: "8px",
                            }}
                          >
                            <Typography sx={{ fontSize: "14px" }}>
                              {`${employee.firstName} ${employee.lastName || ""}`}
                            </Typography>
                            <IconButton
                              size="small"
                              onClick={() => {
                                setFieldValue(
                                  "employeesId",
                                  values.employeesId.filter(
                                    (id) => id !== employee._id
                                  )
                                );
                              }}
                              sx={{
                                padding: "2px",
                                "&:hover": {
                                  backgroundColor: "rgba(0, 0, 0, 0.1)",
                                },
                              }}
                            >
                              <Cancel sx={{ fontSize: "16px" }} />
                            </IconButton>
                          </Box>
                        ))}
                    </Box>
                  </Box>

                  {/* Training Cost */}
                  <Box>
                    <InputLabel required sx={labelStyles}>
                      Training Cost
                    </InputLabel>
                    <Field
                      as={TextField}
                      fullWidth
                      type="number"
                      name="trainingCost"
                      error={
                        touched.trainingCost && Boolean(errors.trainingCost)
                      }
                      helperText={touched.trainingCost && errors.trainingCost}
                    />
                  </Box>

                  {/* Start and End Dates */}
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Box sx={{ width: "50%" }}>
                      <InputLabel required sx={labelStyles}>
                        Start Date
                      </InputLabel>
                      <Field
                        as={TextField}
                        type="date"
                        name="startDate"
                        fullWidth
                        error={touched.startDate && Boolean(errors.startDate)}
                        helperText={touched.startDate && errors.startDate}
                      />
                    </Box>
                    <Box sx={{ width: "50%" }}>
                      <InputLabel required sx={labelStyles}>
                        End Date
                      </InputLabel>
                      <Field
                        as={TextField}
                        type="date"
                        name="endDate"
                        fullWidth
                        error={touched.endDate && Boolean(errors.endDate)}
                        helperText={touched.endDate && errors.endDate}
                      />
                    </Box>
                  </Box>

                  {/* Description */}
                  <Box>
                    <InputLabel required sx={labelStyles}>
                      Description
                    </InputLabel>
                    {/* <Field
                      as={TextField}
                      fullWidth
                      name="description"
                      multiline
                      rows={3}
                      error={touched.description && Boolean(errors.description)}
                      helperText={touched.description && errors.description}
                    /> */}
                    <JoditEditorWrapper
                      value={values.description}
                      onChange={(content) =>
                        setFieldValue("description", content)
                      }
                      height={200}
                    />
                  </Box>

                  {/* Status */}
                  <Box>
                    <InputLabel required sx={labelStyles}>
                      Status
                    </InputLabel>
                    <Field
                      as={TextField}
                      fullWidth
                      select
                      name="isActive"
                      error={touched.isActive && Boolean(errors.isActive)}
                      helperText={touched.isActive && errors.isActive}
                    >
                      <MenuItem value="true">Active</MenuItem>
                      <MenuItem value="false">Inactive</MenuItem>
                    </Field>
                  </Box>
                </Box>
              )}
            </DialogContent>

            <DialogActions>
              <Button
                sx={{
                  fontWeight: "400",
                  fontSize: "14px",
                  color: "#111827",
                  border: "1px solid #E5E7EB",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                onClick={onClose}
                variant="outlined"
              >
                Cancel
              </Button>
              <Button
                sx={{
                  display: "flex",
                  gap: "8px",
                  backgroundColor: "#F26522",
                  borderColor: "#F26522",
                  color: "#FFF",
                  fontWeight: 400,
                  fontSize: "14px",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                type="submit"
                variant="contained"
              >
                {isEditMode ? "Update Training" : "Add Training"}
              </Button>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default AddEditTraining;

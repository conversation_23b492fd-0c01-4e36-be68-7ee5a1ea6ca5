"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Di<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Avatar,
  AvatarGroup,
  Paper,
} from "@mui/material";
import { useCallback, useState } from "react";
import "./training.scss";
import {
  HomeOutlined,
  EditNote,
  Delete,
  ControlPoint,
  Circle,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
  GridRenderCellParams,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import AddEditTraining from "./AddEditTraining";
import Loader from "@/components/Loader/Loader";
import PolicyFilters from "@/components/policyFilter/PolicyFilters";
import { toast } from "react-toastify";
import {
  addTraining,
  updateTraining,
  deleteTraining,
} from "@/app/services/training/training.service";
import useFetchTrainingData from "../hooks/training/useFetchTrainingData";
import useRoleAuth from "../hooks/useRoleAuth";
import { ROLE_GROUPS } from "../constants/roles";
import ReadMore from "@/components/ReadMore/ReadMore";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";
import { useDebounce } from "../hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

// Define the TrainingBody interface for form submission
interface TrainingBody {
  trainingTypeId: string;
  trainerId: string;
  employeesId: string[];
  trainingCost: number;
  startDate: string;
  endDate: string;
  description: string;
  isActive: boolean;
}

// Define interface for Training Record
interface TrainingRecord {
  _id: string;
  trainingTypeId: string | TrainingType;
  trainerId: string | Trainer;
  employeesId: Array<string | Employee>;
  startDate: string;
  endDate: string;
  description: string;
  trainingCost: number;
  isActive: boolean;
}

// Define interface for Training Type
interface TrainingType {
  _id: string;
  type: string;
  description?: string;
  isActive?: boolean;
}

// Define interface for Trainer
interface Trainer {
  _id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
}

// Define interface for Employee
interface Employee {
  _id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
}

// Utility function to format date range
const formatDateRange = (startDate: string, endDate: string): string => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const options: Intl.DateTimeFormatOptions = {
    day: "2-digit",
    month: "short",
    year: "numeric",
  };
  return `${start.toLocaleDateString("en-GB", options)} - ${end.toLocaleDateString("en-GB", options)}`;
};


export default function Training() {
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Performance", href: "" },
    { label: "Add Training" },
  ];

  // State management
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);

  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedTrainingId, setSelectedTrainingId] = useState<string | null>(
    null
  );
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [trainingToDelete, setTrainingToDelete] = useState<
    string | number | null
  >(null);

  // Filter states
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const currentDate = new Date();

  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Fetch training data
  const [trainingData, total] = useFetchTrainingData({
    setIsLoading,
    refresh,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  // Restrict access to Admin roles only (Admin, SuperAdmin, Manager, HR)
  const { roles } = useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  // Check if the user is an employee (and not an admin role)
  const isEmployee =
    roles.includes("Employee") &&
    !roles.some((role) =>
      ["Admin", "SuperAdmin", "HR", "Manager"].includes(role)
    );

  const rows: GridRowsProp = trainingData
    ? trainingData.map((training: any, index: number) => {
      // Handle populated objects properly
      let trainerName = "Unknown";
      let trainerAvatar = "";

      if (training.trainerId && typeof training.trainerId === "object") {
        trainerName =
          `${training.trainerId.firstName || ""} ${training.trainerId.lastName || ""}`.trim() ||
          "-";
        trainerAvatar = training.trainerId.avatar || "";
      }

      // Handle training type as either object or string
      let trainingTypeName = "Unknown";
      if (
        training.trainingTypeId &&
        typeof training.trainingTypeId === "object"
      ) {
        trainingTypeName = training.trainingTypeId.type || "Unknown";
      }

      // Handle employees array
      const employees =
        training.employeesId && Array.isArray(training.employeesId)
          ? training.employeesId.map((emp: string | Employee) => {
            if (typeof emp === "object") {
              return {
                name: `${emp.firstName || ""} ${emp.lastName || ""}`.trim(),
                avatar: emp.avatar || "",
              };
            }
            return { name: "Unknown", avatar: "" };
          })
          : [];

      const employeesIds =
        training.employeesId && Array.isArray(training.employeesId)
          ? training.employeesId.map((emp: string | Employee) => {
            if (typeof emp === "object") {
              return emp._id;
            }
            return emp;
          })
          : [];

      interface EmployeeDetails {
        name: string;
        avatar: string;
      }

      interface TrainingRow {
        id: string;
        trainingType: string;
        trainer: string;
        trainerAvatar: string;
        employees: EmployeeDetails[];
        employeesNames: string[];
        employeesId: string[];
        timeDuration: string;
        description: string;
        trainingCost: string;
        isActive: boolean;
      }

      return {
        id: training._id || `${index + 1}`,
        trainingType: trainingTypeName,
        trainer: trainerName,
        trainerAvatar: trainerAvatar,
        employees: employees,
        employeesNames: employees.map((emp: EmployeeDetails) => emp.name),
        employeesId: employeesIds,
        timeDuration: formatDateRange(training.startDate, training.endDate),
        description: training.description || "",
        trainingCost: training.trainingCost
          ? `₹${training.trainingCost}`
          : "0",
        isActive: training.isActive || false,
      } as TrainingRow;
    })
    : [];

  // Pagination handlers
  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  // Action handlers
  const handleEditClick = (trainingId: string | number) => {
    setSelectedTrainingId(trainingId as string);
    setIsEditMode(true);
    setOpenModal(true);
  };

  const handleDeleteClick = (trainingId: string | number) => {
    setTrainingToDelete(trainingId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (trainingToDelete !== null) {
      try {
        await deleteTraining(trainingToDelete as string);
        setDeleteDialogOpen(false);
        setTrainingToDelete(null);
        setRefresh(!refresh);
        //toast.success("Training deleted successfully!");
      } catch (error) {
        console.error("Failed to delete training:", error);
        // toast.error("Failed to delete training. Please try again.");
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setTrainingToDelete(null);
  };

  const handleDialogSubmit = async (formData: FormData) => {
    try {
      const trainingBody: TrainingBody = {
        trainingTypeId: formData.get("trainingTypeId") as string,
        trainerId: formData.get("trainerId") as string,
        employeesId: JSON.parse(formData.get("employeesId") as string),
        trainingCost: Number(formData.get("trainingCost")),
        startDate: formData.get("startDate") as string,
        endDate: formData.get("endDate") as string,
        description: formData.get("description") as string,
        isActive: formData.get("isActive") === "true",
      };

      if (isEditMode && selectedTrainingId) {
        await updateTraining(selectedTrainingId, trainingBody);
        // toast.success("Training updated successfully!");
      } else {
        await addTraining(trainingBody);
        // toast.success("Training added successfully!");
      }
      setRefresh(!refresh);
      setOpenModal(false);
      setIsEditMode(false);
      setSelectedTrainingId(null);
    } catch (error) {
      console.error(
        `Failed to ${isEditMode ? "update" : "add"} training:`,
        error
      );
      // toast.error(
      //   `Failed to ${isEditMode ? "update" : "add"} training. Please try again.`
      // );
    }
  };

  const columns: GridColDef[] = [
    {
      field: "trainingType",
      headerName: "Training Type",
      minWidth: 131.281,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ color: "#6B7280", fontSize: "14px" }}>
          {params.row.trainingType}
        </Typography>
      ),
    },
    {
      field: "trainer",
      headerName: "Trainer",
      minWidth: 182.328,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {/* Uncomment if trainerAvatar is reliably available */}
          {/* <Avatar src={params.row.trainerAvatar} alt={params.row.trainer} sx={{ width: 32, height: 32 }} /> */}
          <Typography
            variant="body2"
            sx={{
              color: "#111827",
              fontSize: "14px",
              fontWeight: "500 !important",
            }}
          >
            {params.row.trainer}
          </Typography>
        </Box>
      ),
    },
    {
      field: "employees",
      headerName: "Employees",
      minWidth: 124.156,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <AvatarGroup
            className="avatarGroup"
            max={5}
            sx={{ display: "flex", gap: "-30px" }}
          >
            {params.row.employees.map(
              (employee: { name: string; avatar: string }, index: number) => (
                <Avatar
                  key={index}
                  className="avatar"
                  alt={employee.name}
                  src={
                    employee.avatar ||
                    `/assets/users/user-${(index % 10) + 1}.jpg`
                  }
                />
              )
            )}
          </AvatarGroup>
        </Box>
      ),
    },
    {
      field: "timeDuration",
      headerName: "Time Duration",
      minWidth: 218.047,
      flex: 1,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{
            color: "#6B7280",
            fontSize: "14px",
          }}
        >
          {params.row.timeDuration}
        </Typography>
      ),
    },
    {
      field: "description",
      headerName: "Description",
      minWidth: 352.5,
      flex: 3,
      renderCell: (params) => (
        <Box
          sx={{
            color: "#6B7280",
            fontSize: "14px",
            width: "100%", // Add this
            maxWidth: "100%", // Update this from 300px
            overflow: "hidden", // Add this
          }}
        >
          <ReadMore
            text={params.row.description}
            maxChars={50}
            sx={{
              width: "100%",
              "& .MuiTypography-root": {
                width: "100%",
                whiteSpace: "normal",
                overflow: "visible",
              },
            }}
          />
        </Box>
      ),
    },
    {
      field: "trainingCost",
      headerName: "Cost",
      minWidth: 49.2812,
      flex: 1,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{
            color: "#6B7280",
            fontSize: "14px",
          }}
        >
          {params.row.trainingCost}
        </Typography>
      ),
    },
    // Only show status column for non-employees
    ...(!isEmployee
      ? [
        {
          field: "isActive",
          headerName: "Status",
          minWidth: 79.2656,
          flex: 1,
          renderCell: (params: GridRenderCellParams) => (
            <Box
              sx={{
                backgroundColor: params.value ? "#03C95A" : "#E70D0D",
                color: "#fff",
                borderRadius: "4px",
                display: "flex",
                textAlign: "center",
                justifyContent: "center",
                fontSize: "10px",
                fontWeight: 500,
                padding: "0px 25px",
                lineHeight: "18px",
                letterSpacing: "0.5px",
                alignItems: "center",
                maxWidth: "52px",
              }}
            >
              <Circle sx={{ fontSize: "6px", marginRight: "4px" }} />
              {params.value ? "Active" : "Inactive"}
            </Box>

            // <StatusToggle
            //   isActive={params.value}
            //   onChange={async (newStatus: boolean) => {
            //     try {
            //       await updateTraining(params.row.id, {
            //         isActive: newStatus,
            //       });
            //       setRefresh(!refresh);
            //     } catch (error) {
            //       console.error("Failed to update training status:", error);
            //     }
            //   }}
            //   title={`Change status to ${params.value ? "Inactive" : "Active"}`}
            // />
          ),
        },
      ]
      : []),
    // Only show actions column for non-employees
    ...(!isEmployee
      ? [
        {
          field: "actions",
          headerName: "",
          disableExport: true,
          flex: 1,
          renderCell: (params: GridRenderCellParams) => (
            <Box sx={{ display: "flex", gap: 1 }}>
              <IconButton
                sx={{ color: "#6B7280" }}
                onClick={() => handleEditClick(params.row.id)}
              >
                <EditNote sx={{ fontSize: "16px" }} />
              </IconButton>
              <IconButton
                sx={{ color: "#6B7280" }}
                onClick={() => handleDeleteClick(params.row.id)}
              >
                <Delete sx={{ fontSize: "16px" }} />
              </IconButton>
            </Box>
          ),
        },
      ]
      : []),
  ];

  return (
    <Box className="training-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="training-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Training</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          {/* Only show Add Training button for non-employees */}
          {!isEmployee && (
            <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
              <Button
                variant="contained"
                sx={{
                  display: "flex",
                  gap: "8px",
                  backgroundColor: "#F26522",
                  borderColor: "#F26522",
                  color: "#FFF",
                  fontWeight: 400,
                  fontSize: "14px",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                onClick={() => {
                  setIsEditMode(false);
                  setOpenModal(true);
                }}
              >
                <ControlPoint sx={{ width: "14px", height: "14px" }} />
                Add Training
              </Button>
            </Box>
          )}
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{ display: "flex", flexDirection: "column", minHeight: 400 }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Training List</Typography>
              <PolicyFilters
                departments={[]}
                selectedDepartment={""}
                setSelectedDepartment={() => { }}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={""}
                setSelectedDateRange={() => { }}
                setStartDate={() => { }}
                setEndDate={() => { }}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={false}
                showDepartmentFilter={false}
                showStatusFilter={false}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => { }}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => { }}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => { }}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              // checkboxSelection
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                // Direct approach - use model values directly
                setPage(model.page + 1);
                setPageSize(model.pageSize);
                setLimit(model.pageSize);
                // Refresh data with new pagination values
                setRefresh((prev) => !prev);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
              getRowHeight={() => "auto"} // Enable dynamic row height
              sx={{
                "& .MuiDataGrid-cell": {
                  maxHeight: "none !important",
                  overflow: "visible !important",
                  whiteSpace: "normal !important",
                  lineHeight: "1.5 !important",
                  display: "flex !important",
                  alignItems: "center !important",
                  padding: "8px 16px !important",
                },
                "& .MuiDataGrid-row": {
                  maxHeight: "none !important",
                },
              }}
            />
          </Box>
        </Paper>
      </Box>

      <AddEditTraining
        open={openModal}
        onClose={() => {
          setOpenModal(false);
          setIsEditMode(false);
          setSelectedTrainingId(null);
        }}
        onSubmit={handleDialogSubmit}
        isEditMode={isEditMode}
        trainingId={selectedTrainingId}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this training? This action cannot be undone."
      />
    </Box>
  );
}

"use client";

import "./promotion.scss";
import {
  Box,
  IconButton,
  Button,
  Divider,
  Typography,
  Paper,
  useMediaQuery,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import { HomeOutlined, ControlPoint, Circle } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { EditNote, Delete } from "@mui/icons-material";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import Loader from "@/components/Loader/Loader";
import PolicyFilters from "@/components/policyFilter/PolicyFilters";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";
import {
  getPromotion,
  getAllPromotion,
  getPromotionById,
  addPromotion,
  updatePromotion,
  deletePromotion,
} from "@/app/services/promotion.service";
import AddPromotionDialog from "./AddPromotionDialog";
import useFetchUsersData from "../hooks/users/useFetchUsersData";
import { getAllDesignations } from "@/app/services/designation.service";

// Quick Search Toolbar Component
function QuickSearchToolbar() {
  return (
    <Box
      sx={{
        p: 0.5,
        pb: 0,
        justifyContent: "space-between",
        padding: "10px 20px",
      }}
    >
      <GridToolbarQuickFilter
        placeholder="Search"
        className="grid-search"
        sx={{ textDecoration: "none" }}
      />
      <Box
        className="grid-export"
        sx={{ display: "flex", alignItems: "center" }}
      >
        <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
      </Box>
    </Box>
  );
}

export default function PromotionPage() {
  // State for loading and refresh
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);

  // State for pagination
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);

  // State for the Add/Edit Department modal
  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedPromotionId, setSelectedPromotionId] = useState<string | null>(
    null
  );

  // State for filters
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>("Date Range");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const currentDate = new Date();

  // Restrict access to admin roles only (Admin, SuperAdmin, Manager, HR)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // State for promotions data
  const [promotionsData, setPromotionsData] = useState<any[]>([]);
  const [promotionsTotal, setPromotionsTotal] = useState(0);

  const [userData] = useFetchUsersData({
    setIsLoading,
    refresh,
    limit: 1000,
    page: 1,
    departmentName:
      selectedDepartment === "Department" ? "" : selectedDepartment,
    designationName: "",
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate: startDate || undefined,
    endDate: endDate || undefined,
    isActive: "true", // Always fetch only active employees
  });

  useEffect(() => {
    const fetchPromotions = async () => {
      setIsLoading(true);
      try {
        const response = await getPromotion(
          limit,
          page,
          undefined, // departmentName not needed since filter removed
          selectedSortBy === "Ascending"
            ? "asc"
            : selectedSortBy === "Descending"
              ? "desc"
              : undefined,
          startDate,
          endDate,
          undefined // isActive not needed since filter removed
        );

        // Check if response and promotions exist
        if (response?.promotions?.results) {
          const promotionsData = response.promotions.results.map(
            (promotion: any, index: number) => ({
              id: promotion._id || index + 1,
              promotedEmployee: promotion.empId
                ? `${promotion.empId.firstName || ""} ${promotion.empId.lastName || ""}`.trim()
                : "-",
              department: promotion.empId?.departmentId?.departmentName || "-",
              designationFrom:
                promotion.currentDesignation?.designationName || "-",
              designationTo: promotion.newDesignation?.designationName || "-",
              promotionDate: promotion.promotionDate
                ? new Date(promotion.promotionDate).toISOString().split("T")[0]
                : "-",
              promotionDateRaw: promotion.promotionDate
                ? new Date(promotion.promotionDate)
                : new Date(),
              isActive: promotion.isActive || false,
              // Store full objects for edit functionality
              empId: promotion.empId || null,
              currentDesignation: promotion.currentDesignation || null,
              newDesignation: promotion.newDesignation || null,
            })
          );
          setPromotionsData(promotionsData);
          setPromotionsTotal(response.promotions.total || 0);
        }
      } catch (error) {
        console.error("Error fetching promotions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPromotions();
  }, [refresh, page, limit, selectedSortBy, startDate, endDate]);

  const rows: GridRowsProp = promotionsData;

  // State for delete dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [promotionToDelete, setPromotionToDelete] = useState<
    string | number | null
  >(null);

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Performance", href: "" },
    { label: "Promotion" },
  ];

  const isMobile = useMediaQuery("(max-width:768px)");

  const columns: GridColDef[] = [
    {
      field: "promotedEmployee",
      headerName: "Promoted Employee",
      flex: 1,
    },
    {
      field: "department",
      headerName: "Department",
      flex: 1,
      renderCell: (params) => <Typography>{params.value}</Typography>,
    },
    {
      field: "designationFrom",
      headerName: "Designation From",
      flex: 1,
      renderCell: (params) => <Typography>{params.value || "N/A"}</Typography>,
    },
    {
      field: "designationTo",
      headerName: "Designation To",
      flex: 1,
      renderCell: (params) => <Typography>{params.value || "N/A"}</Typography>,
    },
    {
      field: "promotionDate",
      headerName: "Promotion Date",
      flex: 1,
      renderCell: (params) => (
        <Typography>
          {params.value ? new Date(params.value).toLocaleDateString() : "N/A"}
        </Typography>
      ),
    },

    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 0.5,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setPromotionToDelete(null);
  };

  const handleDeleteClick = (promotionId: string | number) => {
    setPromotionToDelete(promotionId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (promotionToDelete) {
      try {
        await deletePromotion(promotionToDelete.toString());
        setDeleteDialogOpen(false);
        setPromotionToDelete(null);
        setRefresh(!refresh);
      } catch (error) {
        console.error("Error deleting promotion:", error);
      }
    }
  };

  const handlePromotionSubmit = async (data: any) => {
    console.log("Submitting promotion data:", data);
    try {
      setIsLoading(true);
      if (isEditMode && selectedPromotionId) {
        await updatePromotion(selectedPromotionId, {
          empId: data.empId,
          currentDesignation: data.currentDesignation,
          newDesignation: data.newDesignation,
          promotionDate: data.promotionDate,
        });
      } else {
        await addPromotion({
          empId: data.empId,
          currentDesignation: data.currentDesignation,
          newDesignation: data.newDesignation,
          promotionDate: data.promotionDate,
        });
      }
      handleDialogClose();
      setRefresh(!refresh);
    } catch (error) {
      console.error("Error submitting promotion:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDialogClose = () => {
    setOpenModal(false);
    setIsEditMode(false);
    setSelectedPromotionId(null);
  };

  // State for designations
  const [designations, setDesignations] = useState<
    Array<{
      _id: string;
      designationName: string;
    }>
  >([]);

  // Fetch designations on mount
  useEffect(() => {
    const fetchDesignations = async () => {
      try {
        const response = await getAllDesignations();
        if (response?.designations?.results) {
          setDesignations(response.designations.results);
        }
      } catch (error) {
        console.error("Failed to fetch designations:", error);
      }
    };

    fetchDesignations();
  }, []);

  const handleEditClick = async (promotionId: string | number) => {
    try {
      setIsLoading(true);
      const response = await getPromotionById(promotionId.toString());
      if (response?.promotion) {
        const promotion = response.promotion;
        // Update the promotions data with the fetched promotion
        setPromotionsData((prevData) => {
          const newData = [...prevData];
          const index = newData.findIndex((p) => p.id === promotionId);
          if (index !== -1) {
            newData[index] = {
              id: promotion._id,
              promotedEmployee: `${promotion.empId?.firstName || ""} ${promotion.empId?.lastName || ""}`,
              department: promotion.empId?.departmentId?.departmentName || "-",
              designationFrom:
                promotion.currentDesignation?.designationName || "-",
              designationTo: promotion.newDesignation?.designationName || "-",
              promotionDate: promotion.promotionDate || "-",
              isActive: promotion.isActive || false,
              empId: promotion.empId || null,
              currentDesignation: promotion.currentDesignation || null,
              newDesignation: promotion.newDesignation || null,
            };
          }
          return newData;
        });
        setSelectedPromotionId(promotionId.toString());
        setIsEditMode(true);
        setOpenModal(true);
      }
    } catch (error) {
      console.error("Error fetching promotion details:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box className="promotion-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="promotion-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Promotion</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="add-department"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => {
                setIsEditMode(false);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ width: "16px", height: "16px" }} />
              Add Promotion
            </Button>
          </Box>
        </Box>
        {/* DataGrid */}
        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight:
                rows.length < 5 ? "calc(70vh - 200px)" : "calc(100vh - 200px)",
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Promotion List</Typography>
              {/* Integrate PolicyFilters Component */}
              <PolicyFilters
                departments={[]}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={false}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority="Priority"
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              // checkboxSelection
              rows={rows}
              columns={columns}
              rowCount={promotionsTotal}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                setPage(model.page + 1);
                setPageSize(model.pageSize);
                setLimit(model.pageSize);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: QuickSearchToolbar,
              }}
            />
          </Box>
        </Paper>
      </Box>

      {/* Add/Edit Promotion Modal */}
      <AddPromotionDialog
        open={openModal}
        onClose={handleDialogClose}
        isEditMode={isEditMode}
        promotionData={
          selectedPromotionId
            ? promotionsData.find((p) => p.id === selectedPromotionId)
            : undefined
        }
        userData={(userData || []).map((user) => ({
          _id: user._id,
          firstName: user.firstName || "",
          lastName: user.lastName || "",
          designationId: user.designationId,
        }))}
        designations={designations}
        onSubmit={handlePromotionSubmit}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        title="Delete Promotion"
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this promotion? This action cannot be undone."
      />
    </Box>
  );
}

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  TextField,
  IconButton,
  Box,
} from "@mui/material";
import { useFormik } from "formik";
import * as Yup from "yup";
import "./AddPromotionDialog.scss";
import { Cancel } from "@mui/icons-material";

interface AddPromotionDialogProps {
  open: boolean;
  onClose: () => void;
  isEditMode?: boolean;
  promotionData?: {
    empId: {
      firstName: string;
      lastName: string;
      _id: string;
      designationId?: string | { _id: string; designationName: string };
    };
    currentDesignation: { designationName: string; _id: string };
    newDesignation: { designationName: string; _id: string };
    promotionDate: string;
  };
  onSubmit: (data: {
    empId: string;
    currentDesignation: string;
    newDesignation: string;
    promotionDate: string;
  }) => Promise<void>;
  userData?: Array<{
    _id: string;
    firstName: string;
    lastName: string;
    designationId?: string | { _id: string; designationName: string };
  }>;
  designations?: Array<{
    _id: string;
    designationName: string;
  }>;
}

const validationSchema = Yup.object({
  empId: Yup.string().required("Employee is required"),
  currentDesignation: Yup.string().required("Current designation is required"),
  newDesignation: Yup.string()
    .required("New designation is required")
    .notOneOf(
      [Yup.ref("currentDesignation")],
      "New designation must be different from current designation"
    ),
  promotionDate: Yup.date()
    .required("Promotion date is required")
    .test(
      "futureDate",
      "Promotion date cannot be in the past",
      function (value) {
        if (!value) return false;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return new Date(value) >= today;
      }
    ),
});

const AddPromotionDialog: React.FC<AddPromotionDialogProps> = ({
  open,
  onClose,
  isEditMode = false,
  promotionData,
  onSubmit,
  userData = [],
  designations = [],
}) => {
  const formik = useFormik({
    initialValues: {
      empId: "",
      currentDesignation: "",
      newDesignation: "",
      promotionDate: new Date().toISOString().split("T")[0],
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        // Format date to ISO string for API consistency
        const formattedValues = {
          ...values,
          promotionDate: new Date(values.promotionDate).toISOString(),
        };
        await onSubmit(formattedValues);
        onClose();
      } catch (error) {
        console.error("Error submitting promotion:", error);
      }
    },
  });

  // Effect to handle employee selection and set current designation
  React.useEffect(() => {
    if (!isEditMode && formik.values.empId) {
      const selectedEmployee = userData.find(
        (user) => user._id === formik.values.empId
      );
      if (selectedEmployee?.designationId) {
        let currentDesignationId = "";

        // Handle both string and object cases for designationId
        if (
          typeof selectedEmployee.designationId === "object" &&
          selectedEmployee.designationId !== null
        ) {
          currentDesignationId = selectedEmployee.designationId._id;
        } else if (typeof selectedEmployee.designationId === "string") {
          currentDesignationId = selectedEmployee.designationId;
        }

        formik.setFieldValue("currentDesignation", currentDesignationId);
      }
    }
  }, [formik.values.empId, isEditMode]);

  React.useEffect(() => {
    if (isEditMode && promotionData) {
      formik.setValues({
        empId: promotionData.empId._id,
        currentDesignation: promotionData.currentDesignation._id,
        newDesignation: promotionData.newDesignation._id,
        promotionDate: new Date(promotionData.promotionDate)
          .toISOString()
          .split("T")[0],
      });
    } else {
      formik.resetForm();
    }
  }, [isEditMode, promotionData]);

  // Reset form when dialog is closed
  React.useEffect(() => {
    if (!open) {
      formik.resetForm();
    } else if (!isEditMode) {
      // Set today's date when opening in add mode
      formik.setFieldValue(
        "promotionDate",
        new Date().toISOString().split("T")[0]
      );
    }
  }, [open]);

  return (
    <Dialog
      className="add-promotion-dialog"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          width: "500px",
          maxWidth: "500px",
        },
      }}
    >
      <DialogTitle className="dialog-title">
        {isEditMode ? "Edit Promotion" : "Add Promotion"}
        <IconButton aria-label="close" onClick={onClose}>
          <Cancel
            sx={{
              color: "#6B7280",
              fontSize: "20px",
              "&:hover": {
                color: "#D93D3D",
              },
            }}
          />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        <form id="promotionForm" onSubmit={formik.handleSubmit}>
          <Box className="input-fields">
            {/* Promotion For */}
            <InputLabel>
              Promotion For <span className="required">*</span>
            </InputLabel>
            <FormControl
              fullWidth
              error={formik.touched.empId && Boolean(formik.errors.empId)}
            >
              <Select
                name="empId"
                value={formik.values.empId}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                disabled={isEditMode}
              >
                <MenuItem value="">Select Employee</MenuItem>
                {userData.map((user) => (
                  <MenuItem key={user._id} value={user._id}>
                    {`${user.firstName || ""} ${user.lastName || ""}`.trim() ||
                      "Unknown"}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.empId && formik.errors.empId && (
                <div className="error-text">{formik.errors.empId}</div>
              )}
            </FormControl>
          </Box>

          <Box className="input-fields">
            {/* Promotion From */}
            <InputLabel>Promotion From *</InputLabel>
            <FormControl
              fullWidth
              error={
                formik.touched.currentDesignation &&
                Boolean(formik.errors.currentDesignation)
              }
            >
              <Select
                name="currentDesignation"
                value={formik.values.currentDesignation}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                disabled
              >
                <MenuItem value="">Select Designation</MenuItem>
                {designations.map((designation) => (
                  <MenuItem key={designation._id} value={designation._id}>
                    {designation.designationName}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.currentDesignation &&
                formik.errors.currentDesignation && (
                  <div className="error-text">
                    {formik.errors.currentDesignation}
                  </div>
                )}
            </FormControl>
          </Box>

          <Box className="input-fields">
            {/* Promotion To */}
            <InputLabel>
              Promotion To <span className="required">*</span>
            </InputLabel>
            <FormControl
              fullWidth
              error={
                formik.touched.newDesignation &&
                Boolean(formik.errors.newDesignation)
              }
            >
              <Select
                name="newDesignation"
                value={formik.values.newDesignation}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              >
                <MenuItem value="">Select Designation</MenuItem>
                {designations.map((designation) => (
                  <MenuItem key={designation._id} value={designation._id}>
                    {designation.designationName}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.newDesignation &&
                formik.errors.newDesignation && (
                  <div className="error-text">
                    {formik.errors.newDesignation}
                  </div>
                )}
            </FormControl>
          </Box>

          <Box className="input-fields">
            {/* Promotion Date */}
            <InputLabel>
              Promotion Date <span className="required">*</span>
            </InputLabel>
            <TextField
              fullWidth
              type="date"
              name="promotionDate"
              value={formik.values.promotionDate}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.promotionDate &&
                Boolean(formik.errors.promotionDate)
              }
              helperText={
                formik.touched.promotionDate && formik.errors.promotionDate
              }
              InputLabelProps={{ shrink: true }}
            />
          </Box>
        </form>

        <DialogActions
          sx={{ padding: "0px !important", paddingTop: "1rem !important" }}
        >
          <Button
            variant="outlined"
            onClick={onClose}
            className="cancel-button"
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={(e) => {
              e.preventDefault();
              formik.handleSubmit();
            }}
            className="save-button"
            disabled={!formik.isValid || formik.isSubmitting}
          >
            {isEditMode ? "Save Changes" : "Add Promotion"}
          </Button>
        </DialogActions>
      </DialogContent>
    </Dialog>
  );
};

export default AddPromotionDialog;

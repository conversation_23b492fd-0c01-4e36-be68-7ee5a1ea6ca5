"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alog<PERSON><PERSON>,
  Button,
  FormControl,
  Typography,
  Box,
  TextField,
  Autocomplete,
} from "@mui/material";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import { useState, useEffect } from "react";
import { getAllDesignations } from "@/app/services/designation.service";
import { getUserById, getUsers } from "@/app/services/users.service";
import { getPayrollItem, addPayroll, getPayrollById, updatePayroll } from "@/app/services/payroll.service";
import { Cancel } from "@mui/icons-material";
import { toast } from "react-toastify";

interface Designation {
  _id: string;
  designationName: string;
  departmentId: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface PayrollItem {
  payrollItemId?: string;
  type: "Additions" | "Deductions" | "Overtime" | "Earnings";
  name: string;
  amount: number | null;
}

interface PerformanceIndicatorData {
  employeeId: string;
  netSalary: string;
  isActive: string;
  isDeleted: string;
  [key: string]: string; // Allow dynamic fields for payroll items
}

interface Employee {
  _id: string;
  firstName: string;
  lastName: string;
  employeeId: string;
  designationId: {
    designationName: string;
  };
  payroll?: PayrollItem[];
}

// Yup Validation Schema
const validationSchema = Yup.object().shape({
  employeeId: Yup.string().required("Employee is required"),
  netSalary: Yup.number()
    .typeError("Net salary must be a number")
    .required("Net salary is required")
    .min(0, "Net salary cannot be negative"),
  // Dynamic validation for payroll items
  ...[...Array(100)].reduce((acc, _, index) => {
    return {
      ...acc,
      [`earnings[${index}]`]: Yup.number()
        .nullable()
        .min(0, "Amount cannot be negative")
        .typeError("Amount must be a number"),
      [`additions[${index}]`]: Yup.number()
        .nullable()
        .min(0, "Amount cannot be negative")
        .typeError("Amount must be a number"),
      [`overtime[${index}]`]: Yup.number()
        .nullable()
        .min(0, "Amount cannot be negative")
        .typeError("Amount must be a number"),
      [`deductions[${index}]`]: Yup.number()
        .nullable()
        .min(0, "Amount cannot be negative")
        .typeError("Amount must be a number"),
    };
  }, {}),
});

const initialValues: PerformanceIndicatorData = {
  employeeId: "",
  netSalary: "",
  isActive: "true",
  isDeleted: "false",
};

const selectStyles = {
  "& .MuiSelect-select": {
    display: "flex",
    alignItems: "center",
    minHeight: "43px",
  },
  "& .MuiMenuItem-root": {
    display: "flex",
    alignItems: "center",
    minHeight: "43px",
  },
};

const AddEditEmployeeSalary = ({
  open,
  onClose,
  onSubmit,
  mode = "add",
  selectedIndicatorId,
}: {
  open: boolean;
  onClose: () => void;
  onSubmit: (formData: FormData) => void;
  mode?: "add" | "edit";
  selectedIndicatorId?: string | null;
}) => {
  const [designations, setDesignations] = useState<Designation[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(false);
  const [initialData, setInitialData] =
    useState<PerformanceIndicatorData | null>(null);
  const [payrollItems, setPayrollItems] = useState<{
    earnings: PayrollItem[];
    additions: PayrollItem[];
    overtime: PayrollItem[];
    deductions: PayrollItem[];
  }>({ earnings: [], additions: [], overtime: [], deductions: [] });

  // Fetch default payroll items on mount
  useEffect(() => {
    const fetchDefaultPayrollItems = async () => {
      try {
        setLoading(true);
        const response = await getPayrollItem(true);
        const payrolls = response.payrolls.results || [];

        const defaultPayrollItems: PayrollItem[] = payrolls.map((item: any) => ({
          payrollItemId: item._id,
          type: item.type as "Additions" | "Deductions" | "Overtime" | "Earnings",
          name: item.name,
          amount: item.amount,
        }));

        const earnings = defaultPayrollItems.filter(
          (item) => item.type === "Earnings"
        );
        const additions = defaultPayrollItems.filter(
          (item) => item.type === "Additions"
        );
        const overtime = defaultPayrollItems.filter(
          (item) => item.type === "Overtime"
        );
        const deductions = defaultPayrollItems.filter(
          (item) => item.type === "Deductions"
        );

        setPayrollItems({ earnings, additions, overtime, deductions });
      } catch (error) {
        console.error("Failed to fetch default payroll items:", error);
        setPayrollItems({ earnings: [], additions: [], overtime: [], deductions: [] });
      } finally {
        setLoading(false);
      }
    };
    fetchDefaultPayrollItems();
  }, []);

  // Fetch designations on mount
  useEffect(() => {
    const fetchDesignations = async () => {
      try {
        setLoading(true);
        const response = await getAllDesignations();
        const activeDesignations = response.designations.results.filter(
          (d: Designation) => d.isActive && !d.isDeleted
        );
        setDesignations(activeDesignations);
      } catch (error) {
        console.error("Failed to fetch designations:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchDesignations();
  }, []);

  // Fetch employees from API
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setLoading(true);
        const response = await getUsers();
        setEmployees(response.users.results);
      } catch (error) {
        console.error("Failed to fetch employees:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchEmployees();
  }, []);

  // Fetch payroll data when in edit mode
  useEffect(() => {
    const fetchPayrollData = async () => {
      if (mode === "edit" && selectedIndicatorId) {
        try {
          setLoading(true);
          const response = await getPayrollById(selectedIndicatorId);
          const payroll = response.payroll;

          const dynamicData: PerformanceIndicatorData = {
            employeeId: payroll.empId?._id || "",
            netSalary: payroll.netSalary?.toString() || "",
            isActive: payroll.isActive?.toString() || "true",
            isDeleted: payroll.isDeleted?.toString() || "false",
          };

          // Map earnings
          if (payroll.earnings) {
            Object.entries(payroll.earnings).forEach(([key, value]) => {
              dynamicData[key] = value?.toString() || "";
            });
          }
          // Map deductions
          if (payroll.deductions) {
            Object.entries(payroll.deductions).forEach(([key, value]) => {
              dynamicData[key] = value?.toString() || "";
            });
          }
          // Map additions
          if (payroll.additions) {
            Object.entries(payroll.additions).forEach(([key, value]) => {
              dynamicData[key] = value?.toString() || "";
            });
          }
          // Map overtime
          if (payroll.overtime) {
            Object.entries(payroll.overtime).forEach(([key, value]) => {
              dynamicData[key] = value?.toString() || "";
            });
          }

          setInitialData(dynamicData);

          // Update payroll items based on fetched data
          const earnings = Object.entries(payroll.earnings || {}).map(
            ([name, amount]) => ({
              name,
              amount: Number(amount),
              type: "Earnings" as const,
            })
          );
          const additions = Object.entries(payroll.additions || {}).map(
            ([name, amount]) => ({
              name,
              amount: Number(amount),
              type: "Additions" as const,
            })
          );
          const overtime = Object.entries(payroll.overtime || {}).map(
            ([name, amount]) => ({
              name,
              amount: Number(amount),
              type: "Overtime" as const,
            })
          );
          const deductions = Object.entries(payroll.deductions || {}).map(
            ([name, amount]) => ({
              name,
              amount: Number(amount),
              type: "Deductions" as const,
            })
          );

          setPayrollItems({ earnings, additions, overtime, deductions });
        } catch (error) {
          console.error("Failed to fetch payroll data:", error);
          toast.error("Failed to load payroll data. Please try again.");
        } finally {
          setLoading(false);
        }
      }
    };
    fetchPayrollData();
  }, [mode, selectedIndicatorId]);

  // Fetch payroll items when employee is selected
  const fetchPayrollItems = async (employeeId: string) => {
    try {
      setLoading(true);
      let payroll: PayrollItem[] = [];
      if (employeeId) {
        const response = await getUserById(employeeId);
        payroll = response.user.payroll || [];
      } else {
        const response = await getPayrollItem(true);
        payroll = response.payrolls.results || [];
      }

      const defaultPayrollItems: PayrollItem[] = payroll.map((item: any) => ({
        payrollItemId: item._id,
        type: item.type as "Additions" | "Deductions" | "Overtime" | "Earnings",
        name: item.name,
        amount: item.amount,
      }));

      const earnings = defaultPayrollItems.filter(
        (item) => item.type === "Earnings"
      );
      const additions = defaultPayrollItems.filter(
        (item) => item.type === "Additions"
      );
      const overtime = defaultPayrollItems.filter(
        (item) => item.type === "Overtime"
      );
      const deductions = defaultPayrollItems.filter(
        (item) => item.type === "Deductions"
      );

      setPayrollItems({ earnings, additions, overtime, deductions });
    } catch (error) {
      console.error("Failed to fetch payroll items:", error);
      setPayrollItems({ earnings: [], additions: [], overtime: [], deductions: [] });
    } finally {
      setLoading(false);
    }
  };

  // Helper to chunk arrays into groups of up to 4 without placeholders
  const chunkArray = (array: PayrollItem[], size: number) => {
    const result = [];
    for (let i = 0; i < array.length; i += size) {
      result.push(array.slice(i, i + size));
    }
    return result;
  };

  // Render fields for a section
  const renderSectionFields = (
    items: PayrollItem[],
    sectionName: string,
    values: any,
    handleChange: (e: React.ChangeEvent<any>) => void,
    errors: any,
    touched: any,
    loading: boolean
  ) => {
    if (items.length === 0) {
      return null;
    }

    return chunkArray(items, 4).map((row, rowIndex) => (
      <Box
        key={`${sectionName}-row-${rowIndex}`}
        sx={{
          display: "flex",
          flexWrap: "nowrap",
          gap: "24px",
          "& > *": {
            flex: `1 1 ${100 / row.length}%`,
            maxWidth: "173.5px",
          },
          marginBottom: "1rem",
        }}
      >
        {row.map((item) => (
          <FormControl fullWidth key={item.payrollItemId || item.name}>
            <label>{item.name}</label>
            <Field
              as={TextField}
              name={item.name}
              type="number"
              value={values[item.name] || item.amount?.toString() || ""}
              onChange={handleChange}
              disabled={loading}
              size="small"
              placeholder="Enter amount"
              error={touched[item.name] && Boolean(errors[item.name])}
              helperText={touched[item.name] && errors[item.name]}
            />
          </FormControl>
        ))}
      </Box>
    ));
  };

  return (
    <Dialog
      className="add-employee-salary-dialog"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth="md"
      sx={{
        "& .MuiDialog-paper": { maxWidth: "800px" },
        "& label": {
          fontSize: "14px",
          fontWeight: 500,
          color: "#202C4B",
          marginBottom: "8px",
          marginTop: "0 !important",
          padding: 0,
          display: "block",
        },
      }}
    >
      <DialogTitle className="dialog-title">
        <Typography
          sx={{
            fontSize: "20px !important",
            fontWeight: 600,
            color: "#202C4B",
          }}
        >
          {mode === "add" ? "Add Employee Salary" : "Edit Employee Salary"}
        </Typography>
        <Cancel
          sx={{
            color: "#6B7280",
            cursor: "pointer",
            fontSize: "20px",
            ":hover": { color: "#D93D3D" },
          }}
          onClick={onClose}
        />
      </DialogTitle>
      <DialogContent className="dialog-content">
        <Formik
          initialValues={
            mode === "edit" && initialData ? initialData : initialValues
          }
          validationSchema={validationSchema}
          enableReinitialize={true}
          onSubmit={async (values) => {
            const formData = new FormData();
            formData.append("empId", values.employeeId);
            formData.append("netSalary", values.netSalary);
            formData.append("isActive", values.isActive);

            // Append earnings
            payrollItems.earnings.forEach((item, index) => {
              formData.append(`earnings[${index}].name`, item.name);
              formData.append(
                `earnings[${index}].amount`,
                values[item.name] || item.amount?.toString() || ""
              );
            });
            // Append additions
            payrollItems.additions.forEach((item, index) => {
              formData.append(`additions[${index}].name`, item.name);
              formData.append(
                `additions[${index}].amount`,
                values[item.name] || item.amount?.toString() || ""
              );
            });
            // Append overtime
            payrollItems.overtime.forEach((item, index) => {
              formData.append(`overtime[${index}].name`, item.name);
              formData.append(
                `overtime[${index}].amount`,
                values[item.name] || item.amount?.toString() || ""
              );
            });
            // Append deductions
            payrollItems.deductions.forEach((item, index) => {
              formData.append(`deductions[${index}].name`, item.name);
              formData.append(
                `deductions[${index}].amount`,
                values[item.name] || item.amount?.toString() || ""
              );
            });

            try {
              if (mode === "edit" && selectedIndicatorId) {
                await updatePayroll(selectedIndicatorId, formData);
                toast.success("Payroll updated successfully.");
              } else {
                await addPayroll(formData);
                toast.success("Payroll added successfully.");
              }
              onSubmit(formData);
              onClose();
            } catch (error) {
              console.error(`Failed to ${mode === "edit" ? "update" : "add"} payroll:`, error);
              toast.error(`Failed to ${mode === "edit" ? "update" : "add"} payroll. Please try again.`);
            }
          }}
        >
          {({ values, handleChange, setFieldValue, touched, errors }) => (
            <Form>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                <Box sx={{ display: "flex", gap: 2 }}>
                  <Box sx={{ width: "100%" }}>
                    <FormControl fullWidth>
                      <label>Employee Name</label>
                      <Autocomplete<Employee>
                        id="employee-autocomplete"
                        options={employees}
                        getOptionLabel={(option: Employee) =>
                          `${option.firstName} ${option.lastName} (${option.employeeId})`
                        }
                        value={
                          employees.find(
                            (emp) => emp._id === values.employeeId
                          ) || null
                        }
                        onChange={(event, newValue) => {
                          const employeeId = newValue ? newValue._id : "";
                          setFieldValue("employeeId", employeeId);
                          fetchPayrollItems(employeeId);
                        }}
                        disabled={loading}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            placeholder="Select Employee"
                            error={
                              touched.employeeId && Boolean(errors.employeeId)
                            }
                            helperText={touched.employeeId && errors.employeeId}
                          />
                        )}
                      />
                    </FormControl>
                  </Box>
                  <Box sx={{ width: "100%" }}>
                    <FormControl fullWidth>
                      <label>Net Salary</label>
                      <Field
                        name="netSalary"
                        as={TextField}
                        type="number"
                        placeholder="Enter net salary"
                        fullWidth
                        InputLabelProps={{
                          shrink: true,
                        }}
                        inputProps={{
                          min: 0,
                        }}
                        disabled={loading}
                        error={touched.netSalary && Boolean(errors.netSalary)}
                        helperText={touched.netSalary && errors.netSalary}
                      />
                    </FormControl>
                  </Box>
                </Box>

                {/* Earnings Section */}
                <Box sx={{ width: "100%", marginBottom: "1rem" }}>
                  <Typography
                    variant="h6"
                    sx={{ fontSize: "14px", fontWeight: 500, color: "#202C4B" }}
                  >
                    Earnings
                  </Typography>
                </Box>
                {renderSectionFields(
                  payrollItems.earnings,
                  "earnings",
                  values,
                  handleChange,
                  errors,
                  touched,
                  loading
                )}

                {/* Additions Section */}
                <Box sx={{ width: "100%", marginBottom: "1rem" }}>
                  <Typography
                    variant="h6"
                    sx={{ fontSize: "14px", fontWeight: 500, color: "#202C4B" }}
                  >
                    Additions
                  </Typography>
                </Box>
                {renderSectionFields(
                  payrollItems.additions,
                  "additions",
                  values,
                  handleChange,
                  errors,
                  touched,
                  loading
                )}

                {/* Overtime Section */}
                <Box sx={{ width: "100%", marginBottom: "1rem" }}>
                  <Typography
                    variant="h6"
                    sx={{ fontSize: "14px", fontWeight: 500, color: "#202C4B" }}
                  >
                    Overtime
                  </Typography>
                </Box>
                {renderSectionFields(
                  payrollItems.overtime,
                  "overtime",
                  values,
                  handleChange,
                  errors,
                  touched,
                  loading
                )}

                {/* Deductions Section */}
                <Box sx={{ width: "100%", marginBottom: "1rem" }}>
                  <Typography
                    variant="h6"
                    sx={{ fontSize: "14px", fontWeight: 500, color: "#202C4B" }}
                  >
                    Deductions
                  </Typography>
                </Box>
                {renderSectionFields(
                  payrollItems.deductions,
                  "deductions",
                  values,
                  handleChange,
                  errors,
                  touched,
                  loading
                )}
              </Box>

              <DialogActions
                sx={{
                  padding: "0px !important",
                  paddingTop: "1rem !important",
                }}
              >
                <Button
                  onClick={onClose}
                  sx={{
                    backgroundColor: "#f8f9fa",
                    border: "1px solid #f8f9fa",
                    color: "#111827",
                    textTransform: "none",
                    borderRadius: "5px",
                    fontSize: 14,
                    fontWeight: 500,
                    padding: ".5rem .85rem",
                    transition: "all .5s",
                  }}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  sx={{
                    backgroundColor: "#F26522",
                    color: "#FFF",
                    textTransform: "none",
                    "&:hover": {
                      backgroundColor: "#d55a1d",
                    },
                  }}
                  disabled={loading}
                >
                  {mode === "add" ? "Add Employee Salary" : "Save Changes"}
                </Button>
              </DialogActions>
            </Form>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  );
};

export default AddEditEmployeeSalary;
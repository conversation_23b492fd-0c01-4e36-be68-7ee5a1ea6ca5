"use client";

import {
  <PERSON>,
  Divider,
  Avatar,
  Typography,
  Paper,
  Button,
  IconButton,
} from "@mui/material";
import React, { useState, useEffect, Suspense, useCallback } from "react";
import { useRouter } from "next/navigation";
import "./employee-salary.scss";
import {
  HomeOutlined,
  ControlPoint,
  EditNote,
  Delete,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  useGridApiRef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import Link from "next/link";
import Loader from "@/components/Loader/Loader";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import { toast } from "react-toastify";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import AddEditEmployeeSalary from "./AddEditEmployeeSalary";
import { deletePayRoll, getPayRoll } from "@/app/services/payroll.service";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

interface EmployeesList {
  id: string;
  empId: string;
  name: string;
  email: string;
  phone: string;
  designationName: string;
  joiningDate: string;
  isActive: string;
  netSalary: number;
  earnings: { [key: string]: number };
  deductions: { [key: string]: number };
  additions: { [key: string]: number };
  overtime: { [key: string]: number };
}

interface Department {
  _id: string;
  departmentName: string;
}

const formatDateForApi = (dateStr: string) => {
  if (!dateStr) return undefined;
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return undefined;
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return undefined;
  }
};

function EmployeesListContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);
  const [payrollData, setPayrollData] = useState<any[]>([]);
  const [totalPayrollsCount, setTotalPayrollsCount] = useState(0);
  const [activePayrollsCount, setActivePayrollsCount] = useState(0);
  const [inactivePayrollsCount, setInactivePayrollsCount] = useState(0);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [openModal, setOpenModal] = useState(false);
  const [modalMode, setModalMode] = useState<"add" | "edit">("add");
  const [selectedIndicatorId, setSelectedIndicatorId] = useState<string | null>(
    null
  );

  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");

  // Restrict access to admin roles only
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // Fetch payroll data
  useEffect(() => {
    const fetchPayrollData = async () => {
      setIsLoading(true);
      try {
        const response = await getPayRoll(
          limit,
          page,
          selectedSortBy === "Ascending"
            ? "asc"
            : selectedSortBy === "Descending"
              ? "desc"
              : "desc",
          startDate ? formatDateForApi(startDate) : undefined,
          endDate ? formatDateForApi(endDate) : undefined,
          selectedStatus === "Active"
            ? "true"
            : selectedStatus === "Inactive"
              ? "false"
              : undefined,
          debouncedSearchQuery && debouncedSearchQuery.length >= 3
            ? debouncedSearchQuery
            : undefined
        );
        if (response.success) {
          setPayrollData(response.payrolls.results);
          setTotalPayrollsCount(response.payrolls.total);
          setActivePayrollsCount(response.payrolls.activeCount);
          setInactivePayrollsCount(response.payrolls.inactiveCount);
        }
      } catch (error) {
        console.error("Error fetching payroll data:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchPayrollData();
  }, [
    refresh,
    limit,
    page,
    selectedSortBy,
    startDate,
    endDate,
    selectedStatus,
    selectedDepartment,
    debouncedSearchQuery,
  ]);
  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  const rows: GridRowsProp = payrollData.map((payroll: any, index: number) => ({
    id: payroll._id || index + 1,
    empId: payroll.empId.employeeId,
    name: `${payroll.empId.firstName} ${payroll.empId.lastName}`,
    avatar: payroll.empId.avatar,
    department: payroll.empId.departmentId?.departmentName || "-",
    email: payroll.empId.email,
    phone: payroll.empId.phone,
    designationName: payroll.empId.designationId?.designationName || "-",
    joiningDate: payroll.empId.joiningDate
      ? new Date(payroll.empId.joiningDate).toLocaleDateString()
      : "-",
    isActive: payroll.isActive ? "Active" : "Inactive",
    netSalary: payroll.netSalary,
    earnings: payroll.earnings,
    deductions: payroll.deductions,
    additions: payroll.additions,
    overtime: payroll.overtime,
  }));

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "HR", href: "" },
    { label: "Employee Salary" },
  ];

  const [deleteModal, setDeleteModal] = useState(false);
  const [deletingEmployeesList, setDeletingEmployeesList] =
    useState<EmployeesList | null>(null);

  const apiRef = useGridApiRef();
  const router = useRouter();

  const refreshList = () => {
    setRefresh(!refresh);
  };

  const handleEditClick = (row: EmployeesList) => {
    setSelectedIndicatorId(row.id);
    setModalMode("edit");
    setOpenModal(true);
  };

  const handleDeleteClick = (row: EmployeesList) => {
    setDeletingEmployeesList(row);
    setDeleteModal(true);
  };

  const handleDelete = async () => {
    if (deletingEmployeesList) {
      try {
        await deletePayRoll(deletingEmployeesList.id.toString());
        toast.success("Payroll record deleted successfully.");
        setRefresh(!refresh);
        setDeleteModal(false);
      } catch (error) {
        console.error("Error deleting payroll record:", error);
        toast.error("Failed to delete payroll record. Please try again.");
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteModal(false);
    setDeletingEmployeesList(null);
  };

  const columns: GridColDef[] = [
    {
      field: "empId",
      headerName: "Emp ID",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#111827" }}>
          {params.row.empId}
        </Typography>
      ),
    },
    {
      field: "name",
      headerName: "Name",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={params.row.avatar}
            alt={params.row.name}
            sx={{ width: 32, height: 32 }}
          />
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            <Typography sx={{ fontSize: "14px", color: "#111827" }}>
              <Link href="#">{params.row.name}</Link>
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: "department",
      headerName: "Department",
      editable: false,
      flex: 1.5,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.department}
        </Typography>
      ),
    },
    {
      field: "email",
      headerName: "Email",
      editable: false,
      flex: 1.5,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.email}
        </Typography>
      ),
    },
    {
      field: "phone",
      headerName: "Phone",
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.phone}
        </Typography>
      ),
    },
    {
      field: "designationName",
      headerName: "Designation",
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.designationName}
        </Typography>
      ),
    },
    {
      field: "joiningDate",
      headerName: "Joining Date",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.joiningDate}
        </Typography>
      ),
    },
    {
      field: "netSalary",
      headerName: "Salary",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          ₹{params.row.netSalary.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: "payslip",
      headerName: "Payslip",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Button
          sx={{
            fontSize: "12px !important",
            padding: "5px 12px !important",
            lineHeight: "1.5 !important",
            background: "#212529 !important",
            color: "#FFF !important",
            fontWeight: "600 !important",
            letterSpacing: "0.5px !important",
            borderRadius: "4px !important",
            textTransform: "none",
          }}
          onClick={() => router.push(`/payroll/payslip?id=${params.row.id}`)}
        >
          Generate Slip
        </Button>
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row as EmployeesList)}
          >
            <EditNote sx={{ width: "16px", height: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row as EmployeesList)}
          >
            <Delete sx={{ width: "16px", height: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Box className="employee-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="employee-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Employee Salary</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <Button
              className="add-employee"
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => {
                setModalMode("add");
                setSelectedIndicatorId(null);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ width: "16px", height: "16px" }} />
              Add Salary
            </Button>
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 400,
              maxHeight: "calc(100vh - 200px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Employee Salary List</Typography>
              <PolicyFilters
                departments={[]}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                showDesignationFilter={false}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => {}}
                selectedLeaveType={""}
                setSelectedLeaveType={() => {}}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              apiRef={apiRef}
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={totalPayrollsCount}
              paginationMode="server"
              initialState={{
                pagination: { paginationModel: { pageSize: pageSize } },
              }}
              paginationModel={{ page: page - 1, pageSize: pageSize }}
              onPaginationModelChange={(model) => {
                if (model.pageSize !== pageSize) {
                  setPageSize(model.pageSize);
                  setLimit(model.pageSize);
                  setPage(1);
                } else {
                  setPage(model.page + 1);
                }
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      <AddEditEmployeeSalary
        open={openModal}
        onClose={() => {
          setOpenModal(false);
          setSelectedIndicatorId(null);
          setModalMode("add");
        }}
        onSubmit={(formData) => {
          console.log("Form submitted:", formData);
          setOpenModal(false);
          setSelectedIndicatorId(null);
          setModalMode("add");
          refreshList();
        }}
        mode={modalMode}
        selectedIndicatorId={selectedIndicatorId}
      />

      <DeleteConfirmationDialog
        open={deleteModal}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleDelete}
        title="Confirm Delete"
        message={`Are you sure you want to delete the payroll record for employee ${deletingEmployeesList?.empId}? This action cannot be undone.`}
      />
    </Box>
  );
}

function EmployeesSalaryPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <EmployeesListContent />
    </Suspense>
  );
}

export default EmployeesSalaryPage;

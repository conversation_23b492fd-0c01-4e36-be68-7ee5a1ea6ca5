import React, { useState, useEffect } from "react";
import "./AddEditOvertime.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Button,
  Typography,
  Box,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { Cancel } from "@mui/icons-material";
import * as Yup from "yup";
import { toast } from "react-toastify";
import {
  addPayrollItem,
  updatePayrollItem,
} from "@/app/services/payroll.service";
// import { Payload } from "recharts/types/component/DefaultLegendContent";

interface AddOvertimeDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: FormData) => void;
  editData?: any; // For edit mode
  refreshList?: () => void; // To refresh the list after submit
}

interface FormData {
  name: string;
  rateType: string;
  rate: string;
}

// Validation schema
const validationSchema = Yup.object({
  name: Yup.string().required("Name is required"),
  rateType: Yup.string().required("Rate type is required"),
  rate: Yup.string()
    .required("Rate is required")
    .test(
      "is-number",
      "Rate must be a valid number",
      (value) => !isNaN(Number(value))
    ),
});

const AddOvertimeDialog: React.FC<AddOvertimeDialogProps> = ({
  open,
  onClose,

  onSubmit,
  editData,
  refreshList,
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    rateType: "",
    rate: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form with edit data if available
  useEffect(() => {
    if (editData) {
      setFormData({
        name: editData.name || "",
        rateType: editData.rateType || "",
        rate: editData.rate?.toString() || "",
      });
    } else {
      // Reset form when opening in add mode
      setFormData({
        name: "",
        rateType: "",
        rate: "",
      });
    }
  }, [editData, open]);

  const handleChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error for this field when user changes it
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    try {
      validationSchema.validateSync(formData, { abortEarly: false });
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        const newErrors: Record<string, string> = {};
        error.inner.forEach((err) => {
          if (err.path) {
            newErrors[err.path] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        name: formData.name,
        rateType: formData.rateType,
        rate: formData.rate,
        type: "Overtime",
        isActive: true,
      };

      let response;
      if (editData?._id) {
        response = await updatePayrollItem(editData._id, payload as any);
        toast.success("Overtime updated successfully");
      } else {
        response = await addPayrollItem(payload as any);
        toast.success("Overtime added successfully");
      }

      onSubmit(formData); // Pass data to parent component

      if (refreshList) {
        refreshList(); // Refresh the list if function is provided
      }

      onClose(); // Close the dialog
    } catch (error) {
      console.error("Error submitting overtime:", error);
      toast.error(
        editData ? "Failed to update overtime" : "Failed to add overtime"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <Typography variant="h6" fontWeight="bold">
          {editData ? "Edit Overtime" : "Add Overtime"}
        </Typography>
        <IconButton aria-label="close" onClick={onClose}>
          <Cancel sx={{ fontSize: "20px", color: "#6B7280" }} />
        </IconButton>
      </DialogTitle>

      <DialogContent
        dividers
        sx={{ display: "flex", flexDirection: "column", gap: 2 }}
      >
        <Box className="dialog-text-field">
          <label>
            Name <span style={{ color: "red" }}>*</span>
          </label>
          <TextField
            fullWidth
            value={formData.name}
            onChange={(e) => handleChange("name", e.target.value)}
            error={!!errors.name}
            helperText={errors.name}
          />
        </Box>

        <Box className="dialog-text-field">
          <label>
            Rate Type <span style={{ color: "red" }}>*</span>
          </label>
          <TextField
            select
            fullWidth
            value={formData.rateType}
            onChange={(e) => handleChange("rateType", e.target.value)}
            error={!!errors.rateType}
            helperText={errors.rateType}
          >
            <MenuItem value="">Select</MenuItem>
            <MenuItem value="Hourly">Hourly</MenuItem>
            <MenuItem value="Daily">Daily</MenuItem>
            <MenuItem value="Fixed">Fixed</MenuItem>
          </TextField>
        </Box>

        <Box className="dialog-text-field">
          <label>
            Rate <span style={{ color: "red" }}>*</span>
          </label>
          <TextField
            fullWidth
            value={formData.rate}
            onChange={(e) => handleChange("rate", e.target.value)}
            error={!!errors.rate}
            helperText={errors.rate}
          />
        </Box>

        <DialogActions sx={{ padding: "1rem 0px 0px 0px !important" }}>
          <Button
            className="cancel-button"
            onClick={onClose}
            variant="outlined"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            className="add-button"
            onClick={handleSubmit}
            variant="contained"
            color="warning"
            disabled={isSubmitting}
          >
            {isSubmitting
              ? "Submitting..."
              : editData
                ? "Update"
                : "Add Overtime"}
          </Button>
        </DialogActions>
      </DialogContent>
    </Dialog>
  );
};

export default AddOvertimeDialog;

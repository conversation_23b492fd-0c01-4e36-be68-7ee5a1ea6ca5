"use client";
import {
  <PERSON>,
  Tabs,
  Tab,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Paper,
  IconButton,
} from "@mui/material";
import {
  HomeOutlined,
  EditNote,
  Delete,
  ControlPoint,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import Loader from "@/components/Loader/Loader";
import "./PayrollItem.scss";
import React, { useEffect, useState, useCallback } from "react";
import {
  GridColDef,
  GridRenderCellParams,
  GridRowsProp,
  GridToolbarExport,
  GridToolbarQuickFilter,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import AddAdditionDialog from "./AddEditAddition";
import AddOvertimeDialog from "./AddEditOvertime";
import {
  addPayrollItem,
  deletePayrollItem,
  getPayrollWithPagination,
  getPayrollItemById,
} from "@/app/services/payroll.service";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import { toast } from "react-toastify";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

export interface PayrollItem {
  _id: string;
  name: string;
  categoryName: string;
  amount: number;
  calculationUnit: string;
  rateType?: string;
  rate?: string;
  isActive: boolean;
  isAll: boolean;
  isEmpty: boolean;
  assignedEmployees: string[];
  createdAt: string;
  updatedAt: string;
}

export interface PayrollResponse {
  results: PayrollItem[];
  total: number;
  page: number;
  limit: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 0 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

function QuickSearchToolbar() {
  return (
    <Box
      sx={{
        p: 0.5,
        pb: 0,
        justifyContent: "space-between",
        padding: "10px 20px",
      }}
    >
      <GridToolbarQuickFilter
        placeholder="Search"
        className="grid-search"
        sx={{ textDecoration: "none" }}
      />
      <Box
        className="grid-export"
        sx={{ display: "flex", alignItems: "center" }}
      >
        <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
      </Box>
    </Box>
  );
}

export default function PayrollItems() {
  const [value, setValue] = React.useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [tabIndex, setTabIndex] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [rows, setRows] = useState<GridRowsProp>([]);
  const [total, setTotal] = useState<number>(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [payrollItemToDelete, setPayrollItemToDelete] = useState<string | null>(
    null
  );
  const [addDialogType, setAddDialogType] = useState<
    "Additions" | "Deductions" | "Overtime" | "Earnings"
  >("Additions");
  const [editData, setEditData] = useState<PayrollItem | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      const type =
        tabIndex === 0
          ? "Additions"
          : tabIndex === 1
            ? "Overtime"
            : tabIndex === 2
              ? "Deductions"
              : "Earnings";
      console.log(
        `Fetching data for type: ${type}, page: ${page}, pageSize: ${pageSize}`
      );
      const response = await getPayrollWithPagination(
        pageSize,
        page,
        "desc",
        undefined,
        undefined,
        "true",
        type
      );

      console.log("Payroll API response:", response);

      if (response && response.payrolls && response.payrolls.results) {
        const formattedRows = response.payrolls.results.map(
          (item: PayrollItem) => {
            let formattedAmount = "";
            if (item.calculationUnit?.toLowerCase() === "figure") {
              formattedAmount = `$${item.amount ?? 0}`;
            } else {
              formattedAmount = `${item.amount ?? 0}%`;
            }

            return {
              id: item._id,
              name: item.name,
              category: item.categoryName || "",
              amount: formattedAmount,
              rate: `${item.rateType || ""} ${item.rate || ""}`.trim(),
            };
          }
        );

        console.log("Formatted rows:", formattedRows);
        setRows(formattedRows);
        setTotal(response.payrolls.total || 0);
      } else {
        setRows([]);
        setTotal(0);
        console.log("No data returned from API or invalid structure");
      }
    } catch (error) {
      console.error("Failed to fetch payroll data:", error);
      setRows([]);
      setTotal(0);
    } finally {
      setIsLoading(false);
    }
  }, [page, pageSize, tabIndex]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleAddSubmit = useCallback(
    async (data: any) => {
      console.log("handleAddSubmit called with data:", data);
      try {
        const response = await addPayrollItem(data);
        console.log("addPayrollItem successful, response:", response);
        toast.success("Item added successfully");
        fetchData();
      } catch (err) {
        console.error("Add failed", err);
        toast.error("Failed to add item");
      }
    },
    [fetchData]
  );

  const handleEditClick = useCallback(
    async (payrollItemId: string) => {
      console.log("handleEditClick triggered for ID:", payrollItemId);
      try {
        setIsLoading(true);
        const type =
          tabIndex === 0
            ? "Additions"
            : tabIndex === 1
              ? "Overtime"
              : tabIndex === 2
                ? "Deductions"
                : "Earnings";
        const response = await getPayrollItemById(payrollItemId);
        console.log("Fetched payroll item response:", response);
        const item = response.PayrollItem; // Extract the PayrollItem object
        console.log("Extracted payroll item for edit:", item);
        setEditData(item);
        setAddDialogType(type);
        setOpenDialog(true);
      } catch (error) {
        console.error("Failed to fetch payroll item for edit:", error);
        toast.error("Failed to load payroll item for editing");
      } finally {
        setIsLoading(false);
      }
    },
    [tabIndex]
  );

  const handleDeleteClick = useCallback((payrollItemId: string) => {
    console.log("handleDeleteClick triggered for ID:", payrollItemId);
    setPayrollItemToDelete(payrollItemId);
    setDeleteDialogOpen(true);
  }, []);

  const handleCloseDeleteDialog = useCallback(() => {
    console.log("Closing delete dialog");
    setDeleteDialogOpen(false);
    setPayrollItemToDelete(null);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (payrollItemToDelete !== null) {
      console.log("Confirming delete for ID:", payrollItemToDelete);
      try {
        await deletePayrollItem(payrollItemToDelete);
        setDeleteDialogOpen(false);
        setPayrollItemToDelete(null);
        fetchData();
      } catch (error) {
        console.error("Failed to delete payroll item:", error);
        toast.error("Failed to delete item");
      }
    }
  }, [payrollItemToDelete, fetchData]);

  const handleChange = useCallback(
    (event: React.SyntheticEvent, newValue: number) => {
      console.log("Tab changed to:", newValue);
      setValue(newValue);
      setTabIndex(newValue);
      setPage(1);
    },
    []
  );

  const handleAddClick = useCallback(
    (type: "Additions" | "Deductions" | "Overtime" | "Earnings") => {
      console.log("handleAddClick triggered for type:", type);
      setAddDialogType(type);
      setEditData(null);
      setOpenDialog(true);
    },
    []
  );

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "HR", href: "" },
    { label: "Payroll Items" },
  ];

  const columns: GridColDef[] = [
    { field: "name", headerName: "Name", flex: 1 },
    { field: "category", headerName: "Category", flex: 1 },
    { field: "amount", headerName: "Default / Unit Amount", flex: 1 },
    {
      field: "actions",
      headerName: "",
      flex: 0.5,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const deductionsColumns: GridColDef[] = [
    { field: "name", headerName: "Name", flex: 1 },
    { field: "amount", headerName: "Default/Unit Amount", flex: 1 },
    {
      field: "actions",
      headerName: "",
      flex: 0.5,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const overtimeColumns: GridColDef[] = [
    { field: "name", headerName: "Overtime Title", flex: 1 },
    { field: "rate", headerName: "Rate", flex: 1 },
    {
      field: "actions",
      headerName: "",
      flex: 0.5,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Box className="payslip-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="payslip-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Payslip</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
        </Box>

        <Box sx={{ width: "100%" }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              "& .MuiTabs-indicator": {
                display: "none",
              },
              "& .Mui-selected": {
                backgroundColor: "#3B7080 !important",
                color: "#FFF !important",
                padding: "0.5rem 0.85rem !important",
              },
            }}
          >
            <Tabs
              value={value}
              onChange={handleChange}
              aria-label="payroll items tabs"
            >
              <Tab label="Additions" {...a11yProps(0)} />
              <Tab label="Overtime" {...a11yProps(1)} />
              <Tab label="Deductions" {...a11yProps(2)} />
              <Tab label="Earnings" {...a11yProps(3)} />
            </Tabs>

            <Box
              className="add-department"
              sx={{ display: "flex", gap: 1, alignItems: "center" }}
            >
              <Button
                variant="contained"
                onClick={() =>
                  handleAddClick(
                    value === 0
                      ? "Additions"
                      : value === 1
                        ? "Overtime"
                        : value === 2
                          ? "Deductions"
                          : "Earnings"
                  )
                }
                sx={{
                  display: "flex",
                  gap: "8px",
                  color: "#FFF",
                  fontWeight: 400,
                  fontSize: "14px",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
              >
                <ControlPoint sx={{ width: "16px", height: "16px" }} />
                {value === 0 && "Add Addition"}
                {value === 1 && "Add Overtime"}
                {value === 2 && "Add Deduction"}
                {value === 3 && "Add Earning"}
              </Button>
            </Box>
          </Box>

          <CustomTabPanel value={value} index={0}>
            <Paper>
              <Box
                className="DataGrid-container"
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  minHeight: "calc(90vh - 200px)",
                  maxHeight: "calc(100vh - 550px)",
                  p: 0,
                  "& .MuiPaper-root": {
                    p: 0,
                    borderRadius: 0,
                  },
                }}
              >
                <Box className="DataGrid-header">
                  <Typography variant="h5">Additions List</Typography>
                </Box>
                <Divider />
                <CustomDataGrid
                  disableRowSelectionOnClick
                  disableColumnFilter
                  disableColumnSelector
                  disableDensitySelector
                  rows={rows}
                  columns={columns}
                  rowCount={total}
                  paginationMode="server"
                  paginationModel={{ page: page - 1, pageSize }}
                  onPaginationModelChange={(model) => {
                    setPage(model.page + 1);
                    setPageSize(model.pageSize);
                  }}
                  pageSizeOptions={[10, 25, 50, 100]}
                  slots={{
                    pagination: CustomPagination,
                    toolbar: QuickSearchToolbar,
                  }}
                />
              </Box>
            </Paper>
          </CustomTabPanel>

          <CustomTabPanel value={value} index={1}>
            <Paper>
              <Box
                className="DataGrid-container"
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  minHeight: "calc(90vh - 200px)",
                  maxHeight: "calc(100vh - 550px)",
                  p: 0,
                  "& .MuiPaper-root": {
                    p: 0,
                    borderRadius: 0,
                  },
                }}
              >
                <Box className="DataGrid-header">
                  <Typography variant="h5">Overtime List</Typography>
                </Box>
                <Divider />
                <CustomDataGrid
                  disableRowSelectionOnClick
                  disableColumnFilter
                  disableColumnSelector
                  disableDensitySelector
                  rows={rows}
                  columns={overtimeColumns}
                  rowCount={total}
                  paginationMode="server"
                  paginationModel={{ page: page - 1, pageSize }}
                  onPaginationModelChange={(model) => {
                    setPage(model.page + 1);
                    setPageSize(model.pageSize);
                  }}
                  pageSizeOptions={[10, 25, 50, 100]}
                  slots={{
                    pagination: CustomPagination,
                    toolbar: QuickSearchToolbar,
                  }}
                />
              </Box>
            </Paper>
          </CustomTabPanel>

          <CustomTabPanel value={value} index={2}>
            <Paper>
              <Box
                className="DataGrid-container"
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  minHeight: "calc(90vh - 200px)",
                  maxHeight: "calc(100vh - 550px)",
                  p: 0,
                  "& .MuiPaper-root": {
                    p: 0,
                    borderRadius: 0,
                  },
                }}
              >
                <Box className="DataGrid-header">
                  <Typography variant="h5">Deductions List</Typography>
                </Box>
                <Divider />
                <CustomDataGrid
                  disableRowSelectionOnClick
                  disableColumnFilter
                  disableColumnSelector
                  disableDensitySelector
                  rows={rows}
                  columns={deductionsColumns}
                  rowCount={total}
                  paginationMode="server"
                  paginationModel={{ page: page - 1, pageSize }}
                  onPaginationModelChange={(model) => {
                    setPage(model.page + 1);
                    setPageSize(model.pageSize);
                  }}
                  pageSizeOptions={[10, 25, 50, 100]}
                  slots={{
                    pagination: CustomPagination,
                    toolbar: QuickSearchToolbar,
                  }}
                />
              </Box>
            </Paper>
          </CustomTabPanel>

          <CustomTabPanel value={value} index={3}>
            <Paper>
              <Box
                className="DataGrid-container"
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  minHeight: "calc(90vh - 200px)",
                  maxHeight: "calc(100vh - 550px)",
                  p: 0,
                  "& .MuiPaper-root": {
                    p: 0,
                    borderRadius: 0,
                  },
                }}
              >
                <Box className="DataGrid-header">
                  <Typography variant="h5">Earnings List</Typography>
                </Box>
                <Divider />
                <CustomDataGrid
                  disableRowSelectionOnClick
                  disableColumnFilter
                  disableColumnSelector
                  disableDensitySelector
                  rows={rows}
                  columns={columns}
                  rowCount={total}
                  paginationMode="server"
                  paginationModel={{ page: page - 1, pageSize }}
                  onPaginationModelChange={(model) => {
                    setPage(model.page + 1);
                    setPageSize(model.pageSize);
                  }}
                  pageSizeOptions={[10, 25, 50, 100]}
                  slots={{
                    pagination: CustomPagination,
                    toolbar: QuickSearchToolbar,
                  }}
                />
              </Box>
            </Paper>
          </CustomTabPanel>
        </Box>

        {value === 0 && (
          <AddAdditionDialog
            type="Additions"
            open={openDialog}
            onClose={() => {
              console.log("Closing AddAdditionDialog for Additions");
              setOpenDialog(false);
              setEditData(null);
            }}
            onSubmit={handleAddSubmit}
            editData={editData}
            refreshList={fetchData}
          />
        )}
        {value === 1 && (
          <AddOvertimeDialog
            open={openDialog}
            onClose={() => {
              console.log("Closing AddOvertimeDialog");
              setOpenDialog(false);
              setEditData(null);
            }}
            onSubmit={handleAddSubmit}
            editData={editData}
            refreshList={fetchData}
          />
        )}
        {value === 2 && (
          <AddAdditionDialog
            type="Deductions"
            open={openDialog}
            onClose={() => {
              console.log("Closing AddAdditionDialog for Deductions");
              setOpenDialog(false);
              setEditData(null);
            }}
            onSubmit={handleAddSubmit}
            editData={editData}
            refreshList={fetchData}
          />
        )}
        {value === 3 && (
          <AddAdditionDialog
            type="Earnings"
            open={openDialog}
            onClose={() => {
              console.log("Closing AddAdditionDialog for Earnings");
              setOpenDialog(false);
              setEditData(null);
            }}
            onSubmit={handleAddSubmit}
            editData={editData}
            refreshList={fetchData}
          />
        )}

        <DeleteConfirmationDialog
          open={deleteDialogOpen}
          onClose={handleCloseDeleteDialog}
          onConfirm={handleConfirmDelete}
          title="Delete Payroll Item"
          message="Are you sure you want to delete this payroll item? This action cannot be undone."
        />
      </Box>
    </Box>
  );
}

label {
    margin-top: 0px !important;
  }
  
  .dialog-title-text {
    font-size: 20px !important;
    font-weight: 600 !important;
    color: #111827;
  }
  
  .cancel-button {
    border-radius: 5px !important;
    padding: 0.5rem 0.85rem !important;
    font-size: 14px !important;
    transition: all 0.5s !important;
    font-weight: 500 !important;
    color: #111827 !important;
    text-transform: none !important;
    border-color: #ccc;
  
    &:hover {
      border-color: #999;
    }
  
    &:disabled {
      color: #ccc;
      border-color: #ccc;
    }
  }
  
  .add-button {
    border-radius: 5px !important;
    padding: 0.5rem 0.85rem !important;
    font-size: 14px !important;
    transition: all 0.5s !important;
    font-weight: 500 !important;
    color: #FFF !important;
    text-transform: none !important;
    background-color: #f26522;
  
    &:hover {
      background-color: #e55a1b;
    }
  
    &:disabled {
      background-color: #ccc;
    }
  }
  
  .form-field {
    margin-bottom: 16px;
    width: 100%;
  
    label {
      font-size: 14px;
      color: #111827;
      font-weight: 500;
      margin-bottom: 8px;
      display: block;
    }
  
    .custom-autocomplete {
      .MuiInputBase-root {
        padding: 8px !important;
        display: flex;
        flex-wrap: wrap;
        height: auto !important;
        min-height: 38px !important;
        max-height: none !important;
      }
  
      .MuiInputBase-input {
        padding: 0 !important;
        height: auto !important;
      }
  
      .MuiAutocomplete-endAdornment {
        position: absolute;
        right: 9px;
        top: 50%;
        transform: translateY(-50%);
      }
  
      .MuiAutocomplete-tag {
        margin: 3px;
      }
    }
  
    .autocomplete-input {
      width: 100%;
  
      .MuiOutlinedInput-root {
        height: auto;
        min-height: 38px;
      }
    }
  }
  
  .MuiFormControl-root {
    .MuiFormControlLabel-root.radio-label {
      .MuiTypography-root {
        font-size: 0.875rem !important;
        font-weight: 500 !important;
        color: #212529 !important;
      }
  
      .MuiRadio-root {
        color: #9CA3AF;
  
        &.Mui-checked {
          color: #F26522;
        }
      }
    }
  }
  
  .MuiSwitch-root {
    .MuiSwitch-thumb {
      background-color: white;
      width: 11px;
      height: 11px;
    }
  
    .Mui-checked {
      color: #F26522; // Replaced var(--primary-color) with #F26522
  
      .MuiSwitch-track {
        background-color: #F26522; // Replaced var(--primary-color) with #F26522
        opacity: 1;
      }
    }
  
    .MuiButtonBase-root {
      padding: 13px;
    }
  
    .MuiSwitch-track {
      background-color: #9CA3AF;
    }
  }
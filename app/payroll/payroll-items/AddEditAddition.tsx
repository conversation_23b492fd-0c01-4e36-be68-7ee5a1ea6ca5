"use client";
import React, { useState, useEffect, useCallback } from "react";
import "./AddEditAddition.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Switch,
  Button,
  Typography,
  Box,
  IconButton,
  Checkbox,
  Autocomplete,
  Chip,
} from "@mui/material";
import { Cancel, Close } from "@mui/icons-material";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { updatePayrollItem } from "@/app/services/payroll.service";
import { getUsers } from "@/app/services/users.service";

interface Employee {
  _id: string;
  firstName: string;
  lastName: string;
  employeeId: string;
  avatar?: string;
  email?: string;
}

interface AddAdditionDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  type: "Additions" | "Deductions" | "Overtime" | "Earnings";
  editData?: any;
  refreshList?: () => void;
}

interface FormData {
  name: string;
  category: string;
  amount: string;
  unitCalculation: boolean;
  assignee: "none" | "all" | "select";
  type: "Additions" | "Deductions" | "Overtime" | "Earnings";
  isDefault: boolean;
  selectedEmployees: Employee[];
}

const validationSchema = Yup.object({
  name: Yup.string().required("Name is required"),
  category: Yup.string().required("Category is required"),
  amount: Yup.string()
    .required("Amount is required")
    .test(
      "is-number",
      "Amount must be a valid number",
      (value) => !isNaN(Number(value))
    ),
});

const AddAdditionDialog: React.FC<AddAdditionDialogProps> = ({
  open,
  onClose,
  onSubmit,
  type,
  editData,
  refreshList,
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    category: "Monthly Remuneration",
    amount: "",
    unitCalculation: true,
    assignee: "none",
    type: type || "Additions",
    isDefault: false,
    selectedEmployees: [],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loadingEmployees, setLoadingEmployees] = useState(false);

  useEffect(() => {
    const fetchEmployees = async () => {
      if (open) {
        try {
          setLoadingEmployees(true);
          const response = await getUsers();
          const fetchedEmployees = response.users.results || [];
          console.log("Fetched employees:", fetchedEmployees);
          setEmployees(fetchedEmployees);
        } catch (error) {
          console.error("Failed to fetch employees:", error);
          toast.error("Failed to load employees");
        } finally {
          setLoadingEmployees(false);
        }
      }
    };
    fetchEmployees();
  }, [open]);

  useEffect(() => {
    console.log(
      `AddAdditionDialog opened for type: ${type}, editData:`,
      editData
    );
    if (editData && open) {
      console.log("Raw editData received:", JSON.stringify(editData, null, 2));
      const newFormData: FormData = {
        name: editData.name || "",
        category: editData.categoryName || "Monthly Remuneration",
        amount: editData.amount != null ? editData.amount.toString() : "",
        unitCalculation: editData.calculationUnit === "Figure",
        assignee: editData.isAll
          ? "all"
          : editData.assignedEmployees && editData.assignedEmployees.length > 0
            ? "select"
            : "none",
        type: editData.type || type || "Additions",
        isDefault: editData.isDefault || false,
        selectedEmployees: editData.assignedEmployees || [],
      };
      console.log("Setting formData to:", newFormData);
      setFormData(newFormData);
    } else {
      const defaultFormData: FormData = {
        name: "",
        category: "Monthly Remuneration",
        amount: "",
        unitCalculation: true,
        assignee: "none",
        type: type || "Additions",
        isDefault: false,
        selectedEmployees: [],
      };
      console.log("Resetting formData to default:", defaultFormData);
      setFormData(defaultFormData);
    }
  }, [editData, type, open]);

  const handleChange = useCallback(
    (field: keyof FormData, value: any) => {
      console.log(`handleChange - Field: ${field}, Value:`, value);
      setFormData((prev) => ({ ...prev, [field]: value }));
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: "" }));
      }
    },
    [errors]
  );

  const validateForm = useCallback(() => {
    try {
      validationSchema.validateSync(formData, { abortEarly: false });
      setErrors({});
      console.log("Form validation passed");
      return true;
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        const newErrors: Record<string, string> = {};
        error.inner.forEach((err) => {
          if (err.path) {
            newErrors[err.path] = err.message;
          }
        });
        console.log("Validation errors:", newErrors);
        setErrors(newErrors);
      }
      return false;
    }
  }, [formData]);

  const handleSubmit = useCallback(async () => {
    console.log("handleSubmit triggered with formData:", formData);
    if (!validateForm()) {
      console.log("Validation failed, errors:", errors);
      return;
    }

    if (isSubmitting) {
      console.log("Submission already in progress, ignoring");
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        name: formData.name,
        categoryName: formData.category,
        amount: Number(formData.amount),
        calculationUnit: formData.unitCalculation ? "Figure" : "Percent",
        type: formData.type,
        isActive: true,
        isAll: formData.assignee === "all",
        isEmpty: formData.assignee === "none",
        assignedEmployees:
          formData.assignee === "select"
            ? formData.selectedEmployees.map((emp) => emp._id)
            : [],
        isDefault: formData.isDefault,
      };

      console.log("Submitting payload:", payload);

      if (editData?._id) {
        console.log("Updating existing item with ID:", editData._id);
        const response = await updatePayrollItem(editData._id, payload);
        console.log("updatePayrollItem response:", response);
        toast.success("Item updated successfully");
        if (refreshList) {
          console.log("Calling refreshList");
          refreshList();
        }
      } else {
        console.log("Passing payload to onSubmit for adding new item");
        onSubmit(payload);
        // toast.success("Item added successfully");
      }

      onClose();
    } catch (error) {
      console.error("Error submitting payroll item:", error);
      toast.error(editData ? "Failed to update item" : "Failed to add item");
    } finally {
      setIsSubmitting(false);
    }
  }, [
    formData,
    isSubmitting,
    validateForm,
    type,
    editData,
    onSubmit,
    refreshList,
    errors,
  ]);

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
      PaperProps={{ sx: { maxWidth: "500px" } }}
    >
      <DialogTitle>
        <Typography
          variant="h6"
          fontWeight="bold"
          className="dialog-title-text"
        >
          {editData ? `Edit ${type}` : `Add ${type}`}
        </Typography>
        <IconButton aria-label="close" onClick={onClose}>
          <Cancel sx={{ fontSize: "20px", color: "#6B7280" }} />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
        <Box>
          <label>
            Name <span className="required">*</span>
          </label>
          <TextField
            fullWidth
            value={formData.name}
            onChange={(e) => handleChange("name", e.target.value)}
            error={!!errors.name}
            helperText={errors.name}
            placeholder="Enter name"
          />
        </Box>
        <label>
          Category Name <span className="required">*</span>
        </label>
        <TextField
          select
          fullWidth
          value={formData.category}
          onChange={(e) => handleChange("category", e.target.value)}
          error={!!errors.category}
          helperText={errors.category}
          placeholder="Select category"
        >
          <MenuItem value="Monthly Remuneration">Monthly Remuneration</MenuItem>
          <MenuItem value="Additional Remuneration">
            Additional Remuneration
          </MenuItem>
        </TextField>
        <Box display="flex" alignItems="center" gap={2}>
          <Box sx={{ flex: 1 }}>
            <label>
              Amount <span className="required">*</span>
            </label>
            <TextField
              value={formData.amount}
              onChange={(e) => handleChange("amount", e.target.value)}
              fullWidth
              InputProps={{
                endAdornment: formData.unitCalculation ? "$" : "%",
              }}
              error={!!errors.amount}
              helperText={errors.amount}
              placeholder="Enter amount"
            />
          </Box>
          <Box display="flex" flexDirection="column" alignItems="center">
            <label>Unit Calculation</label>
            <Switch
              checked={formData.unitCalculation}
              sx={{
                "& .MuiSwitch-switchBase": {
                  color: "#9CA3AF",
                  "&.Mui-checked": {
                    color: "#F97316",
                  },
                  "&.Mui-checked + .MuiSwitch-track": {
                    backgroundColor: "#F26522",
                    opacity: 1,
                  },
                },
                "& .MuiSwitch-track": {
                  backgroundColor: "#9CA3AF",
                },
              }}
              onChange={(e) =>
                handleChange("unitCalculation", e.target.checked)
              }
              color="warning"
            />
          </Box>
          <Box display="flex" flexDirection="column" alignItems="center">
            <label>Is Default</label>
            <Checkbox
              checked={formData.isDefault}
              onChange={(e) => handleChange("isDefault", e.target.checked)}
              sx={{
                color: "#9CA3AF",
                "&.Mui-checked": {
                  color: "#F97316",
                },
              }}
            />
          </Box>
        </Box>
        <FormControl>
          <FormLabel>Assignee</FormLabel>
          <RadioGroup
            row
            value={formData.assignee}
            onChange={(e) =>
              handleChange("assignee", e.target.value as FormData["assignee"])
            }
          >
            <FormControlLabel
              className="radio-label"
              value="none"
              control={<Radio />}
              label="No Assignee"
            />
            <FormControlLabel
              className="radio-label"
              value="all"
              control={<Radio />}
              label="All Employees"
            />
            <FormControlLabel
              className="radio-label"
              value="select"
              control={<Radio />}
              label="Select Employee"
            />
          </RadioGroup>
        </FormControl>
        {formData.assignee === "select" && (
          <Box className="form-field" sx={{ mt: 1 }}>
            <label>Select Employees</label>
            <Autocomplete
              multiple
              id="employee-autocomplete"
              options={employees}
              getOptionLabel={(option: Employee) =>
                `${option.firstName} ${option.lastName} (${option.employeeId})`
              }
              value={formData.selectedEmployees}
              onChange={(event, newValue) => {
                handleChange("selectedEmployees", newValue);
              }}
              disabled={loadingEmployees}
              renderTags={(value: Employee[], getTagProps) =>
                value.map((option, index) => {
                  const { key, ...otherProps } = getTagProps({ index });
                  return (
                    <Chip
                      key={option._id}
                      label={`${option.firstName} ${option.lastName} (${option.employeeId})`}
                      deleteIcon={<Close />}
                      sx={{
                        margin: "2px",
                        borderRadius: "4px",
                        "& .MuiChip-label": {
                          fontSize: "12px",
                        },
                      }}
                      {...otherProps}
                    />
                  );
                })
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder={
                    formData.selectedEmployees.length === 0
                      ? "Select employees"
                      : ""
                  }
                  variant="outlined"
                  className="custom-autocomplete"
                />
              )}
              className="autocomplete-input"
              noOptionsText={
                loadingEmployees ? "Loading..." : "No employees available"
              }
            />
          </Box>
        )}
      </DialogContent>
      <DialogActions
        sx={{ padding: "16px !important", paddingTop: "1rem !important" }}
      >
        <Button
          className="cancel-button"
          onClick={onClose}
          variant="outlined"
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          className="add-button"
          onClick={handleSubmit}
          variant="contained"
          color="warning"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Submitting..." : editData ? "Update" : `Add ${type}`}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddAdditionDialog;

"use client";
import { Box, Button, Typography } from "@mui/material";
import "./payslip.scss";
import React, { useState } from "react";
import {
  HomeOutlined,
  Download,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import Loader from "@/components/Loader/Loader";
import Payslip from "@/components/payslip/Payslip";
import { useSearchParams } from "next/navigation";
import useAuthStore from "@/store/authStore";

export default function PayslipPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [downloadPayslip, setDownloadPayslip] = useState<(() => void) | null>(null);
  const searchParams = useSearchParams();
  const { employeeId: currentEmployeeId } = useAuthStore();
  
  // Get userId from URL parameter, fall back to current user's employeeId
  const userId = searchParams.get('id') || currentEmployeeId;

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Payroll", href: "/payroll" },
    { label: "Payslip" },
  ];

  const handleDownloadRef = (downloadFn: () => void) => {
    setDownloadPayslip(() => downloadFn);
  };

  return (
    <Box className="payslip-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="payslip-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Payslip</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="add-department"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              onClick={() => downloadPayslip && downloadPayslip()}
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#111827 !important",
                borderColor: "#111827 !important",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
            >
              <Download sx={{ width: "16px", height: "16px" }} />
              Download
            </Button>
          </Box>
        </Box>

        <Box className="payslip-content">
          {userId ? (
            <Payslip onDownloadRef={handleDownloadRef} userId={userId} />
          ) : (
            <Typography variant="h6" color="textSecondary" align="center" sx={{ mt: 4 }}>
              No employee ID found. Please ensure you are logged in or provide an employee ID in the URL.
            </Typography>
          )}
        </Box>
      </Box>
    </Box>
  );
}

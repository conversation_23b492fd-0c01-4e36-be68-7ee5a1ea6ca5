.payslip-container {
    .content {
        padding: 24px;

        .payslip-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;

            .breadcrumbs-box {
                h2 {
                    font-size: 24px;
                    font-weight: 700;
                    color: #202C4B;
                }
            }

            .add-department {
                button {
                    display: flex;
                    gap: 8px;
                    background-color: #F26522;
                    border-color: #F26522;
                    color: #FFF;
                    font-weight: 400;
                    font-size: 14px;
                    border-radius: 5px;
                    text-transform: none;
                    padding: 8px 13.6px;
                }
            }
        }

        .payslip-content {
            width: 100% !important;
            margin-bottom: 1.5rem !important;
            background-color: #FFF !important;
            transition: all 0.5s ease-in-out !important;
            position: relative !important;
            border-radius: 5px !important;
            border: 1px solid #E5E7EB !important;
            box-shadow: 0px 1px 1px 1px rgba(198, 198, 198, 0.2) !important;
        }

        .pageSelect-dropdown {
            display: flex;
            justify-self: start;
            align-items: center;
            gap: 5px;
            margin: 30px 0px;

            p {
                font-size: 14px;
                color: #374151;
            }

            .MuiInputBase-root {
                .MuiSelect-select {
                    padding: 2px 20px 2px 10px;
                }

                .MuiSvgIcon-root {
                    right: 0px;
                }
            }
        }



    }
}
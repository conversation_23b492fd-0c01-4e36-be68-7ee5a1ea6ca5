"use client";
import {
  Box,
  Divider,
  MenuItem,
  Select,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Paper,
  useMediaQuery,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import "./Holiday.scss";
import {
  HomeOutlined,
  ControlPoint,
  Cancel,
  Circle,
} from "@mui/icons-material";
import { IconButton, Button } from "@mui/material";
import {
  getHolidays,
  editHoliday,
  postHoliday,
  deleteHoliday,
} from "@/app/services/holiday";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  useGridApiRef,
  GridToolbarQuickFilter,
  GridToolbarExport,
  GridAlignment,
  GridRenderCellParams,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { EditNote, Delete } from "@mui/icons-material";
import { Formik, Form, Field } from "formik";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import useRoleAuth from "../hooks/useRoleAuth";
import { ROLE_GROUPS } from "../constants/roles";
import ViewToggle from "@/components/ViewToggleButton/ViewToggle";
import ViewToggleHolidays from "@/components/ViewToggleButton/ViewToggleHoliday";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import ReadMore from "@/components/ReadMore/ReadMore";

// Update the Holiday interface
interface Holiday {
  id: number;
  title: string;
  date: string;
  description: string;
  status: "Active" | "Inactive";
}

function QuickSearchToolbar() {
  return (
    <Box
      sx={{
        p: 0.5,
        pb: 0,
        justifyContent: "space-between",
        padding: "10px 20px",
      }}
    >
      <GridToolbarQuickFilter
        className="grid-search"
        sx={{ textDecoration: "none" }}
        placeholder="Search"
      />
      <Box
        className="grid-export"
        sx={{ display: "flex", alignItems: "center" }}
      >
        <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
      </Box>
    </Box>
  );
}

// First, modify how you use useRoleAuth
function HolidayPage() {
  // Remove this line
  // const { user } = useRoleAuth();

  // Add this line instead
  const { hasAccess, roles } = useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const [loading, setLoading] = useState(false);

  // Remove this duplicate call
  // useRoleAuth({
  //   allowedRoles: ROLE_GROUPS.USER_ROLES,
  //   redirectTo: "/unauthorized",
  // });

  // Update all user?.role checks to use roles array instead
  const isEmployee = roles.includes("Employee");

  useEffect(() => {
    let isMounted = true;

    const fetchHoliday = async () => {
      setLoading(true);
      try {
        const response = await getHolidays();
        if (response?.holidays?.results) {
          interface ApiHoliday {
            _id: string;
            title: string;
            date: string;
            description: string;
            isActive: boolean;
          }

          interface FormattedHoliday {
            id: string;
            title: string;
            date: string;
            description: string;
            status: "Active" | "Inactive";
          }

          const formattedHolidays: FormattedHoliday[] =
            response.holidays.results.map((holi: ApiHoliday) => ({
              id: holi._id,
              title: holi.title,
              date: new Date(holi.date).toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "short",
                year: "numeric",
              }),
              description: holi.description,
              status: holi.isActive ? "Active" : "Inactive",
            }));

          if (isMounted) setRows(formattedHolidays); // ✅ Backend data replaces hardcoded rows
        } else {
          if (isMounted) setRows([]);
          // toast.error("No holidays found.");
        }
      } catch (error) {
        console.error("API request failed:", error);
        if (isMounted) setRows([]);
        // toast.error("Failed to fetch holidays.");
      } finally {
        if (isMounted) setLoading(false);
      }
    };

    fetchHoliday();
    return () => {
      isMounted = false;
    };
  }, []);

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Holiday Calendar" },
  ];

  const [openModal, setOpenModal] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const [deletingHoliday, setDeletingHoliday] = useState<Holiday | null>(null);
  const [editingHoliday, setEditingHoliday] = useState<Holiday | null>(null);
  const [rows, setRows] = useState<GridRowsProp>([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const apiRef = useGridApiRef();

  // Helper functions for date formatting
  const parseDateToInputFormat = (dateStr: string): string => {
    if (!dateStr) return "";

    try {
      // Parse date from "dd MMM yyyy" format (e.g., "15 Jan 2023")
      const dateParts = dateStr.split(" ");
      const day = dateParts[0];
      const month = dateParts[1];
      const year = dateParts[2];

      const monthMap: { [key: string]: string } = {
        Jan: "01",
        Feb: "02",
        Mar: "03",
        Apr: "04",
        May: "05",
        Jun: "06",
        Jul: "07",
        Aug: "08",
        Sep: "09",
        Oct: "10",
        Nov: "11",
        Dec: "12",
      };

      // Format as YYYY-MM-DD
      return `${year}-${monthMap[month]}-${day.padStart(2, "0")}`;
    } catch (error) {
      console.error("Error parsing date:", error);
      return "";
    }
  };

  const formatDateToDisplay = (dateStr: string): string => {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
  };

  // Handle edit icon click
  const handleEditClick = (row: Holiday) => {
    if (isEmployee) return;
    setEditingHoliday(row);
    setEditModal(true);
  };

  // Handle save changes for editing
  const handleSaveChanges = async (values: {
    title: string;
    date: string;
    description: string;
    status: "Active" | "Inactive";
    dateInput: string;
  }) => {
    if (isEmployee) return;
    if (!editingHoliday) return;

    setLoading(true);
    try {
      const holidayId = editingHoliday.id.toString();
      const statusBoolean = values.status === "Active";

      // Use dateInput directly as it's already in YYYY-MM-DD format
      const apiDate = values.dateInput;

      const updatedHoliday = await editHoliday(
        holidayId,
        values.title,
        apiDate,
        values.description,
        statusBoolean
      );

      if (!updatedHoliday) {
        // toast.error("Failed to update holiday");
        return;
      }

      // Format the date for display
      const formattedDate = new Date(apiDate).toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      });

      const updatedHolidayObj = {
        ...editingHoliday,
        title: values.title,
        date: formattedDate,
        description: values.description,
        status: values.status,
      };

      setRows((prevRows) =>
        prevRows.map((row) =>
          row.id === editingHoliday.id ? updatedHolidayObj : row
        )
      );

      // toast.success("Holiday updated successfully!");
      setEditModal(false);
    } catch (error) {
      console.error("Failed to update holiday:", error);
      // toast.error("Failed to update holiday");
    } finally {
      setLoading(false);
    }
  };

  // Handle delete icon click
  const handleDeleteClick = (row: Holiday) => {
    if (isEmployee) return;
    setDeletingHoliday(row);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDelete = async () => {
    if (isEmployee) return;
    if (deletingHoliday) {
      setLoading(true);
      try {
        const response = await deleteHoliday(deletingHoliday.id.toString());
        console.log("Holiday deleted:", response);

        if (response.success) {
          // toast.success("Holiday deleted successfully!");
        } else {
          // toast.error("Failed to delete holiday.");
        }

        const updatedRows = rows.filter((row) => row.id !== deletingHoliday.id);
        setRows(updatedRows);
        setDeleteDialogOpen(false);
      } catch (error) {
        console.error("Failed to delete holiday:", error);
        // toast.error("Failed to delete holiday. Please try again.");
      } finally {
        setLoading(false); // Stop loader
      }
    }
  };

  const isMobile = useMediaQuery("(max-width:768px)");

  // Update the columns definition to conditionally include both status and actions columns
  interface StatusCellProps {
    value: "Active" | "Inactive";
  }

  interface ActionsCellProps {
    row: Holiday;
  }

  const columns: GridColDef[] = [
    {
      field: "title",
      headerName: "Title",
      editable: false,
      minWidth: 349.917,
      renderCell: (params: GridRenderCellParams) => (
        <Typography
          variant="body2"
          sx={{
            fontSize: "14px",
            fontWeight: 500,
            color: "#111827",
          }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: "date",
      headerName: "Date",
      editable: false,
      minWidth: 188.933,
      renderCell: (params: GridRenderCellParams) => (
        <Typography
          variant="body2"
          sx={{
            fontSize: "14px",
            color: "#6B7280",
          }}
        >
          {formatDateToDisplay(params.value)}
        </Typography>
      ),
    },
    {
      field: "description",
      headerName: "Description",
      editable: false,
      minWidth: 428.033,
      flex: 1,
      renderCell: (params: GridRenderCellParams) => (
        <Typography
          variant="body2"
          sx={{
            fontSize: "14px",
            color: "#6B7280",
          }}
        >
          <ReadMore
            text={params.value}
            maxChars={isMobile ? 50 : 100}
            sx={{
              fontSize: "14px",
              color: "#6B7280",
              backgroundColor: "transparent !important",
              "&:hover": {
                backgroundColor: "transparent !important",
              },
            }}
          />
        </Typography>
      ),
    },
    // Only include the status column if the user is not an employee
    ...(!isEmployee
      ? [
          {
            field: "status",
            headerName: "Status",
            editable: false,
            minWidth: 180.817,
            renderCell: (params: GridRenderCellParams) => (
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {isMobile ? (
                  <Circle
                    sx={{
                      fontSize: 12,
                      color: params.value === "Active" ? "#03C95A" : "#E70D0D",
                    }}
                  />
                ) : (
                  <Box
                    sx={{
                      backgroundColor:
                        params.value === "Active" ? "#03C95A" : "#E70D0D",
                      color: "#fff",
                      borderRadius: "5px",
                      display: "flex",
                      textAlign: "center",
                      // minWidth: "80px",
                      justifyContent: "center !important",
                      fontSize: "11px",
                      fontWeight: 600,
                      padding: "0px 5px",
                      lineHeight: "18px",
                      alignItems: "center",
                    }}
                  >
                    <Circle sx={{ fontSize: "6px", marginRight: "4px" }} />
                    {params.value}
                  </Box>
                )}
              </Box>
            ),
          },
        ]
      : []),
    // Only include the actions column if the user is not an employee
    ...(!isEmployee
      ? [
          {
            field: "actions",
            headerName: "",
            editable: false,
            disableExport: true,
            flex: 0.5,
            headerAlign: "center" as GridAlignment,
            align: "center" as GridAlignment,
            renderCell: (params: { row: Holiday }) => (
              <Box sx={{ display: "flex", gap: 1, justifyContent: "center" }}>
                <IconButton
                  sx={{ color: "#6B7280" }}
                  onClick={() => handleEditClick(params.row)}
                >
                  <EditNote sx={{ fontSize: "16px" }} />
                </IconButton>
                <IconButton
                  sx={{ color: "#6B7280" }}
                  onClick={() => handleDeleteClick(params.row)}
                >
                  <Delete sx={{ fontSize: "16px" }} />
                </IconButton>
              </Box>
            ),
          },
        ]
      : []),
  ];

  return (
    <Box className="holiday-container">
      {loading && <Loader loading={loading} />}

      <Box className="content">
        <Box className="holiday-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Holiday Calendar</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
            <ViewToggleHolidays />
            {!isEmployee && (
              <Button
                className="add-holiday"
                variant="contained"
                sx={{ textTransform: "none" }}
                onClick={() => setOpenModal(true)}
              >
                <ControlPoint sx={{ fontSize: "14px" }} />
                Add Holiday
              </Button>
            )}
          </Box>
        </Box>

        {/* DataGrid */}
        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: rows.length < 5 ? "60vh" : "80vh",
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Holiday Calendar List</Typography>
            </Box>
            <Divider />
            <CustomDataGrid
              apiRef={apiRef}
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              // checkboxSelection
              rows={rows}
              columns={columns}
              pageSizeOptions={[10, 25, 50, 100]}
              initialState={{
                pagination: {
                  paginationModel: {
                    pageSize: 10,
                  },
                },
              }}
              slots={{
                pagination: CustomPagination,
                toolbar: QuickSearchToolbar,
              }}
            />
          </Box>
        </Paper>
      </Box>

      {/* Add Holiday Dialog with Formik */}
      <Dialog
        open={openModal}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            setOpenModal(false);
          }
        }}
        disableEscapeKeyDown
        PaperProps={{
          sx: {
            width: "500px",
            maxWidth: "none",
            borderRadius: "5px",
            "& .MuiFormControl-root": {
              marginTop: "0 !important",
            },
            "& .MuiFormLabel-root": {
              marginTop: "0 !important",
            },
            "& .MuiBox-root": {
              marginTop: "16px !important",
              "&:first-of-type": {
                marginTop: "0 !important",
              },
              "& label": {
                marginTop: "0 !important",
                marginBottom: "8px",
                display: "block",
              },
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "14px",
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 600, color: "#202C4B" }}>
            Add Holiday
          </Typography>
          <Cancel
            onClick={() => setOpenModal(false)}
            sx={{
              width: "20px",
              height: "20px",
              borderRadius: "50%",
              backgroundColor: "#fff",
              color: "#6B7280",
              ":hover": { color: "#F26522" },
            }}
          />
        </DialogTitle>
        <DialogContent sx={{ padding: "1rem !important" }}>
          <Formik
            initialValues={{
              title: "",
              date: "",
              description: "",
              status: "Active",
            }}
            validate={(values) => {
              const errors: Partial<typeof values> = {};
              const today = new Date();
              today.setHours(0, 0, 0, 0); // Reset time part for accurate date comparison
              const selectedDate = new Date(values.date);
              const currentYear = today.getFullYear();

              if (!values.title.trim()) {
                errors.title = "Title is required";
              }

              if (!values.date) {
                errors.date = "Date is required";
              } else if (selectedDate.getFullYear() < currentYear) {
                errors.date = "Holiday date must be in current year or future";
              }

              if (!values.description.trim()) {
                errors.description = "Description is required";
              }

              return errors;
            }}
            onSubmit={async (values, { setSubmitting }) => {
              setLoading(true); // Start loader
              try {
                const formattedDate = values.date; // Already in YYYY-MM-DD format
                const payload = {
                  title: values.title,
                  date: formattedDate,
                  description: values.description,
                  isActive: values.status === "Active",
                };

                const response = await postHoliday(payload);
                console.log("Holiday Added:", response);
                if (response?.holiday) {
                  const formattedDate = new Date(
                    response.holiday.date
                  ).toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "short",
                    year: "numeric",
                  });

                  // Use the actual ID from the API response instead of array length
                  setRows([
                    ...rows,
                    {
                      id: response.holiday._id, // Use the actual ID from API
                      title: response.holiday.title,
                      date: formattedDate,
                      description: response.holiday.description,
                      status: response.holiday.isActive ? "Active" : "Inactive",
                    },
                  ]);
                  // toast.success("Holiday added successfully!");
                  setOpenModal(false);
                } else {
                  // toast.error("Failed to add holiday.");
                }
              } catch (error) {
                console.error("API request failed:", error);
                // toast.error("Failed to add holiday. Please try again.");
              } finally {
                setSubmitting(false);
                setLoading(false);
              }
            }}
          >
            {({ errors, touched, isSubmitting }) => (
              <Form>
                <Box sx={{ mb: 2 }}>
                  <label>
                    Title <span className="required">*</span>
                  </label>
                  <Field
                    as={TextField}
                    fullWidth
                    name="title"
                    variant="outlined"
                    size="small"
                    error={touched.title && !!errors.title}
                    helperText={touched.title && errors.title}
                  />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <label>
                    Date <span className="required">*</span>
                  </label>
                  <Field
                    as={TextField}
                    fullWidth
                    type="date"
                    name="date"
                    variant="outlined"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    error={touched.date && !!errors.date}
                    helperText={touched.date && errors.date}
                  />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <label>
                    Description <span className="required">*</span>
                  </label>
                  <Field
                    as={TextField}
                    fullWidth
                    name="description"
                    variant="outlined"
                    size="small"
                    multiline
                    rows={3}
                    error={touched.description && !!errors.description}
                    helperText={touched.description && errors.description}
                  />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <label>Status</label>
                  <Field
                    as={Select}
                    fullWidth
                    name="status"
                    variant="outlined"
                    size="small"
                  >
                    <MenuItem value="Active">Active</MenuItem>
                    <MenuItem value="Inactive">Inactive</MenuItem>
                  </Field>
                </Box>
                <DialogActions sx={{ padding: "0px !important" }}>
                  <Button
                    sx={{
                      fontWeight: "400",
                      fontSize: "14px",
                      color: "#111827",
                      border: "1px solid #E5E7EB",
                      borderRadius: "5px",
                      textTransform: "none",
                      padding: "8px 13.6px",
                    }}
                    onClick={() => setOpenModal(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    sx={{
                      display: "flex",
                      gap: "8px",
                      backgroundColor: "#F26522",
                      borderColor: "#F26522",
                      color: "#FFF",
                      fontWeight: 400,
                      fontSize: "14px",
                      borderRadius: "5px",
                      textTransform: "none",
                      padding: "8px 13.6px",
                    }}
                    variant="contained"
                  >
                    Add Holiday
                  </Button>
                </DialogActions>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>

      {/* Edit Holiday Dialog with Formik */}
      <Dialog
        open={editModal}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            setEditModal(false);
          }
        }}
        disableEscapeKeyDown
        PaperProps={{
          sx: {
            width: "498px",
            // height: "521px",
            maxWidth: "none",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Edit Holiday
          </Typography>
          <Cancel
            onClick={() => setEditModal(false)}
            sx={{ ":hover": { color: "#F26522" } }}
          />
        </DialogTitle>
        <DialogContent sx={{ padding: "1rem" }}>
          {editingHoliday && (
            <Formik
              initialValues={{
                title: editingHoliday.title,
                date: editingHoliday.date,
                description: editingHoliday.description,
                status: editingHoliday.status,
                dateInput: parseDateToInputFormat(editingHoliday.date),
              }}
              validate={(values) => {
                const errors: Partial<typeof values> = {};
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                if (!values.title.trim()) {
                  errors.title = "Title is required";
                }

                if (!values.dateInput) {
                  errors.dateInput = "Date is required";
                } else {
                  const selectedDate = new Date(values.dateInput);
                  selectedDate.setHours(0, 0, 0, 0);

                  // Only validate future date for new entries, not existing ones
                  /* if (selectedDate < today) {
                    errors.dateInput = "Holiday date must be in the future";
                  } */
                }

                if (!values.description.trim()) {
                  errors.description = "Description is required";
                }

                return errors;
              }}
              onSubmit={(values, { setSubmitting }) => {
                handleSaveChanges({
                  ...values,
                  dateInput: parseDateToInputFormat(values.date),
                });
                setSubmitting(false);
              }}
            >
              {({ errors, touched, isSubmitting, setFieldValue }) => (
                <Form>
                  <Box sx={{ mb: 2 }}>
                    <label>
                      {" "}
                      Name <span className="required">*</span>
                    </label>
                    <Field
                      as={TextField}
                      fullWidth
                      name="title"
                      variant="outlined"
                      size="small"
                      error={touched.title && !!errors.title}
                      helperText={touched.title && errors.title}
                    />
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <label>
                      {" "}
                      Date <span className="required">*</span>
                    </label>
                    <Field
                      as={TextField}
                      fullWidth
                      type="date"
                      name="dateInput"
                      variant="outlined"
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      /* inputProps={{
                        min: new Date().toISOString().split("T")[0],
                      }} */
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        const newDate = e.target.value;
                        setFieldValue("dateInput", newDate);

                        // Update the display format (date)
                        const displayDate = new Date(
                          newDate
                        ).toLocaleDateString("en-GB", {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        });
                        setFieldValue("date", displayDate);
                      }}
                      error={touched.dateInput && !!errors.dateInput}
                      helperText={touched.dateInput && errors.dateInput}
                    />
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <label>
                      {" "}
                      Description <span className="required">*</span>
                    </label>
                    <Field
                      as={TextField}
                      fullWidth
                      name="description"
                      variant="outlined"
                      size="small"
                      multiline
                      rows={3}
                      error={touched.description && !!errors.description}
                      helperText={touched.description && errors.description}
                    />
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body1"
                      sx={{ fontWeight: 500, mb: 0.5 }}
                    >
                      Status
                    </Typography>
                    <Field
                      as={Select}
                      fullWidth
                      name="status"
                      variant="outlined"
                      size="small"
                    >
                      <MenuItem value="Active">Active</MenuItem>
                      <MenuItem value="Inactive">Inactive</MenuItem>
                    </Field>
                  </Box>
                  <DialogActions sx={{ padding: "0px !important" }}>
                    <Button
                      sx={{
                        fontWeight: "400",
                        fontSize: "14px",
                        color: "#111827",
                        border: "1px solid #E5E7EB",
                        borderRadius: "5px",
                        textTransform: "none",
                        padding: "8px 13.6px",
                      }}
                      onClick={() => setEditModal(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      sx={{
                        display: "flex",
                        gap: "8px",
                        backgroundColor: "#F26522",
                        borderColor: "#F26522",
                        color: "#FFF",
                        fontWeight: 400,
                        fontSize: "14px",
                        borderRadius: "5px",
                        textTransform: "none",
                        padding: "8px 13.6px",
                      }}
                      variant="contained"
                    >
                      Save Changes
                    </Button>
                  </DialogActions>
                </Form>
              )}
            </Formik>
          )}
        </DialogContent>
      </Dialog>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        title="Delete Holiday"
        message="Are you sure you want to delete this holiday? This action cannot be undone."
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
      />
    </Box>
  );
}

export default HolidayPage;

import axios from "axios";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";

// First, define an enum or object for error codes
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  PARTIAL_CONTENT = 206,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  PAYLOAD_TOO_LARGE = 413,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
}

// Define success status codes
const SUCCESS_STATUS_CODES = [
  HttpStatusCode.OK,
  HttpStatusCode.CREATED,
  HttpStatusCode.PARTIAL_CONTENT,
  HttpStatusCode.PAYLOAD_TOO_LARGE,
];

// Base Axios instance
const base = axios.create({
  headers: {
    "Content-Type": "application/json",
    Platform: "ios",
    "Accepted-Language": "en",
  },
});

base.interceptors.request.use((config) => {
  const token = localStorage.getItem("token");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

base.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const { status, data } = error.response;
      const errorMessage = data?.error?.errors;

      if (status === HttpStatusCode.INTERNAL_SERVER_ERROR) {
        toast.error(errorMessage);
      } else if (
        
        status === HttpStatusCode.UNAUTHORIZED
      ) {
        localStorage.removeItem("token");
        localStorage.removeItem("userEmail");
        sessionStorage.removeItem("auth-storage");
        window.location.href = "/login";
        toast.error(errorMessage);
      } else if (!SUCCESS_STATUS_CODES.includes(status)) {
        toast.error(errorMessage);
      }
    } else {
      const networkError = "Network error. Please check your connection.";
      toast.error(networkError);
    }
    return Promise.reject(error);
  }
);

export const base2 = axios.create({
  headers: {
    "Content-Type": "application/json",
  },
});

base2.interceptors.request.use((config) => {
  config.headers["Accept-Language"] = "en";
  config.headers["X-SGI-Platform"] = "ios";
  config.headers["X-SGI-Version"] = "1.0.0";
  const token = localStorage.getItem("token");

  if (token) {
    config.headers["Authorization"] = `Bearer ${token}`;
  }
  console.log("Request Headers:", config.headers);
  return config;
});

base2.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const { status, data } = error.response;

      const errorMessage = data?.message || "An unexpected error occurred";

      if (status === HttpStatusCode.INTERNAL_SERVER_ERROR) {
        console.error(errorMessage);
        toast.error(errorMessage);
      } else if (
        status === HttpStatusCode.UNAUTHORIZED ||
        !SUCCESS_STATUS_CODES.includes(status)
      ) {
        if (status === HttpStatusCode.UNAUTHORIZED) {
          sessionStorage.removeItem("token");
          const router = useRouter();
          router.push("/login");
        }
        toast.error(errorMessage);
      }
    } else {
      const networkError = "Network error. Please check your connection.";
      console.error(networkError);
      toast.error(networkError);
    }
    return Promise.reject(error);
  }
);

export default base;
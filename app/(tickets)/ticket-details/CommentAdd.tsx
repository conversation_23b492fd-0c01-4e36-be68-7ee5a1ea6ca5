"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  TextField,
  Button,
  Box,
  Typography,
  DialogActions,
  CircularProgress,
} from "@mui/material";
import { Close, AttachFile as AttachFileIcon } from "@mui/icons-material";
import { toast } from "react-toastify";
import { useFormik } from "formik";
import * as Yup from "yup";
import Image from "next/image";
import useUploadMedia from "@/app/hooks/useUploadMedia";
import TextEditor from "@/components/TextEditor/TextEditor";
import useAuthStore from "@/store/authStore";
import { addCommentToTicket } from "../../services/tickets/tickets.service";
import "./CommentAdd.scss";
import Loader from "@/components/Loader/Loader";

const stripHtmlTags = (html: any) => {
  const div = document.createElement("div");
  div.innerHTML = html;
  return div.textContent || div.innerText || "";
};

interface CommentDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: {
    message: string;
    commentBy: string;
    cc: string[];
    commentDate: string;
    media: string[];
  }) => void;
  ticketId: string;
}

const CommentAdd: React.FC<CommentDialogProps> = ({
  open,
  onClose,
  onSubmit,
  ticketId,
}) => {
  const [attachmentFiles, setAttachmentFiles] = useState<File[]>([]);
  const [attachmentPreviews, setAttachmentPreviews] = useState<string[]>([]);
  const [attachmentsUrl, setAttachmentsUrl] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false); // New loading state
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { uploadMedia } = useUploadMedia();
  const { employeeId: authEmployeeId } = useAuthStore();

  const validationSchema = Yup.object({
    message: Yup.string().required("Message is required"),
  });

  const formik = useFormik({
    initialValues: {
      message: "",
      commentBy: authEmployeeId || "",
      media: [] as string[],
    },
    validationSchema,
    onSubmit: async (values) => {
      if (!authEmployeeId) {
        toast.error("User not authenticated");
        return;
      }

      setIsSubmitting(true); // Start loading
      try {
        const payload = {
          message: stripHtmlTags(values.message),
          commentBy: authEmployeeId,
          media: attachmentsUrl,
        };
        await addCommentToTicket(ticketId, payload);
        onSubmit({
          ...payload,
          cc: [],
          commentDate: new Date().toISOString(),
        });
        toast.success("Comment added successfully");
        handleClose();
      } catch (error: any) {
        console.error("Error adding comment:", error);
        toast.error(error?.response?.data?.message || "Failed to add comment");
      } finally {
        setIsSubmitting(false); // Stop loading
      }
    },
  });

  const handleClose = () => {
    formik.resetForm();
    setAttachmentFiles([]);
    setAttachmentPreviews([]);
    setAttachmentsUrl([]);
    onClose();
  };

  const handleAttachmentChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files).filter((file) => {
        if (file.size > 4 * 1024 * 1024) {
          toast.error(`${file.name} exceeds 4 MB limit`);
          return false;
        }
        return true;
      });
      if (newFiles.length === 0) return;

      setIsUploading(true);
      try {
        const newPreviews: string[] = [];
        const newUrls: string[] = [];
        for (const file of newFiles) {
          const result = await uploadMedia(
            file,
            "comment_attachments",
            1,
            setIsUploading,
            file.name
          );
          newPreviews.push(URL.createObjectURL(file));
          newUrls.push(result.preview);
        }
        setAttachmentFiles((prev) => [...prev, ...newFiles]);
        setAttachmentPreviews((prev) => [...prev, ...newPreviews]);
        setAttachmentsUrl((prev) => [...prev, ...newUrls]);
      } catch (error) {
        console.error("Attachment upload error:", error);
        toast.error("Failed to upload attachments");
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleRemoveAttachment = (index: number) => {
    if (attachmentPreviews[index]?.startsWith("blob:")) {
      URL.revokeObjectURL(attachmentPreviews[index]);
    }
    setAttachmentFiles((prev) => prev.filter((_, i) => i !== index));
    setAttachmentPreviews((prev) => prev.filter((_, i) => i !== index));
    setAttachmentsUrl((prev) => prev.filter((_, i) => i !== index));
  };

  useEffect(() => {
    return () => {
      attachmentPreviews.forEach((preview) => {
        if (preview?.startsWith("blob:")) {
          URL.revokeObjectURL(preview);
        }
      });
    };
  }, [attachmentPreviews]);

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          handleClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
      className="comment-dialog"
    >
      <DialogTitle className="dialog-title">
        <Box className="title-container">
          <Typography variant="h6" className="dialog-title-text">
            Add Comment
          </Typography>
        </Box>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{
            backgroundColor: "#6b7280",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": { backgroundColor: "#d55a1d" },
            "& .MuiSvgIcon-root": { fontSize: "14px" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent className="dialog-content">
        {isSubmitting && <Loader loading={isSubmitting} />}
        <form onSubmit={formik.handleSubmit}>
          <Box className="form-container">
            <Box className="form-field">
              <Typography variant="subtitle1">
                Message <span className="required">*</span>
              </Typography>
              <Box>
                <TextEditor
                  value={formik.values.message}
                  onChange={(value) => formik.setFieldValue("message", value)}
                />
                {formik.touched.message && formik.errors.message && (
                  <Typography color="error" variant="caption">
                    {formik.errors.message}
                  </Typography>
                )}
              </Box>
            </Box>

            <Box className="form-field">
              <Typography variant="subtitle1">Upload Files</Typography>
              <Box className="upload-files-section">
                <Button
                  variant="outlined"
                  component="label"
                  className="file-upload-btn"
                  disabled={isUploading || isSubmitting}
                  startIcon={<AttachFileIcon />}
                >
                  {isUploading ? "Uploading..." : "Choose Files"}
                  <input
                    type="file"
                    multiple
                    hidden
                    onChange={handleAttachmentChange}
                    ref={fileInputRef}
                  />
                </Button>
              </Box>

              {attachmentPreviews.length > 0 && (
                <Box className="uploaded-files-section">
                  <Box className="file-previews">
                    {attachmentPreviews.map((preview, index) => (
                      <Box key={index} className="file-preview-item">
                        {attachmentFiles[index]?.type.startsWith("image/") ? (
                          <Image
                            className="file-preview-image"
                            src={preview}
                            alt={attachmentFiles[index]?.name}
                            width={100}
                            height={100}
                            style={{ objectFit: "cover" }}
                          />
                        ) : (
                          <Box className="file-preview-placeholder">
                            <AttachFileIcon className="file-icon" />
                            <Button
                              variant="text"
                              className="view-file-btn"
                              href={attachmentsUrl[index]}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              View File
                            </Button>
                          </Box>
                        )}
                        <IconButton
                          size="small"
                          className="remove-file-btn"
                          onClick={() => handleRemoveAttachment(index)}
                          disabled={isSubmitting || isUploading}
                        >
                          <Close />
                        </IconButton>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
          </Box>

          <DialogActions className="dialog-actions">
            <Button
              variant="outlined"
              onClick={handleClose}
              className="cancel-button"
              disabled={isSubmitting || isUploading}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              type="submit"
              className="save-button"
              disabled={isSubmitting || isUploading}
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </DialogActions>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CommentAdd;

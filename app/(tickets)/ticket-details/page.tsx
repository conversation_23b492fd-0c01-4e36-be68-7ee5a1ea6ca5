"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Box, Typography, CircularProgress } from "@mui/material";
import Link from "next/link";
import { KeyboardBackspace } from "@mui/icons-material";
import TicketDetailsContent from "@/components/ticket-details-content/ticket-details-content";
import TicketDetailsCard from "@/components/TicketDetailsCard/TicketDetailsCard";
import { getTicketById } from "../../services/tickets/tickets.service";
import { toast } from "react-toastify";
import "./ticket-details.scss";
import Loader from "@/components/Loader/Loader";

interface Employee {
  _id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  email: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Comment {
  _id: string;
  message: string;
  commentBy: Employee;
  cc: Employee[];
  commentDate: string;
  media: string[];
  createdAt: string;
  updatedAt: string;
}

interface TicketCategory {
  _id: string;
  category: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Department {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Ticket {
  _id: string;
  empId: Employee;
  category: TicketCategory | null;
  departmentId: Department | null;
  subject: string;
  description: string;
  priority: "High" | "Low" | "Medium";
  status: "Open" | "OnHold" | "Reopened" | "Closed";
  comments: Comment[];
  isActive: boolean;
  isDeleted: boolean;
  ticketId: string;
  createdAt: string;
  updatedAt: string;
  cc: Employee[];
  assignTo?: Employee;
}

const TicketDetails = () => {
  const searchParams = useSearchParams();
  const [ticket, setTicket] = useState<Ticket | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTicket = async () => {
    const id = searchParams.get("id");
    if (!id) {
      setError("No ticket ID provided");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await getTicketById(id);
      setTicket(response.ticket);
    } catch (err) {
      console.error("Error fetching ticket:", err);
      setError("Failed to fetch ticket details");
      toast.error("Failed to fetch ticket details");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTicket();
  }, [searchParams]);

  if (loading) {
    return (
      <Loader loading={loading}/>
    );
  }

  if (error || !ticket) {
    return (
      <Box sx={{ p: 4 }}>
        <Typography color="error">{error || "Ticket not found"}</Typography>
      </Box>
    );
  }

  return (
    <Box className="ticket-details-container">
      <Box className="ticket-details-header">
        <Typography variant="h6">
          <Link href="/tickets">
            <KeyboardBackspace
              sx={{ fontSize: "14px", marginRight: "0.5rem" }}
            />
            Ticket Details
          </Link>
        </Typography>
      </Box>

      <Box className="ticket-details-content">
        <Box sx={{ width: "75%" }}>
          <TicketDetailsContent ticket={ticket} />
        </Box>
        <Box sx={{ width: "25%" }}>
          <TicketDetailsCard ticket={ticket} onUpdate={fetchTicket} />
        </Box>
      </Box>
    </Box>
  );
};

export default TicketDetails;

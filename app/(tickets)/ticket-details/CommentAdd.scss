.comment-dialog {
    .dialog-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px !important;
  
      .title-container {
        display: flex;
        align-items: center;
        gap: 12px;
  
        .dialog-title-text {
          font-size: 20px;
          color: #111827;
          font-weight: 600;
        }
      }
    }
  
    .dialog-content {
      padding: 16px !important;
      max-height: 90vh;
      overflow-y: auto;
  
      .form-container {
        display: flex;
        flex-direction: column;
      }
  
      .upload-files-section {
        margin-bottom: 16px;
      }
  
      .uploaded-files-section {
        margin-top: 16px;
  
        .file-previews {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
        }
  
        .file-preview-item {
          position: relative;
          width: 100px;
          height: 100px;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f9f9f9;
  
          .file-preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
  
          .file-preview-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 8px;
            width: 100%;
            height: 100%;
            gap: 4px;
  
            .file-icon {
              font-size: 24px;
              color: #6b7280;
            }
  
            .view-file-btn {
              color: #f26522;
              text-transform: none;
              font-size: 12px;
              padding: 2px 8px;
  
              &:hover {
                background-color: rgba(242, 101, 34, 0.1);
              }
            }
          }
  
          .remove-file-btn {
            position: absolute;
            top: 4px;
            right: 4px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 2px;
  
            &:hover {
              background-color: rgba(0, 0, 0, 0.7);
            }
  
            .MuiSvgIcon-root {
              font-size: 8px;
            }
          }
        }
      }
  
      .form-field {
        margin-bottom: 0px;
        width: 100%;
  
        .required {
          color: red;
        }
  
        .custom-autocomplete {
          .MuiInputBase-root {
            padding: 8px !important;
            display: flex;
            flex-wrap: wrap;
            height: auto !important;
            min-height: 38px !important;
            max-height: none !important;
          }
  
          .MuiInputBase-input {
            padding: 0 !important;
            height: auto !important;
          }
  
          .MuiAutocomplete-endAdornment {
            position: absolute;
            right: 9px;
            top: 50%;
            transform: translateY(-50%);
          }
  
          .MuiAutocomplete-tag {
            margin: 3px;
          }
        }
  
        .autocomplete-input {
          width: 100%;
  
          .MuiOutlinedInput-root {
            height: auto;
            min-height: 38px;
          }
        }
      }
  
      .dialog-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin: 0px !important;
  
        .cancel-button {
          color: #666;
          border-color: #ccc;
          text-transform: none;
        }
  
        .save-button {
          background-color: #f26522;
          text-transform: none;
  
          &:hover {
            background-color: #e55a1b;
          }
        }
      }
    }
  }
  
  .file-upload-btn {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 8px !important;
    padding: 6px 16px !important;
    margin: 0 !important;
    border-color: #e0e0e0 !important;
    color: #6b7280 !important;
    text-transform: none !important;
    font-size: 14px !important;
  
    .MuiButton-startIcon {
      margin: 0 !important;
      display: flex !important;
    }
  
    &:hover {
      background-color: rgba(0, 0, 0, 0.04) !important;
      border-color: #d0d0d0 !important;
    }
  
    &:disabled {
      color: #ccc !important;
      border-color: #e0e0e0 !important;
    }
  }
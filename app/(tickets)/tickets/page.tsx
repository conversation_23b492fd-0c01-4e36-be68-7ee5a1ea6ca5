"use client";

import { Box, Typography } from "@mui/material";
import React, { useState, useEffect, Suspense } from "react";
import "./tickets.scss";
import {
  HomeOutlined,
  ControlPoint,
  ConfirmationNumber,
  FolderOpen,
  DoneAll,
  ReportGmailerrorred,
} from "@mui/icons-material";
import { Button } from "@mui/material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import ViewToggleTicket from "@/components/ViewToggleButton/ViewToggleTicket";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import useFetchTickets from "../../hooks/tickets/useFetchTickets";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import TicketCounterCard from "@/components/TicketCounterCard/TicketCounterCard";
import TicketCard from "@/components/TicketCard/TicketCard";
import InfoListCard from "@/components/InfoListCard/InfoListCard";
import AddTicketDialog from "@/components/TicketCard/AddTicketDialog";
import {
  deleteTicket,
  getAllCategoryTicketsCount,
} from "@/app/services/tickets/tickets.service";

interface Employee {
  _id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  email: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Comment {
  _id: string;
  message: string;
  commentBy: Employee;
  cc: Employee[];
  commentDate: string;
  media: string[];
  createdAt: string;
  updatedAt: string;
}

interface TicketCategory {
  _id: string;
  category: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Department {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Ticket {
  _id: string;
  empId: Employee;
  category: TicketCategory | null;
  subject: string;
  cc: Employee[];
  description: string;
  priority: "High" | "Low" | "Medium";
  status: "Open" | "OnHold" | "Reopened" | "Closed";
  comments: Comment[];
  isActive: boolean;
  isDeleted: boolean;
  ticketId: string;
  createdAt: string;
  updatedAt: string;
  assignTo: Employee;
  departmentId?: Department;
}

interface CategoryCount {
  categoryId: string;
  categoryName: string;
  ticketCount: number;
}

function TicketsContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [limit] = useState(10);
  const [categoryData, setCategoryData] = useState<CategoryCount[]>([]);

  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const [selectedDesignation, setSelectedDesignation] =
    useState<string>("Designation");
  const [selectedPriority, setSelectedPriority] = useState<string>("Priority");

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deletingTicketId, setDeletingTicketId] = useState<string | null>(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingTicketId, setEditingTicketId] = useState<string | null>(null);

  const [
    ticketsData,
    total,
    activeCount,
    inactiveCount,
    pendingCount,
    assignedStats,
  ] = useFetchTickets({
    setIsLoading,
    refresh,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate,
    endDate,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    priority: selectedPriority === "Priority" ? undefined : selectedPriority,
    status: selectedStatus === "Select Status" ? undefined : selectedStatus,
  });

  useEffect(() => {
    const fetchCategoryCounts = async () => {
      try {
        const response = await getAllCategoryTicketsCount();
        if (response.success) {
          setCategoryData(response.data);
        } else {
          console.error("Failed to fetch category counts:", response.message);
          toast.error("Failed to load category ticket counts");
        }
      } catch (error) {
        console.log(error)
      }
    };

    fetchCategoryCounts();
  }, [refresh]);

  const ticketCards = ticketsData.map((ticket: Ticket) => ({
    _id: ticket._id,
    ticketId: ticket.ticketId,
    title: ticket.subject,
    assignedTo: ticket.assignTo
      ? `${ticket.assignTo.firstName} ${ticket.assignTo.lastName}`
      : "N/A",
    userAvatar: ticket.assignTo?.avatar || "/avatars/default.png",
    updatedAt: new Date(ticket.updatedAt).toLocaleString(),
    commentsCount: ticket.comments.length,
    status: ticket.status,
    priority: ticket.priority,
    category: ticket.category?.category || "N/A",
    departmentName: ticket.departmentId?.departmentName || "N/A",
    onEdit: () => {
      setEditingTicketId(ticket._id);
      setEditModalOpen(true);
    },
    onDelete: () => {
      setDeletingTicketId(ticket._id);
      setDeleteModalOpen(true);
    },
  }));

  const formattedCategoryData = categoryData.map((item) => ({
    label: item.categoryName,
    count: item.ticketCount,
  }));

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Employee", href: "" },
    { label: "Tickets" },
  ];

  const refreshList = () => {
    setRefresh(!refresh);
  };

  const handleConfirmDelete = async () => {
    if (deletingTicketId) {
      try {
        await deleteTicket(deletingTicketId);
        toast.success("Ticket deleted successfully");
        refreshList();
      } catch (error) {
        console.error("Error deleting ticket:", error);
        toast.error("Failed to delete ticket");
      }
    }
    setDeleteModalOpen(false);
    setDeletingTicketId(null);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteModalOpen(false);
    setDeletingTicketId(null);
  };

  return (
    <Box className="tickets-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="tickets-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Tickets</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>

          <Box
            className="add-policy"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <ViewToggleTicket />
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 500,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px !important",
                minHeight: "fit-content",
              }}
              onClick={() => setEditModalOpen(true)}
            >
              <ControlPoint sx={{ width: "14px", height: "14px" }} />
              Add Ticket
            </Button>
          </Box>
        </Box>

        <Box className="header-cards">
          <TicketCounterCard
            graphSvg="/assets/tickets/svgexport-1.svg"
            iconPath={<ConfirmationNumber />}
            color="#F26522"
            label="New Tickets"
            count={total || 0}
            bgColor="#FEF1EB !important"
          />
          <TicketCounterCard
            graphSvg="/assets/tickets/svgexport-2.svg"
            iconPath={<FolderOpen />}
            color="#AB47BC"
            label="Open Tickets"
            count={activeCount || 0}
            bgColor="#F7EEF9 !important"
          />
          <TicketCounterCard
            graphSvg="/assets/tickets/svgexport-3.svg"
            iconPath={<DoneAll />}
            color="#03C95A"
            label="Solved Tickets"
            count={inactiveCount || 0}
            bgColor="#D2F5E1 !important"
          />
          <TicketCounterCard
            graphSvg="/assets/tickets/svgexport-4.svg"
            iconPath={<ReportGmailerrorred />}
            color="#1B84FF"
            label="Pending Tickets"
            count={pendingCount || 0}
            bgColor="#D6E9FF !important"
          />
        </Box>

        <Box
          className="DataGrid-container"
          sx={{
            display: "flex",
            flexDirection: "column",
            maxHeight: "calc(100vh - 200px)",
            backgroundColor: "#fff",
          }}
        >
          <Box className="DataGrid-header">
            <Typography variant="h5">Ticket List</Typography>
            <PolicyFilters
              departments={[]}
              designations={[]}
              selectedDepartment={selectedDepartment}
              setSelectedDepartment={setSelectedDepartment}
              selectedDesignation={selectedDesignation}
              setSelectedDesignation={setSelectedDesignation}
              selectedSortBy={selectedSortBy}
              setSelectedSortBy={setSelectedSortBy}
              selectedDateRange={selectedDateRange}
              setSelectedDateRange={setSelectedDateRange}
              setStartDate={setStartDate}
              setEndDate={setEndDate}
              selectedStatus={selectedStatus}
              setSelectedStatus={setSelectedStatus}
              selectedPriority={selectedPriority}
              setSelectedPriority={setSelectedPriority}
              setPage={setPage}
              currentDate={currentDate}
              showDateRangeFilter={true}
              showDepartmentFilter={false}
              showStatusFilter={true}
              showSortByFilter={true}
              showDesignationFilter={false}
              showPriorityFilter={true}
              selectedLeaveType="Leave Type"
              setSelectedLeaveType={() => {}}
              showLeaveTypeFilter={false}
              optionalStatusFilter={true}
            />
          </Box>
        </Box>

        <Box sx={{ display: "flex", gap: "24px" }}>
          <Box className="Profile-Cards">
            {ticketCards.length > 0 ? (
              ticketCards.map((ticket) => (
                <TicketCard key={ticket.ticketId} {...ticket} />
              ))
            ) : (
              <Typography>No tickets available</Typography>
            )}
          </Box>
          {ticketCards.length > 0 && (
            <Box className="Info-List-Cards">
              <InfoListCard
                title="Ticket Categories"
                items={formattedCategoryData}
                showAvatar={false}
              />
            </Box>
          )}
        </Box>
      </Box>

      <AddTicketDialog
        open={editModalOpen}
        onClose={() => {
          setEditModalOpen(false);
          setEditingTicketId(null);
        }}
        onAdd={() => {
          setEditModalOpen(false);
          setEditingTicketId(null);
          refreshList();
        }}
        ticketId={editingTicketId ?? undefined}
        isEditing={!!editingTicketId}
      />

      <DeleteConfirmationDialog
        open={deleteModalOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Confirm Delete"
        message="You want to delete this ticket, this can't be undone."
      />
    </Box>
  );
}

function TicketsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TicketsContent />
    </Suspense>
  );
}

export default TicketsPage;
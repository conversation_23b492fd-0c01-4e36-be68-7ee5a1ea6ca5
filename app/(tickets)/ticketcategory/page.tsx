"use client";

import {
  <PERSON>,
  IconButt<PERSON>,
  Button,
  Divider,
  Typography,
  Paper,
  useMediaQuery,
} from "@mui/material";
import React, { useCallback, useState } from "react";
import "./TicketCategory.scss";
import { HomeOutlined, ControlPoint } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import { GridRowsProp, GridColDef } from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { EditNote, Delete } from "@mui/icons-material";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import useFetchTicketCategoryData from "@/app/hooks/tickets/useFetchTicketsCategory";
import {
  addTicketCategory,
  updateTicketCategory,
  deleteTicketCategory,
} from "@/app/services/tickets/tickets.service";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import AddTicketCategoryDialog from "./AddTicketCategory";
import PolicyFilters from "@/components/policyFilter/PolicyFilters";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";

// Define interfaces
interface TicketCategory {
  _id: string;
  category: string;
  departmentId: {
    _id: string;
    departmentName: string;
    isActive: boolean;
    isDeleted: boolean;
  };
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function TicketCategoryContent() {
  // State for loading and refresh
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);

  // State for pagination
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);

  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const handlePageChange = (newPage: number): void => {
    setPage(newPage + 1);
  };

  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  // State for the Add/Edit Ticket Category modal
  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(
    null
  );

  // State for filters
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>("Date Range");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");

  const currentDate = new Date();

  // Fetch ticket category data
  const [ticketCategoryData, total] = useFetchTicketCategoryData({
    setIsLoading,
    refresh,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate,
    endDate,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  // Restrict access to admin roles only (Admin, SuperAdmin, Manager, HR)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // Map ticket category data to a list of category names for PolicyFilters
  const categories = ticketCategoryData
    ? ticketCategoryData.map((category: TicketCategory) => category.category)
    : [];

  const rows: GridRowsProp = ticketCategoryData
    ? ticketCategoryData.map((category: TicketCategory, index: number) => ({
        id: category._id || index + 1,
        categoryName: category.category || "N/A",
        departmentName: category.departmentId?.departmentName || "-",
        isActive: category.isActive || false,
      }))
    : [];

  // State for delete dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<
    string | number | null
  >(null);

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Ticket Management", href: "" },
    { label: "Ticket Categories" },
  ];

  const isMobile = useMediaQuery("(max-width:768px)");

  // Handle status toggle
  const handleStatusChange = async (categoryId: string, newStatus: boolean) => {
    try {
      const formData = new FormData();
      formData.append("isActive", newStatus.toString());
      await updateTicketCategory(categoryId, formData);
      toast.success(
        `Ticket category status updated to ${newStatus ? "Active" : "Inactive"}!`
      );
      setRefresh(!refresh); // Refresh data
    } catch (error) {
      console.error("Failed to update ticket category status:", error);
      toast.error("Failed to update status. Please try again.");
    }
  };

  const columns: GridColDef[] = [
    {
      field: "categoryName",
      headerName: "Ticket Category",
      flex: 1,
    },
    {
      field: "departmentName",
      headerName: "Department",
      flex: 1,
      renderCell: (params) => <Typography>{params.value}</Typography>,
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 1,
      renderCell: (params) => (
        <StatusToggle
          isActive={params.value}
          onChange={(newStatus) => handleStatusChange(params.row.id, newStatus)}
          title={params.row.categoryName}
        />
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 0.5,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handleEditClick = async (categoryId: string | number) => {
    try {
      setSelectedCategoryId(categoryId as string);
      setIsEditMode(true);
      setOpenModal(true);
    } catch (error) {
      console.error("Error preparing edit:", error);
      toast.error("Failed to prepare edit. Please try again.");
    }
  };

  const handleDeleteClick = (categoryId: string | number) => {
    setCategoryToDelete(categoryId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (categoryToDelete !== null) {
      try {
        await deleteTicketCategory(categoryToDelete as string);
        setDeleteDialogOpen(false);
        setCategoryToDelete(null);
        setRefresh(!refresh);
        toast.success("Ticket category deleted successfully!", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      } catch (error) {
        console.error("Failed to delete ticket category:", error);
        toast.error("Failed to delete ticket category. Please try again.", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setCategoryToDelete(null);
  };

  const handleDialogSubmit = async (formData: FormData) => {
    try {
      if (isEditMode && selectedCategoryId) {
        await updateTicketCategory(selectedCategoryId, formData);
        toast.success("Ticket category updated successfully!", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      } else {
        await addTicketCategory(formData);
        toast.success("Ticket category added successfully!", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }
      setRefresh(!refresh);
      setOpenModal(false);
    } catch (error) {
      console.error("Error submitting ticket category:", error);
      toast.error("Failed to submit ticket category. Please try again.", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    }
  };

  const handleDialogClose = () => {
    setOpenModal(false);
    setIsEditMode(false);
    setSelectedCategoryId(null);
  };

  return (
    <Box className="ticket-category-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="ticket-category-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Ticket Categories</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="add-ticket-category"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => {
                setIsEditMode(false);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ width: "16px", height: "16px" }} />
              Add Ticket Category
            </Button>
          </Box>
        </Box>

        {/* DataGrid */}
        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight:
                rows.length <= 5 ? "calc(70vh - 200px)" : "calc(100vh - 200px)",
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Ticket Category List</Typography>
              <PolicyFilters
                departments={categories}
                selectedDepartment={""}
                setSelectedDepartment={() => {}}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={false}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
                showPriorityFilter={false}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                setPage(model.page + 1);
                setPageSize(model.pageSize);
                setLimit(model.pageSize);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      {/* Ticket Category Dialog */}
      <AddTicketCategoryDialog
        open={openModal}
        onClose={handleDialogClose}
        onSubmit={handleDialogSubmit}
        isEditMode={isEditMode}
        categoryId={selectedCategoryId}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this ticket category? This action cannot be undone."
      />
    </Box>
  );
}

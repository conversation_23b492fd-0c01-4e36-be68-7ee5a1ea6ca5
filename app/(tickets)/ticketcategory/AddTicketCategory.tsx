"use client";
import type React from "react";
import { useState, useEffect } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Typography,
  IconButton,
  Select,
  FormControl,
  MenuItem,
  Autocomplete,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { getTicketCategoryById } from "@/app/services/tickets/tickets.service";
import { getDepartments } from "@/app/services/department.service";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Loader from "@/components/Loader/Loader";

interface AddTicketCategoryDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (formData: FormData) => Promise<void>;
  isEditMode: boolean;
  categoryId: string | null;
}

interface TicketCategoryFormValues {
  category: string;
  status: "active" | "inactive";
  departmentId: string | null;
}

interface Department {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

const validationSchema = Yup.object({
  category: Yup.string().required("Ticket category name is required"),
  status: Yup.string().required("Status is required"),
  departmentId: Yup.string().required("Department is required"),
});

const initialValues: TicketCategoryFormValues = {
  category: "",
  status: "active",
  departmentId: null,
};

const AddTicketCategoryDialog: React.FC<AddTicketCategoryDialogProps> = ({
  open,
  onClose,
  onSubmit,
  isEditMode,
  categoryId,
}) => {
  const [initialFormValues, setInitialFormValues] =
    useState<TicketCategoryFormValues>(initialValues);
  const [loadingCategory, setLoadingCategory] = useState(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loadingDepartments, setLoadingDepartments] = useState(false);

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setLoadingDepartments(true);
        const response = await getDepartments();
        if (
          response.success &&
          response.departments &&
          Array.isArray(response.departments.results)
        ) {
          setDepartments(response.departments.results);
        } else {
          toast.error("Failed to load departments.");
        }
      } catch (error) {
        console.error("Error fetching departments:", error);
        toast.error("Failed to load departments.");
      } finally {
        setLoadingDepartments(false);
      }
    };

    const fetchCategoryDetails = async () => {
      if (isEditMode && categoryId) {
        try {
          setLoadingCategory(true);
          const response = await getTicketCategoryById(categoryId);
          console.log("Fetched ticket category data:", response);
          const categoryData = response.ticketCategory;
          setInitialFormValues({
            category: categoryData.category || "",
            status: categoryData.isActive ? "active" : "inactive",
            departmentId: categoryData.departmentId?._id || null,
          });
        } catch (error) {
          console.error("Error fetching ticket category:", error);
          toast.error("Failed to load ticket category details.");
        } finally {
          setLoadingCategory(false);
        }
      } else {
        setInitialFormValues(initialValues);
      }
    };

    if (open) {
      fetchDepartments();
      fetchCategoryDetails();
    }
  }, [open, isEditMode, categoryId]);

  const handleSubmit = async (values: TicketCategoryFormValues) => {
    try {
      const formData = new FormData();
      formData.append("category", values.category);
      formData.append("isActive", values.status === "active" ? "true" : "false");
      if (values.departmentId) {
        formData.append("departmentId", values.departmentId);
      }

      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error(
        `Error ${isEditMode ? "updating" : "adding"} ticket category:`,
        error
      );
      toast.error(
        `Failed to ${isEditMode ? "update" : "add"} ticket category. Please try again.`
      );
    }
  };

  return (
    <>
      <ToastContainer />
      {loadingCategory || loadingDepartments ? (
        <Loader loading={loadingCategory || loadingDepartments} />
      ) : (
        <Dialog
          open={open}
          onClose={onClose}
          sx={{
            "& .MuiDialog-paper": {
              maxWidth: "498px",
              width: "100%",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "16px !important",
            }}
          >
            <Typography
              sx={{ fontSize: "20px", fontWeight: 600, color: "#202c4b" }}
            >
              {isEditMode ? "Edit Ticket Category" : "Add Ticket Category"}
            </Typography>
            <IconButton
              onClick={onClose}
              aria-label="close"
              sx={{
                backgroundColor: "#6b7280",
                backgroundImage: "none",
                borderRadius: "50%",
                color: "#fff",
                height: "20px",
                width: "20px",
                margin: 0,
                padding: 0,
                "&:hover": {
                  backgroundColor: "#d55a1d",
                },
                "& .MuiSvgIcon-root": {
                  fontSize: "14px",
                },
              }}
            >
              <Close />
            </IconButton>
          </DialogTitle>

          <Formik
            initialValues={initialFormValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            enableReinitialize
          >
            {({ errors, touched, setFieldValue, values }) => (
              <Form>
                <DialogContent sx={{ padding: "16px !important" }}>
                  <Box sx={{ mt: 0 }}>
                    {/* Ticket Category Name */}
                    <Box sx={{ mb: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{ mb: 0.5, fontSize: "14px" }}
                      >
                        Ticket Category Name <span className="required">*</span>
                      </Typography>
                      <Field
                        as={TextField}
                        fullWidth
                        name="category"
                        variant="outlined"
                        error={touched.category && Boolean(errors.category)}
                        helperText={touched.category && errors.category}
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            height: "38px",
                            fontSize: "14px",
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          },
                          "& .MuiInputBase-input": {
                            padding: "8px 14px",
                          },
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#ccc",
                          },
                        }}
                      />
                    </Box>

                    {/* Department Autocomplete */}
                    <Box sx={{ mb: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{ mb: 0.5, fontSize: "14px" }}
                      >
                        Department <span className="required">*</span>
                      </Typography>
                      <Autocomplete
                        options={departments}
                        getOptionLabel={(option: Department) => option.departmentName}
                        value={
                          departments.find(
                            (dept) => dept._id === values.departmentId
                          ) || null
                        }
                        onChange={(_event, newValue: Department | null) => {
                          setFieldValue("departmentId", newValue?._id || null);
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            variant="outlined"
                            error={touched.departmentId && Boolean(errors.departmentId)}
                            helperText={touched.departmentId && errors.departmentId}
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                height: "38px",
                                fontSize: "14px",
                                "&:hover .MuiOutlinedInput-notchedOutline": {
                                  borderColor: "#ccc",
                                },
                                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                                  borderColor: "#ccc",
                                },
                              },
                              "& .MuiInputBase-input": {
                                padding: "8px 14px",
                              },
                              "& .MuiOutlinedInput-notchedOutline": {
                                borderColor: "#ccc",
                              },
                            }}
                          />
                        )}
                      />
                    </Box>

                    {/* Status Select */}
                    <Box sx={{ mb: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{ mb: 0.5, fontSize: "14px" }}
                      >
                        Status <span className="required">*</span>
                      </Typography>
                      <FormControl fullWidth>
                        <Field
                          as={Select}
                          name="status"
                          variant="outlined"
                          error={touched.status && Boolean(errors.status)}
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              height: "38px",
                              fontSize: "14px",
                            },
                            "& .MuiInputBase-input": {
                              padding: "8px 14px",
                            },
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: "#ccc",
                            },
                          }}
                        >
                          <MenuItem value="active">Active</MenuItem>
                          <MenuItem value="inactive">Inactive</MenuItem>
                        </Field>
                        <ErrorMessage
                          name="status"
                          component="div"
                          render={(msg) => (
                            <div style={{ color: "red", fontSize: "12px" }}>
                              {msg}
                            </div>
                          )}
                        />
                      </FormControl>
                    </Box>
                  </Box>
                </DialogContent>

                <DialogActions sx={{ padding: "16px !important" }}>
                  <Button
                    onClick={onClose}
                    variant="outlined"
                    sx={{
                      textTransform: "none",
                      borderColor: "#ccc",
                      color: "#000",
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    sx={{
                      backgroundColor: "#F26522",
                      color: "#FFF",
                      textTransform: "none",
                      "&:hover": {
                        backgroundColor: "#d55a1d",
                      },
                    }}
                  >
                    {isEditMode ? "Update Ticket Category" : "Add Ticket Category"}
                  </Button>
                </DialogActions>
              </Form>
            )}
          </Formik>
        </Dialog>
      )}
    </>
  );
};

export default AddTicketCategoryDialog;
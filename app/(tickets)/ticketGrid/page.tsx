"use client";
import { Box, Typography } from "@mui/material";
import React, { useState, Suspense } from "react";
import "./ticketGrid.scss";
import {
  HomeOutlined,
  ControlPoint,
  ConfirmationNumber,
  FolderOpen,
  DoneAll,
  ReportGmailerrorred,
} from "@mui/icons-material";
import { Button } from "@mui/material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import ViewToggleTicket from "@/components/ViewToggleButton/ViewToggleTicket";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import { deleteTicket } from "@/app/services/tickets/tickets.service";
import useFetchTickets from "@/app/hooks/tickets/useFetchTickets";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import TicketCounterCard from "@/components/TicketCounterCard/TicketCounterCard";
import TicketCard from "@/components/TicketCardGrid/TicketCardGrid";
import AddTicketDialog from "@/components/TicketCard/AddTicketDialog";

interface Employee {
  _id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  email: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Comment {
  _id: string;
  message: string;
  commentBy: Employee;
  cc: Employee[];
  commentDate: string;
  media: string[];
  createdAt: string;
  updatedAt: string;
}

interface Department {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Category {
  _id: string;
  category: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Ticket {
  _id: string;
  empId: Employee;
  title?: string;
  category: Category | null;
  subject: string;
  description: string;
  priority: "High" | "Low" | "Medium";
  status: "Open" | "On Hold" | "Reopened" | "Closed";
  comments: Comment[];
  isActive: boolean;
  isDeleted: boolean;
  ticketId: string;
  createdAt: string;
  updatedAt: string;
  assignTo: Employee;
  departmentId?: Department;
}

function TicketsContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [limit] = useState(10);
  const [departments, setDepartments] = useState<Department[]>([]);

  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const [selectedDesignation, setSelectedDesignation] =
    useState<string>("Designation");
  const [selectedPriority, setSelectedPriority] = useState<string>("Priority");

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deletingTicketId, setDeletingTicketId] = useState<string | null>(null);

  const [openModal, setOpenModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<string | null>(null);

  const [
    ticketsData,
    total,
    activeCount,
    inactiveCount,
    pendingCount,
    categoryStats,
    assignedStats,
  ] = useFetchTickets({
    setIsLoading,
    refresh,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate,
    endDate,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    priority: selectedPriority === "Priority" ? undefined : selectedPriority,
  });

  const ticketCards = ticketsData.map((ticket: Ticket) => ({
    _id: ticket._id,
    ticketId: ticket.ticketId,
    title: ticket.subject,
    name: ticket.empId.firstName,
    avatarUrl: ticket.empId.avatar || "/avatars/default.png",
    category: ticket.category?.category || "N/A",
    categoryType:
      ticket.category?.category || "N/A",
    status: ticket.status,
    priority: ticket.priority,
    assignee: ticket.assignTo
      ? `${ticket.assignTo.firstName} ${ticket.assignTo.lastName}`
      : "N/A",
    assigneeAvatar: ticket.assignTo?.avatar || "/avatars/default.png",
    departmentName: ticket.departmentId?.departmentName || "N/A", // Add departmentName
  }));

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Employee", href: "" },
    { label: "Tickets" },
  ];

  const refreshList = () => {
    setRefresh(!refresh);
  };

  const handleEditTicket = (_id: string) => {
    setSelectedTicket(_id);
    setIsEditing(true);
    setOpenModal(true);
  };

  const handleDeleteTicket = (_id: string) => {
    setDeletingTicketId(_id);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (deletingTicketId) {
      try {
        await deleteTicket(deletingTicketId);
        toast.success("Ticket deleted successfully");
        refreshList();
      } catch (error) {
        toast.error("Failed to delete ticket");
        console.error("Error deleting ticket:", error);
      }
    }
    setDeleteModalOpen(false);
    setDeletingTicketId(null);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteModalOpen(false);
    setDeletingTicketId(null);
  };

  return (
    <Box className="tickets-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="tickets-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Tickets</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>

          <Box
            className="add-policy"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <ViewToggleTicket />
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 500,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px !important",
                minHeight: "fit-content",
              }}
              onClick={() => {
                setIsEditing(false);
                setSelectedTicket(null);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ width: "14px", height: "14px" }} />
              Add Ticket
            </Button>
          </Box>
        </Box>

        <Box className="header-cards">
          <TicketCounterCard
            graphSvg="/assets/tickets/svgexport-1.svg"
            iconPath={<ConfirmationNumber />}
            color="#F26522"
            label="New Tickets"
            count={total || 0}
            bgColor="#FEF1EB !important"
          />
          <TicketCounterCard
            graphSvg="/assets/tickets/svgexport-2.svg"
            iconPath={<FolderOpen />}
            color="#AB47BC"
            label="Open Tickets"
            count={activeCount || 0}
            bgColor="#F7EEF9 !important"
          />
          <TicketCounterCard
            graphSvg="/assets/tickets/svgexport-3.svg"
            iconPath={<DoneAll />}
            color="#03C95A"
            label="Solved Tickets"
            count={inactiveCount || 0}
            bgColor="#D2F5E1 !important"
          />
          <TicketCounterCard
            graphSvg="/assets/tickets/svgexport-4.svg"
            iconPath={<ReportGmailerrorred />}
            color="#1B84FF"
            label="Pending Tickets"
            count={pendingCount || 0}
            bgColor="#D6E9FF !important"
          />
        </Box>

        <Box
          className="DataGrid-container"
          sx={{
            display: "flex",
            flexDirection: "column",
            maxHeight: "calc(100vh - 200px)",
            backgroundColor: "#fff",
            marginBottom: "1.5rem",
          }}
        >
          <Box className="DataGrid-header">
            <Typography variant="h5">Ticket List</Typography>
            <PolicyFilters
              departments={[]}
              designations={[]}
              selectedDepartment={selectedDepartment}
              setSelectedDepartment={setSelectedDepartment}
              selectedDesignation={selectedDesignation}
              setSelectedDesignation={setSelectedDesignation}
              selectedSortBy={selectedSortBy}
              setSelectedSortBy={setSelectedSortBy}
              selectedDateRange={selectedDateRange}
              setSelectedDateRange={setSelectedDateRange}
              setStartDate={setStartDate}
              setEndDate={setEndDate}
              selectedStatus={selectedStatus}
              setSelectedStatus={setSelectedStatus}
              selectedPriority={selectedPriority}
              setSelectedPriority={setSelectedPriority}
              setPage={setPage}
              currentDate={currentDate}
              showDateRangeFilter={true}
              showDepartmentFilter={false}
              showStatusFilter={true}
              showSortByFilter={true}
              showDesignationFilter={false}
              showPriorityFilter={true}
              selectedLeaveType="Leave Type"
              setSelectedLeaveType={() => {}}
              showLeaveTypeFilter={false}
            />
          </Box>
        </Box>

        <Box sx={{ display: "flex", gap: "24px", width: "100%" }}>
          <Box
            display="flex"
            gap={2}
            sx={{
              flexWrap: "wrap",
              maxWidth: "100%",
              width: "100%",
            }}
          >
            {ticketCards.length > 0 ? (
              ticketCards.map((ticket) => (
                <TicketCard
                  _id={ticket._id}
                  key={ticket._id}
                  avatarUrl={ticket.avatarUrl}
                  name={ticket.name}
                  ticketId={ticket.ticketId}
                  title={ticket.title}
                  category={ticket.category}
                  categoryType={ticket.categoryType}
                  status={ticket.status}
                  priority={ticket.priority}
                  assignee={ticket.assignee}
                  assigneeAvatar={ticket.assigneeAvatar}
                  departmentName={ticket.departmentName}
                  onEdit={handleEditTicket}
                  onDelete={handleDeleteTicket}
                />
              ))
            ) : (
              <Typography>No tickets available</Typography>
            )}
          </Box>
        </Box>
      </Box>

      <AddTicketDialog
        open={openModal}
        isEditing={isEditing}
        ticketId={selectedTicket || undefined}
        onClose={() => {
          setOpenModal(false);
          setIsEditing(false);
          setSelectedTicket(null);
        }}
        onAdd={() => {
          setOpenModal(false);
          setIsEditing(false);
          setSelectedTicket(null);
          refreshList();
        }}
      />

      <DeleteConfirmationDialog
        open={deleteModalOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Confirm Delete"
        message="You want to delete the selected ticket, this can't be undone once you delete."
      />
    </Box>
  );
}

function TicketsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TicketsContent />
    </Suspense>
  );
}

export default TicketsPage;
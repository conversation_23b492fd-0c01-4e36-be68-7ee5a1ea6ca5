"use client";
import { usePathname } from "next/navigation";
import ClientLayout from "./ClientLayout";

export default function ConditionalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const publicPaths = [
    "/login",
    "/login/",
    "/signup",
    "/signup/",
    "/forget-password",
    "/forget-password/",
    "/reset-password",
    "/reset-password/",
    "/success",
    "/success/",
  ];

  // Check if the current path is in the public paths list
  const isPublicPath = publicPaths.includes(pathname);

  return isPublicPath ? (
    <>{children}</>
  ) : (
    <ClientLayout>{children}</ClientLayout>
  );
}

"use client";
import {
  Box,
  Divider,
  MenuItem,
  Select,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Paper,
  useMediaQuery,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import "./HolidaysGrid.scss";
import {
  HomeOutlined,
  ControlPoint,
  Cancel,
  Circle,
} from "@mui/icons-material";
import { IconButton, Button } from "@mui/material";
import {
  getHolidays,
  editHoliday,
  postHoliday,
  deleteHoliday,
} from "@/app/services/holiday";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
} from "@mui/x-data-grid";
import { Formik, Form, Field } from "formik";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import useRoleAuth from "../hooks/useRoleAuth";
import { ROLE_GROUPS } from "../constants/roles";
import ViewToggleHolidays from "@/components/ViewToggleButton/ViewToggleHoliday";
import BigHolidayCalendar from '@/components/Calendar/BigHolidayCalendar';

// Define the type for a Holiday
interface Holiday {
  id: number;
  col1: string; // Title
  col2: string; // Date (Formatted as string)
  col3: string; // Description
  col4: "Active" | "Inactive"; // Status should be a union type
}

function HolidayPage() {
  const [loading, setLoading] = useState(false);

  // Restrict access to Employee roles only (Admin, SuperAdmin, Manager, HR, Employee)
  const { roles } = useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  // Add this constant to check if the user is an employee
  const isEmployee = roles.includes("Employee");

  useEffect(() => {
    let isMounted = true;

    const fetchHoliday = async () => {
      setLoading(true);
      try {
        const response = await getHolidays();
        if (response?.holidays?.results) {
          interface ApiHoliday {
            _id: string;
            title: string;
            date: string;
            description: string;
            isActive: boolean;
          }

          interface FormattedHoliday {
            id: string;
            col1: string;
            col2: string;
            col3: string;
            col4: "Active" | "Inactive";
          }

          const formattedHolidays: FormattedHoliday[] =
            response.holidays.results.map((holi: ApiHoliday) => ({
              id: holi._id,
              col1: holi.title,
              col2: new Date(holi.date).toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "short",
                year: "numeric",
              }),
              col3: holi.description,
              col4: holi.isActive ? "Active" : "Inactive",
            }));

          if (isMounted) setRows(formattedHolidays); // ✅ Backend data replaces hardcoded rows
        } else {
          if (isMounted) setRows([]);
          // toast.error("No holidays found.");
        }
      } catch (error) {
        console.error("API request failed:", error);
        if (isMounted) setRows([]);
        // toast.error("Failed to fetch holidays.");
      } finally {
        if (isMounted) setLoading(false);
      }
    };

    fetchHoliday();
    return () => {
      isMounted = false;
    };
  }, []);

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Employee", href: "" },
    { label: "Holiday" },
  ];

  const [openModal, setOpenModal] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const [deletingHoliday, setDeletingHoliday] = useState<Holiday | null>(null);
  const [editingHoliday, setEditingHoliday] = useState<Holiday | null>(null);
  const [rows, setRows] = useState<GridRowsProp>([]);

  // Helper functions for date formatting
  const parseDateToInputFormat = (dateStr: string): string => {
    if (!dateStr) return "";
    const [day, monthStr, year] = dateStr.split(" ");
    const monthIndex = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ].indexOf(monthStr);
    if (monthIndex === -1 || !day || !year) return "";
    const parsedDate = new Date(parseInt(year), monthIndex, parseInt(day));
    const yearFormatted = parsedDate.getFullYear();
    const monthFormatted = String(parsedDate.getMonth() + 1).padStart(2, "0");
    const dayFormatted = String(parsedDate.getDate()).padStart(2, "0");
    return `${yearFormatted}-${monthFormatted}-${dayFormatted}`;
  };

  const formatDateToDisplay = (dateStr: string): string => {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
  };


  // Handle save changes for editing
  const handleSaveChanges = async (values: {
    col1: string;
    col2: string;
    col3: string;
    col4: "Active" | "Inactive";
  }) => {
    if (!editingHoliday) return;

    setLoading(true); // Start loader
    try {
      const { id } = editingHoliday;

      // Convert status correctly
      const statusBoolean = values.col4 === "Active";

      const [day, monthStr, year] = values.col2.split(" ");
      const monthIndex = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
      ].indexOf(monthStr);
      const formattedDateForApi = new Date(
        parseInt(year),
        monthIndex,
        parseInt(day)
      );
      const yearFormatted = formattedDateForApi.getFullYear();
      const monthFormatted = String(
        formattedDateForApi.getMonth() + 1
      ).padStart(2, "0");
      const dayFormatted = String(formattedDateForApi.getDate()).padStart(
        2,
        "0"
      );
      const apiDate = `${yearFormatted}-${monthFormatted}-${dayFormatted}`;

      // Call API to update holiday
      const updatedHoliday = await editHoliday(
        id.toString(),
        values.col1,
        apiDate,
        values.col3,
        statusBoolean
      );

      if (!updatedHoliday) {
        console.error("Invalid API response:", updatedHoliday);
        // toast.error("Failed to update holiday.");
        return;
      }

      // Format the updated date for display
      const formattedDate = updatedHoliday.date
        ? new Date(updatedHoliday.date).toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "short",
            year: "numeric",
          })
        : values.col2;

      // Update UI immediately
      setRows((prevRows) =>
        prevRows.map((row) =>
          row.id === id
            ? {
                ...row,
                col1: updatedHoliday.title || values.col1,
                col2: formattedDate,
                col3: updatedHoliday.description || values.col3,
                col4: updatedHoliday.isActive ? "Active" : "Inactive",
              }
            : row
        )
      );

      toast.success("Holiday updated successfully!");
      setEditModal(false);
    } catch (error) {
      console.error("Failed to update holiday:", error);
      // toast.error("Failed to update holiday. Please try again.");
    } finally {
      setLoading(false); // Stop loader
    }
  };



  // Handle delete confirmation
  const handleDelete = async () => {
    if (deletingHoliday) {
      setLoading(true);
      try {
        const response = await deleteHoliday(deletingHoliday.id.toString());
        console.log("Holiday deleted:", response);

        if (response.success) {
          toast.success("Holiday deleted successfully!");
        } else {
          // toast.error("Failed to delete holiday.");
        }

        const updatedRows = rows.filter((row) => row.id !== deletingHoliday.id);
        setRows(updatedRows);
        setDeleteModal(false);
      } catch (error) {
        console.error("Failed to delete holiday:", error);
        // toast.error("Failed to delete holiday. Please try again.");
      } finally {
        setLoading(false); // Stop loader
      }
    }
  };

  const isMobile = useMediaQuery("(max-width:768px)");



  return (
    <Box className="holiday-container">
      {loading && <Loader loading={loading} />}

      <Box className="content">
        <Box className="holiday-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Holiday</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
            <ViewToggleHolidays />
            {!isEmployee && (
              <Button
                className="add-holiday"
                variant="contained"
                sx={{ textTransform: "none" }}
                onClick={() => setOpenModal(true)}
              >
                <ControlPoint />
                Add Holiday
              </Button>
            )}
          </Box>
        </Box>
        
        {/* Add the calendar component */}
        <Box sx={{ mt: 2 }}>
          <BigHolidayCalendar />
        </Box>

      </Box>

      {/* Add Holiday Dialog with Formik */}
      <Dialog
        open={openModal}
        onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          setOpenModal(false);
        }
      }}
      disableEscapeKeyDown
        PaperProps={{
          sx: {
            width: "498px",
            height: "521px",
            maxWidth: "none",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Add New Holiday
          </Typography>
          <Cancel
            onClick={() => setOpenModal(false)}
            sx={{ ":hover": { color: "#F26522" } }}
          />
        </DialogTitle>
        <DialogContent>
          <Formik
            initialValues={{
              title: "",
              date: "",
              description: "",
              status: "Active",
            }}
            validate={(values) => {
              const errors: Partial<typeof values> = {};
              const today = new Date();
              today.setHours(0, 0, 0, 0); // Reset time part for accurate date comparison
              const selectedDate = new Date(values.date);

              if (!values.title.trim()) {
                errors.title = "Required";
              }

              if (!values.date) {
                errors.date = "Required";
              } else if (selectedDate <= today) {
                errors.date = "Holiday date must be in the future";
              }

              if (!values.description.trim()) {
                errors.description = "Required";
              }

              return errors;
            }}
            onSubmit={async (values, { setSubmitting }) => {
              setLoading(true); // Start loader
              try {
                const formattedDate = values.date; // Already in YYYY-MM-DD format
                const payload = {
                  title: values.title,
                  date: formattedDate,
                  description: values.description,
                  isActive: values.status === "Active",
                };

                const response = await postHoliday(payload);
                console.log("Holiday Added:", response);
                if (response?.holiday) {
                  const formattedDate = new Date(
                    response.holiday.date
                  ).toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "short",
                    year: "numeric",
                  });

                  setRows([
                    ...rows,
                    {
                      id: rows.length + 1,
                      col1: response.holiday.title,
                      col2: formattedDate,
                      col3: response.holiday.description,
                      col4: response.holiday.isActive ? "Active" : "Inactive",
                    },
                  ]);
                  toast.success("Holiday added successfully!");
                  setOpenModal(false);
                } else {
                  // toast.error("Failed to add holiday.");
                }
              } catch (error) {
                console.error("API request failed:", error);
                // toast.error("Failed to add holiday. Please try again.");
              } finally {
                setSubmitting(false);
                setLoading(false);
              }
            }}
          >
            {({ errors, touched, isSubmitting }) => (
              <Form>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body1" sx={{ fontWeight: 500, mb: 0.5 }}>
                    Holiday Name
                  </Typography>
                  <Field
                    as={TextField}
                    fullWidth
                    name="title"
                    variant="outlined"
                    size="small"
                    error={touched.title && !!errors.title}
                    helperText={touched.title && errors.title}
                  />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body1" sx={{ fontWeight: 500, mb: 0.5 }}>
                    Holiday Date
                  </Typography>
                  <Field
                    as={TextField}
                    fullWidth
                    type="date"
                    name="date"
                    variant="outlined"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    inputProps={{
                      min: new Date().toISOString().split("T")[0],
                    }}
                    error={touched.date && !!errors.date}
                    helperText={touched.date && errors.date}
                  />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body1" sx={{ fontWeight: 500, mb: 0.5 }}>
                    Description
                  </Typography>
                  <Field
                    as={TextField}
                    fullWidth
                    name="description"
                    variant="outlined"
                    size="small"
                    multiline
                    rows={3}
                    error={touched.description && !!errors.description}
                    helperText={touched.description && errors.description}
                  />
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body1" sx={{ fontWeight: 500, mb: 0.5 }}>
                    Status
                  </Typography>
                  <Field
                    as={Select}
                    fullWidth
                    name="status"
                    variant="outlined"
                    size="small"
                  >
                    <MenuItem value="Active">Active</MenuItem>
                    <MenuItem value="Inactive">Inactive</MenuItem>
                  </Field>
                </Box>
                <DialogActions>
                  <Button
                    sx={{
                      fontWeight: "400",
                      fontSize: "14px",
                      color: "#111827",
                      border: "1px solid #E5E7EB",
                      borderRadius: "5px",
                      textTransform: "none",
                      padding: "8px 13.6px",
                    }}
                    onClick={() => setOpenModal(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    sx={{
                      display: "flex",
                      gap: "8px",
                      backgroundColor: "#F26522",
                      borderColor: "#F26522",
                      color: "#FFF",
                      fontWeight: 400,
                      fontSize: "14px",
                      borderRadius: "5px",
                      textTransform: "none",
                      padding: "8px 13.6px",
                    }}
                    variant="contained"
                  >
                    Add Holiday
                  </Button>
                </DialogActions>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>

      {/* Edit Holiday Dialog with Formik */}
      <Dialog
        open={editModal}
       onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          setEditModal(false);
        }
      }}
      disableEscapeKeyDown
        PaperProps={{
          sx: {
            width: "498px",
            height: "521px",
            maxWidth: "none",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Edit Holiday
          </Typography>
          <Cancel
            onClick={() => setEditModal(false)}
            sx={{ ":hover": { color: "#F26522" } }}
          />
        </DialogTitle>
        <DialogContent>
          {editingHoliday && (
            <Formik
              initialValues={{
                col1: editingHoliday.col1,
                col2: editingHoliday.col2,
                col3: editingHoliday.col3,
                col4: editingHoliday.col4,
              }}
              validate={(values) => {
                const errors: Partial<typeof values> = {};
                const today = new Date();
                today.setHours(0, 0, 0, 0); // Reset time part for accurate date comparison

                // Parse the date from the GB format (dd MMM yyyy)
                const [day, month, year] = values.col2.split(" ");
                const monthIndex = [
                  "Jan",
                  "Feb",
                  "Mar",
                  "Apr",
                  "May",
                  "Jun",
                  "Jul",
                  "Aug",
                  "Sep",
                  "Oct",
                  "Nov",
                  "Dec",
                ].indexOf(month);

                const selectedDate = new Date(
                  parseInt(year),
                  monthIndex,
                  parseInt(day)
                );

                if (!values.col1.trim()) {
                  errors.col1 = "Required";
                }

                if (!values.col2) {
                  errors.col2 = "Required";
                } else if (selectedDate <= today) {
                  errors.col2 = "Holiday date must be in the future";
                }

                if (!values.col3.trim()) {
                  errors.col3 = "Required";
                }

                return errors;
              }}
              onSubmit={(values, { setSubmitting }) => {
                handleSaveChanges(values);
                setSubmitting(false);
              }}
            >
              {({ errors, touched, isSubmitting, setFieldValue }) => (
                <Form>
                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body1"
                      sx={{ fontWeight: 500, mb: 0.5 }}
                    >
                      Holiday Name
                    </Typography>
                    <Field
                      as={TextField}
                      fullWidth
                      name="col1"
                      variant="outlined"
                      size="small"
                      error={touched.col1 && !!errors.col1}
                      helperText={touched.col1 && errors.col1}
                    />
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body1"
                      sx={{ fontWeight: 500, mb: 0.5 }}
                    >
                      Holiday Date
                    </Typography>
                    <Field
                      as={TextField}
                      fullWidth
                      type="date"
                      name="col2"
                      variant="outlined"
                      size="small"
                      value={parseDateToInputFormat(editingHoliday.col2)}
                      InputLabelProps={{ shrink: true }}
                      inputProps={{
                        min: new Date().toISOString().split("T")[0],
                      }}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        const formattedDate = formatDateToDisplay(
                          e.target.value
                        );
                        setFieldValue("col2", formattedDate);
                      }}
                      error={touched.col2 && !!errors.col2}
                      helperText={touched.col2 && errors.col2}
                    />
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body1"
                      sx={{ fontWeight: 500, mb: 0.5 }}
                    >
                      Description
                    </Typography>
                    <Field
                      as={TextField}
                      fullWidth
                      name="col3"
                      variant="outlined"
                      size="small"
                      multiline
                      rows={3}
                      error={touched.col3 && !!errors.col3}
                      helperText={touched.col3 && errors.col3}
                    />
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="body1"
                      sx={{ fontWeight: 500, mb: 0.5 }}
                    >
                      Status
                    </Typography>
                    <Field
                      as={Select}
                      fullWidth
                      name="col4"
                      variant="outlined"
                      size="small"
                    >
                      <MenuItem value="Active">Active</MenuItem>
                      <MenuItem value="Inactive">Inactive</MenuItem>
                    </Field>
                  </Box>
                  <DialogActions>
                    <Button
                      sx={{
                        fontWeight: "400",
                        fontSize: "14px",
                        color: "#111827",
                        border: "1px solid #E5E7EB",
                        borderRadius: "5px",
                        textTransform: "none",
                        padding: "8px 13.6px",
                      }}
                      onClick={() => setEditModal(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      sx={{
                        display: "flex",
                        gap: "8px",
                        backgroundColor: "#F26522",
                        borderColor: "#F26522",
                        color: "#FFF",
                        fontWeight: 400,
                        fontSize: "14px",
                        borderRadius: "5px",
                        textTransform: "none",
                        padding: "8px 13.6px",
                      }}
                      variant="contained"
                    >
                      Save Changes
                    </Button>
                  </DialogActions>
                </Form>
              )}
            </Formik>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Holiday Dialog */}
      <Dialog open={deleteModal} onClose={() => setDeleteModal(false)}>
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Confirm Delete
          </Typography>
          <Cancel
            onClick={() => setDeleteModal(false)}
            sx={{ ":hover": { color: "#F26522" } }}
          />
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to delete the holiday {deletingHoliday?.col1}?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            sx={{
              fontWeight: "400",
              fontSize: "14px",
              color: "#111827",
              border: "1px solid #E5E7EB",
              borderRadius: "5px",
              textTransform: "none",
              padding: "8px 13.6px",
            }}
            onClick={() => setDeleteModal(false)}
          >
            Cancel
          </Button>
          <Button
            sx={{
              display: "flex",
              gap: "8px",
              backgroundColor: "#F26522",
              borderColor: "#F26522",
              color: "#FFF",
              fontWeight: 400,
              fontSize: "14px",
              borderRadius: "5px",
              textTransform: "none",
              padding: "8px 13.6px",
            }}
            onClick={handleDelete}
            variant="contained"
          >
            Yes, Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default HolidayPage;

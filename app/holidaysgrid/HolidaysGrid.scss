.holiday-container {
    .content {
        padding: 24px;

        .holiday-header {
            display: flex;
            justify-content: space-between;

            .breadcrumbs-box {
                h2 {
                    font-size: 24px;
                    font-weight: 700;
                    color: #202C4B;
                }
            }

            .add-holiday {

                display: flex;
                gap: 8px;
                background-color: #F26522;
                border-color: #F26522;
                color: #FFF;
                font-weight: 400;
                font-size: 14px;
                border-radius: 5px;
                text-transform: none;
                padding: 8px 13.6px;

            }
        }

    }
}

@media (max-width: 768px) {
    .holiday-container {
        .content {
            padding: 16px;

            .holiday-header {
                flex-direction: column;
                align-items: flex-start;

                .breadcrumbs-box {
                    margin-bottom: 10px;
                }

                .add-holiday {
                    margin-top: 10px;
                }
            }

          
        }
    }
}
"use client";
import {
  Box,
  Divider,
  Typography,
  Paper,
  useMediaQuery,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import { GridRowsProp, GridColDef, GridToolbarQuickFilter, GridToolbarExport } from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";
import { getAssetById } from "@/app/services/assets/asset.service";
import "./AssetDetail.scss";
import { HomeOutlined } from "@mui/icons-material";

function QuickSearchToolbar() {
  return (
    <Box
      sx={{
        p: 0.5,
        pb: 0,
        justifyContent: "space-between",
        padding: "10px 20px",
        display: "flex",
      }}
    >
      <GridToolbarQuickFilter
        placeholder="Search"
        className="grid-search"
        sx={{ textDecoration: "none" }}
      />
      <Box
        className="grid-export"
        sx={{ display: "flex", alignItems: "center" }}
      >
        <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
      </Box>
    </Box>
  );
}

export default function AssetDetails() {
  const [isLoading, setIsLoading] = useState(false);
  const [assets, setAssets] = useState<any[]>([]);
  const [page, setPage] = useState(0); // State for current page
  const [pageSize, setPageSize] = useState(10); // Default page size to 10

  const router = useRouter();
  const searchParams = useSearchParams();
  const assetId = searchParams.get("id");
  const isMobile = useMediaQuery("(max-width:768px)");

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    {
      label: "Asset Management",
      href: "/assets",
    },
    { label: "Asset Details" },
  ];

  useEffect(() => {
    if (!assetId) {
      toast.error("No asset ID provided");
      router.push("/assets");
      return;
    }

    const fetchAsset = async () => {
      setIsLoading(true);
      try {
        const response = await getAssetById(assetId);
        if (response.success && response.assets?.serialNumberDetails) {
          setAssets(response.assets.serialNumberDetails);
        } else {
          setAssets([]);
          toast.error("No asset data found");
          router.push("/assets");
        }
      } catch (error) {
        console.error("Failed to fetch assets:", error);
        setAssets([]);
        toast.error("Failed to fetch asset details");
        router.push("/assets");
      } finally {
        setIsLoading(false);
      }
    };
    fetchAsset();
  }, [assetId, router]);

  const rows: GridRowsProp = assets.map((asset) => ({
    id: asset.serialNumber, // Using serialNumber as unique ID
    brand: asset.brand,
    type: asset.type,
    quantity: asset.quantity,
    category: asset.category,
    serialNumber: asset.serialNumber,
    cost: asset.cost,
    vendor: asset.vendor,
    warrentyTo: new Date(asset.warrentyTo).toLocaleDateString(),
    location: asset.location,
    isActive: asset.isActive,
    isAssigned: asset.isAssigned,
    assignedTo: asset.isAssigned
      ? `${asset.assignedTo?.firstName} ${asset.assignedTo?.lastName}`
      : "-",
    assetImages: asset.assetImage,
  }));

  const columns: GridColDef[] = [
    { field: "brand", headerName: "Brand", flex: 1 },
    { field: "type", headerName: "Type", flex: 1 },
    { field: "category", headerName: "Category", flex: 1 },
    { field: "serialNumber", headerName: "Serial Number", flex: 1 },
    { field: "vendor", headerName: "Vendor", flex: 1 },
    {
      field: "warrentyTo",
      headerName: "Warranty Until",
      flex: 1,
      renderCell: (params) => <Typography>{params.value}</Typography>,
    },
    { field: "location", headerName: "Location", flex: 1 },
    {
      field: "isAssigned",
      headerName: "Assignment Status",
      flex: 1,
      renderCell: (params) => (
        <Typography
          sx={{
            display: "flex",
            alignItems: "center",
            gap: "3px",
            backgroundColor: params.value ? "#0288D1" : "#F57C00",
            color: "#fff",
            borderRadius: "5px",
            textAlign: "center",
            minWidth: "66px",
            justifyContent: "center",
            fontSize: "10px",
            fontWeight: 500,
            padding: "0px 5px",
            lineHeight: "18px",
          }}
        >
          <Box
            sx={{
              width: "5px",
              height: "5px",
              borderRadius: "100%",
              backgroundColor: "#fff",
            }}
          />
          {params.value ? "Assigned" : "Unassigned"}
        </Typography>
      ),
    },
    {
      field: "assignedTo",
      headerName: "Assigned To",
      flex: 1,
      renderCell: (params) => <Typography>{params.value}</Typography>,
    },
  ];

  return (
    <Box className="assets-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="assets-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Asset Details</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight:
                rows.length <= 2 ? "calc(70vh - 200px)" : "calc(100vh - 220px)",
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Asset Information</Typography>
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={rows.length}
              paginationMode="client"
              pageSizeOptions={[10, 25, 50, 100]}
              paginationModel={{ page, pageSize }} // Pass pagination model
              onPaginationModelChange={(newModel) => {
                setPage(newModel.page);
                setPageSize(newModel.pageSize);
              }}
              slots={{
                pagination: CustomPagination,
                toolbar: QuickSearchToolbar,
              }}
            />
          </Box>
        </Paper>
      </Box>
    </Box>
  );
}
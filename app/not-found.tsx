'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Box, Typography, Button } from '@mui/material';
import Loader from '@/components/Loader/Loader';

export default function NotFound() {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Check if we're on a known route pattern that should be handled
    const isDashboardRoute = pathname.includes('/Dashboard/');
    const isEmployeesRoute = pathname.includes('/employees/');

    if (isDashboardRoute) {
      // Redirect to the appropriate dashboard
      const token = localStorage.getItem("token");
      if (token) {
        router.push('/');
      } else {
        router.push('/login');
      }
    } else if (isEmployeesRoute) {
      // Redirect to employees main page
      router.push('/employees');
    }
  }, [pathname, router]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        textAlign: 'center',
        padding: '0 20px',
      }}
    >
      <Typography variant="h1" sx={{ fontSize: '6rem', marginBottom: '1rem' }}>
        404
      </Typography>
      <Typography variant="h4" sx={{ marginBottom: '2rem' }}>
        Page Not Found
      </Typography>
      <Typography variant="body1" sx={{ marginBottom: '2rem', maxWidth: '600px' }}>
        The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
      </Typography>
      <Button
        variant="contained"
        color="primary"
        onClick={() => router.push('/')}
        sx={{ marginBottom: '1rem' }}
      >
        Go to Home
      </Button>
      <Loader loading={false} />
    </Box>
  );
}
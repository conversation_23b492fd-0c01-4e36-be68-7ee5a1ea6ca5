"use client";
import {
  <PERSON>,
  IconButton,
  Button,
  Divider,
  Typography,
  Paper,
  Avatar,
  Select,
  MenuItem,
  SelectChangeEvent,
} from "@mui/material";
import React, {
  useState,
  useEffect,
  Suspense,
  useMemo,
  useCallback,
} from "react";
import "./resignation.scss";
import {
  HomeOutlined,
  ControlPoint,
  KeyboardArrowDown,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
  GridRenderCellParams,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { EditNote, Delete } from "@mui/icons-material";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import useFetchResignationData from "@/app/hooks/resignation/useFetchResignationSectionData";
import ResignationDialog from "@/components/AddEditResignation/AddEditResignation";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import {
  addResignation,
  updateResignation,
  getResignationById,
  deleteResignation,
  approveResignationById,
} from "@/app/services/resignation.service";
// import { getUserById } from "../services/users.service";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import useRoleAuth from "../hooks/useRoleAuth";
import { ROLE_GROUPS, ROLES } from "../constants/roles";
import { getDepartments } from "../services/department.service";
import { ResignationStatus } from "../types/resignation";
import StatusChangeConfirmationDialog from "@/components/StatusChangeConfirmation/StatusChangeConfirmation";
import useAuthStore from "@/store/authStore";
import ReadMore from "@/components/ReadMore/ReadMore";
import { useDebounce } from "../hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

// Interface for Resignation data
interface ResignationRecord {
  _id?: string;
  empId?: string;
  reason?: string;
  resignationDate?: string;
  noticeDate?: string;
  resignationStatus?: string; // Add this field
}

// Interface for Edit data
interface EditResignationData {
  empId: string;
  employeeName: string;
  reason: string;
  noticeDate: string;
  resignationDate: string;
}

// Interface for User data from getUserById
interface User {
  _id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

// Interface for getUserById response
interface UserResponse {
  user: User;
}

const formatDateForApi = (dateStr: string) => {
  if (!dateStr) return undefined;

  try {
    // Assuming dateStr is in format "MM/DD/YYYY"
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return undefined;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return undefined;
  }
};

function ResignationContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [hasActiveResignation, setHasActiveResignation] = useState(false);
  const { employeeId } = useAuthStore();
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);
  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedResignationId, setSelectedResignationId] = useState<
    string | null
  >(null);
  const [editData, setEditData] = useState<EditResignationData | null>(null);
  const [employees, setEmployees] = useState<User[]>([]);
  const [employeesLoading, setEmployeesLoading] = useState(false); // Initialize to false
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const [departments, setDepartments] = useState<string[]>([]);
  const [pendingStatusChange, setPendingStatusChange] = useState<{
    resignationId: string;
    newStatus: ResignationStatus;
  } | null>(null);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const { roles } = useAuthStore();

  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Check if user is an admin or superadmin
  const isAdminOrSuperAdmin = roles.some(
    (role) =>
      role === ROLES.Admin ||
      role === ROLES.SuperAdmin ||
      role === ROLES.HR ||
      role === ROLES.Manager
  );

  // Check if user is only an employee (no administrative roles)
  const isEmployeeOnly =
    roles.includes(ROLES.Employee) &&
    !roles.some(
      (role) =>
        role === ROLES.Admin ||
        role === ROLES.SuperAdmin ||
        role === ROLES.HR ||
        role === ROLES.Manager
    );

  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  const handleStatusChange = (
    event: SelectChangeEvent,
    resignationId: string
  ) => {
    const newStatus = event.target.value as ResignationStatus;
    setPendingStatusChange({ resignationId, newStatus });
    setStatusDialogOpen(true);
  };

  const handleStatusConfirm = async () => {
    if (pendingStatusChange) {
      const { resignationId, newStatus } = pendingStatusChange;
      setIsLoading(true);
      try {
        console.log(
          `Approving resignation: ID=${resignationId}, status=${newStatus}`
        );
        await approveResignationById(resignationId, newStatus);
        //toast.success(`Resignation status updated to ${newStatus}`);
        setRefresh(!refresh);
      } catch (error) {
        console.error("Failed to update resignation status:", error);
        // toast.error("Failed to update resignation status");
      } finally {
        setIsLoading(false);
        setStatusDialogOpen(false);
        setPendingStatusChange(null);
      }
    }
  };

  const handleStatusCancel = () => {
    setStatusDialogOpen(false);
    setPendingStatusChange(null);
  };

  const [resignationData, total] = useFetchResignationData({
    setIsLoading,
    refresh,
    limit: 1000, // Set a high number to fetch all records
    page,
    departmentName:
      selectedDepartment === "Department" ? undefined : selectedDepartment,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate: startDate ? formatDateForApi(startDate) : undefined,
    endDate: endDate ? formatDateForApi(endDate) : undefined,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  // Restrict access to Employee and Admin roles  (Admin, SuperAdmin, Manager, HR, Employee)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  // Fetch departments using getDepartments service
  useEffect(() => {
    const fetchDepartments = async () => {
      setIsLoading(true);
      try {
        const response = await getDepartments();
        if (response.success && response.departments?.results) {
          const departmentNames = response.departments.results.map(
            (dept: { departmentName: string }) => dept.departmentName
          );
          setDepartments(departmentNames);
        }
      } catch (error) {
        console.error("Failed to fetch departments:", error);
        // toast.error("Failed to load departments");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDepartments();
  }, []);

  // Fetch employee data based on resignation empIds
  // This useEffect is no longer needed since the API returns populated data
  // useEffect(() => {
  //   const fetchEmployees = async () => {
  //     if (!resignationData) return;
  //     setEmployeesLoading(true);
  //     try {
  //       const empIds = resignationData.map(
  //         (res: ResignationRecord) => res.empId
  //       );
  //       const employeePromises = empIds
  //         .filter((empId): empId is string => empId !== undefined)
  //         .map((empId: string) => getUserById(empId));
  //       const employeeResponses = await Promise.all(employeePromises);
  //       setEmployees(employeeResponses.map((res: UserResponse) => res.user));
  //     } catch (error) {
  //       console.error("Error fetching employees:", error);
  //       toast.error("Failed to fetch employee data");
  //     } finally {
  //       setEmployeesLoading(false);
  //     }
  //   };

  //   fetchEmployees();
  // }, [resignationData]);

  // Add new useEffect to check for active resignations
  useEffect(() => {
    if (resignationData && employeeId) {
      const activeResignation = resignationData.find(
        (resignation: ResignationRecord) =>
          (resignation.empId as any)?._id === employeeId &&
          (resignation.resignationStatus === "Pending" ||
            resignation.resignationStatus === "Approved")
      );
      setHasActiveResignation(!!activeResignation);
    }
  }, [resignationData, employeeId]);

  // Filter resignations based on department
  const filteredResignationData = useMemo(() => {
    if (!resignationData) return [];

    return resignationData.filter((resignation: ResignationRecord) => {
      // If no department is selected or department is "Department", show all
      if (!selectedDepartment || selectedDepartment === "Department") {
        return true;
      }

      // Get department name from the nested structure in the API response
      let departmentName = "";

      if (resignation.empId && typeof resignation.empId === "object") {
        if (
          (resignation.empId as any).departmentId &&
          typeof (resignation.empId as any).departmentId === "object"
        ) {
          departmentName =
            (resignation.empId as any).departmentId.departmentName || "";
        }
      }

      return departmentName === selectedDepartment;
    });
  }, [resignationData, selectedDepartment]);

  // Map resignation data to rows with employee details
  const rows: GridRowsProp = filteredResignationData
    ? filteredResignationData.map(
        (resignation: ResignationRecord, index: number) => {
          // Extract department name from the nested structure in the API response
          let departmentName = "-";

          if (resignation.empId && typeof resignation.empId === "object") {
            if (
              (resignation.empId as any).departmentId &&
              typeof (resignation.empId as any).departmentId === "object"
            ) {
              departmentName =
                (resignation.empId as any).departmentId.departmentName || "-";
            }
          }

          return {
            id: resignation._id || index + 1,
            empId: (resignation.empId as any)?._id || "-",
            employeeName: resignation.empId
              ? typeof resignation.empId === "object" &&
                resignation.empId !== null
                ? ((resignation.empId as any)?.firstName || "") +
                  " " +
                  ((resignation.empId as any)?.lastName || "")
                : "-"
              : "-",
            avatar: (resignation.empId as any)?.avatar || "",
            department: departmentName,
            reason: resignation.reason || "-",
            resignationDate: resignation.resignationDate
              ? new Date(resignation.resignationDate).toLocaleDateString(
                  "en-GB", // Changed from en-US to en-GB for day-month-year format
                  {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                  }
                )
              : "-",
            noticeDate: resignation.noticeDate
              ? new Date(resignation.noticeDate).toLocaleDateString(
                  "en-GB", // Changed from en-US to en-GB for day-month-year format
                  {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                  }
                )
              : "-",
            status: resignation.resignationStatus || "Pending", // Use resignationStatus instead of status
          };
        }
      )
    : [];

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "HR", href: "" },
    { label: "Resignations" },
  ];

  const columns: GridColDef[] = [
    {
      field: "employeeName",
      headerName: "Employee Name",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={params.row.avatar || ""}
            alt={params.value}
            sx={{ width: 32, height: 32 }}
          />
          <Typography variant="body2">{params.value}</Typography>
        </Box>
      ),
    },
    { field: "department", headerName: "Department", editable: false, flex: 1 },
    {
      field: "reason",
      headerName: "Reason",
      editable: false,
      flex: 2,
      minWidth: 250,
      renderCell: (params) => (
        <ReadMore text={params.value as string} maxChars={100} />
      ),
    },
    {
      field: "noticeDate",
      headerName: "Notice Date",
      editable: false,
      flex: 1,
    },
    {
      field: "resignationDate",
      headerName: "Resignation Date",
      editable: false,
      flex: 1,
    },
    {
      field: "status",
      headerName: "Status",
      flex: 1,
      renderCell: (params) => (
        <Select
          value={params.row.status || "Pending"}
          onChange={(event: SelectChangeEvent) =>
            handleStatusChange(event, params.row.id)
          }
          size="small"
          disabled={isLoading || isEmployeeOnly} // Disable for employee-only users
          IconComponent={KeyboardArrowDown}
          sx={{
            height: "32px",
            width: "120px",
            backgroundColor:
              params.row.status === "Approved"
                ? "#E6F4EA"
                : params.row.status === "Declined"
                  ? "#FEEAE6"
                  : "#FFF8E6",
            color:
              params.row.status === "Approved"
                ? "#34A853"
                : params.row.status === "Declined"
                  ? "#EA4335"
                  : "#FBBC04",
            ".MuiOutlinedInput-notchedOutline": { border: 0 },
            "&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: 0 },
            "&:hover .MuiOutlinedInput-notchedOutline": { border: 0 },
          }}
        >
          <MenuItem value="Pending">
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                fontSize: "14px",
              }}
            >
              Pending
            </Box>
          </MenuItem>
          <MenuItem value="Approved">
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                fontSize: "14px",
              }}
            >
              Approved
            </Box>
          </MenuItem>
          <MenuItem value="Declined">
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                fontSize: "14px",
              }}
            >
              Declined
            </Box>
          </MenuItem>
        </Select>
      ),
    },
    // ...(isEmployeeOnly ? [
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 0.5,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          {/* <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ width: "20px", height: "20px" }} />
          </IconButton> */}
        </Box>
      ),
    },
    // ] : []),
  ];

  const handleEditClick = async (resignationId: string | number) => {
    try {
      const response = await getResignationById(resignationId as string);
      const resignation = response.resignation;
      console.log("Raw resignation response:", resignation);

      // Format dates for the form if they exist
      const formatDate = (dateString: string) => {
        if (!dateString) return "";
        return new Date(dateString).toISOString().split("T")[0];
      };

      // Make sure we're getting all the necessary fields from the API response
      const editData: EditResignationData = {
        empId: resignation.empId || "",
        employeeName:
          typeof resignation.empId === "object"
            ? `${(resignation.empId as any)?.firstName || ""} ${(resignation.empId as any)?.lastName || ""}`
            : "-",
        reason: resignation.reason || "",
        // Format dates properly
        noticeDate: formatDate(resignation.noticeDate),
        resignationDate: formatDate(resignation.resignationDate),
      };

      console.log("Formatted editData for form:", editData);
      setEditData(editData);
      setSelectedResignationId(resignationId as string);
      setIsEditMode(true);
      setOpenModal(true);
    } catch (error) {
      console.error("Error fetching resignation for edit:", error);
      // toast.error("Failed to load resignation data for editing.");
    }
  };

  const handleDeleteClick = (resignationId: string | number) => {
    setResignationToDelete(resignationId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (resignationToDelete !== null) {
      try {
        await deleteResignation(resignationToDelete as string);
        setDeleteDialogOpen(false);
        setResignationToDelete(null);
        setRefresh(!refresh);
        // toast.success("Resignation deleted successfully!");
      } catch (error) {
        console.error("Failed to delete resignation:", error);
        // toast.error("Failed to delete resignation. Please try again.");
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setResignationToDelete(null);
  };

  const handleSaveResignation = async (formData: {
    empId: string;
    reason: string;
    noticeDate: string;
    resignationDate: string;
  }) => {
    try {
      setIsLoading(true);
      console.log("Saving resignation with data:", formData);

      if (isEditMode && selectedResignationId) {
        // Update existing resignation
        await updateResignation(selectedResignationId, formData);
        // toast.success("Resignation updated successfully");
      } else {
        // Add new resignation
        const response = await addResignation(formData);
        console.log("Resignation added response:", response);
      }

      // Refresh the data
      setRefresh(!refresh);
      setOpenModal(false);
      setEditData(null);
      setSelectedResignationId(null);
      setIsEditMode(false);
    } catch (error: any) {
      console.error("Error saving resignation:", error);
      const errorMessage =
        error.response?.data?.message || "Failed to save resignation";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDialogClose = () => {
    setOpenModal(false);
    setIsEditMode(false);
    setSelectedResignationId(null);
    setEditData(null);
  };

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [resignationToDelete, setResignationToDelete] = useState<
    string | number | null
  >(null);

  return (
    <Box className="employee-container">
      {(isLoading || employeesLoading) && (
        <Loader loading={isLoading || employeesLoading} />
      )}

      <Box className="content">
        <Box className="employee-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Resignations</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          {isEmployeeOnly && !hasActiveResignation && (
            <Box
              className="add-policy"
              sx={{ display: "flex", gap: 1, alignItems: "center" }}
            >
              <Button
                variant="contained"
                sx={{
                  display: "flex",
                  gap: "8px",
                  backgroundColor: "#F26522",
                  borderColor: "#F26522",
                  color: "#FFF",
                  fontWeight: 500,
                  fontSize: "14px",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                onClick={() => {
                  setIsEditMode(false);
                  setOpenModal(true);
                }}
              >
                <ControlPoint sx={{ fontSize: "14px" }} />
                Add Resignation
              </Button>
            </Box>
          )}
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 550,
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Resignations List</Typography>
              <PolicyFilters
                departments={departments}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={false}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              getRowHeight={({ model }) => {
                const reasonContent = model.reason as string;
                // Calculate height based on content length
                if (reasonContent && reasonContent.length > 150) {
                  return "auto";
                }
                return 47; // Default height
              }}
              rows={rows}
              columns={columns}
              rowCount={rows.length} // Update to use filtered length
              paginationMode="client" // Change to client-side pagination
              initialState={{
                pagination: {
                  paginationModel: { pageSize, page: page - 1 },
                },
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      <StatusChangeConfirmationDialog
        open={statusDialogOpen}
        onClose={handleStatusCancel}
        onConfirm={handleStatusConfirm}
        title={`Confirm ${pendingStatusChange?.newStatus || ResignationStatus.Pending} Resignation`}
        message={`Are you sure you want to change the status to ${pendingStatusChange?.newStatus || ResignationStatus.Pending}? This action cannot be undone.`}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this resignation? This action cannot be undone."
      />

      <ResignationDialog
        open={openModal}
        onClose={handleDialogClose}
        onSave={handleSaveResignation}
        editData={isEditMode && editData ? editData : undefined}
      />
    </Box>
  );
}

function ResignationPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ResignationContent />
    </Suspense>
  );
}

export default ResignationPage;

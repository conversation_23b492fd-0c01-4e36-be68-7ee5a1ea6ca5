import { useEffect, useState } from "react";
import { getDepartment } from "@/app/services/department.service";

interface DepartmentRecord {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt?: string;
  updatedAt?: string;
  numberOfEmployees?: string;
}

const useFetchDepartmentData = ({
  setIsLoading,
  refresh,
  limit = 10000,
  page = 1,
  departmentName = "Department",
  order = "Sort By",
  startDate,
  endDate,
  isActive,
  search
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  departmentName?: string;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [departmentData, setDepartmentData] = useState<
    DepartmentRecord[] | null
  >(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getDepartmentSectionData = async () => {
      setIsLoading(true);
      try {
        const result = await getDepartment(
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          isActive,
          search
        );
        if (
          result &&
          result.departments &&
          Array.isArray(result.departments.results)
        ) {
          setDepartmentData(result.departments.results);
          setTotal(Number(result.departments.total) || 0);
        } else {
          setDepartmentData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch Department section data:", err);
        setDepartmentData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    getDepartmentSectionData();
  }, [
    refresh,
    limit,
    page,
    departmentName,
    order,
    startDate,
    endDate,
    isActive,
    search,
    setIsLoading,
  ]);

  return [departmentData, total] as const;
};

export default useFetchDepartmentData;

import { useEffect, useState } from "react";
import { getTrainingType } from "@/app/services/trainers/trainer.service";

// Define the correct interface for training types
interface TrainingTypeData {
  _id: string;
  type: string;
  description: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

const useFetchTrainingTypeData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  order = "",
  startDate,
  endDate,
  isActive,
  search,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [trainingTypeData, setTrainingTypeData] = useState<TrainingTypeData[] | null>(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getTrainingTypeSectionData = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching training types with params:", {
          limit,
          page,
          order,
          startDate,
          endDate,
          isActive,
        });
        const result = await getTrainingType(
          limit,
          page,
          "",
          order,
          startDate,
          endDate,
          isActive,
          search
        );
        console.log("API Response:", result);
        if (
          result &&
          result.trainingTypes &&
          Array.isArray(result.trainingTypes.results)
        ) {
          // Use the data directly from the API without additional sorting
          setTrainingTypeData(result.trainingTypes.results);
          setTotal(Number(result.trainingTypes.total) || 0);
        } else {
          setTrainingTypeData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch training types data:", err);
        setTrainingTypeData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    getTrainingTypeSectionData();
  }, [limit, page, order, startDate, endDate, isActive,search, refresh]);

  return [trainingTypeData, total] as const;
};

export default useFetchTrainingTypeData;

import { useEffect, useState } from "react";
import { getTraining } from "@/app/services/training/training.service";

// Define the Training interface to match the API response
interface Training {
  _id: string;
  trainingType: string; // Name of the training type
  trainer: string; // Trainer's name
  trainerAvatar?: string; // Optional trainer avatar
  employees: string[]; // Array of employee names
  employeesId: string[]; // Array of employee IDs
  startDate: string;
  endDate: string;
  description: string;
  trainingCost: number;
  isActive: boolean;
}

const useFetchTrainingData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  order = "",
  startDate,
  endDate,
  isActive,
  search,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [trainingData, setTrainingData] = useState<Training[] | null>(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getTrainingSectionData = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching training with params:", {
          limit,
          page,
          order,
          startDate,
          endDate,
          isActive,
          search
        });
        const result = await getTraining(
          limit,
          page,
          "",
          order,
          startDate,
          endDate,
          isActive,
          search
        );
        console.log("API Response:", result);
        if (
          result &&
          result.trainings &&
          Array.isArray(result.trainings.results)
        ) {
          setTrainingData(result.trainings.results);
          setTotal(Number(result.trainings.total) || 0);
        } else {
          setTrainingData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch trainings section data:", err);
        setTrainingData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    getTrainingSectionData();
  }, [
    refresh,
    limit,
    page,
    order,
    startDate,
    endDate,
    isActive,
    search,
    setIsLoading,
  ]);

  return [trainingData, total] as const;
};

export default useFetchTrainingData;
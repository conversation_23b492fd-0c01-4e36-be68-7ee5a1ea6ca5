import { useEffect, useState } from "react";
import { getPerformanceIndicator } from "@/app/services/performance/performance.service";

// Define an interface for the employee
interface Employee {
  _id: string;
  firstName?: string;
  lastName?: string;
  departmentId?: string | { _id: string };
  designationId?: string | { _id: string };
  [key: string]: any; // Allow other properties
}

// Define an interface for the policy data
interface PerformanceRecord {
  createdAt: string | number | Date;
  empId: Employee; // Use the Employee interface
  _id: string;
  designationId: string;
  designationName?: string;
  customerExperience: string;
  marketing: string;
  management: string;
  administration: string;
  presentationSkill: string;
  qualityWork: string;
  efficiency: string;
  integrity: string;
  professionalism: string;
  teamWork: string;
  criticalThinking: string;
  conflictManagement: string;
  attendence: string;
  meetDedline: string;
  isActive: boolean;
}

const useFetchPerformanceSectionData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  departmentName = "Department",
  order = "Sort By",
  startDate,
  endDate,
  search,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  departmentName?: string;
  order?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}) => {
  const [performanceData, setPerformanceData] = useState<PerformanceRecord[] | null>(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getPerformanceSectionData = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching Performance with params:", {
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          search,
        });
        const result = await getPerformanceIndicator(
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          search
        );
        console.log("API Response:", result);
        if (
          result &&
          result.performance &&
          Array.isArray(result.performance.results)
        ) {
          setPerformanceData(result.performance.results);
          setTotal(Number(result.performance.total) || 0);
        } else {
          setPerformanceData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch Policy section data:", err);
        setPerformanceData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    getPerformanceSectionData();
  }, [
    refresh,
    limit,
    page,
    departmentName,
    order,
    startDate,
    endDate,
    search,
    setIsLoading,
  ]);

  return [performanceData, total] as const;
};

export default useFetchPerformanceSectionData;

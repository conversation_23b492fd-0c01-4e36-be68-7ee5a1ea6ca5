import { useEffect, useState } from "react";
import { getPerformanceAppraisal } from "@/app/services/performance/performance.service";

interface Employee {
  _id: string;
  firstName: string;
  departmentName: string;
  designationName: string;
}

interface Competency {
  _id: string;
  competencyKey: string;
  setValue: string;
}

interface PerformanceAppraisal {
  _id: string;
  empId: Employee;
  designationId: string;
  appraisalDate: string;
  competencies: Competency[];
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface AppraisalStats {
  total: number;
  activeCount: number;
  inactiveCount: number;
  newJoiners: number;
  page: string;
  limit: string;
  totalPages: number;
}

interface ApiResponse {
  success: boolean;
  statusCode: number;
  message: string;
  appraisals: {
    results: PerformanceAppraisal[];
  } & AppraisalStats;
  meta: {
    version: string;
    forceUpdate: boolean;
    maintenance: boolean;
    hasUpdate: boolean;
  };
}

const useFetchPerformanceAppraisalData = (
  limit: number,
  page: number,
  order: string,
  startDate?: string,
  endDate?: string,
  refresh?: boolean,
  search?: string
) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<ApiResponse | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await getPerformanceAppraisal(limit, page, order, startDate, endDate, search);
        setData(response);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
        setData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [limit, page, order, startDate, endDate, search, refresh]);

  return { data, loading, error };
};

export default useFetchPerformanceAppraisalData;
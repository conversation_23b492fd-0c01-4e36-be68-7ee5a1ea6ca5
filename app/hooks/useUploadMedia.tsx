import axios from "axios";
import { getPreSignedUrl } from "../services/common.service";

const useUploadMedia = () => {
  const uploadMedia = async (
    file: File,
    dir: string,
    count = 1,
    setIsLoading: (isLoading: boolean) => void,
    fileName ="",
  ) => {
    try {
      setIsLoading(true);

      // Prepare the params for getPreSignedUrl
      const params: {
        location: string;
        type: string;
        count: number;
        fileName?: string;
      } = {
        location: dir,
        type: file.type,
        count,
      };
      // Conditionally add fileName if provided
      if (fileName && fileName !== "") {
        const fileNameWithoutExt = fileName.split('.')[0];
        params.fileName = fileNameWithoutExt;
      }
      
      // console.log("Sending params to getPreSignedUrl:", params);

      const response = await getPreSignedUrl(params);

      if (response.success) {
        const { url, preview } = response.data[0];

        // Upload the media
        const uploadResponse = await axios.put(url, file, {
          headers: {
            "Content-Type": file.type,
          },
        });

        if (uploadResponse.status === 200) {
          return { url, preview };
        } else {
          throw new Error("Failed to upload media.");
        }
      } else {
        throw new Error("Failed to get presigned URL.");
      }
    } catch (error) {
      console.error("Upload media error:", error);
      if (error instanceof Error) {
        throw new Error(error.message);
      } else {
        throw new Error("An unknown error occurred.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return { uploadMedia };
};

export default useUploadMedia;

import { useEffect, useState } from "react";
import { getAssets } from "@/app/services/assets/asset.service";

interface AssetData {
  type: string;
  brand: string;
  category: string;
  quantity: number;
  serialNumber: string[];
  cost: string;
  vendor: string;
  warrentyUntil: string;
  location: string;
  assetImage: string[];
}

const useFetchAssetData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  departmentName = "Department",
  order = "Sort By",
  startDate,
  endDate,
  isActive,
}: {
  setIsLoading?: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  departmentName?: string;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
}) => {
  const [assetData, setAssetData] = useState<AssetData[] | null>(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getAssetSectionData = async () => {
      if (setIsLoading) {
        setIsLoading(true);
      }
      try {
        console.log("Fetching Assets with params:", {
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          isActive,
        });
        const result = await getAssets(
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          isActive
        );
        console.log("API Response:", result);

        if (
          result &&
          result.success &&
          result.assets &&
          Array.isArray(result.assets.results)
        ) {

          setAssetData(result.assets.results);

          setTotal(Number(result.assets.total) || 0);
        } else {
          setAssetData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch asset section data:", err);
        setAssetData([]);
        setTotal(0);
      } finally {
        if (setIsLoading) {
          setIsLoading(false);
        }
      }
    };

    getAssetSectionData();
  }, [
    refresh,
    limit,
    page,
    departmentName,
    order,
    startDate,
    endDate,
    isActive,
    setIsLoading,
  ]);

  return [assetData, total] as const;
};

export default useFetchAssetData;
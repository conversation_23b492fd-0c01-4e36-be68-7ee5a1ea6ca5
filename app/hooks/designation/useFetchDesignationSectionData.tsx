import { useEffect, useState } from "react";
import { getDesignations } from "@/app/services/designation.service";

// Define an interface for the designation data
interface DesignationRecord {
  _id: string;
  designationName?: string;
  departmentName?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  numberOfEmployees?: number;
}

const useFetchDesignationData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  departmentName = "Department",
  order = "Sort By",
  startDate,
  endDate,
  isActive,
  search,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  departmentName?: string;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [designationData, setDesignationData] = useState<DesignationRecord[] | null>(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getDesignationData = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching designations with params:", {
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          isActive,
          search,
        });
        const result = await getDesignations(
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          isActive,
          search
        );
        console.log("API Response:", result);
        if (
          result &&
          result.designations &&
          Array.isArray(result.designations.results)
        ) {
          setDesignationData(result.designations.results);
          setTotal(Number(result.designations.total) || 0);
        } else {
          setDesignationData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch Designation data:", err);
        setDesignationData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    getDesignationData();
  }, [
    refresh,
    limit,
    page,
    departmentName,
    order,
    startDate,
    endDate,
    isActive,
    search,
    setIsLoading,
  ]);

  return [designationData, total] as const;
};

export default useFetchDesignationData;
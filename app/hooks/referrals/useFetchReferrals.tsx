import { useEffect, useState } from "react";
import { getAllReferrals } from "@/app/services/referrals/referrals.service"; 

const useFetchReferrals = ({
  setIsLoading,
  refresh,
  limit,
  page,
  order = "Sort By",
  startDate,
  endDate,
  isActive,
  search,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [referralData, setReferralData] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const fetchReferralData = async () => {
      setIsLoading(true);
      try {
        const result = await getAllReferrals(limit, page, order, startDate, endDate, isActive, search);
        console.log("API Response:", result);

        if (result && result.refferals && Array.isArray(result.refferals.results)) {
          setReferralData(result.refferals.results);
          setTotal(Number(result.refferals.total) || 0);
        } else {
          console.warn("No valid referral data found in API response:", result);
          setReferralData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch referral data:", err);
        setReferralData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    fetchReferralData();
  }, [refresh, limit, page, order, startDate, endDate, isActive,search, setIsLoading]);

  return [referralData, total] as const;
};

export default useFetchReferrals;
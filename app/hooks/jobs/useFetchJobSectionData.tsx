import { useEffect, useState } from "react";
import { getAllJobs } from "@/app/services/jobs/jobs.service";

const useFetchJobSectionData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  order = "Sort By",
  startDate,
  endDate,
  isActive,
  search
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [jobData, setJobData] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const fetchJobData = async () => {
      setIsLoading(true);
      try {
        const result = await getAllJobs(limit, page, order, startDate, endDate, isActive,search);
        console.log("API Response:", result); // Log the API response to inspect it
    
        if (result && result.jobs && Array.isArray(result.jobs.results)) {
          setJobData(result.jobs.results);
          setTotal(Number(result.jobs.total) || 0);
        } else {
          console.warn("No valid job data found in API response:", result);
          setJobData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch job section data:", err);
        setJobData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    fetchJobData();
  }, [refresh, limit, page, order, startDate, endDate, isActive,search, setIsLoading]);

  return [jobData, total] as const;
};

export default useFetchJobSectionData;

import { useEffect, useState } from "react";
import { getPolicies } from "@/app/services/policies.service";

// Define an interface for the policy data
interface PolicyRecord {
  _id: string;
  policyName: string;
  departmentId: string;
  departmentName: string;
  description: string;
  createdAt: string;
  implementationDate?: string;
  policyDocument?: string;
  isActive?: boolean;
  isDeleted?: boolean;
  updatedAt?: string;
  __v?: number;
}

const useFetchPolicySectionData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  departmentName = "Department",
  order = "Sort By",
  startDate,
  endDate,
  search,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  departmentName?: string;
  order?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}) => {
  const [policyData, setPolicyData] = useState<PolicyRecord[] | null>(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getPolicySectionData = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching policies with params:", {
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          search,
        });
        const result = await getPolicies(
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          search
        );
        console.log("API Response:", result);
        if (
          result &&
          result.policies &&
          Array.isArray(result.policies.results)
        ) {
          setPolicyData(result.policies.results);
          setTotal(Number(result.policies.total) || 0);
        } else {
          setPolicyData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch Policy section data:", err);
        setPolicyData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    getPolicySectionData();
  }, [
    refresh,
    limit,
    page,
    departmentName,
    order,
    startDate,
    endDate,
    search,
    setIsLoading,
  ]);

  return [policyData, total] as const;
};

export default useFetchPolicySectionData;
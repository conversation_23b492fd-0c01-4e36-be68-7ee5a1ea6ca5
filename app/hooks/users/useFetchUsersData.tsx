import { useEffect, useState } from "react";
import { getUser } from "@/app/services/users.service";
// Define an interface for the user data
interface UserRecord {
  _id: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  departmentName?: string;
  designationName?: string;
  isActive?: boolean;
  joinDate?: string;
  createdAt?: string;
  avatar?: string;
  designationId?: string;
}
interface UserResponse {
  users: {
    results: UserRecord[];
    totalEmployeesCount: number | string;
    activeEmployeesCount: number | string;
    inactiveEmployeesCount: number | string;
    newJoiners: number | string;
  };
}
const useFetchUsersData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  departmentName = "Department",
  designationName = "Designation",
  order = "Sort By",
  startDate,
  endDate,
  isActive,
  search,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  departmentName?: string;
  designationName?: string;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [userData, setUserData] = useState<UserRecord[] | null>(null);
  const [totalEmployeesCount, setTotalEmployeesCount] = useState<number>(0);
  const [activeEmployeesCount, setActiveEmployeesCount] = useState<number>(0);
  const [inactiveEmployeesCount, setInactiveEmployeesCount] = useState<number>(0);
  const [newJoiners, setNewJoiners] = useState<number>(0);
  useEffect(() => {
    const getUserSectionData = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching user with params:", {
          limit,
          page,
          departmentName,
          designationName,
          order,
          startDate,
          endDate,
          isActive,
          search,
        });
        const result: UserResponse = await getUser(
          limit,
          page,
          departmentName,
          designationName,
          order,
          startDate,
          endDate,
          isActive,
          search
        );
        console.log("API Response:", result);
        if (result && result.users && Array.isArray(result.users.results)) {
          setUserData(result.users.results);
          setTotalEmployeesCount(Number(result.users.totalEmployeesCount) || 0);
          setActiveEmployeesCount(Number(result.users.activeEmployeesCount) || 0);
          setInactiveEmployeesCount(Number(result.users.inactiveEmployeesCount) || 0);
          setNewJoiners(Number(result.users.newJoiners) || 0);
        } else {
          setUserData([]);
          setTotalEmployeesCount(0);
          setActiveEmployeesCount(0);
          setInactiveEmployeesCount(0);
          setNewJoiners(0);
        }
      } catch (err) {
        console.error("Failed to fetch Users section data:", err);
        setUserData([]);
        setTotalEmployeesCount(0);
        setActiveEmployeesCount(0);
        setInactiveEmployeesCount(0);
        setNewJoiners(0);
      } finally {
        setIsLoading(false);
      }
    };
    getUserSectionData();
  }, [
    refresh,
    limit,
    page,
    departmentName,
    designationName,
    order,
    startDate,
    endDate,
    isActive,
    search,
    setIsLoading,
  ]);
  return [userData, totalEmployeesCount, activeEmployeesCount, inactiveEmployeesCount, newJoiners] as const;
};
export default useFetchUsersData;

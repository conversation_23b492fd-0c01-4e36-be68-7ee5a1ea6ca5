import { useEffect } from "react";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";

interface RoleAuthOptions {
  // Change the type to accept readonly arrays
  allowedRoles: readonly string[];
  redirectTo?: string;
}

const useRoleAuth = ({
  allowedRoles,
  redirectTo = "/unauthorized",
}: RoleAuthOptions) => {
  const router = useRouter();
  const { roles } = useAuthStore();

  useEffect(() => {
    const hasAccess = roles.some((role) => allowedRoles.includes(role));

    if (!hasAccess) {
      router.push(redirectTo);
    }
  }, [roles, allowedRoles, redirectTo, router]);

  return {
    hasAccess: roles.some((role) => allowedRoles.includes(role)),
    roles,
  };
};

export default useRoleAuth;

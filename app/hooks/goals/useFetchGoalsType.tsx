import { useEffect, useState } from "react";
import { getAllGoalsType } from "@/app/services/goals/goals.service";

const useFetchGoalsType = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  order = "Sort By",
  startDate,
  endDate,
  isActive,
  search,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [goalTypeData, setGoalTypeData] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const fetchGoalTypeData = async () => {
      setIsLoading(true);
      try {
        const result = await getAllGoalsType(limit, page, order, startDate, endDate, search, isActive);
        console.log("API Response:", result);
    
        if (result && result.goalTypes && Array.isArray(result.goalTypes.results)) {
          setGoalTypeData(result.goalTypes.results);
          setTotal(Number(result.goalTypes.total) || 0);
        } else {
          console.warn("No valid job data found in API response:", result);
          setGoalTypeData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch goal type section data:", err);
        setGoalTypeData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGoalTypeData();
  }, [refresh, limit, page, order, startDate, endDate, isActive, search, setIsLoading]);

  return [goalTypeData, total] as const;
};

export default useFetchGoalsType;

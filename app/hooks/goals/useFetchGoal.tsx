import { useEffect, useState } from "react";
import { getAllGoals } from "@/app/services/goals/goals.service";

const useFetchGoal = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  order = "Sort By",
  startDate,
  endDate,
  isActive,
  search,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [goalData, setGoalData] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const fetchGoalData = async () => {
      setIsLoading(true);
      try {
        const result = await getAllGoals(limit, page, order, startDate, endDate, isActive, search);
        console.log("API Response:", result);
    
        if (result && result.goals && Array.isArray(result.goals.results)) {
          setGoalData(result.goals.results);
          setTotal(Number(result.goals.total) || 0);
        } else {
          console.warn("No valid job data found in API response:", result);
          setGoalData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch goal section data:", err);
        setGoalData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGoalData();
  }, [refresh, limit, page, order, startDate, endDate, isActive, search, setIsLoading]);

  return [goalData, total] as const;
};

export default useFetchGoal;

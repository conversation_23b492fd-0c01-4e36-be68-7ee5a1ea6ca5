import { useState, useEffect } from "react";
import { getAllGoalType } from "@/app/services/goals/goals.service";

interface GoalType {
  _id: string;
  goalType: string;
  description: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

const useFetchAllGoalTypes = () => {
  const [goalTypes, setGoalTypes] = useState<{ _id: string; goalType: string }[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchGoalTypes = async () => {
      setLoading(true);
      try {
        const response = await getAllGoalType();
        if (response.success && response.goalTypes && Array.isArray(response.goalTypes.results)) {
          const activeGoalTypes = response.goalTypes.results
            .filter((type: GoalType) => type.isActive && !type.isDeleted)
            .map((type: GoalType) => ({
              _id: type._id,
              goalType: type.goalType,
            }));
          setGoalTypes(activeGoalTypes);
        } else {
          setGoalTypes([]);
          setError("No valid goal types found in API response.");
        }
      } catch (err) {
        console.error("Failed to fetch goal types:", err);
        setError("Failed to fetch goal types. Please try again.");
        setGoalTypes([]);
      } finally {
        setLoading(false);
      }
    };

    fetchGoalTypes();
  }, []);

  return { goalTypes, loading, error };
};

export default useFetchAllGoalTypes;
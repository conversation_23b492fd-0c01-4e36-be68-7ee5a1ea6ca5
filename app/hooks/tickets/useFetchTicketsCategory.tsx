import { useEffect, useState } from "react";
import { getAllTicketsCategory } from "@/app/services/tickets/tickets.service";

interface TicketCategory {
  _id: string;
  category: string;
  departmentId: {
    _id: string;
    departmentName: string;
    isActive: boolean;
    isDeleted: boolean;
  };
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

interface FetchTicketCategoryParams {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}

const useFetchTicketCategoryData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  order = "",
  startDate,
  endDate,
  isActive,
  search
}: FetchTicketCategoryParams) => {
  const [ticketCategoryData, setTicketCategoryData] = useState<TicketCategory[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [activeCount, setActiveCount] = useState<number>(0);
  const [inactiveCount, setInactiveCount] = useState<number>(0);

  useEffect(() => {
    const fetchTicketCategoryData = async () => {
      setIsLoading(true);
      try {
        const result = await getAllTicketsCategory(limit, page, order, startDate, endDate, isActive, search);
        console.log("API Response:", result);

        if (result?.success && Array.isArray(result.ticketCategories?.results)) {
          setTicketCategoryData(result.ticketCategories.results);
          setTotal(Number(result.ticketCategories.total) || 0);
          setActiveCount(Number(result.ticketCategories.activeCount) || 0);
          setInactiveCount(Number(result.ticketCategories.inactiveCount) || 0);
        } else {
          console.warn("No valid ticket category data found in API response:", result);
          setTicketCategoryData([]);
          setTotal(0);
          setActiveCount(0);
          setInactiveCount(0);
        }
      } catch (err) {
        console.error("Failed to fetch ticket category data:", err);
        setTicketCategoryData([]);
        setTotal(0);
        setActiveCount(0);
        setInactiveCount(0);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTicketCategoryData();
  }, [refresh, limit, page, order, startDate, endDate, isActive, search, setIsLoading]);

  return [ticketCategoryData, total, activeCount, inactiveCount] as const;
};

export default useFetchTicketCategoryData;
import { useEffect, useState } from "react";
import { getAllTickets } from "@/app/services/tickets/tickets.service";

const useFetchTickets = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  order = "",
  priority,
  startDate,
  endDate,
  isActive,
  status,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  order?: string;
  startDate?: string;
  priority?:string;
  endDate?: string;
  isActive?: string;
  status?: string;
}) => {
  const [ticketsData, setTicketsData] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [activeCount, setActiveCount] = useState<number>(0);
  const [inactiveCount, setInactiveCount] = useState<number>(0);
  const [pendingCount, setPendingCount] = useState<number>(0);
  const [categoryStats, setCategoryStats] = useState<{ [key: string]: number }>({});
  const [assignedStats, setAssignedStats] = useState<any[]>([]);

  useEffect(() => {
    const fetchTicketsData = async () => {
      setIsLoading(true);
      try {
        const result = await getAllTickets(limit, page, order, startDate, endDate, isActive, priority, status);
        console.log("API Response:", result);

        if (result && result.tickets && Array.isArray(result.tickets.results)) {
          setTicketsData(result.tickets.results);
          setTotal(Number(result.tickets.total) || 0);
          setActiveCount(Number(result.tickets.activeCount) || 0);
          setInactiveCount(Number(result.tickets.inactiveCount) || 0);
          setPendingCount(Number(result.tickets.stats?.pending) || 0);
          setCategoryStats(result.tickets.categoryStats || {});
          setAssignedStats(result.tickets.assignedStats || []);
        } else {
          console.warn("No valid tickets data found in API response:", result);
          setTicketsData([]);
          setTotal(0);
          setActiveCount(0);
          setInactiveCount(0);
          setPendingCount(0);
          setCategoryStats({});
          setAssignedStats([]);
        }
      } catch (err) {
        console.error("Failed to fetch tickets section data:", err);
        setTicketsData([]);
        setTotal(0);
        setActiveCount(0);
        setInactiveCount(0);
        setPendingCount(0);
        setCategoryStats({});
        setAssignedStats([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTicketsData();
  }, [refresh, limit, page, order, startDate, endDate, isActive, priority, status, setIsLoading]);

  return [ticketsData, total, activeCount, inactiveCount, pendingCount, categoryStats, assignedStats] as const;
};

export default useFetchTickets;
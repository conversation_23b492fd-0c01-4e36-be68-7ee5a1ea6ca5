"use client";

import { useEffect, useState } from "react";
import { getLeave } from "@/app/services/leave/leave.service";
import { GridValidRowModel } from "@mui/x-data-grid";
import dayjs from "dayjs";

interface LeaveRecord {
  _id?: string;
  empId?: string | {
    _id?: string;
    firstName?: string;
    email?: string;
    designationName?: string;
  };
  leaveType?: string;
  leaveFrom?: string;
  leaveTo?: string;
  leaveTime?: string;
  leaveDays?: number;
  remainingDays?: number;
  reason?: string;
  leaveStatus?: string;
  approvedBy?: string | null;
  isActive?: boolean;
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
  approverName?: string;
  approverRole?: string;
}

interface LeaveEmployee {
  _id: string;
  employeeId: string;
  employeeName?: string;
  leaveType: string;
  from: string;
  to: string;
  days: number;
  leaveTime: string;
  remainingDays: number;
  status: string;
  reason: string;
  avatar?: string;
  department?: string;
  approvedBy: string | null;
}

interface LeaveSummary {
  totalLeaves: number;
  plannedLeaves: number;
  unplannedLeaves: number;
  pendingRequests: number;
  globalTotalLeaves: number;
  globalPlannedLeaves: number;
  globalUnplannedLeaves: number;
  globalPendingRequests: number;
}

const useFetchLeaveData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  departmentName = "Department",
  order = "Sort By",
  empId,
  startDate,
  endDate,
  isActive,
  leaveType = "Leave Type", // New parameter
  search
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  departmentName?: string;
  order?: string;
  empId?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  leaveType?: string; // New parameter
  search?: string;
}) => {
  const [leaveData, setLeaveData] = useState<GridValidRowModel[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [leaveSummary, setLeaveSummary] = useState<LeaveSummary>({
    totalLeaves: 0,
    plannedLeaves: 0,
    unplannedLeaves: 0,
    pendingRequests: 0,
    globalTotalLeaves: 0,
    globalPlannedLeaves: 0,
    globalUnplannedLeaves: 0,
    globalPendingRequests: 0,
  });

  const transformLeaveData = (leave: any) => {
    console.log("Original leave data:", JSON.stringify(leave, null, 2));
    
    // Extract employee information from empId
    let employeeName = "Unknown";
    let avatar = "/assets/users/default.jpg";
    let departmentName = "-";
    let employeeId = "";
    
    if (leave.empId) {
      if (typeof leave.empId === "object") {
        // Extract employee name
        const firstName = leave.empId.firstName || "";
        const lastName = leave.empId.lastName || "";
        employeeName = `${firstName} ${lastName}`.trim() || "Unknown";
        
        // Extract avatar
        avatar = leave.empId.avatar || "/assets/users/default.jpg";
        
        // Extract department name
        if (leave.empId.departmentId && typeof leave.empId.departmentId === "object") {
          departmentName = leave.empId.departmentId.departmentName || "-";
        }
        
        // Extract employee ID
        employeeId = leave.empId._id || "";
      } else {
        // If empId is just a string (ID)
        employeeId = leave.empId;
      }
    }
    
    // Extract approvedBy information
    let approvedByInfo = null;
    if (leave.approvedBy && typeof leave.approvedBy === "object") {
      approvedByInfo = {
        firstName: leave.approvedBy.firstName || "",
        designationName: leave.approvedBy.designationName || "",
        avatar: leave.approvedBy.avatar || "/assets/users/default.jpg"
      };
    }
    
    return {
      _id: leave._id || '',
      employeeId: employeeId,
      employeeName: employeeName,
      leaveType: leave.leaveType || '',
      from: dayjs(leave.leaveFrom).format("DD MMM YYYY"),
      to: dayjs(leave.leaveTo).format("DD MMM YYYY"),
      days: leave.leaveDays || 0,
      leaveTime: leave.leaveTime || '',
      remainingDays: leave.remainingDays || 0,
      status: leave.leaveStatus || 'Pending',
      reason: leave.reason || '',
      avatar: avatar,
      department: departmentName,
      approvedBy: approvedByInfo
    };
  };

  useEffect(() => {
    const getLeaveSectionData = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching leave with params:", {
          limit,
          page,
          departmentName,
          order,
          empId,
          startDate,
          endDate,
          isActive,
          leaveType, // Include in log
          search
        });
        const result = await getLeave(limit, page, departmentName, order, empId, startDate, endDate, isActive, leaveType);
        console.log("API Response:", result);

        if (result && result.success) {
          const leaveRecords = result.leaves?.results || (Array.isArray(result.leaves) ? result.leaves : []);

          const transformedData: LeaveEmployee[] = leaveRecords.map(transformLeaveData);
          setLeaveData(transformedData);
          setTotal(result.leaves.total || transformedData.length);
          setLeaveSummary({
            totalLeaves: result.leaves.totalLeaves || 0,
            plannedLeaves: result.leaves.plannedLeaves || 0,
            unplannedLeaves: result.leaves.unplannedLeaves || 0,
            pendingRequests: result.leaves.pendingRequests || 0,
            globalTotalLeaves : result.leaves.globalTotalLeaves || 0,
            globalPlannedLeaves : result.leaves.globalPlannedLeaves || 0,
            globalUnplannedLeaves : result.leaves.globalUnplannedLeaves || 0,
            globalPendingRequests : result.leaves.globalPendingRequests || 0,
          });
        } else {
          setLeaveData([]);
          setTotal(0);
          setLeaveSummary({
            totalLeaves: 0,
            plannedLeaves: 0,
            unplannedLeaves: 0,
            pendingRequests: 0,
            globalTotalLeaves : 0,
            globalPlannedLeaves : 0,
            globalUnplannedLeaves : 0,
            globalPendingRequests : 0,
          });
        }
      } catch (err) {
        console.error("Failed to fetch Leave section data:", err);
        setLeaveData([]);
        setTotal(0);
        setLeaveSummary({
          totalLeaves: 0,
          plannedLeaves: 0,
          unplannedLeaves: 0,
          pendingRequests: 0,
          globalTotalLeaves : 0,
          globalPlannedLeaves : 0,
          globalUnplannedLeaves : 0,
          globalPendingRequests : 0,
        });
      } finally {
        setIsLoading(false);
      }
    };

    getLeaveSectionData();
  }, [refresh, limit, page, departmentName, order, empId, startDate, endDate, isActive, leaveType, search, setIsLoading]);

  return [leaveData, total, leaveSummary] as const;
};

export default useFetchLeaveData;

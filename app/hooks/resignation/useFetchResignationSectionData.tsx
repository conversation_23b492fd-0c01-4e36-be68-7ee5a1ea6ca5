import { useEffect, useState } from "react";
import { getResignations } from "@/app/services/resignation.service";

// Define an interface for the resignation data
interface ResignationRecord {
  _id: string;
  employeeId: string;
  employeeName?: string;
  departmentName: string;
  resignationDate: string;
  status: string;
  reason?: string;
  createdAt?: string;
  isActive?: boolean;
}

const useFetchResignationSectionData = ({
  setIsLoading,
  refresh,
  limit = 100,
  page = 1,
  departmentName = "Department",
  order = "Sort By",
  startDate,
  endDate,
  isActive,
  search
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  departmentName?: string;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [resignationData, setResignationData] = useState<ResignationRecord[] | null>(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getResignationSectionData = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching resignations with params:", {
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          isActive,
        });
        const result = await getResignations(
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          isActive,
          search
        );
        console.log("API Response:", result);
        if (
          result &&
          result.resignations &&
          Array.isArray(result.resignations.results)
        ) {
          setResignationData(result.resignations.results);
          setTotal(Number(result.resignations.total) || 0);
        } else {
          setResignationData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch resignations section data:", err);
        setResignationData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    getResignationSectionData();
  }, [
    refresh,
    limit,
    page,
    departmentName,
    order,
    startDate,
    endDate,
    setIsLoading,
    isActive,
    search
  ]);

  return [resignationData, total] as const;
};

export default useFetchResignationSectionData;
import { useState, useEffect } from "react";
import { getAttendanceByEmpId } from "@/app/services/attendance.service";
import useAuthStore from "@/store/authStore";

interface AttendanceRecord {
  _id: string;
  empId: {
    _id: string;
    firstName: string;
    email: string;
    departmentName: string;
  };
  date?: string;
  avatar?: string;
  status?: string;
  employeeName?: string;
  departmentName?: string;
  isActive?: boolean;
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
  break?: number;
  checkIn?: string;
  checkOut?: string;
  late?: number;
  productionHours?: number;
  totalWorkingHours?: number;
}

interface AttendanceResponse {
  success: boolean;
  statusCode: number;
  message: string;
  attendanceRecords: {
    results: AttendanceRecord[];
    total: number;
    activeCount: number;
    inactiveCount: number;
    newJoiners: number;
    page: string;
    limit: string;
    totalPages: number;
  };
}

interface FetchAttendanceByEmpIdProps {
  empId: string;
  setIsLoading: (loading: boolean) => void;
  refresh: number | boolean;
  limit?: number;
  page?: number;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
}

const useFetchAttendanceByEmpId = ({
  empId,
  setIsLoading,
  refresh,
  limit,
  page,
  order="desc",
  startDate,
  endDate,
  isActive,
}: FetchAttendanceByEmpIdProps) => {
  const [attendanceData, setAttendanceData] = useState<any>(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const fetchData = async () => {
      if (setIsLoading) {
        setIsLoading(true);
      }
      try {
        const result = await getAttendanceByEmpId(
          empId,
          limit,
          page,
          order,
          startDate,
          endDate,
          isActive
        );

        console.log("API Response in hook:", result);

        if (result && result.success) {
          setAttendanceData(result.attendanceRecords);
          setTotal(result.attendanceRecords.total || 0);
        } else {
          setAttendanceData(null);
          setTotal(0);
        }
      } catch (error) {
        console.error("Error fetching attendance data:", error);
        setAttendanceData(null);
        setTotal(0);
      } finally {
        if (setIsLoading) {
          setIsLoading(false);
        }
      }
    };

    if (empId) {
      fetchData();
    }
  }, [empId, refresh, limit, page, order, startDate, endDate, isActive]);

  return [attendanceData, total] as const;
};

export default useFetchAttendanceByEmpId;

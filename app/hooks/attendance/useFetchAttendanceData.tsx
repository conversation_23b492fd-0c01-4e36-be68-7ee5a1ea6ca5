import { useEffect, useState } from "react";
import { getAttendance } from "@/app/services/attendance.service";

// Define the AttendanceData interface based on your API response
interface AttendanceData {
  _id: string;
  empId: string;
  date?: string;
  status?: string;
  departmentName?: string;
  employeeName?: string;
  avatar?: string;
  checkIn?: string;
  checkOut?: string;
  break?: number;
  late?: number;
  productionHours?: number;
  isActive?: boolean;
}

const useFetchAttendanceData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  departmentName = "Department",
  order = "Sort By",
  startDate,
  endDate,
  isActive,
}: {
  setIsLoading?: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  departmentName?: string;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
}) => {
  const [attendanceData, setAttendanceData] = useState<AttendanceData[] | null>(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getAttendanceSectionData = async () => {
      if (setIsLoading) {
        setIsLoading(true);
      }
      try {
        console.log("Fetching Attendance with params:", {
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          isActive,
        });
        const result = await getAttendance(
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          isActive
        );
        console.log("API Response:", result);

        if (
          result &&
          result.success &&
          result.attendanceRecords &&
          Array.isArray(result.attendanceRecords.results)
        ) {

          setAttendanceData(result.attendanceRecords.results);

          setTotal(Number(result.attendanceRecords.total) || 0);
        } else {
          setAttendanceData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch attendance section data:", err);
        setAttendanceData([]);
        setTotal(0);
      } finally {
        if (setIsLoading) {
          setIsLoading(false);
        }
      }
    };

    getAttendanceSectionData();
  }, [
    refresh,
    limit,
    page,
    departmentName,
    order,
    startDate,
    endDate,
    isActive,
    setIsLoading,
  ]);

  return [attendanceData, total] as const;
};

export default useFetchAttendanceData;
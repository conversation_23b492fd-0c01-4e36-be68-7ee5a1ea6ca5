import { useState, useEffect } from "react";
import { getTermination } from "@/app/services/termination.service";

// Define an interface for the termination data
interface TerminationRecord {
  _id: string;
  employeeId: string;
  employeeName?: string;
  departmentName: string;
  terminationDate: string;
  status: string;
  reason?: string;
  createdAt?: string;
  isActive?: boolean;
}

interface TerminationResponse {
  terminations: {
    results: TerminationRecord[];
    total: number | string;
  };
}

const useFetchTerminationSectionData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  departmentName = "Department",
  order = "Sort By",
  startDate,
  endDate,
  isActive,
  search,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  departmentName?: string;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [terminationData, setTerminationData] = useState<
    TerminationRecord[] | null
  >(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getTerminationSectionData = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching terminations with params:", {
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          isActive,
        });
        const result: TerminationResponse = await getTermination(
          limit,
          page,
          departmentName,
          order,
          startDate,
          endDate,
          isActive,
          search
        );
        console.log("API Response:", result);
        if (
          result &&
          result.terminations &&
          Array.isArray(result.terminations.results)
        ) {
          setTerminationData(result.terminations.results);
          setTotal(Number(result.terminations.total) || 0);
        } else {
          setTerminationData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch Termination section data:", err);
        setTerminationData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };
    getTerminationSectionData();
  }, [
    refresh,
    limit,
    page,
    departmentName,
    order,
    startDate,
    endDate,
    setIsLoading,
    isActive,
    search,
  ]);

  return {
    terminationData,
    total,
  } as const; // Using `as const` for better type inference
};

export default useFetchTerminationSectionData;

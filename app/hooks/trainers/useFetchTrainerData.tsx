import { useEffect, useState } from "react";
import { getTrainers } from "@/app/services/trainers/trainer.service";

// Define the correct Trainer interface
interface Trainer {
  _id: string;
  firstName: string;
  lastName: string;
  role: string;
  trainerAvatar: string;
  phone: string;
  email: string;
  description: string;
  isActive: boolean;
}

const useFetchTrainerData = ({
  setIsLoading,
  refresh,
  limit = 10,
  page = 1,
  order = "",
  startDate,
  endDate,
  isActive,
  search,
}: {
  setIsLoading: (loading: boolean) => void;
  refresh: boolean;
  limit?: number;
  page?: number;
  order?: string;
  startDate?: string;
  endDate?: string;
  isActive?: string;
  search?: string;
}) => {
  const [trainerData, setTrainerData] = useState<Trainer[] | null>(null);
  const [total, setTotal] = useState<number>(0);

  useEffect(() => {
    const getTrainerSectionData = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching trainer with params:", {
          limit,
          page,
          order,
          startDate,
          endDate,
          isActive,
        });
        const result = await getTrainers(
          limit,
          page,
          "", // Remove departmentName since it's not relevant for trainers
          order,
          startDate,
          endDate,
          isActive,
          search
        );
        console.log("API Response:", result);
        if (
          result &&
          result.trainers &&
          Array.isArray(result.trainers.results)
        ) {
          setTrainerData(result.trainers.results);
          setTotal(Number(result.trainers.total) || 0);
        } else {
          setTrainerData([]);
          setTotal(0);
        }
      } catch (err) {
        console.error("Failed to fetch trainers section data:", err);
        setTrainerData([]);
        setTotal(0);
      } finally {
        setIsLoading(false);
      }
    };

    getTrainerSectionData();
  }, [
    refresh,
    limit,
    page,
    order,
    startDate,
    endDate,
    isActive,
    search,
    setIsLoading,
  ]);

  return [trainerData, total] as const;
};

export default useFetchTrainerData;
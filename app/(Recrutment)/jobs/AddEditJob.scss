.job-dialog {
  .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px !important;


    .title-container {
      display: flex;
      align-items: center;
      gap: 12px;

      .dialog-title-text {
        font-size: 20px;
        color: #111827;
        font-weight: 600;
      }

      .job-id {
        color: #212529;
        font-weight: normal;
        font-size: 14px;
      }
    }
  }

  .tabs-container {
    border-bottom: 1px solid #e0e0e0;

    .dialog-tabs {
      .MuiTab-root {
        min-width: 120px;
        text-transform: none;
        font-weight: 500;
        color: #6b7280;
        font-size: 14px;
        padding: 12px 16px;
      }

      .active-tab {
        color: #f26522;
      }

      .MuiTabs-indicator {
        background-color: #f26522;
      }
    }
  }

  .dialog-content {
    padding: 0px !important;
    max-height: 90vh;
    overflow-y: auto;

    .basic-info-tab,
    .location-tab {
      display: flex;
      flex-direction: column;

      .logo-upload-section {
        display: flex;
        align-items: center;
        padding: 16px;
        background-color: #f8f9fa;
        gap: 16px;
        margin-bottom: 1.5rem !important;

        .image-placeholder {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background-color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #e0e0e0;

          .image-preview {
            border-radius: 50%;
          }
        }

        .upload-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 4px;

          .upload-title {
            font-size: 14px;
            font-weight: 600;
            color: #111827;
          }

          .upload-hint {
            font-size: 12px;
            color: #6b7280;
          }

          .upload-buttons {
            display: flex;
            gap: 8px;

            .upload-btn {
              background-color: #f26522;
              color: #fff;
              text-transform: none;
              font-size: 14px;
              padding: 3px 5px !important;
              border-radius: 4px;

              &:hover {
                background-color: #e55a1b;
              }

              &:disabled {
                background-color: #ccc;
                color: #666;
              }
            }

            .cancel-btn {
              color: #000;
              border: none;
              text-transform: none;
              font-size: 14px;
              margin-top: 5px;

              &:hover {
                background-color: transparent;
                color: #e55a1b;
              }

              &:disabled {
                color: #ccc;
              }
            }
          }
        }
      }

      .two-column-fields {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .half-width {
          flex: 1;
          min-width: 200px;
        }
      }

      .form-field {
        margin-bottom: 16px;
        width: 100%;

        .required {
          color: red;
        }

        .MuiTypography-subtitle1 {
          font-size: 14px;
          font-weight: 500;
          color: #111827;
        }

        .MuiTextField-root,
        .MuiFormControl-root {
          .MuiInputBase-root {
            border-radius: 4px;
            background-color: #fff;

            .MuiSelect-select {
              padding: 10px 14px;
            }
          }

          .MuiOutlinedInput-notchedOutline {
            border-color: #e5e7eb;
          }

          &:hover .MuiOutlinedInput-notchedOutline {
            border-color: #d1d5db;
          }

          &.Mui-focused .MuiOutlinedInput-notchedOutline {
            border-color: #d1d5db;
          }

          .MuiInputBase-input {
            padding: 10px 14px;
          }
        }

        .MuiSelect-root {
          .MuiSelect-select {
            padding: 10px 14px;
          }

          &.Mui-focused .MuiOutlinedInput-notchedOutline {
            border-color: #d1d5db;
          }
        }
      }
    }
  }

  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
    margin: 0px !important;

    .cancel-button {
      color: #666;
      border-color: #ccc;
      text-transform: none;
      padding: 6px 16px;

      &:hover {
        border-color: #9ca3af;
      }
    }

    .save-button {
      background-color: #f26522;
      text-transform: none;
      padding: 6px 16px;

      &:hover {
        background-color: #e55a1b;
      }
    }
  }
  .custom-autocomplete {
    .MuiInputBase-root {
      padding: 8px !important;
      display: flex;
      flex-wrap: wrap;
      height: auto !important;
      min-height: 38px !important;
      max-height: none !important;
    }
  
    .MuiInputBase-input {
      padding: 0 !important;
      height: auto !important;
    }
  
    .MuiAutocomplete-endAdornment {
      position: absolute;
      right: 9px;
      top: 50%;
      transform: translateY(-50%);
    }
  
    .MuiAutocomplete-tag {
      margin: 3px;
    }
  }
  
  .autocomplete-input {
    width: 100%;
  
    .MuiOutlinedInput-root {
      height: auto;
      min-height: 38px;
    }
  }
}
"use client";

import type React from "react";
import { useState, useEffect, useRef } from "react";
import {
  Di<PERSON>,
  DialogTitle,
  DialogContent,
  IconButton,
  Tabs,
  Tab,
  TextField,
  Button,
  Box,
  Typography,
  Select,
  MenuItem,
  Chip,
  FormControl,
  FormHelperText,
  DialogActions,
  Avatar,
  Autocomplete,
} from "@mui/material";
import { Close, Image as ImageIcon } from "@mui/icons-material";
import { toast } from "react-toastify";
import { useFormik } from "formik";
import * as Yup from "yup";
import { addJob, updateJob } from "@/app/services/jobs/jobs.service";
import TextEditor from "@/components/TextEditor/TextEditor";
import useUploadMedia from "@/app/hooks/useUploadMedia";
import "./AddEditJob.scss";

const stripHtmlTags = (html: any) => {
  const div = document.createElement("div");
  div.innerHTML = html;
  return div.textContent || div.innerText || "";
};

interface JobDialogProps {
  open: boolean;
  onClose: () => void;
  refreshList?: () => void;
  jobData?: any;
  mode: "add" | "edit";
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

const JobDialog: React.FC<JobDialogProps> = ({
  open,
  onClose,
  refreshList,
  jobData,
  mode,
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { uploadMedia } = useUploadMedia();

  const validationSchema = Yup.object({
    jobTitle: Yup.string().required("Job Title is required"),
    jobDescription: Yup.string().required("Job Description is required"),
    jobCategory: Yup.string().required("Job Category is required"),
    jobType: Yup.string().required("Job Type is required"),
    jobLevel: Yup.string().required("Job Level is required"),
    experienceLevel: Yup.string().required("Experience Level is required"),
    experienceTime: Yup.string().required("Job Experience Time is required"),
    qualification: Yup.string().required("Qualification is required"),
    gender: Yup.string().required("Gender is required"),
    minSalary: Yup.number()
      .required("Minimum Salary is required")
      .positive("Minimum Salary must be positive"),
    maxSalary: Yup.number()
      .required("Maximum Salary is required")
      .positive("Maximum Salary must be positive")
      .moreThan(
        Yup.ref("minSalary"),
        "Maximum Salary must be greater than Minimum Salary"
      ),
    jobExpiredDate: Yup.date()
      .required("Job Expiry Date is required")
      .nullable(),
    requiredSkills: Yup.array()
      .of(Yup.string())
      .min(1, "At least one skill is required")
      .required("Required Skills are required"),
    jobAddress: Yup.string().required("Address is required"),
    jobCountry: Yup.string().required("Country is required"),
    jobState: Yup.string().required("State is required"),
    jobCity: Yup.string().required("City is required"),
    jobZipCode: Yup.string().required("Zip Code is required"),
    profileImage: Yup.string(),
  });

  const formik = useFormik({
    initialValues: {
      jobTitle: "",
      jobDescription: "",
      jobCategory: "",
      jobType: "",
      jobLevel: "",
      experienceLevel: "",
      experienceTime: "",
      qualification: "",
      gender: "",
      minSalary: "",
      maxSalary: "",
      jobExpiredDate: "",
      requiredSkills: [] as string[],
      jobAddress: "",
      jobCountry: "",
      jobState: "",
      jobCity: "",
      jobZipCode: "",
      profileImage: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        const payload = {
          jobTitle: values.jobTitle,
          jobDiscription: stripHtmlTags(values.jobDescription),
          jobCategory: values.jobCategory,
          jobType: values.jobType,
          jobLevel: values.jobLevel,
          experienceLevel: values.experienceLevel,
          experienceTime: values.experienceTime,
          qualification: values.qualification,
          gender: values.gender,
          minSalary: parseFloat(values.minSalary),
          maxSalary: parseFloat(values.maxSalary),
          jobExpiredDate: new Date(values.jobExpiredDate).toISOString(),
          requiredSkills: values.requiredSkills,
          jobAddress: values.jobAddress,
          jobCountry: values.jobCountry,
          jobState: values.jobState,
          jobCity: values.jobCity,
          jobZipCode: values.jobZipCode,
          profileImage: values.profileImage || undefined,
        };

        let response: { message: string };
        if (mode === "edit" && jobData?._id) {
          response = await updateJob(jobData._id, payload);
          toast.success(response.message);
        } else {
          response = await addJob(payload);
          toast.success(response.message || "Job added successfully");
        }

        if (refreshList) {
          refreshList();
        }
        onClose();
      } catch (error: any) {
        console.error(
          `Error ${mode === "edit" ? "updating" : "adding"} job:`,
          error
        );
        const errorMessage = error.response?.data?.message;
        toast.error(errorMessage);
      }
    },
  });

  // Reset form when dialog opens in "add" mode or closes
  useEffect(() => {
    if (open) {
      if (mode === "add") {
        formik.resetForm();
        setSelectedImage(null);
      } else if (mode === "edit" && jobData) {
        formik.setValues({
          jobTitle: jobData.jobTitle || "",
          jobDescription: jobData.jobDiscription || "",
          jobCategory: jobData.jobCategory || "",
          jobType: jobData.jobType || "",
          jobLevel: jobData.jobLevel || "",
          experienceLevel: jobData.experienceLevel || "",
          experienceTime: jobData.experienceTime || "",
          qualification: jobData.qualification || "",
          gender: jobData.gender || "",
          minSalary: jobData.minSalary?.toString() || "",
          maxSalary: jobData.maxSalary?.toString() || "",
          jobExpiredDate: jobData.jobExpiredDate
            ? new Date(jobData.jobExpiredDate).toISOString().split("T")[0]
            : "",
          requiredSkills: jobData.requiredSkills || [],
          jobAddress: jobData.jobAddress || "",
          jobCountry: jobData.jobCountry || "",
          jobState: jobData.jobState || "",
          jobCity: jobData.jobCity || "",
          jobZipCode: jobData.jobZipCode || "",
          profileImage: jobData.profileImage || "",
        });
        setSelectedImage(jobData.profileImage || null);
      }
    } else {
      formik.resetForm();
      setSelectedImage(null);
      setTabValue(0);
    }
  }, [open, mode, jobData]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 4 * 1024 * 1024) {
        toast.error("Image should be below 4 MB");
        return;
      }
      setIsUploading(true);
      try {
        const { preview } = await uploadMedia(
          file,
          "job_profiles",
          1,
          setIsUploading,
          file.name
        );
        setSelectedImage(preview);
        formik.setFieldValue("profileImage", preview);
        toast.success("Profile image uploaded successfully");
      } catch (error) {
        console.error("Upload error:", error);
        toast.error("Failed to upload profile image");
      } finally {
        setIsUploading(false);
      }
    }
  };

  const jobCategories = ["Software", "Marketing", "Design", "Finance", "HR"];
  const jobTypes = ["FullTime", "PartTime", "Contract"];
  const jobLevels = ["TeamLead", "Manager", "Senior", "Junior"];
  const experienceLevels = ["Beginner", "Intermediate", "Expert"];
  const jobExperienceTimes = ["0-1", "1-2", "2-3", "3-5", "5+"];
  const qualifications = ["Bachelor", "Master", "Others"];
  const genders = ["Male", "Female", "Other"];
  const availableSkills = [
    "JavaScript",
    "React",
    "Node.js",
    "Python",
    "SQL",
    "Design",
    "Marketing",
  ];
  const countries = ["USA", "Canada", "UK", "India", "Australia"];
  const states = ["New York", "California", "Ontario", "London", "Maharashtra"];
  const cities = ["New York", "Los Angeles", "Toronto", "London", "Mumbai"];

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="md"
      fullWidth
      className="job-dialog"
    >
      <DialogTitle className="dialog-title">
        <Box className="title-container">
          <Typography variant="h6" className="dialog-title-text">
            {mode === "edit" ? "Edit Job" : "Add Job"}
          </Typography>
          {mode === "edit" && (
            <Typography className="job-id">Job ID: {jobData?.jobId}</Typography>
          )}
        </Box>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            backgroundColor: "#6b7280",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": { backgroundColor: "#d55a1d" },
            "& .MuiSvgIcon-root": { fontSize: "14px" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <Box className="tabs-container">
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          className="dialog-tabs"
          aria-label="job dialog tabs"
        >
          <Tab
            label="Basic Information"
            className={tabValue === 0 ? "active-tab" : ""}
            {...a11yProps(0)}
          />
          <Tab
            label="Location"
            className={tabValue === 1 ? "active-tab" : ""}
            {...a11yProps(1)}
          />
        </Tabs>
      </Box>

      <DialogContent className="dialog-content">
        <form onSubmit={formik.handleSubmit}>
          <TabPanel value={tabValue} index={0}>
            <Box className="basic-info-tab">
              <Box className="logo-upload-section">
                <Box className="image-placeholder">
                  {selectedImage ? (
                    <Avatar
                      src={selectedImage}
                      alt="Profile Image"
                      sx={{ width: "100%", height: "100%" }}
                      className="image-preview"
                    />
                  ) : (
                    <ImageIcon sx={{ fontSize: 16, color: "#6b7280" }} />
                  )}
                </Box>
                <Box className="upload-info">
                  <Typography className="upload-title">
                    Upload Profile Image
                  </Typography>
                  <Typography className="upload-hint">
                    Image should be below 4 MB
                  </Typography>
                  <Box className="upload-buttons">
                    <Button
                      variant="contained"
                      component="label"
                      className="upload-btn"
                      disabled={isUploading}
                    >
                      {isUploading ? "Uploading..." : "Upload"}
                      <input
                        type="file"
                        hidden
                        accept="image/*"
                        onChange={handleImageUpload}
                      />
                    </Button>
                    {selectedImage && (
                      <Button
                        variant="outlined"
                        onClick={() => {
                          setSelectedImage(null);
                          formik.setFieldValue("profileImage", "");
                        }}
                        className="cancel-btn"
                        disabled={isUploading}
                      >
                        Cancel
                      </Button>
                    )}
                  </Box>
                </Box>
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Job Title <span className="required">*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="jobTitle"
                  value={formik.values.jobTitle}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.jobTitle && Boolean(formik.errors.jobTitle)
                  }
                  helperText={formik.touched.jobTitle && formik.errors.jobTitle}
                />
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Job Description <span className="required">*</span>
                </Typography>
                <TextEditor
                  value={formik.values.jobDescription}
                  onChange={(newContent: any) =>
                    formik.setFieldValue("jobDescription", newContent)
                  }
                />
                {formik.touched.jobDescription &&
                  formik.errors.jobDescription && (
                    <FormHelperText error>
                      {formik.errors.jobDescription}
                    </FormHelperText>
                  )}
              </Box>

              <Box className="two-column-fields">
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Job Category <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    variant="outlined"
                    name="jobCategory"
                    value={formik.values.jobCategory}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.jobCategory &&
                      Boolean(formik.errors.jobCategory)
                    }
                    helperText={
                      formik.touched.jobCategory && formik.errors.jobCategory
                    }
                  />
                </Box>
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Job Type <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="jobType"
                      value={formik.values.jobType}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.jobType && Boolean(formik.errors.jobType)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Select</em>;
                        }
                        return selected;
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Select</em>
                      </MenuItem>
                      {jobTypes.map((type) => (
                        <MenuItem key={type} value={type}>
                          {type}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.jobType && formik.errors.jobType && (
                      <FormHelperText error>
                        {formik.errors.jobType}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
              </Box>

              <Box className="two-column-fields">
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Job Level <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="jobLevel"
                      value={formik.values.jobLevel}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.jobLevel &&
                        Boolean(formik.errors.jobLevel)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Select</em>;
                        }
                        return selected;
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Select</em>
                      </MenuItem>
                      {jobLevels.map((level) => (
                        <MenuItem key={level} value={level}>
                          {level}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.jobLevel && formik.errors.jobLevel && (
                      <FormHelperText error>
                        {formik.errors.jobLevel}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Experience Level <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="experienceLevel"
                      value={formik.values.experienceLevel}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.experienceLevel &&
                        Boolean(formik.errors.experienceLevel)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Select</em>;
                        }
                        return selected;
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Select</em>
                      </MenuItem>
                      {experienceLevels.map((exp) => (
                        <MenuItem key={exp} value={exp}>
                          {exp}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.experienceLevel &&
                      formik.errors.experienceLevel && (
                        <FormHelperText error>
                          {formik.errors.experienceLevel}
                        </FormHelperText>
                      )}
                  </FormControl>
                </Box>
              </Box>

              <Box className="two-column-fields">
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Job Experience Time (Years){" "}
                    <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    variant="outlined"
                    name="experienceTime"
                    value={formik.values.experienceTime}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.experienceTime &&
                      Boolean(formik.errors.experienceTime)
                    }
                    helperText={
                      formik.touched.experienceTime &&
                      formik.errors.experienceTime
                    }
                  />
                </Box>
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Qualification <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="qualification"
                      value={formik.values.qualification}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.qualification &&
                        Boolean(formik.errors.qualification)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Select</em>;
                        }
                        return selected;
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Select</em>
                      </MenuItem>
                      {qualifications.map((qual) => (
                        <MenuItem key={qual} value={qual}>
                          {qual}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.qualification &&
                      formik.errors.qualification && (
                        <FormHelperText error>
                          {formik.errors.qualification}
                        </FormHelperText>
                      )}
                  </FormControl>
                </Box>
              </Box>

              <Box className="two-column-fields">
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Gender <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="gender"
                      value={formik.values.gender}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.gender && Boolean(formik.errors.gender)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Select</em>;
                        }
                        return selected;
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Select</em>
                      </MenuItem>
                      {genders.map((gender) => (
                        <MenuItem key={gender} value={gender}>
                          {gender}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.gender && formik.errors.gender && (
                      <FormHelperText error>
                        {formik.errors.gender}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Min. Salary <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    variant="outlined"
                    name="minSalary"
                    value={formik.values.minSalary}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.minSalary &&
                      Boolean(formik.errors.minSalary)
                    }
                    helperText={
                      formik.touched.minSalary && formik.errors.minSalary
                    }
                    type="number"
                  />
                </Box>
              </Box>

              <Box className="two-column-fields">
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Max. Salary <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    variant="outlined"
                    name="maxSalary"
                    value={formik.values.maxSalary}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.maxSalary &&
                      Boolean(formik.errors.maxSalary)
                    }
                    helperText={
                      formik.touched.maxSalary && formik.errors.maxSalary
                    }
                    type="number"
                  />
                </Box>
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Job Expired Date <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    variant="outlined"
                    type="date"
                    name="jobExpiredDate"
                    value={formik.values.jobExpiredDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.jobExpiredDate &&
                      Boolean(formik.errors.jobExpiredDate)
                    }
                    helperText={
                      formik.touched.jobExpiredDate &&
                      formik.errors.jobExpiredDate
                    }
                    InputLabelProps={{ shrink: true }}
                  />
                </Box>
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Required Skills <span className="required">*</span>
                </Typography>
                <Autocomplete
                  multiple
                  id="required-skills"
                  options={availableSkills}
                  value={availableSkills.filter((skill) =>
                    formik.values.requiredSkills.includes(skill)
                  )}
                  onChange={(event, newValue) => {
                    const newSkills = newValue.map((skill) => skill);
                    formik.setFieldValue("requiredSkills", newSkills);
                  }}
                  onBlur={() => formik.setFieldTouched("requiredSkills", true)}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => {
                      const { key, ...otherProps } = getTagProps({ index });
                      return (
                        <Chip
                          key={option}
                          label={option}
                          deleteIcon={<Close />}
                          {...otherProps}
                          sx={{
                            margin: "2px",
                            borderRadius: "4px",
                            "& .MuiChip-label": {
                              fontSize: "12px",
                            },
                          }}
                        />
                      );
                    })
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      placeholder="Add required skills"
                      error={
                        formik.touched.requiredSkills &&
                        Boolean(formik.errors.requiredSkills)
                      }
                      helperText={
                        formik.touched.requiredSkills &&
                        formik.errors.requiredSkills
                      }
                      className="autocomplete-input"
                    />
                  )}
                  className="custom-autocomplete"
                  fullWidth
                />
              </Box>
            </Box>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Box className="location-tab">
              <Box className="form-field">
                <Typography variant="subtitle1">
                  Address <span className="required">*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="jobAddress"
                  value={formik.values.jobAddress}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.jobAddress &&
                    Boolean(formik.errors.jobAddress)
                  }
                  helperText={
                    formik.touched.jobAddress && formik.errors.jobAddress
                  }
                />
              </Box>

              <Box className="two-column-fields">
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Country <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="jobCountry"
                      value={formik.values.jobCountry}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.jobCountry &&
                        Boolean(formik.errors.jobCountry)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Select</em>;
                        }
                        return selected;
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Select</em>
                      </MenuItem>
                      {countries.map((country) => (
                        <MenuItem key={country} value={country}>
                          {country}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.jobCountry && formik.errors.jobCountry && (
                      <FormHelperText error>
                        {formik.errors.jobCountry}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    State <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="jobState"
                      value={formik.values.jobState}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.jobState &&
                        Boolean(formik.errors.jobState)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Select</em>;
                        }
                        return selected;
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Select</em>
                      </MenuItem>
                      {states.map((state) => (
                        <MenuItem key={state} value={state}>
                          {state}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.jobState && formik.errors.jobState && (
                      <FormHelperText error>
                        {formik.errors.jobState}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
              </Box>

              <Box className="two-column-fields">
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    City <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="jobCity"
                      value={formik.values.jobCity}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.jobCity && Boolean(formik.errors.jobCity)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Select</em>;
                        }
                        return selected;
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Select</em>
                      </MenuItem>
                      {cities.map((city) => (
                        <MenuItem key={city} value={city}>
                          {city}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.jobCity && formik.errors.jobCity && (
                      <FormHelperText error>
                        {formik.errors.jobCity}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Zip Code <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    variant="outlined"
                    name="jobZipCode"
                    value={formik.values.jobZipCode}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.jobZipCode &&
                      Boolean(formik.errors.jobZipCode)
                    }
                    helperText={
                      formik.touched.jobZipCode && formik.errors.jobZipCode
                    }
                  />
                </Box>
              </Box>
            </Box>
          </TabPanel>

          <DialogActions className="dialog-actions">
            <Button
              variant="outlined"
              onClick={onClose}
              className="cancel-button"
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              type="submit"
              className="save-button"
              disabled={isUploading}
            >
              Save
            </Button>
          </DialogActions>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default JobDialog;

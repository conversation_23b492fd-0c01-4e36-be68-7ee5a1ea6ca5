"use client";
import {
  <PERSON>,
  Divider,
  Avatar,
  Typography,
  Paper,
  IconButton,
  Button,
} from "@mui/material";
import React, { useState, Suspense, useCallback } from "react";
import "./Jobs.scss";
import {
  HomeOutlined,
  EditNote,
  Delete,
  ControlPoint,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  useGridApiRef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import Link from "next/link";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";
import useFetchJobSectionData from "../../hooks/jobs/useFetchJobSectionData";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import { deleteJob, getJobById } from "@/app/services/jobs/jobs.service";
import JobDialog from "./AddEditJob";
import ViewToggleJobs from "@/components/ViewToggleButton/ViewToggleJobs";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";


interface Job {
  id: string;
  jobId: string;
  jobTitle: string;
  jobCategory: string;
  jobCountry: string;
  jobState: string;
  jobCity: string;
  minSalary: number;
  maxSalary: number;
  jobExpiredDate: string;
  profileImage?: string;
  isActive: boolean;
}

function JobsListContent() {
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [loading, setLoading] = useState<boolean>(false);
  const [editLoading, setEditLoading] = useState<boolean>(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [deletingJob, setDeletingJob] = useState<Job | null>(null);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [dialogMode, setDialogMode] = useState<"add" | "edit">("add");
  const [editingJobData, setEditingJobData] = useState<any>(null);
  const [refresh, setRefresh] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const [jobs, total] = useFetchJobSectionData({
    setIsLoading: setLoading,
    refresh,
    limit: pageSize,
    page,
    order: selectedSortBy,
    startDate,
    endDate,
    isActive:
      selectedStatus === "Select Status"
        ? undefined
        : selectedStatus === "Active"
          ? "true"
          : "false",

    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });

  // console.log("Jobs Data:", jobs);
  // console.log("Total Jobs:", total);

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  const apiRef = useGridApiRef();

  const fetchJobs = () => {
    setRefresh(!refresh);
  };

  const handleOpenAddDialog = () => {
    setDialogMode("add");
    setEditingJobData(null);
    setDialogOpen(true);
  };

  const handleEditClick = async (row: Job) => {
    setEditLoading(true);
    try {
      const jobResponse = await getJobById(row.id);
      const jobData = jobResponse.job;
      setEditingJobData(jobData);
      setDialogMode("edit");
      setDialogOpen(true);
    } catch (error) {
      console.error("Error fetching job data:", error);
      toast.error("Failed to load job data");
    } finally {
      setEditLoading(false);
    }
  };

  const handleDeleteClick = (row: Job) => {
    setDeletingJob(row);
    setDeleteDialogOpen(true);
  };

  const handleDelete = async () => {
    if (deletingJob) {
      setLoading(true);
      try {
        await deleteJob(deletingJob.id);
        toast.success("Job deleted successfully");
        setDeleteDialogOpen(false);
        setDeletingJob(null);
        fetchJobs();
      } catch (error) {
        console.error("Error deleting job:", error);
        toast.error("Failed to delete job");
      } finally {
        setLoading(false);
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeletingJob(null);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingJobData(null);
  };

  const rows: GridRowsProp = jobs.map((job: any) => ({
    id: job._id,
    jobId: job.jobId,
    jobTitle: job.jobTitle,
    jobCategory: job.jobCategory,
    location: `${job.jobCity}, ${job.jobState}, ${job.jobCountry}`,
    salaryRange: `${job.minSalary.toLocaleString()} - ${job.maxSalary.toLocaleString()} /month`,
    jobExpiredDate: job.jobExpiredDate,
    profileImage: job.profileImage,
    isActive: job.isActive,
    applicants: 25,
  }));

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Jobs", href: "" },
    { label: "JobsList" },
  ];

  const columns: GridColDef[] = [
    {
      field: "jobId",
      headerName: "Job ID",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#111827" }}>
          {params.row.jobId}
        </Typography>
      ),
    },
    {
      field: "jobTitle",
      headerName: "Job Title",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={params.row.profileImage}
            sx={{ width: 24, height: 24 }}
          />
          <Box sx={{ display: "flex", flexDirection: "column" }}>
            <Typography sx={{ fontSize: "14px", color: "#111827" }}>
              <Link href="#">{params.row.jobTitle}</Link>
            </Typography>
            <Typography sx={{ fontSize: "12px", color: "#6B7280" }}>
              {params.row.applicants} Applicants
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: "jobCategory",
      headerName: "Category",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.jobCategory}
        </Typography>
      ),
    },
    {
      field: "location",
      headerName: "Location",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.location}
        </Typography>
      ),
    },
    {
      field: "salaryRange",
      headerName: "Salary Range",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.salaryRange}
        </Typography>
      ),
    },
    {
      field: "jobExpiredDate",
      headerName: "Posted Date",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {new Date(params.row.jobExpiredDate).toLocaleDateString()}
        </Typography>
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row as Job)}
            disabled={editLoading || loading}
          >
            <EditNote sx={{ width: "16px", height: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row as Job)}
            disabled={editLoading || loading}
          >
            <Delete sx={{ width: "16px", height: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Box className="jobs-container">
      {loading && <Loader loading={loading} />}
      <Box className="content">
        <Box className="jobs-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Jobs List</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <ViewToggleJobs />
            <Button
              className="add-job"
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={handleOpenAddDialog}
            >
              <ControlPoint sx={{ width: "16px", height: "16px" }} />
              Post Job
            </Button>
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 400,
              maxHeight: "calc(100vh - 200px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Jobs List</Typography>
              <PolicyFilters
                departments={[]}
                selectedDepartment={""}
                setSelectedDepartment={() => { }}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={new Date()}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                showDesignationFilter={false}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => { }}
                selectedLeaveType={""}
                setSelectedLeaveType={() => { }}
                selectedPriority=""
                setSelectedPriority={() => { }}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              apiRef={apiRef}
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              initialState={{
                pagination: { paginationModel: { pageSize: pageSize } },
              }}
              paginationModel={{ page: page - 1, pageSize: pageSize }}
              onPaginationModelChange={(model) => {
                if (model.pageSize !== pageSize) {
                  setPageSize(model.pageSize);
                  setPage(1);
                } else {
                  setPage(model.page + 1);
                }
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
        <JobDialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          refreshList={fetchJobs}
          jobData={editingJobData}
          mode={dialogMode}
        />

        <DeleteConfirmationDialog
          open={deleteDialogOpen}
          title="Delete Job"
          message={`Are you sure you want to delete the job "${deletingJob?.jobTitle || ""}"? This action cannot be undone.`}
          onClose={handleCloseDeleteDialog}
          onConfirm={handleDelete}
        />
      </Box>
    </Box>
  );
}

function JobsListPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <JobsListContent />
    </Suspense>
  );
}

export default JobsListPage;

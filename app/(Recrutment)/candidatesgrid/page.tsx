"use client";
import { Box, Typography } from "@mui/material";
import React, { useState, useEffect, Suspense } from "react";
import "./candidates.scss";
import { HomeOutlined } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import Loader from "@/components/Loader/Loader";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import ProfileCardCandidate from "@/components/ProfileCardCandidate/ProfileCardCandidate";
import ViewToggleCandidates from "@/components/ViewToggleButton/ViewToggleCandidates";
import useFetchReferrals from "@/app/hooks/referrals/useFetchReferrals";

function CandidatesGridContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [limit] = useState(10);
  // Filter states
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDesignation, setSelectedDesignation] =
    useState<string>("Designation");

  // Use the hook to fetch referral data
  const [referralData, total] = useFetchReferrals({
    setIsLoading,
    refresh,
    limit,
    page,
    order: selectedSortBy === "Sort By" ? undefined : selectedSortBy,
    startDate,
    endDate,
    isActive: selectedStatus === "Select Status" ? undefined : selectedStatus,
  });

  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  useEffect(() => {
    setIsLoading(false);
  }, []);

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Administration", href: "" },
    { label: "Candidates Grid" },
  ];

  return (
    <Box className="candidate-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="candidate-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Candidates</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>

          <Box
            className="add-policy"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <ViewToggleCandidates />
          </Box>
        </Box>

        {/* DataGrid Container with Filters */}
        <Box
          className="DataGrid-container"
          sx={{
            display: "flex",
            flexDirection: "column",
            maxHeight: "calc(100vh - 200px)",
            backgroundColor: "#fff",
          }}
        >
          <Box className="DataGrid-header">
            <Typography variant="h5">Candidates Grid</Typography>
            <PolicyFilters
              designations={[]}
              departments={[]}
              selectedDepartment=""
              setSelectedDepartment={() => {}}
              selectedDesignation={selectedDesignation}
              setSelectedDesignation={setSelectedDesignation}
              selectedSortBy={selectedSortBy}
              setSelectedSortBy={setSelectedSortBy}
              selectedDateRange={selectedDateRange}
              setSelectedDateRange={setSelectedDateRange}
              setStartDate={setStartDate}
              setEndDate={setEndDate}
              selectedStatus={selectedStatus}
              setSelectedStatus={setSelectedStatus}
              setPage={setPage}
              currentDate={currentDate}
              showDateRangeFilter={true}
              showDepartmentFilter={false}
              showStatusFilter={true}
              showSortByFilter={true}
              showDesignationFilter={false}
              selectedLeaveType="Leave Type"
              setSelectedLeaveType={() => {}}
              showLeaveTypeFilter={false}
              selectedPriority=""
              setSelectedPriority={() => {}}
            />
          </Box>
        </Box>

        {/* Profile Cards */}
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))",
            gap: "24px",
          }}
        >
          {referralData.map((candidate) => (
            <ProfileCardCandidate
              key={candidate.refferalId}
              name={`${candidate.firstName} ${candidate.lastName}`}
              candidateId={candidate.refferalId}
              email={candidate.email}
              role={candidate.jobId?.jobTitle || "-"}
              date={new Date(candidate.createdAt).toLocaleDateString()}
              status={candidate.status}
              imageUrl={candidate.profileImage}
            />
          ))}
        </Box>
      </Box>
    </Box>
  );
}

function CandidatesGridPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CandidatesGridContent />
    </Suspense>
  );
}

export default CandidatesGridPage;
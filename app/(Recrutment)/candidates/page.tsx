"use client";

import {
  Box,
  IconButton,
  Divider,
  Typography,
  Paper,
  Avatar,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Select,
  MenuItem,
  SelectChangeEvent,
} from "@mui/material";
import React, { useCallback, useState } from "react";
import "./Candidates.scss";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import {
  EditNote,
  Delete,
  HomeOutlined,
  ControlPoint,
  FileDownloadOutlined,
  Description,
  KeyboardArrowDown,
} from "@mui/icons-material";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import useFetchReferrals from "@/app/hooks/referrals/useFetchReferrals";
import Image from "next/image";
import { deleteReferral, changeReferralStatus } from "@/app/services/referrals/referrals.service"; // Add changeReferralStatus
import ViewToggleCandidates from "@/components/ViewToggleButton/ViewToggleCandidates";
import { saveAs } from "file-saver";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

interface Referral {
  _id: string;
  empId: {
    _id: string;
    firstName: string;
    lastName: string;
    avatar: string;
    departmentId: { _id: string; departmentName: string };
    isActive: boolean;
    isDeleted: boolean;
  };
  jobId: {
    _id: string;
    jobTitle: string;
    profileImage: string;
    isActive: boolean;
    isDeleted: boolean;
  };
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  profileImage: string;
  linkedinProfile: string;
  resume: string;
  reasonReferred: string;
  refferalBonus: number;
  status: string;
  isActive: boolean;
  isDeleted: boolean;
  refferalId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface ReferralRow {
  id: string;
  candidate: {
    name: string;
    email: string;
    avatar: string;
  };
  appliedRole: string;
  phone: string;
  appliedDate: string;
  resume: string;
  status: string;
}

export default function Referrals() {
  const [loading, setIsLoading] = useState<boolean>(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const relativelimit = 10;
  const [limit, setLimit] = useState(relativelimit);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<"add" | "edit">("add");
  const [selectedReferral, setSelectedReferral] = useState<Referral | null>(null);
  const [resumeDialogOpen, setResumeDialogOpen] = useState(false);
  const [selectedResumeUrl, setSelectedResumeUrl] = useState<string>("");

  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Status");

  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const [referralData, total] = useFetchReferrals({
    setIsLoading,
    refresh,
    limit,
    page,
    order: selectedSortBy === "Sort By" ? undefined : selectedSortBy,
    startDate,
    endDate,
    isActive: selectedStatus === "Status" ? undefined : selectedStatus,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });

  const { roles } = useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const isEmployee = roles.includes("Employee");

  const rows: GridRowsProp = referralData.map((referral: Referral) => ({
    id: referral._id,
    candidate: {
      name: `${referral.firstName} ${referral.lastName}`,
      email: referral.email,
      avatar: referral.profileImage || "",
    },
    appliedRole: referral.jobId.jobTitle,
    phone: referral.phone,
    appliedDate: new Date(referral.createdAt).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }),
    resume: referral.resume,
    status: referral.status,
  }));

  const handleSearchChange = useCallback(
      (event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(event.target.value);
      },
      []
    );

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [referralToDelete, setReferralToDelete] = useState<string | null>(null);

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Administration", href: "" },
    { label: "Candidate List" },
  ];

  const handleViewResume = (resumeUrl: string) => {
    setSelectedResumeUrl(resumeUrl);
    setResumeDialogOpen(true);
  };

  const handleCloseResumeDialog = () => {
    setResumeDialogOpen(false);
    setSelectedResumeUrl("");
  };

  const handleDownloadResume = async (resumeUrl: string, candidateName: string) => {
    try {
      const response = await fetch(resumeUrl);
      const blob = await response.blob();
      saveAs(blob, `${candidateName}_resume.pdf`);
    } catch (error) {
      console.error("Failed to download resume:", error);
      toast.error("Failed to download resume. Please try again.", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    }
  };

  const handleStatusSelect = async (row: ReferralRow, newStatus: string) => {
    try {
      await changeReferralStatus(row.id, { status: newStatus });
      toast.success("Status updated successfully!");
      setRefresh(!refresh);
    } catch (error) {
      console.error("Failed to update status:", error);
      toast.error("Failed to update status. Please try again.");
    }
  };

  const getDisplayStatus = (status: string) => {
    return status === "Application Received" ? "App Received" : status;
  };

  const columns: GridColDef<ReferralRow>[] = [
    {
      field: "candidate",
      headerName: "Candidate",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={params.row.candidate.avatar}
            alt="avatar"
            sx={{ width: 30, height: 30, borderRadius: "50%" }}
          />
          <Box sx={{ display: "flex", flexDirection: "column" }}>
            <Typography
              sx={{ fontSize: "14px", color: "#111827", fontWeight: 500 }}
            >
              {params.row.candidate.name}
            </Typography>
            <Typography sx={{ color: "#6B7280", fontSize: "14px" }}>
              {params.row.candidate.email}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: "appliedRole",
      headerName: "Applied Role",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography
          sx={{ fontSize: "14px", color: "#111827", fontWeight: 500 }}
        >
          {params.row.appliedRole}
        </Typography>
      ),
    },
    {
      field: "phone",
      headerName: "Phone",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.phone}
        </Typography>
      ),
    },
    {
      field: "appliedDate",
      headerName: "Applied Date",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.appliedDate}
        </Typography>
      ),
    },
    {
      field: "resume",
      headerName: "Resume",
      editable: false,
      flex: 0.5,
      renderCell: (params) => (
        <>
          <IconButton
            onClick={() => handleViewResume(params.row.resume)}
            sx={{ color: "#6B7280" }}
          >
            <Description sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            onClick={() => handleDownloadResume(params.row.resume, params.row.candidate.name)}
            sx={{ color: "#6B7280" }}
          >
            <FileDownloadOutlined sx={{ fontSize: "16px" }} />
          </IconButton>
        </>
      ),
    },
    {
      field: "status",
      headerName: "Status",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const status = params.row.status;
        const displayStatus = getDisplayStatus(status);
        const getStatusColor = (status: string) => {
          switch (status) {
            case "Sent":
            case "Application Received":
              return "#A855F7";
            case "Scheduled":
              return "#FD3995";
            case "Interviewed":
              return "#1B84FF";
            case "Offered":
              return "#FFC107";
            case "Hired":
              return "#03C95A";
            case "Rejected":
              return "#EF4444";
            default:
              return "#000000";
          }
        };

        return (
          <Box sx={{ display: "flex", gap: "3px", alignItems: "center" }}>
            <Select
              value={status}
              onChange={(event: SelectChangeEvent<string>) =>
                handleStatusSelect(params.row, event.target.value)
              }
              sx={{
                minWidth: "85px",
                width: "140px",
                height: "30px",
                borderRadius: "4px",
                "& .MuiSelect-select": {
                  padding: "2px 5px",
                  display: "flex",
                  alignItems: "center",
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  border: `1px solid ${getStatusColor(status)}`,
                },
                "&:hover .MuiOutlinedInput-notchedOutline": {
                  border: `1px solid ${getStatusColor(status)}`,
                },
                "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                  border: `1px solid ${getStatusColor(status)}`,
                },
                "& .MuiSelect-icon": {
                  display: "none",
                },
              }}
              renderValue={(value: string) => (
                <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                  <Box
                    sx={{
                      width: "8px",
                      height: "8px",
                      borderRadius: "50%",
                      backgroundColor: getStatusColor(value),
                    }}
                  />
                  <Typography sx={{ fontSize: "14px", color: getStatusColor(value) }}>
                    {getDisplayStatus(value)} <KeyboardArrowDown />
                  </Typography>
                </Box>
              )}
              disabled={isEmployee}
            >
              {[
                "Sent",
                "Application Received",
                "Scheduled",
                "Interviewed",
                "Offered",
                "Rejected",
                "Hired",
              ].map((statusOption) => (
                <MenuItem key={statusOption} value={statusOption}>
                  <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                    <Box
                      sx={{
                        width: "8px",
                        height: "8px",
                        borderRadius: "50%",
                        backgroundColor: getStatusColor(statusOption),
                      }}
                    />
                    <Typography sx={{ fontSize: "14px", color: getStatusColor(statusOption) }}>
                      {getDisplayStatus(statusOption)}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 0.5,
      renderCell: (params: { row: ReferralRow }) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
            disabled={isEmployee}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handleDeleteClick = (id: string) => {
    if (isEmployee) return;
    setReferralToDelete(id);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (referralToDelete !== null) {
      try {
        await deleteReferral(referralToDelete);
        setDeleteDialogOpen(false);
        setReferralToDelete(null);
        setRefresh(!refresh);
        toast.success("Referral deleted successfully!");
      } catch (error) {
        console.error("Failed to delete referral:", error);
        toast.error("Failed to delete referral. Please try again.", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setReferralToDelete(null);
  };

  return (
    <Box className="referrals-container">
      {loading && <Loader loading={loading} />}
      <Box className="content">
        <Box className="referrals-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Candidate List</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <ViewToggleCandidates />
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: "550px",
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Candidate List</Typography>
              <PolicyFilters
                departments={[]}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showStatusFilter={true}
                showDepartmentFilter={false}
                showSortByFilter={true}
                showDateRangeFilter={true}
                showDesignationFilter={false}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => { }}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => { }}
                showLeaveTypeFilter={false}
                selectedDepartment=""
                setSelectedDepartment={() => { }}
                selectedPriority=""
                setSelectedPriority={() => { }}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns as readonly GridColDef[]}
              rowCount={total}
              paginationMode="server"
              initialState={{ pagination: { paginationModel: { pageSize } } }}
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                handlePageChange(model.page);
                handlePageSizeChange(model.pageSize);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      <Dialog
        open={resumeDialogOpen}
        onClose={handleCloseResumeDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ padding: "0px" }}>Resume Preview</DialogTitle>
        {/* <DialogContent> */}
        <iframe
          src={selectedResumeUrl}
          style={{ width: "100%", height: "100vh" }}
        />
        {/* </DialogContent> */}
        {/* <DialogActions>
          <Button onClick={handleCloseResumeDialog}>Close</Button>
        </DialogActions> */}
      </Dialog>

      <DeleteConfirmationDialog
        title="Delete Referral"
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this referral? This action cannot be undone."
      />
    </Box>
  );
}
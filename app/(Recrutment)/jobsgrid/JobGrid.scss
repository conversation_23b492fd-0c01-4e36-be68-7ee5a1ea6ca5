.jobs-container {
  .content {
    padding: 24px;

    .jobs-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;

      .breadcrumbs-box {
        h2 {
          font-size: 24px;
          font-weight: 700;
          color: #202C4B;
        }
      }

      .add-policy {
        display: flex;
        gap: 8px;
        align-items: center;

        .add-project-btn {
          display: flex;
          gap: 8px;
          background-color: #F26522;
          border-color: #F26522;
          color: #FFF;
          font-weight: 400;
          font-size: 14px;
          border-radius: 5px;
          text-transform: none;
          padding: 8px 13.6px;

          &:hover {
            background-color: #E55A1B;
          }
        }
      }
    }

    .header-cards {
      display: flex;
      width: 100%;
      gap: 25px;
    }

    .Project-DataGrid-container {
      border: 1px solid #E5E7EB;
      margin-top: 16px;

      .DataGrid-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        background-color: #FFFFFF;

        h5 {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }

    .Profile-Cards {
      display: flex;
      flex-wrap: wrap;
      gap: 40px;
      margin-top: 24px;
      // justify-content: center;
    }
  }
}
"use client";
import { Box, Typography } from "@mui/material";
import React, { useState, useEffect, Suspense } from "react";
import "./JobGrid.scss";
import { HomeOutlined, ControlPoint } from "@mui/icons-material";
import { Button } from "@mui/material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import ViewToggleJobs from "@/components/ViewToggleButton/ViewToggleJobs";
import { getAllJob } from "@/app/services/jobs/jobs.service";
import JobCard from "@/components/JobCard/JobCard";
import JobDialog from "../jobs/AddEditJob";

function JobGridContent() {
  const [refresh, setRefresh] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [jobs, setJobs] = useState<any[]>([]);

  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Jobs" },
  ];

  useEffect(() => {
    const fetchJobs = async () => {
      setLoading(true);
      try {
        const isActive =
          selectedStatus === "Select Status"
            ? undefined
            : selectedStatus === "Active"
              ? "true"
              : "false";
        const order =
          selectedSortBy === "Sort By"
            ? undefined
            : selectedSortBy.toLowerCase();
        const data = await getAllJob(order, startDate, endDate, isActive);
        setJobs(data.jobs.results);
      } catch (error) {
        toast.error("Failed to fetch jobs");
      } finally {
        setLoading(false);
      }
    };
    fetchJobs();
  }, [refresh, selectedSortBy, startDate, endDate, selectedStatus]);

  // Function to refresh job list after adding a job
  const handleRefreshList = () => {
    setRefresh(!refresh);
  };

  return (
    <Box className="jobs-container">
      <Box className="content">
        <Box className="jobs-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Jobs</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box className="add-policy">
            <ViewToggleJobs />
            <Button
              variant="contained"
              className="add-project-btn"
              onClick={() => setOpenModal(true)}
              disabled={loading}
            >
              <ControlPoint sx={{ fontSize: "16px" }} />
              Post Job
            </Button>
          </Box>
        </Box>
        <Box className="Project-DataGrid-container">
          <Box className="DataGrid-header">
            <Typography variant="h5">Job Grid</Typography>
            <PolicyFilters
              departments={[]}
              designations={[]}
              selectedDepartment=""
              setSelectedDepartment={() => {}}
              selectedDesignation=""
              setSelectedDesignation={() => {}}
              selectedSortBy={selectedSortBy}
              setSelectedSortBy={setSelectedSortBy}
              selectedDateRange={selectedDateRange}
              setSelectedDateRange={setSelectedDateRange}
              setStartDate={setStartDate}
              setEndDate={setEndDate}
              selectedStatus={selectedStatus}
              setSelectedStatus={setSelectedStatus}
              currentDate={currentDate}
              showDateRangeFilter={true}
              showDepartmentFilter={false}
              showStatusFilter={true}
              showSortByFilter={true}
              showDesignationFilter={false}
              selectedLeaveType="Leave Type"
              setSelectedLeaveType={() => {}}
              showLeaveTypeFilter={false}
              setPage={() => {}}
              selectedPriority=""
              setSelectedPriority={() => {}}
            />
          </Box>
        </Box>
        <Box
          className="Profile-Cards"
          sx={{ display: "flex", flexWrap: "wrap", gap: 2 }}
        >
          {loading ? (
            <Loader loading={loading} />
          ) : jobs.length > 0 ? (
            jobs.map((job) => (
              <JobCard
                key={job._id}
                title={job.jobTitle}
                applicants={job.applicants?.length || 0}
                location={`${job.jobCity}, ${job.jobCountry}`}
                salaryMin={job.minSalary}
                salaryMax={job.maxSalary}
                salaryPeriod="year"
                experience={`${job.experienceTime} years of experience`}
                tags={[job.jobType, job.jobLevel]}
                filled={job.applicants?.length || 0}
                total={job.applicants?.length || 25}
                logoUrl={job.profileImage}
              />
            ))
          ) : (
            <Typography variant="body1" sx={{ m: 2 }}>
              No Jobs found.
            </Typography>
          )}
        </Box>
      </Box>
      {/* Add JobDialog component */}
      <JobDialog
        open={openModal}
        onClose={() => setOpenModal(false)}
        refreshList={handleRefreshList}
        mode="add"
      />
    </Box>
  );
}

function JobGridPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <JobGridContent />
    </Suspense>
  );
}

export default JobGridPage;

"use client";

import {
  <PERSON>,
  IconButton,
  Divider,
  Typo<PERSON>,
  Paper,
  Avatar,
  Button,
} from "@mui/material";
import React, { useState, useEffect, useCallback } from "react";
import "./referrals.scss";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import {
  EditNote,
  Delete,
  HomeOutlined,
  ControlPoint,
} from "@mui/icons-material";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import useFetchReferrals from "@/app/hooks/referrals/useFetchReferrals";
import Image from "next/image";
import ReferralDialog from "./AddEditReferrals";
import { deleteReferral } from "@/app/services/referrals/referrals.service";
import useAuthStore from "@/store/authStore";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

interface Referral {
  _id: string;
  empId: {
    _id: string;
    firstName: string;
    lastName: string;
    avatar: string;
    departmentId: { _id: string; departmentName: string };
    isActive: boolean;
    isDeleted: boolean;
  };
  jobId: {
    _id: string;
    jobTitle: string;
    profileImage: string;
    isActive: boolean;
    isDeleted: boolean;
  };
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  profileImage: string;
  linkedinProfile: string;
  resume: string;
  reasonReferred: string;
  refferalBonus: number;
  status: string;
  isActive: boolean;
  isDeleted: boolean;
  refferalId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

interface ReferralRow {
  id: string; // This will now map to _id
  referralId: string; // This will display refferalId
  referrerName: string;
  jobReferred: {
    title: string;
    icon: string;
  };
  refereeName: string;
  refferalBonus: number;
  referrer: {
    name: string;
    avatar: string;
    department: string;
  };
  referee: {
    name: string;
    avatar: string;
    email: string;
  };
}



export default function Referrals() {
  const [loading, setIsLoading] = useState<boolean>(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<"add" | "edit">("add");
  const [selectedReferral, setSelectedReferral] = useState<Referral | null>(
    null
  );
  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Status");

  const [referralData, total] = useFetchReferrals({
    setIsLoading,
    refresh,
    limit,
    page,
    order: selectedSortBy === "Sort By" ? undefined : selectedSortBy,
    startDate,
    endDate,
    isActive: selectedStatus === "Status" ? undefined : selectedStatus,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  const { roles } = useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const isEmployee = roles.includes("Employee");

  const rows: GridRowsProp = referralData.map((referral: Referral) => ({
    id: referral._id,
    referralId: referral.refferalId,
    referrerName: `${referral.empId.firstName} ${referral.empId.lastName}`,
    jobReferred: {
      title: referral.jobId.jobTitle,
      icon: referral.jobId.profileImage,
    },
    refereeName: `${referral.firstName} ${referral.lastName}`,
    // refferalBonus: referral.refferalBonus,
    referrer: {
      name: `${referral.empId.firstName} ${referral.empId.lastName}`,
      avatar: referral.empId.avatar || "",
      department: referral.empId.departmentId.departmentName,
    },
    referee: {
      name: `${referral.firstName} ${referral.lastName}`,
      avatar: referral.profileImage || "",
      email: referral.email,
    },
  }));

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [referralToDelete, setReferralToDelete] = useState<string | null>(null);

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Administration", href: "" },
    { label: "Referrals" },
  ];

  const columns: GridColDef<ReferralRow>[] = [
    {
      field: "referralId",
      headerName: "Referral ID",
      flex: 0.5,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.referralId}
        </Typography>
      ),
    },
    {
      field: "referrerName",
      headerName: "Referrer Name",
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={params.row.referrer.avatar}
            alt="avatar"
            sx={{ width: 30, height: 30, borderRadius: "50%" }}
          />
          <Box sx={{ display: "flex", flexDirection: "column" }}>
            <Typography
              sx={{ fontSize: "14px", color: "#111827", fontWeight: 500 }}
            >
              {params.row.referrerName}
            </Typography>
            <Typography sx={{ color: "#6B7280", fontSize: "14px" }}>
              {params.row.referrer.department}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: "jobReferred",
      headerName: "Job Referred",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Image
            src={params.row.jobReferred.icon}
            alt="job icon"
            width={20}
            height={20}
          />
          <Typography
            sx={{ fontSize: "14px", color: "#111827", fontWeight: 500 }}
          >
            {params.row.jobReferred.title}
          </Typography>
        </Box>
      ),
    },
    {
      field: "refereeName",
      headerName: "Referee Name",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={params.row.referee.avatar}
            alt="avatar"
            sx={{ width: 30, height: 30, borderRadius: "50%" }}
          />
          <Box sx={{ display: "flex", flexDirection: "column" }}>
            <Typography
              sx={{ fontSize: "14px", color: "#111827", fontWeight: 500 }}
            >
              {params.row.refereeName}
            </Typography>
            <Typography sx={{ color: "#6B7280", fontSize: "14px" }}>
              {params.row.referee.email}
            </Typography>
          </Box>
        </Box>
      ),
    },
    // {
    //   field: "refferalBonus",
    //   headerName: "Referral Bonus",
    //   editable: false,
    //   flex: 1,
    //   renderCell: (params) => (
    //     <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
    //       {params.row.refferalBonus}
    //     </Typography>
    //   ),
    // },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 0.5,
      renderCell: (params: { row: ReferralRow }) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
            disabled={isEmployee}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
            disabled={isEmployee}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handleAddClick = () => {
    // if (isEmployee) return;
    setDialogMode("add");
    setSelectedReferral(null);
    setDialogOpen(true);
  };

  const handleEditClick = (id: string) => {
    // if (isEmployee) return;
    const referral = referralData.find((r: Referral) => r._id === id); // Changed to use _id
    if (referral) {
      setSelectedReferral(referral);
      setDialogMode("edit");
      setDialogOpen(true);
    } else {
      toast.error("Referral not found.");
    }
  };

  const handleDeleteClick = (id: string) => {
    if (isEmployee) return;
    setReferralToDelete(id); // Use _id for deletion
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (referralToDelete !== null) {
      try {
        await deleteReferral(referralToDelete); // Pass _id to deleteReferral
        setDeleteDialogOpen(false);
        setReferralToDelete(null);
        setRefresh(!refresh);
        toast.success("Referral deleted successfully!");
      } catch (error) {
        console.error("Failed to delete referral:", error);
        toast.error("Failed to delete referral. Please try again.", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setReferralToDelete(null);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedReferral(null);
  };

  const handleRefreshList = () => {
    setRefresh(!refresh);
  };

  return (
    <Box className="referrals-container">
      {loading && <Loader loading={loading} />}
      <Box className="content">
        <Box className="referrals-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Referrals</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={handleAddClick}
            >
              <ControlPoint sx={{ fontSize: "14px" }} />
              Add Referrals
            </Button>
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: "550px",
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Referrals List</Typography>
              <PolicyFilters
                departments={[]}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showStatusFilter={true}
                showDepartmentFilter={false}
                showSortByFilter={true}
                showDateRangeFilter={true}
                showDesignationFilter={false}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => { }}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => { }}
                showLeaveTypeFilter={false}
                selectedDepartment=""
                setSelectedDepartment={() => { }}
                selectedPriority=""
                setSelectedPriority={() => { }}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns as readonly GridColDef[]}
              rowCount={total}
              paginationMode="server"
              initialState={{ pagination: { paginationModel: { pageSize } } }}
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                handlePageChange(model.page);
                handlePageSizeChange(model.pageSize);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      <DeleteConfirmationDialog
        title="Delete Referral"
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this referral? This action cannot be undone."
      />

      <ReferralDialog
        open={dialogOpen}
        onClose={handleDialogClose}
        refreshList={handleRefreshList}
        referralData={selectedReferral}
        mode={dialogMode}
      />
    </Box>
  );
}

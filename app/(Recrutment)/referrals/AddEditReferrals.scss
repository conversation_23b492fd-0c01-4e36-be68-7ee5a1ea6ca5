.referral-dialog {
  .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px !important;

    .title-container {
      display: flex;
      align-items: center;
      gap: 12px;

      .dialog-title-text {
        font-size: 20px;
        color: #111827;
        font-weight: 600;
      }

      .referral-id {
        color: #212529;
        font-weight: normal;
        font-size: 14px;
      }
    }
  }

  .dialog-content {
    padding: 0px !important;
    max-height: 90vh;
    overflow-y: auto;

    .referral-form {
      display: flex;
      flex-direction: column;
      padding: 16px;

      .logo-upload-section {
        display: flex;
        align-items: center;
        padding: 16px;
        background-color: #f8f9fa;
        gap: 16px;
        margin-bottom: 1.5rem !important;

        .image-placeholder {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background-color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #e0e0e0;

          .image-preview {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
          }
        }

        .upload-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 4px;

          .upload-title {
            font-size: 14px;
            font-weight: 600;
            color: #111827;
          }

          .upload-hint {
            font-size: 12px;
            color: #6b7280;
          }

          .upload-buttons {
            display: flex;
            align-items: center;
            gap: 8px;

            .upload-btn {
              background-color: #f26522;
              color: #fff;
              text-transform: none;
              font-size: 14px;
              padding: 6px 12px !important;
              border-radius: 4px;
              min-width: 90px;
              display: flex;
              align-items: center;
              justify-content: center;

              &:hover {
                background-color: #e55a1b;
              }

              &:disabled {
                background-color: #ccc;
                color: #666;
              }
            }

            .cancel-btn {
              color: #000;
              border: 1px solid #e0e0e0 !important;
              text-transform: none;
              font-size: 14px;
              padding: 6px 12px !important;
              border-radius: 4px;

              &:hover {
                background-color: rgba(242, 101, 34, 0.1);
                color: #e55a1b;
              }

              &:disabled {
                color: #ccc;
                border-color: #ccc !important;
              }
            }
          }
        }
      }

      .upload-files-section {
        margin-bottom: 16px;

        .file-upload-btn {
          display: flex !important;
          flex-direction: row !important;
          align-items: center !important;
          justify-content: flex-start !important;
          gap: 8px !important;
          padding: 6px 16px !important;
          margin: 0 !important;
          border-color: #e0e0e0 !important;
          color: #6b7280 !important;
          text-transform: none !important;
          font-size: 14px !important;

          .MuiButton-startIcon {
            margin: 0 !important;
            display: flex !important;
          }

          &:hover {
            background-color: rgba(0, 0, 0, 0.04) !important;
            border-color: #d0d0d0 !important;
          }

          &:disabled {
            color: #ccc !important;
            border-color: #e0e0e0 !important;
          }
        }
      }

      .uploaded-files-section {
        margin-top: 16px;

        .file-previews {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
        }

        .file-preview-item {
          position: relative;
          width: 100px;
          height: 100px;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f9f9f9;

          .file-preview-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 8px;
            width: 100%;
            height: 100%;
            gap: 4px;

            .file-icon {
              font-size: 24px;
              color: #6b7280;
            }

            .view-file-btn {
              color: #f26522;
              text-transform: none;
              font-size: 12px;
              padding: 2px 8px;

              &:hover {
                background-color: rgba(242, 101, 34, 0.1);
              }
            }
          }

          .remove-file-btn {
            position: absolute;
            top: 4px;
            right: 4px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 2px;

            &:hover {
              background-color: rgba(0, 0, 0, 0.7);
            }

            .MuiSvgIcon-root {
              font-size: 16px;
            }
          }
        }
      }

      .two-column-fields {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .half-width {
          flex: 1;
          // min-width: 500px;
        }
      }

      .form-field {
        margin-bottom: 16px;
        width: 100%;

        .required {
          color: red;
        }

        .MuiTypography-subtitle1 {
          font-size: 14px;
          font-weight: 500;
          color: #111827;
        }

        .MuiTextField-root,
        .MuiFormControl-root {
          .MuiInputBase-root {
            border-radius: 4px;
            background-color: #fff;

            .MuiSelect-select {
              padding: 10px 14px;
            }
          }

          .MuiOutlinedInput-notchedOutline {
            border-color: #e5e7eb;
          }

          &:hover .MuiOutlinedInput-notchedOutline {
            border-color: #d1d5db;
          }

          &.Mui-focused .MuiOutlinedInput-notchedOutline {
            border-color: #d1d5db;
          }

          .MuiInputBase-input {
            padding: 10px 14px;
          }
        }
      }
    }
  }

  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
    margin: 0px !important;

    .cancel-button {
      color: #666;
      border-color: #ccc;
      text-transform: none;
      padding: 6px 16px;

      &:hover {
        border-color: #9ca3af;
      }
    }

    .save-button {
      background-color: #f26522;
      text-transform: none;
      padding: 6px 16px;

      &:hover {
        background-color: #e55a1b;
      }
    }
  }
}
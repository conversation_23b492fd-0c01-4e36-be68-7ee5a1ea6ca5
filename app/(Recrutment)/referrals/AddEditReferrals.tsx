"use client";

import type React from "react";
import { useState, useEffect, useRef } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  TextField,
  Button,
  Box,
  Typography,
  FormHelperText,
  DialogActions,
  Switch,
  FormControlLabel,
  MenuItem,
  Select,
  FormControl,
} from "@mui/material";
import {
  Close,
  AttachFile as AttachFileIcon,
  Image as ImageIcon,
} from "@mui/icons-material";
import { toast } from "react-toastify";
import { useFormik } from "formik";
import * as Yup from "yup";
import useUploadMedia from "@/app/hooks/useUploadMedia";
import "./AddEditReferrals.scss";
import {
  addReferral,
  updateReferral,
  getReferralById,
} from "@/app/services/referrals/referrals.service";
import TextEditor from "@/components/TextEditor/TextEditor";
import useAuthStore from "@/store/authStore";
import { getAllJobWithoutPagination } from "@/app/services/jobs/jobs.service";
import Loader from "@/components/Loader/Loader";

interface ReferralDialogProps {
  open: boolean;
  onClose: () => void;
  refreshList?: () => void;
  referralData?: any;
  mode: "add" | "edit";
}

interface Job {
  _id: string;
  jobTitle: string;
  jobId: string;
}

const ReferralDialog: React.FC<ReferralDialogProps> = ({
  open,
  onClose,
  refreshList,
  referralData,
  mode,
}) => {
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [resumePreview, setResumePreview] = useState<string | null>(null);
  const [resumeUrl, setResumeUrl] = useState<string | null>(null);
  const [profileImageFile, setProfileImageFile] = useState<File | null>(null);
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(
    null
  );
  const [profileImageUrl, setProfileImageUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false); // Added loading state
  const [jobs, setJobs] = useState<Job[]>([]);
  const resumeInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  const { uploadMedia } = useUploadMedia();
  const { employeeId: authEmployeeId } = useAuthStore();

  // Fetch jobs when the dialog opens
  useEffect(() => {
    if (open) {
      const fetchJobs = async () => {
        try {
          const response = await getAllJobWithoutPagination();
          const jobList = response.jobs.results.map((job: any) => ({
            _id: job._id,
            jobTitle: job.jobTitle,
            jobId: job.jobId,
          }));
          setJobs(jobList);
        } catch (error) {
          console.error("Error fetching jobs:", error);
          toast.error("Failed to load job list");
        }
      };
      fetchJobs();
    }
  }, [open]);

  useEffect(() => {
    if (open && mode === "edit" && referralData?._id) {
      const fetchReferral = async () => {
        setIsLoading(true);
        try {
          const response = await getReferralById(referralData._id);
          const referral = response.refferal; // Access the 'refferal' object from the response
          formik.setValues({
            jobId: referral.jobId._id || "", // Use jobId._id from the response
            firstName: referral.firstName || "",
            lastName: referral.lastName || "",
            email: referral.email || "",
            phone: referral.phone || "",
            linkedinProfile: referral.linkedinProfile || "",
            resume: referral.resume || "",
            profileImage: referral.profileImage || "",
            reasonReferred: referral.reasonReferred || "",
            refferalBonus: referral.refferalBonus?.toString() || "",
            isActive: referral.isActive ?? true,
          });
          setResumePreview(referral.resume || null);
          setResumeUrl(referral.resume || null);
          setProfileImagePreview(referral.profileImage || null);
          setProfileImageUrl(referral.profileImage || null);
        } catch (error) {
          console.error("Error fetching referral:", error);
          toast.error("Failed to load referral data");
        } finally {
          setIsLoading(false);
        }
      };
      fetchReferral();
    }
  }, [open, mode, referralData]);

  const validationSchema = Yup.object({
    jobId: Yup.string().required("Job ID is required"),
    firstName: Yup.string().required("First Name is required"),
    lastName: Yup.string().required("Last Name is required"),
    email: Yup.string()
      .email("Invalid email address")
      .required("Email is required"),
    phone: Yup.string()
      .matches(/^\+?[1-9]\d{1,14}$/, "Invalid phone number")
      .required("Phone number is required"),
    linkedinProfile: Yup.string()
      .url("Invalid LinkedIn URL")
      .required("LinkedIn Profile is required"),
    resume: Yup.string().required("Resume is required"),
    profileImage: Yup.string().required("Profile image is required"),
    reasonReferred: Yup.string().required("Reason for referral is required"),
    // refferalBonus: Yup.number()
    //   .required("Referral Bonus is required")
    //   .positive("Referral Bonus must be positive"),
    isActive: Yup.boolean().required("Active status is required"),
  });

  const formik = useFormik({
    initialValues: {
      jobId: "",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      linkedinProfile: "",
      resume: "",
      profileImage: "",
      reasonReferred: "",
      refferalBonus: "",
      isActive: true,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        if (!authEmployeeId) {
          toast.error("Employee ID is missing. Please log in again.");
          return;
        }

        const payload = {
          empId: authEmployeeId,
          jobId: values.jobId,
          firstName: values.firstName,
          lastName: values.lastName,
          email: values.email,
          phone: values.phone,
          linkedinProfile: values.linkedinProfile,
          resume: values.resume,
          profileImage: values.profileImage,
          reasonReferred: values.reasonReferred,
          // refferalBonus: parseFloat(values.refferalBonus),
          isActive: values.isActive,
        };

        let response: { message: string };
        if (mode === "edit" && referralData?._id) {
          response = await updateReferral(referralData._id, payload);
          toast.success(response.message);
        } else {
          response = await addReferral(payload);
          toast.success(response.message || "Referral added successfully");
        }

        if (refreshList) {
          refreshList();
        }
        onClose();
      } catch (error: any) {
        console.error(
          `Error ${mode === "edit" ? "updating" : "adding"} referral:`,
          error
        );
        const errorMessage = error.response?.data?.message;
        toast.error(errorMessage);
      }
    },
  });

  useEffect(() => {
    if (open && mode === "add") {
      formik.resetForm();
      setResumeFile(null);
      setResumePreview(null);
      setResumeUrl(null);
      setProfileImageFile(null);
      setProfileImagePreview(null);
      setProfileImageUrl(null);
    }
  }, [open, mode]);

  const handleResumeUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 4 * 1024 * 1024) {
        toast.error("Resume should be below 4 MB");
        return;
      }
      if (!["application/pdf"].includes(file.type)) {
        toast.error("Resume must be a PDF file");
        return;
      }
      setIsUploading(true);
      try {
        const result = await uploadMedia(
          file,
          "referral_resumes",
          1,
          setIsUploading,
          file.name
        );
        const newPreview = URL.createObjectURL(file);
        setResumeFile(file);
        setResumePreview(newPreview);
        setResumeUrl(result.preview);
        formik.setFieldValue("resume", result.preview);
        toast.success("Resume uploaded successfully");
        if (resumeInputRef.current) {
          resumeInputRef.current.value = "";
        }
      } catch (error: any) {
        console.error("Upload error:", error);
        toast.error(error.response?.data?.message || "Failed to upload resume");
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleProfileImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) {
        toast.error("Image should be below 2 MB");
        return;
      }
      if (!["image/png", "image/jpeg"].includes(file.type)) {
        toast.error("Image must be PNG or JPG");
        return;
      }
      setIsUploading(true);
      try {
        const result = await uploadMedia(
          file,
          "referral_images",
          1,
          setIsUploading,
          file.name
        );
        const newPreview = URL.createObjectURL(file);
        setProfileImageFile(file);
        setProfileImagePreview(newPreview);
        setProfileImageUrl(result.preview);
        formik.setFieldValue("profileImage", result.preview);
        toast.success("Profile image uploaded successfully");
        if (imageInputRef.current) {
          imageInputRef.current.value = "";
        }
      } catch (error: any) {
        console.error("Upload error:", error);
        toast.error(
          error.response?.data?.message || "Failed to upload profile image"
        );
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleRemoveResume = () => {
    if (resumePreview?.startsWith("blob:")) {
      URL.revokeObjectURL(resumePreview);
    }
    setResumeFile(null);
    setResumePreview(null);
    setResumeUrl(null);
    formik.setFieldValue("resume", "");
  };

  const handleRemoveProfileImage = () => {
    if (profileImagePreview?.startsWith("blob:")) {
      URL.revokeObjectURL(profileImagePreview);
    }
    setProfileImageFile(null);
    setProfileImagePreview(null);
    setProfileImageUrl(null);
    formik.setFieldValue("profileImage", "");
  };

  return (
    <Dialog
      open={open}
      disableEscapeKeyDown
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
        formik.resetForm();
        setResumeFile(null);
        setResumePreview(null);
        setResumeUrl(null);
        setProfileImageFile(null);
        setProfileImagePreview(null);
        setProfileImageUrl(null);
      }}
      maxWidth="md"
      fullWidth
      className="referral-dialog"
    >
      {isLoading && <Loader loading={isLoading} />}
      <DialogTitle className="dialog-title">
        <Box className="title-container">
          <Typography variant="h6" className="dialog-title-text">
            {mode === "edit" ? "Edit Referral" : "Add Referral"}
          </Typography>
          {mode === "edit" && (
            <Typography className="referral-id">
              Referral ID: {referralData?.refferalId}
            </Typography>
          )}
        </Box>
        <IconButton
          aria-label="close"
          onClick={() => {
            onClose();
            formik.resetForm();
            setResumeFile(null);
            setResumePreview(null);
            setResumeUrl(null);
            setProfileImageFile(null);
            setProfileImagePreview(null);
            setProfileImageUrl(null);
          }}
          sx={{
            backgroundColor: "#6b7280",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": { backgroundColor: "#d55a1d" },
            "& .MuiSvgIcon-root": { fontSize: "14px" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent className="dialog-content">
        <form onSubmit={formik.handleSubmit}>
          <Box className="referral-form">
            <Box className="form-field">
              <Box className="logo-upload-section">
                <Box className="image-placeholder">
                  {profileImagePreview ? (
                    <img
                      src={profileImagePreview}
                      alt="Profile Preview"
                      className="image-preview"
                    />
                  ) : (
                    <ImageIcon sx={{ fontSize: 16, color: "#6b7280" }} />
                  )}
                </Box>
                <Box className="upload-info">
                  <Typography className="upload-title">
                    Profile Image
                  </Typography>
                  <Typography className="upload-hint">
                    PNG or JPG, max 2MB
                  </Typography>
                  <Box className="upload-buttons">
                    <Button
                      variant="contained"
                      component="label"
                      className="upload-btn"
                      disabled={isUploading || !!profileImagePreview}
                    >
                      {isUploading ? "Uploading..." : "Upload"}
                      <input
                        type="file"
                        hidden
                        accept="image/png,image/jpeg"
                        multiple={false}
                        onChange={handleProfileImageUpload}
                        ref={imageInputRef}
                      />
                    </Button>
                    {profileImagePreview && (
                      <Button
                        variant="outlined"
                        className="cancel-btn"
                        onClick={handleRemoveProfileImage}
                        disabled={isUploading}
                      >
                        Cancel
                      </Button>
                    )}
                  </Box>
                  {formik.touched.profileImage &&
                    formik.errors.profileImage && (
                      <FormHelperText error>
                        {formik.errors.profileImage}
                      </FormHelperText>
                    )}
                </Box>
              </Box>
            </Box>

            <Box className="form-field">
              <Typography variant="subtitle1">
                Job Title <span className="required">*</span>
              </Typography>
              <FormControl
                fullWidth
                variant="outlined"
                error={formik.touched.jobId && Boolean(formik.errors.jobId)}
              >
                <Select
                  name="jobId"
                  value={formik.values.jobId}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                >
                  {jobs.map((job) => (
                    <MenuItem key={job._id} value={job._id}>
                      {job.jobTitle}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.jobId && formik.errors.jobId && (
                  <FormHelperText error>{formik.errors.jobId}</FormHelperText>
                )}
              </FormControl>
            </Box>

            <Box className="two-column-fields">
              <Box className="form-field half-width">
                <Typography variant="subtitle1">
                  First Name <span className="required">*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="firstName"
                  value={formik.values.firstName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.firstName && Boolean(formik.errors.firstName)
                  }
                  helperText={
                    formik.touched.firstName && formik.errors.firstName
                  }
                />
              </Box>
              <Box className="form-field half-width">
                <Typography variant="subtitle1">
                  Last Name <span className="required">*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="lastName"
                  value={formik.values.lastName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.lastName && Boolean(formik.errors.lastName)
                  }
                  helperText={formik.touched.lastName && formik.errors.lastName}
                />
              </Box>
            </Box>

            <Box className="two-column-fields">
              <Box className="form-field half-width">
                <Typography variant="subtitle1">
                  Email <span className="required">*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="email"
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.email && Boolean(formik.errors.email)}
                  helperText={formik.touched.email && formik.errors.email}
                />
              </Box>
              <Box className="form-field half-width">
                <Typography variant="subtitle1">
                  Phone <span className="required">*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="phone"
                  value={formik.values.phone}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.phone && Boolean(formik.errors.phone)}
                  helperText={formik.touched.phone && formik.errors.phone}
                />
              </Box>
            </Box>

            <Box className="form-field">
              <Typography variant="subtitle1">
                LinkedIn Profile <span className="required">*</span>
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                name="linkedinProfile"
                value={formik.values.linkedinProfile}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={
                  formik.touched.linkedinProfile &&
                  Boolean(formik.errors.linkedinProfile)
                }
                helperText={
                  formik.touched.linkedinProfile &&
                  formik.errors.linkedinProfile
                }
              />
            </Box>

            <Box className="form-field">
              <Typography variant="subtitle1">
                Upload Resume <span className="required">*</span>
              </Typography>
              <Box className="upload-files-section">
                <Button
                  variant="outlined"
                  component="label"
                  className="file-upload-btn"
                  disabled={isUploading || !!resumePreview}
                  startIcon={<AttachFileIcon />}
                >
                  {isUploading ? "Uploading..." : "Choose File"}
                  <input
                    type="file"
                    hidden
                    accept="application/pdf"
                    multiple={false}
                    onChange={handleResumeUpload}
                    ref={resumeInputRef}
                  />
                </Button>
                {formik.touched.resume && formik.errors.resume && (
                  <FormHelperText error>{formik.errors.resume}</FormHelperText>
                )}
              </Box>

              {resumePreview && (
                <Box className="uploaded-files-section">
                  <Box className="file-previews">
                    <Box className="file-preview-item">
                      <Box className="file-preview-placeholder">
                        <AttachFileIcon className="file-icon" />
                        <Button
                          variant="text"
                          className="view-file-btn"
                          href={resumeUrl || resumePreview}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          View File
                        </Button>
                      </Box>
                      <IconButton
                        size="small"
                        className="remove-file-btn"
                        onClick={handleRemoveResume}
                      >
                        <Close />
                      </IconButton>
                    </Box>
                  </Box>
                </Box>
              )}
            </Box>

            <Box className="form-field">
              <Typography variant="subtitle1">
                Reason for Referral <span className="required">*</span>
              </Typography>
              <TextEditor
                value={formik.values.reasonReferred}
                onChange={(value) =>
                  formik.setFieldValue("reasonReferred", value)
                }
              />
              {formik.touched.reasonReferred &&
                formik.errors.reasonReferred && (
                  <FormHelperText error>
                    {formik.errors.reasonReferred}
                  </FormHelperText>
                )}
            </Box>

            <Box className="two-column-fields">
              {/* <Box className="form-field half-width">
                <Typography variant="subtitle1">
                  Referral Bonus <span className="required">*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="refferalBonus"
                  value={formik.values.refferalBonus}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={
                    formik.touched.refferalBonus &&
                    Boolean(formik.errors.refferalBonus)
                  }
                  helperText={
                    formik.touched.refferalBonus && formik.errors.refferalBonus
                  }
                  type="number"
                />
              </Box> */}
              <Box className="form-field half-width">
                <Typography variant="subtitle1">
                  Active Status <span className="required">*</span>
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      name="isActive"
                      checked={formik.values.isActive}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      sx={{
                        "& .MuiSwitch-switchBase.Mui-checked": {
                          color: "#f26522",
                        },
                        "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":
                          {
                            backgroundColor: "#f26522",
                          },
                      }}
                    />
                  }
                  label={formik.values.isActive ? "Active" : "Inactive"}
                  sx={{ margin: "0 !important" }}
                />
                {formik.touched.isActive && formik.errors.isActive && (
                  <FormHelperText error>
                    {formik.errors.isActive}
                  </FormHelperText>
                )}
              </Box>
            </Box>
          </Box>

          <DialogActions className="dialog-actions">
            <Button
              variant="outlined"
              onClick={() => {
                onClose();
                formik.resetForm();
                setResumeFile(null);
                setResumePreview(null);
                setResumeUrl(null);
                setProfileImageFile(null);
                setProfileImagePreview(null);
                setProfileImageUrl(null);
              }}
              className="cancel-button"
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              type="submit"
              className="save-button"
              disabled={isUploading || isLoading} // Disable button during loading
            >
              Save
            </Button>
          </DialogActions>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ReferralDialog;

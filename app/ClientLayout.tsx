"use client";
import Sidebar from "@/components/Sidebar/Sidebar";
import Navbar from "@/components/Navbar/Navbar";
import { useState } from "react";
import { Box, useMediaQuery } from "@mui/material"; // Add useMediaQuery
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Footer from "@/components/Footer/Footer";

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const isTablet = useMediaQuery("(max-width:1199px)");

  const handleMobileMenuToggle = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  const handleMobileClose = () => {
    setIsMobileOpen(false);
  };

  return (
    <>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      <Box
        sx={{
          marginLeft: isTablet ? 0 : isCollapsed ? "80px" : "251px",
          position: "fixed",
          top: 0,
          right: 0,
          left: 0,
          zIndex: 1100,
          transition: "margin-left 0.3s ease-in-out",
          backgroundColor: "#fff",
        }}
      >
        <Navbar
          onToggleSidebar={setIsCollapsed}
          onMobileMenuToggle={handleMobileMenuToggle}
        />
      </Box>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
          paddingTop: "50px", // Height of navbar
        }}
      >
        <div
          style={{
            display: "flex",
            flex: 1,
            overflow: "hidden",
            position: "relative",
          }}
        >
          <Sidebar
            isCollapsed={isCollapsed}
            isMobileOpen={isMobileOpen}
            onMobileClose={handleMobileClose}
          />
          <main
            style={{
              display: "flex",
              flexDirection: "column",
              flex: 1,
              overflowY: "auto",
              backgroundColor: "#f8f9fa",
              width: "100%",
              minHeight: "calc(100vh - 64px - 56px)", // Account for header and footer heights
            }}
          >
            {children}
          </main>
        </div>
        <Footer isCollapsed={isCollapsed} isTablet={isTablet} />
      </div>
    </>
  );
}

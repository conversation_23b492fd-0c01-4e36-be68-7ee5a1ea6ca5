import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON><PERSON>,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  IconButton,
  Box,
  Autocomplete,
} from "@mui/material";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { Close } from "@mui/icons-material";
import { toast } from "react-toastify";
import { assignAsset } from "@/app/services/assets/asset.service";
import "./AssignAsset.scss";

interface SerialNumberStatus {
  serialNumber: string;
  isAssigned: boolean;
  assignedTo?: {
    _id: string;
    firstName: string;
    lastName: string;
    avatar: string;
    email: string;
    employeeId: string;
  };
}

interface AssignAssetDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmitSuccess: () => void;
  asset: {
    id: string;
    serialNumber: string[];
    serialNumberStatus: SerialNumberStatus[];
  } | null;
  employees: { id: string; name: string }[];
}

interface AssignAssetFormValues {
  empId: string;
  serialNumber: string;
  assignedDate: string;
}

const validationSchema = Yup.object({
  empId: Yup.string().required("Employee is required"),
  serialNumber: Yup.string().required("Serial number is required"),
  assignedDate: Yup.date()
    .required("Assigned date is required")
    .max(new Date(), "Assigned date cannot be in the future")
    .typeError("Please enter a valid date"),
});

const AssignAssetDialog: React.FC<AssignAssetDialogProps> = ({
  open,
  onClose,
  onSubmitSuccess,
  asset,
  employees,
}) => {
  const getTodayDate = () => {
    return new Date().toISOString().split("T")[0];
  };

  const initialValues: AssignAssetFormValues = {
    empId: "",
    serialNumber: "",
    assignedDate: getTodayDate(),
  };

  // Filter unassigned serial numbers
  const unassignedSerialNumbers = asset?.serialNumberStatus
    ? asset.serialNumberStatus
        .filter((status) => !status.isAssigned)
        .map((status) => status.serialNumber)
    : [];

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
      className="dialog-asset"
    >
      <DialogTitle className="dialog-title">
        <Typography
          sx={{ fontWeight: 600, fontSize: "20px", color: "#111827" }}
        >
          Assign Asset
        </Typography>
        <IconButton
          onClick={onClose}
          sx={{
            backgroundColor: "#6b7280",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            "&:hover": { backgroundColor: "#d55a1d" },
            "& .MuiSvgIcon-root": { fontSize: "14px" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={async (values, { setSubmitting, resetForm }) => {
          try {
            if (!asset?.id) {
              throw new Error("No asset selected");
            }
            const payload = {
              assetId: asset.id,
              empId: values.empId,
              serialNumber: values.serialNumber,
              assignedDate: new Date(values.assignedDate).toISOString(),
            };
            const response = await assignAsset(payload);
            if (response.success) {
              resetForm();
              onClose();
              onSubmitSuccess();
              toast.success("Asset assigned successfully");
            } else {
              throw new Error("Failed to assign asset");
            }
          } catch (error) {
            console.error("Error assigning asset:", error);
            toast.error("Failed to assign asset");
          } finally {
            setSubmitting(false);
          }
        }}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          isSubmitting,
          setFieldValue,
        }) => (
          <Form>
            <DialogContent className="dialog-content">
              <Box mb={2}>
                <label>
                  Employee <span className="required">*</span>
                </label>
                <Autocomplete
                  id="employee-autocomplete"
                  options={employees}
                  getOptionLabel={(option) => option.name}
                  value={
                    employees.find((emp) => emp.id === values.empId) || null
                  }
                  onChange={(_, newValue) => {
                    setFieldValue("empId", newValue ? newValue.id : "");
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      error={touched.empId && Boolean(errors.empId)}
                      helperText={touched.empId && errors.empId}
                      InputProps={{
                        ...params.InputProps,
                        sx: { fontSize: "14px" },
                      }}
                    />
                  )}
                  ListboxProps={{
                    style: { maxHeight: "200px" },
                  }}
                />
              </Box>

              <Box display="flex" gap={2}>
                <Box flex={1}>
                  <label>
                    Serial Number <span className="required">*</span>
                  </label>
                  {unassignedSerialNumbers.length > 0 ? (
                    <Autocomplete
                      id="serial-number-autocomplete"
                      options={unassignedSerialNumbers}
                      getOptionLabel={(option) => option}
                      value={values.serialNumber || null}
                      onChange={(_, newValue) => {
                        setFieldValue("serialNumber", newValue || "");
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          fullWidth
                          error={
                            touched.serialNumber && Boolean(errors.serialNumber)
                          }
                          helperText={
                            touched.serialNumber && errors.serialNumber
                          }
                          InputProps={{
                            ...params.InputProps,
                            sx: { fontSize: "14px" },
                          }}
                        />
                      )}
                      ListboxProps={{
                        style: { maxHeight: "200px" },
                      }}
                    />
                  ) : (
                    <TextField
                      fullWidth
                      disabled
                      value=""
                      placeholder="No unassigned serial numbers available"
                      InputProps={{
                        sx: { fontSize: "14px" },
                      }}
                    />
                  )}
                </Box>
                <Box flex={1}>
                  <label>
                    Assigned Date <span className="required">*</span>
                  </label>
                  <TextField
                    fullWidth
                    type="date"
                    id="assignedDate"
                    name="assignedDate"
                    value={values.assignedDate}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.assignedDate && Boolean(errors.assignedDate)}
                    helperText={touched.assignedDate && errors.assignedDate}
                    InputLabelProps={{ shrink: true }}
                    inputProps={{ max: new Date().toISOString().split("T")[0] }}
                    InputProps={{ sx: { fontSize: "14px" } }}
                  />
                </Box>
              </Box>
            </DialogContent>
            <DialogActions sx={{ padding: "16px" }}>
              <Button
                onClick={onClose}
                sx={{
                  fontWeight: "400",
                  fontSize: "14px",
                  color: "#111827",
                  border: "1px solid #E5E7EB",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || unassignedSerialNumbers.length === 0}
                sx={{
                  backgroundColor: "#F26522",
                  border: "1px solid #F26522",
                  color: "#FFF",
                  borderRadius: "5px",
                  padding: "0.5rem 0.85rem",
                  fontSize: "14px",
                  fontWeight: 500,
                  textTransform: "none",
                  "&:hover": { backgroundColor: "#E55A1B" },
                }}
              >
                Assign
              </Button>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default AssignAssetDialog;

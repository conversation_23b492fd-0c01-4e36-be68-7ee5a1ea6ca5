"use client";
import React, { useState, useRef } from "react";
import { Formik, Form, FieldArray, Field } from "formik";
import * as Yup from "yup";
import "./AddEditAsset.scss";
import { toast } from "react-toastify";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  CircularProgress,
  IconButton,
  FormControl,
  Select,
  MenuItem,
} from "@mui/material";
import { Cancel, Close, CloudUpload } from "@mui/icons-material";
import { addAsset, updateAsset } from "@/app/services/assets/asset.service";
import useUploadMedia from "@/app/hooks/useUploadMedia";
import Image from "next/image";
import Loader from "@/components/Loader/Loader";

interface AddAssetFormProps {
  open: boolean;
  onClose: () => void;
  onSubmitSuccess: () => void;
  isEditMode: boolean;
  initialAsset?: {
    id: string;
    brand: string;
    type: string;
    category: string;
    quantity: number;
    serialNumber: string[];
    cost: number;
    vendor: string;
    warrentyTo: string;
    location: string;
    assetImages: string[];
    isActive: boolean;
  };
}

interface AssetFormValues {
  type: string;
  brand: string;
  category: string;
  quantity: string;
  serialNumber: string[];
  cost: string;
  vendor: string;
  warrentyTo: string;
  location: string;
  assetImage: string[];
  assetImages?: FileList | null;
  isActive: boolean;
}

const validationSchema = Yup.object({
  type: Yup.string().required("Type is required"),
  brand: Yup.string().required("Brand is required"),
  category: Yup.string().required("Category is required"),
  quantity: Yup.string()
    .required("Quantity is required")
    .test(
      "is-valid-number",
      "Quantity must be a number between 1 and 100",
      (value) => {
        if (!value) return false;
        const num = parseInt(value);
        return !isNaN(num) && num >= 1 && num <= 100;
      }
    ),
  serialNumber: Yup.array()
    .of(Yup.string().required("Serial number is required"))
    .when("quantity", (quantity, schema) => {
      const num = parseInt(String(quantity));
      if (!isNaN(num) && num > 0) {
        return schema
          .min(1, "At least one serial number is required")
          .required("At least one serial number is required");
      }
      return schema.notRequired();
    }),
  cost: Yup.number()
    .required("Cost is required")
    .min(0, "Cost cannot be negative"),
  vendor: Yup.string().required("Vendor is required"),
  warrentyTo: Yup.date()
    .required("Warranty date is required")
    .min(new Date(), "Warranty date must be in the future")
    .typeError("Please enter a valid date"),
  location: Yup.string().required("Location is required"),
  assetImage: Yup.array()
    .of(Yup.string())
    .min(1, "At least one image is required")
    .required("Asset image is required"),
  isActive: Yup.boolean().required("Active status is required"),
});

const AddEditAssetForm: React.FC<AddAssetFormProps> = ({
  open,
  onClose,
  onSubmitSuccess,
  isEditMode,
  initialAsset,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { uploadMedia } = useUploadMedia();

  const initialValues: AssetFormValues = {
    type: initialAsset?.type || "",
    brand: initialAsset?.brand || "",
    category: initialAsset?.category || "",
    quantity: initialAsset?.quantity ? initialAsset.quantity.toString() : "",
    serialNumber: initialAsset?.serialNumber || [""],
    cost: initialAsset?.cost.toString() || "",
    vendor: initialAsset?.vendor || "",
    warrentyTo: initialAsset?.warrentyTo
      ? initialAsset.warrentyTo.split("T")[0]
      : "",
    location: initialAsset?.location || "",
    assetImage: initialAsset?.assetImages || [],
    assetImages: null,
    isActive: initialAsset?.isActive ?? true,
  };

  const handleImageUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
    setFieldValue: (field: string, value: any) => void,
    values: AssetFormValues
  ) => {
    const files = e.target.files;
    if (files) {
      setIsLoading(true);
      try {
        const uploadPromises = Array.from(files).map(async (file) => {
          if (file.size > 4 * 1024 * 1024) {
            throw new Error(`File ${file.name} exceeds 4MB limit`);
          }
          const { preview } = await uploadMedia(
            file,
            "assets",
            1,
            setIsLoading,
            file.name
          );
          return preview;
        });

        const uploadedUrls = await Promise.all(uploadPromises);
        setFieldValue("assetImage", [...values.assetImage, ...uploadedUrls]);
        setFieldValue("assetImages", files);
        toast.success("Images uploaded successfully");
      } catch (error) {
        console.error("Image upload failed:", error);
        toast.error("Failed to upload images");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <Dialog
      className="dialog-asset"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="md"
      fullWidth
    >
      <DialogTitle className="dialog-title">
        <Typography
          variant="h6"
          sx={{ fontWeight: 600, fontSize: "20px", color: "#111827" }}
        >
          {isEditMode ? "Edit Asset" : "Add New Asset"}
        </Typography>
        <IconButton
          color="inherit"
          onClick={onClose}
          aria-label="close"
          sx={{
            backgroundColor: "#6b7280",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": { backgroundColor: "#d55a1d" },
            "& .MuiSvgIcon-root": { fontSize: "14px" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        enableReinitialize
        onSubmit={async (values, { setSubmitting, resetForm }) => {
          try {
            const formData = new FormData();
            const formattedValues = {
              ...values,
              warrentyTo: new Date(values.warrentyTo).toISOString(),
              cost: parseFloat(values.cost).toFixed(2),
              quantity: parseInt(values.quantity),
              isActive: values.isActive,
            };

            Object.keys(formattedValues).forEach((key) => {
              if (
                key !== "assetImage" &&
                key !== "assetImages" &&
                key !== "serialNumber"
              ) {
                const value =
                  formattedValues[key as keyof typeof formattedValues];
                if (value !== undefined && value !== null) {
                  formData.append(key, value.toString());
                }
              }
            });

            values.serialNumber.forEach((serial, index) => {
              formData.append(`serialNumber[${index}]`, serial);
            });

            values.assetImage.forEach((imageUrl, index) => {
              formData.append(`assetImage[${index}]`, imageUrl);
            });

            if (values.assetImages) {
              Array.from(values.assetImages).forEach((file, index) => {
                formData.append(`files[${index}]`, file);
              });
            }

            let response;
            if (isEditMode && initialAsset?.id) {
              response = await updateAsset(initialAsset.id, formData);
            } else {
              response = await addAsset(formData);
            }

            if (response) {
              resetForm();
              onClose();
              onSubmitSuccess();
              const successMessage =
                response.message ||
                (isEditMode
                  ? "Asset updated successfully"
                  : "Asset added successfully");
              toast.success(successMessage);
            }
          } catch (error: any) {
            console.error("Error submitting asset:", error);

            const errorMessage =
              error.response?.data?.message ||
              (isEditMode ? "Failed to update asset" : "Failed to add asset");
            toast.error(errorMessage);
          } finally {
            setSubmitting(false);
          }
        }}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          isSubmitting,
          setFieldValue,
        }) => (
          <Form>
            <DialogContent
              className="dialog-content"
              dividers
              sx={{ overflow: "auto", maxHeight: "75vh" }}
            >
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Brand <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="brand"
                      value={values.brand}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.brand && Boolean(errors.brand)}
                      helperText={touched.brand && errors.brand}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Type <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="type"
                      value={values.type}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.type && Boolean(errors.type)}
                      helperText={touched.type && errors.type}
                    />
                  </Box>
                </Box>

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Category <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="category"
                      value={values.category}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.category && Boolean(errors.category)}
                      helperText={touched.category && errors.category}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Quantity <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="quantity"
                      type="number"
                      value={values.quantity}
                      onChange={(e) => {
                        const inputValue = e.target.value;
                        let qty = parseInt(inputValue);
                        if (inputValue === "") {
                          setFieldValue("quantity", "");
                          setFieldValue("serialNumber", [""]);
                        } else if (!isNaN(qty)) {
                          if (qty > 100) {
                            qty = 100;
                            toast.error("Quantity cannot exceed 100");
                          } else if (qty < 1) {
                            qty = 1; // Enforce minimum 1
                            toast.error("Quantity must be at least 1");
                          }
                          setFieldValue("quantity", qty.toString());
                          setFieldValue(
                            "serialNumber",
                            Array(qty)
                              .fill("")
                              .map((_, i) => values.serialNumber[i] || "")
                          );
                        }
                      }}
                      onBlur={handleBlur}
                      error={touched.quantity && Boolean(errors.quantity)}
                      helperText={touched.quantity && errors.quantity}
                      inputProps={{ min: 1, max: 50 }}
                    />
                  </Box>
                </Box>

                {values.quantity && parseInt(values.quantity) > 0 && (
                  <Box>
                    <Typography
                      variant="body1"
                      sx={{ mb: 1, fontSize: "14px", fontWeight: 500 }}
                    >
                      Serial Numbers <span className="required">*</span>
                    </Typography>
                    <Box
                      sx={{
                        border: "1px solid #E5E7EB",
                        borderRadius: "8px",
                        padding: "16px",
                        backgroundColor: "#F8F9FA",
                      }}
                    >
                      <FieldArray name="serialNumber">
                        {({ push, remove }) => (
                          <Box
                            sx={{ display: "flex", flexWrap: "wrap", gap: 2 }}
                          >
                            {values.serialNumber.map((serial, index) => (
                              <Box
                                key={index}
                                sx={{
                                  flex: "1 1 calc(50% - 16px)",
                                  minWidth: "200px",
                                }}
                              >
                                <TextField
                                  name={`serialNumber[${index}]`}
                                  value={serial}
                                  onChange={handleChange}
                                  onBlur={handleBlur}
                                  error={
                                    touched.serialNumber &&
                                    Boolean(
                                      errors.serialNumber &&
                                        errors.serialNumber[index]
                                    )
                                  }
                                  helperText={
                                    touched.serialNumber &&
                                    errors.serialNumber &&
                                    errors.serialNumber[index]
                                  }
                                  placeholder={`Serial Number ${index + 1}`}
                                  variant="outlined"
                                  fullWidth
                                  sx={{
                                    "& .MuiInputBase-input": {
                                      padding: "10px 14px",
                                    },
                                  }}
                                />
                              </Box>
                            ))}
                          </Box>
                        )}
                      </FieldArray>
                      {touched.serialNumber &&
                        errors.serialNumber &&
                        typeof errors.serialNumber === "string" && (
                          <Typography
                            color="error"
                            variant="caption"
                            sx={{ mt: 1 }}
                          >
                            {errors.serialNumber}
                          </Typography>
                        )}
                    </Box>
                  </Box>
                )}

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Cost <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="cost"
                      type="number"
                      value={values.cost}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.cost && Boolean(errors.cost)}
                      helperText={touched.cost && errors.cost}
                      inputProps={{ step: "0.01" }}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Vendor <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="vendor"
                      value={values.vendor}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.vendor && Boolean(errors.vendor)}
                      helperText={touched.vendor && errors.vendor}
                    />
                  </Box>
                </Box>

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Warranty Until <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      type="date"
                      name="warrentyTo"
                      value={values.warrentyTo}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.warrentyTo && Boolean(errors.warrentyTo)}
                      helperText={touched.warrentyTo && errors.warrentyTo}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Status <span className="required">*</span>
                    </label>
                    <FormControl fullWidth>
                      <Field
                        as={Select}
                        name="isActive"
                        variant="outlined"
                        error={touched.isActive && Boolean(errors.isActive)}
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            height: "38px",
                            fontSize: "14px",
                          },
                          "& .MuiInputBase-input": {
                            padding: "8px 14px",
                          },
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: "#ccc",
                          },
                        }}
                      >
                        <MenuItem value="true">Active</MenuItem>
                        <MenuItem value="false">Inactive</MenuItem>
                      </Field>
                      {touched.isActive && errors.isActive && (
                        <Typography
                          color="error"
                          variant="caption"
                          sx={{ fontSize: "12px" }}
                        >
                          {errors.isActive}
                        </Typography>
                      )}
                    </FormControl>
                  </Box>
                </Box>

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Location <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="location"
                      value={values.location}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.location && Boolean(errors.location)}
                      helperText={touched.location && errors.location}
                    />
                  </Box>
                </Box>

                <Box sx={{ display: "flex", gap: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      variant="body1"
                      sx={{ mb: 1, fontSize: "14px" }}
                    >
                      Asset Image <span className="required">*</span>
                    </Typography>
                    <Box
                      sx={{
                        border: "2px dashed #ccc",
                        borderRadius: "8px",
                        padding: "20px",
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        gap: "10px",
                        position: "relative",
                        backgroundColor: "#F8F9FA",
                      }}
                    >
                      {isLoading ? (
                        <Box sx={{ display: "flex", justifyContent: "center" }}>
                          <CircularProgress size={24} />
                        </Box>
                      ) : (
                        <>
                          <Box
                            sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}
                          >
                            {values.assetImage.length > 0 ? (
                              values.assetImage.map((image, index) => (
                                <Box
                                  key={index}
                                  sx={{
                                    width: "100px",
                                    height: "100px",
                                    position: "relative",
                                    overflow: "hidden",
                                    borderRadius: "8px",
                                    border: "1px solid #E5E7EB",
                                    mb: 2,
                                  }}
                                >
                                  <Image
                                    src={image}
                                    alt={`Asset Preview ${index + 1}`}
                                    fill
                                    style={{ objectFit: "cover" }}
                                  />
                                  <IconButton
                                    sx={{
                                      position: "absolute",
                                      top: 0,
                                      right: 0,
                                      backgroundColor: "rgba(0,0,0,0.5)",
                                      color: "white",
                                      padding: "4px",
                                      zIndex: 1,
                                      "&:hover": {
                                        backgroundColor: "rgba(0,0,0,0.7)",
                                      },
                                    }}
                                    onClick={() => {
                                      const newImages =
                                        values.assetImage.filter(
                                          (_, i) => i !== index
                                        );
                                      setFieldValue("assetImage", newImages);
                                    }}
                                  >
                                    <Cancel fontSize="small" />
                                  </IconButton>
                                </Box>
                              ))
                            ) : (
                              <Box
                                sx={{
                                  width: "50px",
                                  height: "50px",
                                  borderRadius: "50%",
                                  backgroundColor: "#e3f2fd",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  mb: 1,
                                }}
                              >
                                <CloudUpload sx={{ color: "#F26522" }} />
                              </Box>
                            )}
                          </Box>
                          <Typography
                            sx={{
                              fontSize: "0.75rem",
                              color: "text.secondary",
                            }}
                          >
                            Image should be below 4 MB
                          </Typography>
                          <Button
                            variant="contained"
                            sx={{
                              backgroundColor:
                                "rgb(226.95, 82.1538461538, 13.35)",
                              color: "#fff !important",
                              border:
                                "1px solid rgb(226.95, 82.1538461538, 13.35)",
                              padding: "0.25rem 0.5rem",
                              fontSize: "0.75rem",
                              borderRadius: "5px",
                              fontWeight: "600",
                            }}
                            onClick={handleUploadClick}
                            disabled={isSubmitting}
                          >
                            <span>
                              {isSubmitting ? "Uploading..." : "Upload Images"}
                            </span>
                          </Button>
                          <input
                            type="file"
                            hidden
                            accept="image/*"
                            multiple
                            ref={fileInputRef}
                            onChange={(e) =>
                              handleImageUpload(e, setFieldValue, values)
                            }
                          />
                        </>
                      )}
                    </Box>
                    {touched.assetImage && errors.assetImage && (
                      <Typography color="error" variant="caption">
                        {errors.assetImage}
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Box>
              <DialogActions sx={{ padding: "16px 0 0 0 !important" }}>
                <Button
                  onClick={onClose}
                  sx={{
                    fontWeight: "400",
                    fontSize: "14px",
                    color: "#111827",
                    border: "1px solid #E5E7EB",
                    borderRadius: "5px",
                    textTransform: "none",
                    padding: "8px 13.6px",
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  sx={{
                    backgroundColor: "#F26522",
                    border: "1px solid #F26522",
                    color: "#FFF",
                    borderRadius: "5px",
                    padding: "0.5rem 0.85rem",
                    fontSize: "14px",
                    transition: "all 0.5s",
                    fontWeight: 500,
                    textTransform: "none",
                    "&:hover": { backgroundColor: "#E55A1B" },
                  }}
                >
                  {isEditMode ? "Update Asset" : "Add Asset"}
                </Button>
              </DialogActions>
            </DialogContent>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default AddEditAssetForm;

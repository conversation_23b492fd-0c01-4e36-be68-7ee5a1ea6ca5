"use client";
import {
  <PERSON>,
  IconButton,
  Button,
  Di<PERSON>r,
  Typo<PERSON>,
  Paper,
  useMedia<PERSON>uery,
  Tooltip,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import "./Assets.scss";
import {
  HomeOutlined,
  ControlPoint,
  Circle,
  Assignment,
  Visibility,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import { EditNote, Delete } from "@mui/icons-material";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";
import Loader from "@/components/Loader/Loader";
import AddEditAssetForm from "./AddEditAsset";
import AssignAssetDialog from "./AssignAssetDialog";
import { getAssets, deleteAsset, updateAsset } from "@/app/services/assets/asset.service";
import { getUsers } from "@/app/services/users.service";
import { toast } from "react-toastify";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import AssetFileUploadDownload from "@/components/AssetFileUploadDownload/AssetFileUploadDownload";
import { useRouter } from "next/navigation";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";

// Quick Search Toolbar Component
function QuickSearchToolbar() {
  return (
    <Box
      sx={{
        p: 0.5,
        pb: 0,
        justifyContent: "space-between",
        padding: "10px 20px",
        display: "flex",
      }}
    >
      <GridToolbarQuickFilter
        placeholder="Search"
        className="grid-search"
        sx={{ textDecoration: "none" }}
      />
      <Box
        className="grid-export"
        sx={{ display: "flex", alignItems: "center" }}
      >
        <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
      </Box>
    </Box>
  );
}

export default function AssetsContent() {
  // State for loading
  const [isLoading, setIsLoading] = useState(false);

  // State for pagination
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalRows, setTotalRows] = useState<number>(0);

  // State for assets data
  const [assets, setAssets] = useState<any[]>([]);

  // State for the Add/Edit Asset modal
  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<any | null>(null);

  // State for the Assign Asset dialog
  const [openAssignDialog, setOpenAssignDialog] = useState(false);
  const [assetToAssign, setAssetToAssign] = useState<any | null>(null);

  // State for employees
  const [employees, setEmployees] = useState<{ id: string; name: string }[]>(
    []
  );

  // State for filters
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedCategory, setSelectedCategory] = useState<string>("Category");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [assetToDelete, setAssetToDelete] = useState<string | number | null>(
    null
  );

  // Restrict access to admin roles only
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  const router = useRouter();

  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        const response = await getUsers();
        if (response.success && response.users?.results) {
          const fetchedEmployees = response.users.results.map((user: any) => ({
            id: user._id,
            name: `${user.firstName} ${user.lastName}`,
          }));
          setEmployees(fetchedEmployees);
        } else {
          setEmployees([]);
          toast.error("No employees found");
        }
      } catch (error) {
        console.error("Failed to fetch employees:", error);
        setEmployees([]);
        toast.error("Failed to fetch employees");
      }
    };
    fetchEmployees();
  }, []);

  // Fetch assets with filters
  useEffect(() => {
    const fetchAssets = async () => {
      setIsLoading(true);
      try {
        const response = await getAssets(
          pageSize,
          page,
          selectedCategory === "Category" ? "" : selectedCategory,
          selectedSortBy === "Sort By" ? "" : selectedSortBy,
          startDate,
          endDate,
          selectedStatus === "Select Status"
            ? ""
            : selectedStatus === "Active"
              ? "true"
              : "false"
        );
        if (response.success && response.assets?.results) {
          setAssets(
            response.assets.results.map((asset: any) => ({
              id: asset._id,
              brand: asset.brand,
              type: asset.type,
              quantity: asset.quantity,
              category: asset.category,
              isActive: asset.isActive,
              serialNumber: asset.serialNumber,
              serialNumberStatus: asset.serialNumberStatus,
              unassignedCount: asset.unassignedCount,
              cost: asset.cost,
              vendor: asset.vendor,
              warrentyTo: asset.warrentyTo,
              location: asset.location,
              assetImages: asset.assetImage,
            }))
          );
          setTotalRows(response.assets.total || 0);
        } else {
          setAssets([]);
          setTotalRows(0);
          toast.error("No assets found");
        }
      } catch (error) {
        console.error("Failed to fetch assets:", error);
        setAssets([]);
        setTotalRows(0);
        toast.error("Failed to fetch assets");
      } finally {
        setIsLoading(false);
      }
    };
    fetchAssets();
  }, [
    page,
    pageSize,
    selectedCategory,
    selectedSortBy,
    startDate,
    endDate,
    selectedStatus,
  ]);

  const rows: GridRowsProp = assets.map((asset) => ({
    id: asset.id,
    brand: asset.brand,
    type: asset.type,
    quantity: asset.quantity,
    category: asset.category,
    isActive: asset.isActive,
    unassignedCount: asset.unassignedCount,
  }));

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Asset Management" },
  ];

  const isMobile = useMediaQuery("(max-width:768px)");

  const columns: GridColDef[] = [
    {
      field: "brand",
      headerName: "Brand Name",
      flex: 1,
    },
    {
      field: "type",
      headerName: "Type",
      flex: 1,
    },
    {
      field: "quantity",
      headerName: "Net Quantity",
      flex: 1,
      renderCell: (params) => <Typography>{params.value}</Typography>,
    },
    {
      field: "category",
      headerName: "Category",
      flex: 1,
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 1,
      renderCell: (params) => (
        // <Box
        //   sx={{
        //     display: "flex",
        //     alignItems: "center",
        //     justifyContent: "center",
        //   }}
        // >
        //   {isMobile ? (
        //     <Circle
        //       sx={{ fontSize: 12, color: params.value ? "#03C95A" : "#E70D0D" }}
        //     />
        //   ) : (
        //     <Typography
        //       sx={{
        //         display: "flex",
        //         alignItems: "center",
        //         gap: "3px",
        //         backgroundColor: params.value ? "#03C95A" : "#E70D0D",
        //         color: "#fff",
        //         borderRadius: "5px",
        //         textAlign: "center",
        //         minWidth: "66px",
        //         justifyContent: "center",
        //         fontSize: "10px",
        //         fontWeight: 500,
        //         padding: "0px 5px",
        //         lineHeight: "18px",
        //       }}
        //     >
        //       <Box
        //         sx={{
        //           width: "5px",
        //           height: "5px",
        //           borderRadius: "100%",
        //           backgroundColor: "#fff",
        //         }}
        //       />
        //       {params.value ? "Active" : "Inactive"}
        //     </Typography>
        //   )}
        // </Box>
         <StatusToggle
      isActive={params.value}
      onChange={async () => {
        try {
          const formData = new FormData();
          formData.append("isActive", (!params.value).toString());
          await updateAsset(params.row.id, formData);
          toast.success("Assets updated successfully");
          const updatedAssets = assets.map((asset) =>
            asset.id === params.row.id ? { ...asset, isActive: !params.value } : asset
          );
          setAssets(updatedAssets);
        } catch (error) {
          console.error("Failed to update asset status:", error);
          toast.error("Failed to update asset status");
        }
      }}
    />
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 0.5,
      renderCell: (params) => (
        <Box>
          <Tooltip title="View Asset Details" arrow placement="top">
            <IconButton
              sx={{ color: "#6B7280" }}
              onClick={() => router.push(`/assetdetails?id=${params.row.id}`)}
            >
              <Visibility sx={{ fontSize: "16px" }} />
            </IconButton>
          </Tooltip>
          <Tooltip title="Edit Asset" arrow placement="top">
            <IconButton
              sx={{ color: "#6B7280" }}
              onClick={() => handleEditClick(params.row.id)}
            >
              <EditNote sx={{ fontSize: "16px" }} />
            </IconButton>
          </Tooltip>
          <Tooltip
            title={
              params.row.unassignedCount === 0
                ? "No unassigned serial numbers"
                : "Assign Asset"
            }
            arrow
            placement="top"
          >
            <span>
              <IconButton
                sx={{
                  color: "#6B7280",
                  "&:hover": { color: "#F26522" },
                }}
                onClick={() => handleAssignClick(params.row.id)}
                disabled={params.row.unassignedCount === 0}
              >
                <Assignment sx={{ fontSize: "16px" }} />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title="Delete Asset" arrow placement="top">
            <IconButton onClick={() => handleDeleteClick(params.row.id)}>
              <Delete sx={{ fontSize: "16px" }} />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  const handleEditClick = (assetId: string | number) => {
    const asset = assets.find((a) => a.id === assetId);
    if (asset) {
      setSelectedAsset(asset);
      setIsEditMode(true);
      setOpenModal(true);
    }
  };

  const handleAssignClick = (assetId: string | number) => {
    const asset = assets.find((a) => a.id === assetId);
    if (asset) {
      setAssetToAssign({
        id: asset.id,
        serialNumber: asset.serialNumber,
        serialNumberStatus: asset.serialNumberStatus,
      });
      setOpenAssignDialog(true);
    }
  };

  const handleDeleteClick = (assetId: string | number) => {
    setAssetToDelete(assetId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (assetToDelete !== null) {
      setIsLoading(true);
      try {
        await deleteAsset(assetToDelete.toString());
        setAssets(assets.filter((asset) => asset.id !== assetToDelete));
        toast.success("Asset deleted successfully");
      } catch (error) {
        console.error("Failed to delete asset:", error);
        toast.error("Failed to delete asset");
      } finally {
        setIsLoading(false);
        setDeleteDialogOpen(false);
        setAssetToDelete(null);
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setAssetToDelete(null);
  };

  const handleModalClose = () => {
    setOpenModal(false);
    setIsEditMode(false);
    setSelectedAsset(null);
  };

  const handleAssignDialogClose = () => {
    setOpenAssignDialog(false);
    setAssetToAssign(null);
  };

  return (
    <Box className="assets-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="assets-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Asset Management</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="add-asset"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <AssetFileUploadDownload
              onUploadSuccess={() => {
                setIsLoading(true);
                getAssets(
                  pageSize,
                  page,
                  selectedCategory === "Category" ? "" : selectedCategory,
                  selectedSortBy === "Sort By" ? "" : selectedSortBy,
                  startDate,
                  endDate,
                  selectedStatus === "Select Status"
                    ? ""
                    : selectedStatus === "Active"
                      ? "true"
                      : "false"
                )
                  .then((response) => {
                    if (response.success && response.assets?.results) {
                      setAssets(
                        response.assets.results.map((asset: any) => ({
                          id: asset._id,
                          brand: asset.brand,
                          type: asset.type,
                          quantity: asset.quantity,
                          category: asset.category,
                          isActive: asset.isActive,
                          serialNumber: asset.serialNumber,
                          serialNumberStatus: asset.serialNumberStatus,
                          unassignedCount: asset.unassignedCount,
                          cost: asset.cost,
                          vendor: asset.vendor,
                          warrentyTo: asset.warrentyTo,
                          location: asset.location,
                          assetImages: asset.assetImage,
                        }))
                      );
                      setTotalRows(response.assets.total || 0);
                    }
                  })
                  .catch((error) => {
                    console.error("Failed to fetch assets:", error);
                    toast.error("Failed to fetch assets");
                  })
                  .finally(() => {
                    setIsLoading(false);
                  });
              }}
            />
            <Tooltip title="Add New Asset" arrow placement="top">
              <Button
                variant="contained"
                sx={{
                  display: "flex",
                  gap: "8px",
                  backgroundColor: "#F26522",
                  borderColor: "#F26522",
                  color: "#FFF",
                  fontWeight: 400,
                  fontSize: "14px",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                onClick={() => {
                  setIsEditMode(false);
                  setOpenModal(true);
                }}
              >
                <ControlPoint sx={{ width: "16px", height: "16px" }} />
                Add Asset
              </Button>
            </Tooltip>
          </Box>
        </Box>

        {/* DataGrid */}
        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight:
                rows.length <= 2 ? "calc(70vh - 200px)" : "calc(100vh - 220px)",
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Company Assets</Typography>
              <PolicyFilters
                departments={[]}
                selectedDepartment={""}
                setSelectedDepartment={() => {}}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>

            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={totalRows}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                setPage(model.page + 1);
                setPageSize(model.pageSize);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: QuickSearchToolbar,
              }}
            />
          </Box>
        </Paper>
      </Box>

      {/* Add/Edit Modal */}
      <AddEditAssetForm
        open={openModal}
        onClose={handleModalClose}
        onSubmitSuccess={() => {
          setIsLoading(true);
          getAssets(
            pageSize,
            page,
            selectedCategory === "Category" ? "" : selectedCategory,
            selectedSortBy === "Sort By" ? "" : selectedSortBy,
            startDate,
            endDate,
            selectedStatus === "Select Status"
              ? ""
              : selectedStatus === "Active"
                ? "true"
                : "false"
          )
            .then((response) => {
              if (response.success && response.assets?.results) {
                setAssets(
                  response.assets.results.map((asset: any) => ({
                    id: asset._id,
                    brand: asset.brand,
                    type: asset.type,
                    quantity: asset.quantity,
                    category: asset.category,
                    isActive: asset.isActive,
                    serialNumber: asset.serialNumber,
                    serialNumberStatus: asset.serialNumberStatus,
                    unassignedCount: asset.unassignedCount,
                    cost: asset.cost,
                    vendor: asset.vendor,
                    warrentyTo: asset.warrentyTo,
                    location: asset.location,
                    assetImages: asset.assetImage,
                  }))
                );
                setTotalRows(response.assets.total || 0);
              } else {
                setAssets([]);
                setTotalRows(0);
                toast.error("No assets found");
              }
            })
            .catch((error) => {
              console.error("Failed to fetch assets:", error);
              setAssets([]);
              setTotalRows(0);
              toast.error("Failed to fetch assets");
            })
            .finally(() => setIsLoading(false));
        }}
        isEditMode={isEditMode}
        initialAsset={selectedAsset}
      />

      {/* Assign Asset Dialog */}
      <AssignAssetDialog
        open={openAssignDialog}
        onClose={handleAssignDialogClose}
        onSubmitSuccess={() => {
          setIsLoading(true);
          getAssets(
            pageSize,
            page,
            selectedCategory === "Category" ? "" : selectedCategory,
            selectedSortBy === "Sort By" ? "" : selectedSortBy,
            startDate,
            endDate,
            selectedStatus === "Select Status"
              ? ""
              : selectedStatus === "Active"
                ? "true"
                : "false"
          )
            .then((response) => {
              if (response.success && response.assets?.results) {
                setAssets(
                  response.assets.results.map((asset: any) => ({
                    id: asset._id,
                    brand: asset.brand,
                    type: asset.type,
                    quantity: asset.quantity,
                    category: asset.category,
                    isActive: asset.isActive,
                    serialNumber: asset.serialNumber,
                    serialNumberStatus: asset.serialNumberStatus,
                    unassignedCount: asset.unassignedCount,
                    cost: asset.cost,
                    vendor: asset.vendor,
                    warrentyTo: asset.warrentyTo,
                    location: asset.location,
                    assetImages: asset.assetImage,
                  }))
                );
                setTotalRows(response.assets.total || 0);
              } else {
                setAssets([]);
                setTotalRows(0);
                toast.error("No assets found");
              }
            })
            .catch((error) => {
              console.error("Failed to fetch assets:", error);
              setAssets([]);
              setTotalRows(0);
              toast.error("Failed to fetch assets");
            })
            .finally(() => setIsLoading(false));
        }}
        asset={assetToAssign}
        employees={employees}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this asset? This action cannot be undone."
      />
    </Box>
  );
}

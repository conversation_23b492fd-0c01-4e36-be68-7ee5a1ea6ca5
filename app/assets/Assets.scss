.assets-container {
    .content {
        padding: 24px;

        .assets-header {
            display: flex;
            justify-content: space-between;

            .breadcrumbs-box {
                h2 {
                    font-size: 24px;
                    font-weight: 700;
                    color: #202C4B;
                }
            }

            .add-department {
                button {
                    display: flex;
                    gap: 8px;
                    background-color: #F26522;
                    border-color: #F26522;
                    color: #FFF;
                    font-weight: 400;
                    font-size: 14px;
                    border-radius: 5px;
                    text-transform: none;
                    padding: 8px 13.6px;
                }
            }
        }

        .pageSelect-dropdown {
            display: flex;
            justify-self: start;
            align-items: center;
            gap: 5px;
            margin: 30px 0px;

            p {
                font-size: 14px;
                color: #374151;
            }

            .MuiInputBase-root {
                .MuiSelect-select {
                    padding: 2px 20px 2px 10px;
                }

                .MuiSvgIcon-root {
                    right: 0px;
                }
            }
        }

        .DataGrid-container {
            border: 1px solid #E5E7EB;
            margin-top: 16px;

            .DataGrid-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 20px;

                h5 {
                    font-size: 16px;
                    font-weight: 600;
                }

                .filter-dropdown {
                    display: flex;
                    gap: 13px;

                    button {
                        font-weight: 400;
                        font-size: 14px;
                        color: #111827;
                        border: 1px solid #E5E7EB;
                        border-radius: 5px;
                        text-transform: none;
                        padding: 8px 13.6px;
                    }

                    .sort-dropdown {
                        background-color: #F26522;
                        border-color: #F26522;
                        color: #FFF
                    }
                }
            }



            .MuiDataGrid-root {
                .MuiBox-root {
                    display: flex;

                    // SEARCH BAR FOR GRID-DATA
                    .grid-search {

                        .MuiInputBase-root {
                            border: 1px solid #E5E7EB;
                            border-radius: 5px;


                            .MuiSvgIcon-root {
                                display: none;
                            }
                        }
                    }

                    .grid-export {
                        button {
                            font-weight: 400;
                            font-size: 14px;
                            color: #111827;
                            border: 1px solid #E5E7EB;
                            border-radius: 5px;
                            text-transform: none;
                            padding: 8px 13.6px;
                        }
                    }
                }

                .MuiDataGrid-main {
                    margin-top: 10px;

                    .MuiDataGrid-virtualScroller {
                        .MuiDataGrid-topContainer {
                            .MuiDataGrid-columnHeaders {
                                .MuiDataGrid-row--borderBottom {
                                    background: #E5E7EB;

                                    .MuiDataGrid-columnHeader {
                                        .MuiDataGrid-columnHeaderDraggableContainer {
                                            .MuiDataGrid-columnHeaderTitleContainer {
                                                .MuiDataGrid-columnHeaderTitleContainerContent {
                                                    .MuiDataGrid-columnHeaderTitle {
                                                        color: #111827;
                                                        font-size: 14px;
                                                        font-weight: 600;
                                                        padding: 10px 20px;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        .MuiDataGrid-virtualScrollerContent {
                            .MuiDataGrid-virtualScrollerRenderZone {
                                .MuiDataGrid-row {
                                    .MuiDataGrid-cell {
                                        .MuiDataGrid-cell--textLeft {
                                            font-size: 14px;
                                            color: #111827;
                                            padding: 10px 20px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                .MuiDataGrid-footerContainer {
                    .MuiTablePagination-root {
                        .MuiToolbar-root {
                            display: flex;
                            justify-content: center;

                            .MuiTablePagination-spacer {
                                display: none;
                            }
                        }
                    }
                }
            }

        }

    }
}

@media (max-width: 768px) {
    .department-container {
        .content {
            padding: 16px;

            .department-header {
                flex-direction: column;
                align-items: flex-start;

                .breadcrumbs-box {
                    margin-bottom: 10px;
                }

                .add-department {
                    margin-top: 10px;
                }
            }

            .DataGrid-container {
                .DataGrid-header {
                    flex-direction: column;
                    gap: 10px;
                }

                .MuiDataGrid-root {
                    .MuiBox-root {

                        display: flex;
                        align-items: center;
                    }

                    .grid-export {
                        padding: 0px;

                        button {
                            padding: 5px 13px !important;
                        }
                    }

                    .grid-search {
                        padding: 0px;
                    }
                }


            }



            .pageSelect-dropdown {
                margin: 20px 0px;
            }
        }
    }
}
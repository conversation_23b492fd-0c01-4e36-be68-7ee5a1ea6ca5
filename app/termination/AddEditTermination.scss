.add-edit-dialog {
  width: 500px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;

  .dialog-content {
    padding: 1rem !important;

    label {
      font-size: 14px;
      font-weight: 500;
      color: #202C4B;
      margin-bottom: 0.5rem;
      display: block;
    }

    .MuiInputBase-root {
      padding: 5.5px 14px !important;
      height: 36px !important;
      box-sizing: border-box;
      font-size: 14px !important;

      &.MuiInputBase-multiline {
        height: auto !important;
        padding: 5.5px 14px !important;
      }

      .MuiInputBase-input {
        padding: 0 !important;
        height: 100% !important;
        box-sizing: border-box;
      }

      input {
        font-size: 14px !important;
      }

      .MuiAutocomplete-input {
        font-size: 14px !important;
      }
    }

    .MuiAutocomplete-inputRoot {
      padding: 5.5px 14px !important;
      height: 36px !important;
      box-sizing: border-box;

      .MuiAutocomplete-input {
        padding: 0 !important;
        height: 100% !important;
      }
    }

    .MuiAutocomplete-root {
      margin-top: 0px !important;
      margin-bottom: 0px !important;
    }

    .MuiFormControl-root {
      margin-top: 0px !important;
      margin-bottom: 8px !important;
    }

    // Style for autocomplete options
    .MuiAutocomplete-option {
      font-size: 14px !important;
    }

    // Style for helper text and error messages
    .MuiFormHelperText-root {
      font-size: 12px !important;
    }

    // Style for input labels
    .field-label {
      font-size: 14px !important;
      font-weight: 500;
      color: #202C4B;
      margin-bottom: 8px;
    }

    // Style for Jodit Editor
    .jodit-container {
      .jodit-workplace {
        font-size: 14px !important;
      }

      .jodit-toolbar-button {
        font-size: 14px !important;
      }

      .jodit-placeholder {
        font-size: 14px !important;
      }
    }

    // Style for buttons
    .buttons-container {
      button {
        font-size: 14px !important;
      }
    }
  }

  // Dialog title styling
  .add-edit-dialog-title {
    display: flex;
    justify-content: space-between;
    padding: 16px !important;
    align-items: center;

    h6 {
      font-weight: 600;
      font-size: 20px;
      color: #202C4B;
    }

    .cancel-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      color: #6B7280;
      cursor: pointer;

      &:hover {
        color: #F26522;
      }
    }
  }

  // Form field styling
  .form-field {
    margin-bottom: 8px;

    .field-label {
      color: #202C4B;
      font-weight: 500;
      margin-bottom: 8px;
    }
  }

  // Buttons container
  .buttons-container {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 16px;

    .cancel-button {
      font-weight: 400;
      font-size: 14px;
      color: #111827;
      border: 1px solid #E5E7EB;
      border-radius: 5px;
      text-transform: none;
      padding: 8px 13.6px;

      &:hover {
        border-color: #d1d5db;
      }
    }

    .submit-button {
      background-color: #F26522;
      color: #FFF;
      font-weight: 400;
      font-size: 14px;
      border-radius: 5px;
      text-transform: none;
      padding: 8px 13.6px;

      &:hover {
        background-color: #d0551d;
      }
    }
  }
}

// Dialog title
.add-edit-dialog-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 500;
  color: #202c4b;
  padding: 16px 24px;
}

.cancel-icon {
  cursor: pointer;

  &:hover {
    color: #f26522;
  }
}

// Typography for field labels
.field-label {
  font-size: 14px !important; 
  font-weight: 500 !important;
  color: #202C4B !important;
  margin-bottom: .5rem;
}

// Autocomplete input (Terminated Employee)
.autocomplete-input {
  .MuiOutlinedInput-root {
    height: 41px;
    padding: 0 14px;
    display: flex;
    align-items: center;
  }

  .MuiAutocomplete-input {
    padding: 0 !important;
  }
}

// Select input (Termination Type)
.termination-type-select {
  height: 41px;

  .MuiSelect-select {
    padding: 10px 14px;
    display: flex;
    align-items: center;
  }
}

// Date input (Notice Date and Termination Date)
.date-input {
  .MuiOutlinedInput-root {
    height: 41px;
    padding: 0 14px;
    display: flex;
    align-items: center;
  }
}

// Buttons container
.buttons-container {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 16px;
}

// Cancel button
.cancel-button {
  font-weight: 400;
  font-size: 14px;
  color: #111827;
  border-color: #e5e7eb;
  border-radius: 5px;
  text-transform: none;
  padding: 8px 16px;

  &:hover {
    border-color: #d1d5db;
  }
}

// Submit button
.submit-button {
  background-color: #f26522;
  color: #fff;
  font-weight: 400;
  font-size: 14px;
  border-radius: 5px;
  text-transform: none;
  padding: 8px 16px;

  &:hover {
    background-color: #d0551d;
  }
}

// General spacing for form fields
.form-field {
  margin-bottom: 8px;
}

// Add these styles for the Jodit editor
.jodit-container {
  border-radius: 5px;
  margin-top: 8px;
  margin-bottom: 8px;
  border: 1px solid #E5E7EB !important;

  .jodit-workplace {
    border-radius: 0 0 5px 5px;
  }

  .jodit-toolbar__box {
    border-radius: 5px 5px 0 0;
    background-color: #f9fafb;
    border-bottom: 1px solid #E5E7EB;
  }

  .jodit-toolbar-button {
    &:hover:not([disabled]) {
      background-color: rgba(242, 101, 34, 0.1);
    }
  }

  .jodit-ui-group {
    border-right: 1px solid #E5E7EB;
  }

  .jodit-status-bar {
    border-top: 1px solid #E5E7EB;
    background-color: #f9fafb;
    color: #6B7280;
    font-size: 12px;
    border-radius: 0 0 5px 5px;
  }
}

// Make sure the editor fits properly in the dialog
.form-field {
  .jodit-container {
    width: 100%;
    box-sizing: border-box;
  }
}
"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>utt<PERSON>,
  <PERSON><PERSON>,
  Di<PERSON>r,
  Typo<PERSON>,
  Paper,
  Avatar,
} from "@mui/material";
import React, { useState, useEffect, useCallback } from "react";
import "./termination.scss";
import { HomeOutlined, ControlPoint } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
  GridRenderCellParams,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { EditNote, Delete } from "@mui/icons-material";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import useFetchTerminationSectionData from "../hooks/termination/useFetchTerminationSectionData";
import useFetchUsersData from "../hooks/users/useFetchUsersData";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import {
  addTermination,
  updateTermination,
  getTerminationById,
  deleteTermination,
} from "@/app/services/termination.service";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import AddEditTermination from "./AddEditTermination";
import useRoleAuth from "../hooks/useRoleAuth";
import { ROLE_GROUPS } from "../constants/roles";
import ReadMore from "@/components/ReadMore/ReadMore";
import { useDebounce } from "../hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

// Updated UserRecord interface based on API response
interface UserRecord {
  _id: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  // Add other properties if needed
}

interface Employee {
  _id: string;
  employeeName: string;
  avatar?: string;
}

interface TerminationRecord {
  _id: string;
  terminationEmp?: string;
  type?: string;
  reason?: string;
  noticeDate?: string;
  terminationDate?: string;
}

const formatDateForApi = (dateStr: string) => {
  if (!dateStr) return undefined;

  try {
    // Assuming dateStr is in format "MM/DD/YYYY"
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return undefined;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return undefined;
  }
};

export default function TerminationContent() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [refresh, setRefresh] = useState<boolean>(false);

  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [limit, setLimit] = useState<number>(10);

  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  const [openModal, setOpenModal] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [selectedTerminationId, setSelectedTerminationId] = useState<
    string | null
  >(null);

  // Access to All roles (Admin, SuperAdmin, Manager, HR, Employee)
  const { roles } = useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  // Check if the user is an employee (and not an admin role)
  const isEmployee =
    roles.includes("Employee") &&
    !roles.some((role) =>
      ["Admin", "SuperAdmin", "HR", "Manager"].includes(role)
    );

  interface EditData {
    empId: string;
    employeeName: string;
    reason: string;
    noticeDate?: string; // Make optional with ?
    terminationDate?: string; // Make optional with ?
    terminationType?: string;
  }

  const [editData, setEditData] = useState<EditData | null>(null);

  const [employees, setEmployees] = useState<Employee[]>([]);
  const [employeesLoading, setEmployeesLoading] = useState<boolean>(true);

  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);

  const [selectedStatus, setSelectedStatus] = useState<string>("Status");

  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const { terminationData, total } = useFetchTerminationSectionData({
    setIsLoading,
    refresh,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    startDate: startDate ? formatDateForApi(startDate) : undefined,
    endDate: endDate ? formatDateForApi(endDate) : undefined,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  });
 
   const handleSearchChange = useCallback(
      (event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(event.target.value);
      },
      []
    );
  const [employeeData] = useFetchUsersData({
    setIsLoading: setEmployeesLoading,
    refresh,
  });

  useEffect(() => {
    if (employeeData && Array.isArray(employeeData)) {
      const mappedEmployees: any = employeeData.map(
        (user: UserRecord, index: number) => ({
          _id: user?._id || index,
          employeeName:
            `${user?.firstName || ""} ${user?.lastName || ""}`.trim() ||
            "Unknown",
          avatar: user?.avatar,
        })
      );
      setEmployees(mappedEmployees);
      setEmployeesLoading(false);
    }
  }, [employeeData]);

  const rows: GridRowsProp = terminationData
    ? terminationData.map((termination: TerminationRecord, index: number) => {
        // Check if terminationEmp is a populated object with firstName and lastName
        let employeeName = "Unknown";
        let avatar = "";

        if (
          termination.terminationEmp &&
          typeof termination.terminationEmp === "object"
        ) {
          const emp = termination.terminationEmp as any;
          employeeName =
            `${emp.firstName || ""} ${emp.lastName || ""}`.trim() || "Unknown";
          avatar = emp.avatar || "";
        } else {
          // Fallback to the old method if terminationEmp is just an ID
          const employee = employees.find(
            (emp) => emp._id === termination.terminationEmp
          );
          employeeName = employee ? employee.employeeName : "Unknown";
          avatar = employee ? employee.avatar || "" : "";
        }

        return {
          id: termination._id || index + 1,
          empId:
            typeof termination.terminationEmp === "object"
              ? (termination.terminationEmp as any)._id
              : termination.terminationEmp || "N/A",
          employeeName: employeeName,
          avatar: avatar,
          reason: termination.reason || "N/A",
          terminationDate: termination.terminationDate
            ? new Date(termination.terminationDate).toLocaleDateString(
                "en-GB",
                {
                  day: "numeric",
                  month: "long",
                  year: "numeric",
                }
              )
            : "N/A",
          noticeDate: termination.noticeDate
            ? new Date(termination.noticeDate).toLocaleDateString("en-GB", {
                day: "numeric",
                month: "long",
                year: "numeric",
              })
            : "N/A",
        };
      })
    : [];

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "HR", href: "" },
    { label: "Terminations" },
  ];

  // Define columns based on user role
  const columns: GridColDef[] = [
    {
      field: "employeeName",
      headerName: "Employee Name",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={params.row.avatar || ""}
            alt={params.value as string}
            sx={{ width: 32, height: 32 }}
          />
          <Typography variant="body2">{params.value}</Typography>
        </Box>
      ),
    },
    {
      field: "reason",
      headerName: "Reason",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <ReadMore text={params.value as string} maxChars={100} />
      ),
    },
    {
      field: "noticeDate",
      headerName: "Notice Date",
      editable: false,
      flex: 1,
    },
    {
      field: "terminationDate",
      headerName: "Termination Date",
      editable: false,
      flex: 1,
    },
    // Only show actions column for non-employees
    ...(!isEmployee
      ? [
          {
            field: "actions",
            headerName: "",
            editable: false,
            disableExport: true,
            flex: 1,
            renderCell: (params: GridRenderCellParams) => (
              <Box sx={{ display: "flex", gap: 1 }}>
                <IconButton
                  sx={{ color: "#6B7280" }}
                  onClick={() => handleEditClick(params.row.id)}
                >
                  <EditNote sx={{ fontSize: "16px" }} />
                </IconButton>
                <IconButton
                  sx={{ color: "#6B7280" }}
                  onClick={() => handleDeleteClick(params.row.id)}
                >
                  <Delete sx={{ fontSize: "16px" }} />
                </IconButton>
              </Box>
            ),
          },
        ]
      : []),
  ];

  const handleEditClick = async (terminationId: string) => {
    try {
      setIsLoading(true);
      const response = await getTerminationById(terminationId);

      if (!response || !response.termination) {
        // toast.error("Failed to load termination data");
        return;
      }

      const termination = response.termination;
      console.log("Fetched termination data:", termination);

      // Extract employee ID and name
      let employeeId = "";
      let employeeName = "Unknown Employee";

      if (termination.terminationEmp) {
        if (typeof termination.terminationEmp === "object") {
          employeeId = termination.terminationEmp._id || "";
          employeeName =
            `${termination.terminationEmp.firstName || ""} ${termination.terminationEmp.lastName || ""}`.trim();
        } else {
          employeeId = termination.terminationEmp;
          // Try to find employee name from employees list
          const employee = employees.find((emp) => emp._id === employeeId);
          if (employee) {
            employeeName = employee.employeeName;
          }
        }
      }

      // Format dates for the form
      const formatDate = (dateString: string) => {
        if (!dateString) return "";
        return new Date(dateString).toISOString().split("T")[0];
      };

      // Make sure all required fields are properly set
      const editData: EditData = {
        empId: employeeId,
        employeeName: employeeName,
        reason: termination.reason || "",
        terminationType: termination.type || "",
        noticeDate: formatDate(termination.noticeDate),
        terminationDate: formatDate(termination.terminationDate),
      };

      console.log("Prepared edit data:", editData);
      setEditData(editData);
      setSelectedTerminationId(terminationId);
      setIsEditMode(true);
      setOpenModal(true);
    } catch (error) {
      console.error("Error fetching termination for edit:", error);
      // toast.error("Failed to load termination data for editing.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClick = (terminationId: string | number) => {
    setTerminationToDelete(terminationId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (terminationToDelete !== null) {
      try {
        await deleteTermination(terminationToDelete as string);
        setDeleteDialogOpen(false);
        setTerminationToDelete(null);
        setRefresh(!refresh);
        // toast.success("Termination deleted successfully!", {
        //   position: "top-right",
        //   autoClose: 3000,
        //   hideProgressBar: false,
        //   closeOnClick: true,
        //   pauseOnHover: true,
        //   draggable: true,
        // });
      } catch (error) {
        console.error("Failed to delete termination:", error);
        // toast.error("Failed to delete termination. Please try again.", {
        //   position: "top-right",
        //   autoClose: 3000,
        //   hideProgressBar: false,
        //   closeOnClick: true,
        //   pauseOnHover: true,
        //   draggable: true,
        // });
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setTerminationToDelete(null);
  };

  interface TerminationBody {
    terminationEmp?: string;
    type?: string;
    noticeDate?: string;
    terminationDate?: string;
    reason?: string;
    empId?: string;
    terminationType?: string;
  }

  const handleDialogSubmit = async (formData: { [key: string]: string }) => {
    try {
      console.log("Received form data in handleDialogSubmit:", formData);

      // Make sure all fields are explicitly included
      const payload = {
        terminationEmp: formData.empId,
        type: formData.terminationType,
        reason: formData.reason,
        noticeDate: formData.noticeDate, // Pass as is, let service handle formatting
        terminationDate: formData.terminationDate,
      };

      console.log("Termination payload before API call:", payload);

      if (isEditMode && selectedTerminationId) {
        const result = await updateTermination(selectedTerminationId, payload);
        console.log("Termination update result:", result);
        // toast.success("Termination updated successfully");
      } else {
        await addTermination(payload);
        //toast.success("Termination added successfully");
      }

      setRefresh(!refresh);
      setOpenModal(false);
      setEditData(null);
      setSelectedTerminationId(null);
      setIsEditMode(false);
    } catch (error) {
      console.error("Error in handleDialogSubmit:", error);
      // toast.error("Failed to save termination");
    }
  };

  const handleDialogClose = () => {
    setOpenModal(false);
    setIsEditMode(false);
    setSelectedTerminationId(null);
    setEditData(null);
  };

  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [terminationToDelete, setTerminationToDelete] = useState<
    string | number | null
  >(null);

  // Function to handle opening the dialog for adding a new termination
  const handleAddTermination = () => {
    // Reset edit data to ensure form is clean
    setEditData(null);
    setSelectedTerminationId(null);
    setIsEditMode(false);
    setOpenModal(true);
  };

  return (
    <Box className="termination-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="termination-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Terminations</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="add-policy"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            {!isEmployee && (
              <Button
                variant="contained"
                sx={{
                  display: "flex",
                  gap: "8px",
                  backgroundColor: "#F26522",
                  borderColor: "#F26522",
                  color: "#FFF",
                  fontWeight: 500,
                  fontSize: "14px",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                onClick={handleAddTermination}
              >
                <ControlPoint sx={{ fontSize: "14px" }} />
                Add Termination
              </Button>
            )}
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 550,
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Terminations List</Typography>
              <PolicyFilters
                departments={[]}
                selectedDepartment=""
                setSelectedDepartment={() => {}}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={false}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              //checkboxSelection
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              initialState={{ pagination: { paginationModel: { pageSize } } }}
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                handlePageChange(model.page);
                handlePageSizeChange(model.pageSize);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>
      </Box>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this termination record? This action cannot be undone."
      />

      <AddEditTermination
        open={openModal}
        onClose={handleDialogClose}
        onSave={handleDialogSubmit}
        editData={isEditMode && editData ? editData : undefined}
      />
    </Box>
  );
}

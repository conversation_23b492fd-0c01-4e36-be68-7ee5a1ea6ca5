import React, { useState, useRef, useEffect } from "react";
import { useFormik, Field } from "formik";
import * as Yup from "yup";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Button,
  Autocomplete,
  Box,
  Typography,
  TextField,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import useFetchUsersData from "@/app/hooks/users/useFetchUsersData";
import "./AddEditTermination.scss";
import JoditEditorWrapper from "@/components/JoditEditorWrapper";
import { getNoticePeriod } from "@/app/services/setting.service";
import useAuthStore from "@/store/authStore";

interface UserRecord {
  _id: string;
  firstName?: string;
  lastName?: string;
}

// Validation Schema using Yup
const terminationSchema = Yup.object().shape({
  empId: Yup.string().required("Terminated Employee is required"),
  terminationType: Yup.string().required("Termination Type is required"),
  reason: Yup.string().required("Reason is required"),
  terminationDate: Yup.string().required("Termination Date is required"),
});

interface Employee {
  _id: string;
  employeeName: string;
}

interface TerminationTypeOption {
  value: string;
  label: string;
}

interface TerminationDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (formData: { [key: string]: string }) => void;
  editData?: {
    empId: string;
    employeeName?: string;
    reason?: string;
    noticeDate?: string;
    terminationDate?: string;
    terminationType?: string;
  };
}

const AddEditTermination: React.FC<TerminationDialogProps> = ({
  open,
  onClose,
  onSave,
  editData,
}) => {
  const [loadingEmployees, setLoadingEmployees] = useState(false);
  const [noticePeriodDays, setNoticePeriodDays] = useState(0);
  const editorRef = useRef(null);
  const { employeeId } = useAuthStore(); // Get the current logged-in user's ID

  // Fetch notice period days when component mounts
  useEffect(() => {
    const fetchNoticePeriod = async () => {
      try {
        const response = await getNoticePeriod();
        if (response && response.noticeSettings) {
          setNoticePeriodDays(response.noticeSettings.noticePeriodDays || 0);
        }
      } catch (error) {
        console.error("Error fetching notice period:", error);
      }
    };

    fetchNoticePeriod();
  }, []);

  // Fetch employees using the useFetchUsersData hook
  const [userData] = useFetchUsersData({
    setIsLoading: setLoadingEmployees,
    refresh: false,
    limit: 100,
  });

  // Map userData to the Employee interface expected by Autocomplete
  // Filter out the currently logged-in user
  const employees: Employee[] =
    userData
      ?.filter((userRecord: UserRecord) => userRecord._id !== employeeId)
      .map((user: UserRecord) => ({
        _id: user._id,
        employeeName: `${user.firstName || ""} ${user.lastName || ""}`,
      })) || [];

  // Define termination type options for Autocomplete
  const terminationTypes: TerminationTypeOption[] = [
    { value: "Voluntary", label: "Voluntary" },
    { value: "Involuntary", label: "Involuntary" },
  ];

  // Use Formik hook with safe access to editData
  const formik = useFormik({
    initialValues: {
      empId: editData?.empId || "",
      terminationType: editData?.terminationType || "",
      reason: editData?.reason || "",
      noticeDate: editData?.noticeDate || "",
      terminationDate: editData?.terminationDate || "",
    },
    validationSchema: terminationSchema,
    enableReinitialize: true, // Important for updating when editData changes
    onSubmit: (values) => {
      // Send the HTML content directly instead of extracting plain text
      const payload = {
        empId: values.empId,
        terminationType: values.terminationType,
        reason: values.reason, // Use the HTML content directly
        noticeDate: values.noticeDate,
        terminationDate: values.terminationDate,
      };

      console.log("Submitting termination payload:", payload);
      onSave(payload);
    },
  });

  // Calculate notice date when termination date changes, but only in add mode
  useEffect(() => {
    // Only auto-calculate in add mode, not in edit mode
    if (!editData && formik.values.terminationDate && noticePeriodDays > 0) {
      // Calculate notice date by adding notice period days to termination date
      const terminationDate = new Date(formik.values.terminationDate);
      const noticeDate = new Date(terminationDate);
      noticeDate.setDate(terminationDate.getDate() + noticePeriodDays);

      // Format the date as YYYY-MM-DD for the input field
      const formattedDate = noticeDate.toISOString().split("T")[0];
      formik.setFieldValue("noticeDate", formattedDate);
    }
  }, [formik.values.terminationDate, noticePeriodDays, editData]);

  // Reset form when dialog closes
  useEffect(() => {
    if (!open) {
      formik.resetForm();
    }
  }, [open]);

  // Reset form when switching between add and edit modes
  useEffect(() => {
    if (open) {
      console.log("Dialog opened with editData:", editData);

      if (editData) {
        // In edit mode, set values from editData
        const formValues = {
          empId: editData.empId || "",
          terminationType: editData.terminationType || "",
          reason: editData.reason || "",
          noticeDate: editData.noticeDate || "",
          terminationDate: editData.terminationDate || "",
        };

        console.log("Setting form values in edit mode:", formValues);
        formik.setValues(formValues);
      } else {
        // In add mode, reset to empty values except for terminationDate
        const formValues = {
          empId: "",
          terminationType: "",
          reason: "",
          noticeDate: "",
          terminationDate: new Date().toISOString().split("T")[0], // Set to today
        };

        console.log("Setting form values in add mode:", formValues);
        formik.setValues(formValues);
      }
    }
  }, [open, editData]);

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      PaperProps={{ className: "add-edit-dialog" }}
    >
      <DialogTitle className="add-edit-dialog-title">
        <Typography
          variant="h6"
          className="dialog-title"
          sx={{ fontSize: "20px", fontWeight: "600", color: "#202C4B" }}
        >
          {editData ? "Edit Termination" : "Add Termination"}
        </Typography>
        <CancelIcon onClick={onClose} className="cancel-icon" />
      </DialogTitle>
      <DialogContent sx={{ padding: "1rem !important" }}>
        <form onSubmit={formik.handleSubmit}>
          {/* Terminated Employee */}
          <Box className="form-field">
            <Typography variant="subtitle1" className="field-label">
              Terminated Employee <span className="required">*</span>
            </Typography>
            <Autocomplete
              options={employees}
              getOptionLabel={(option: Employee) => option.employeeName || ""}
              value={
                employees.find((emp) => emp._id === formik.values.empId) || null
              }
              onChange={(event, newValue) => {
                formik.setFieldValue("empId", newValue?._id || "");
              }}
              onBlur={() => formik.setFieldTouched("empId", true)}
              disabled={loadingEmployees || !!editData}
              ListboxProps={{
                style: { maxHeight: "200px", fontSize: "14px" },
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  error={formik.touched.empId && !!formik.errors.empId}
                  helperText={
                    formik.touched.empId && formik.errors.empId
                      ? formik.errors.empId
                      : ""
                  }
                  className="autocomplete-input"
                />
              )}
              noOptionsText="No employees available"
              loading={loadingEmployees}
            />
          </Box>

          {/* Termination Type */}
          <Box className="form-field">
            <Typography variant="subtitle1" className="field-label">
              Termination Type <span className="required">*</span>
            </Typography>
            <Autocomplete
              options={terminationTypes}
              getOptionLabel={(option: TerminationTypeOption) =>
                option.label || ""
              }
              value={
                terminationTypes.find(
                  (type) => type.value === formik.values.terminationType
                ) || null
              }
              onChange={(event, newValue) => {
                formik.setFieldValue("terminationType", newValue?.value || "");
              }}
              onBlur={() => formik.setFieldTouched("terminationType", true)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  error={
                    formik.touched.terminationType &&
                    !!formik.errors.terminationType
                  }
                  helperText={
                    formik.touched.terminationType &&
                    formik.errors.terminationType
                      ? formik.errors.terminationType
                      : ""
                  }
                  className="autocomplete-input"
                />
              )}
              noOptionsText="No termination types available"
            />
          </Box>

          {/* Notice Date - Calculated field in add mode, editable in edit mode */}
          <Box className="form-field">
            <Typography variant="subtitle1" className="field-label">
              Notice Date
            </Typography>
            <TextField
              fullWidth
              type="date"
              name="noticeDate"
              value={formik.values.noticeDate}
              disabled={!editData} // Only disabled in add mode, editable in edit mode
              onChange={(e) => {
                const newValue = e.target.value;
                console.log("Notice date changed to:", newValue);
                formik.setFieldValue("noticeDate", newValue);
              }}
              variant="outlined"
              InputLabelProps={{ shrink: true }}
              className="date-input"
              InputProps={{
                inputProps: { min: new Date().toISOString().split("T")[0] },
                sx: {
                  // padding: "5.5px 14px",
                  height: "36px",
                },
              }}
              inputProps={{
                // Remove any readonly attributes that might be preventing edits
                readOnly: false,
              }}
            />
            {!editData && formik.values.terminationDate && (
              <Typography variant="caption" color="textSecondary">
                Automatically calculated based on notice period (
                {noticePeriodDays} days)
              </Typography>
            )}
            {editData && (
              <Typography variant="caption" color="primary">
                You can manually edit the notice date in edit mode
              </Typography>
            )}
          </Box>

          {/* Reason */}
          <Box className="form-field">
            <JoditEditorWrapper
              value={formik.values.reason}
              onChange={(newContent) => {
                formik.setFieldValue("reason", newContent);
              }}
            />
          </Box>

          {/* Buttons aligned to the right */}
          <Box className="buttons-container">
            <Button
              variant="outlined"
              className="cancel-button"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button type="submit" className="submit-button">
              {editData ? "Save Changes" : "Add Termination"}
            </Button>
          </Box>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddEditTermination;

"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Typo<PERSON>,
  Paper,
  Avatar,
} from "@mui/material";
import { useCallback, useState } from "react";
import "./trainers.scss";
import {
  HomeOutlined,
  ControlPoint,
  EditNote,
  Delete,
  Circle,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import AddEditTrainers from "./AddEditTrainers";
import PolicyFilters from "@/components/policyFilter/PolicyFilters";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";
import {
  addTrainer,
  updateTrainer,
  deleteTrainer,
  getTrainerById,
} from "@/app/services/trainers/trainer.service";
import useFetchTrainerData from "../hooks/trainers/useFetchTrainerData";
import { ROLE_GROUPS } from "../constants/roles";
import useRoleAuth from "../hooks/useRoleAuth";
import ReadMore from "@/components/ReadMore/ReadMore";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";
import { useDebounce } from "../hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

// Update the Trainer interface to match what the API returns
interface Trainer {
  _id: string; // Changed from 'id' to '_id'
  firstName: string;
  lastName: string;
  role: string;
  trainerAvatar: string;
  phone: string;
  email: string;
  description: string;
  isActive: boolean;
}

interface TrainerFormValues {
  firstName: string;
  lastName: string;
  role: string;
  phone: string;
  email: string;
  description: string;
  isActive: boolean;
}

export default function TrainersContent() {
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Performance", href: "" },
    { label: "Trainers" },
  ];

  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedTrainerId, setSelectedTrainerId] = useState<string | null>(
    null
  );
  const [selectedTrainerData, setSelectedTrainerData] =
    useState<Trainer | null>(null);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>("Date Range");
  const [, setStartDate] = useState<string>("");
  const [, setEndDate] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const currentDate = new Date();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [trainerToDelete, setTrainerToDelete] = useState<
    string | number | null
  >(null);

  // Restrict access to EMployee roles only (Admin, SuperAdmin, Manager, HR, Employee)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const [trainersData, total] = useFetchTrainerData({
    setIsLoading,
    refresh,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  }) as [Trainer[], number]; // Add type assertion to match the expected return type

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );
  const rows: GridRowsProp = trainersData
    ? trainersData.map((trainer: Trainer) => ({
        id: trainer._id,
        firstName: trainer.firstName || "-",
        lastName: trainer.lastName || "-",
        trainerAvatar: trainer.trainerAvatar || "/assets/users/default.jpg",
        phone: trainer.phone || "-",
        email: trainer.email || "-",
        description: trainer.description || "-",
        isActive: trainer.isActive || false,
      }))
    : [];

  const columns: GridColDef[] = [
    {
      field: "fullName",
      headerName: "Name",
      minWidth: 191.969,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={params.row.trainerAvatar}
            alt={`${params.row.firstName} ${params.row.lastName}`}
            sx={{ width: 32, height: 32 }}
          />
          <Typography
            variant="body2"
            sx={{ color: "#202C4B", fontWeight: "500 !important" }}
          >
            {`${params.row.firstName} ${params.row.lastName}`}
          </Typography>
        </Box>
      ),
    },
    {
      field: "phone",
      headerName: "Phone",
      minWidth: 136.312,
      renderCell: (params) => (
        <Typography sx={{ color: "#6B7280", fontSize: "14px" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "email",
      headerName: "Email",
      minWidth: 211.219,
      renderCell: (params) => (
        <Typography sx={{ color: "#6B7280", fontSize: "14px" }}>
          {params.value}
        </Typography>
      ),
    },
    {
      field: "description",
      headerName: "Description",
      flex: 3, // Important for proper stretching
      minWidth: 300, // Set a minimum width
      renderCell: (params) => (
        <Box
          sx={{
            width: "100%",
            maxWidth: "100%",
            padding: "8px 0",
            color: "#6B7280",
          }}
        >
          <ReadMore
            text={params.value}
            maxChars={50}
            sx={{
              width: "100%",
              "& .MuiTypography-root": {
                width: "100%",
                whiteSpace: "normal",
                overflow: "visible",
              },
            }}
          />
        </Box>
      ),
    },
    {
      field: "isActive",
      headerName: "Status",
      minWidth: 84.4375,
      flex: 1,
      // maxWidth: 90.375,
      renderCell: (params) => (
        // <Box
        //   sx={{
        //     backgroundColor: params.value ? "#03C95A" : "#E70D0D",
        //     color: "#fff",
        //     borderRadius: "4px",
        //     display: "flex",
        //     textAlign: "center",
        //     maxWidth: "60px",
        //     justifyContent: "center",
        //     // margin: "15px 200px 0px 21px",
        //     fontSize: "10px",
        //     fontWeight: 500,
        //     padding: "0px 25px",
        //     lineHeight: "18px",
        //     letterSpacing: "0.5px",
        //     alignItems: "center",
        //   }}
        // >
        //   <Circle sx={{ fontSize: "6px", marginRight: "4px" }} />
        //   {params.value ? "Active" : "Inactive"}
        // </Box>

        <StatusToggle
          isActive={params.value}
          onChange={async (newStatus) => {
            try {
              const currentTrainer = await getTrainerById(params.row.id);
              const trainerData = currentTrainer.trainer || currentTrainer;
              await updateTrainer(params.row.id, {
                firstName: trainerData.firstName,
                lastName: trainerData.lastName,
                role: trainerData.role,
                phone: trainerData.phone,
                email: trainerData.email,
                description: trainerData.description,
                isActive: newStatus,
              });
              setRefresh(!refresh);
            } catch (error) {
              console.error("Failed to update trainer status:", error);
            }
          }}
          title={`Change status to ${params.value ? "Inactive" : "Active"}`}
        />
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  const handleEditClick = async (trainerId: string | number) => {
    setSelectedTrainerId(trainerId as string);
    setIsEditMode(true);
    try {
      const trainerData = await getTrainerById(trainerId as string);
      console.log("Fetched Trainer Data:", trainerData);
      const trainer = trainerData.trainer || trainerData;
      setSelectedTrainerData({
        _id: trainer._id, // Changed from 'id' to '_id'
        firstName: trainer.firstName || "",
        lastName: trainer.lastName || "",
        role: trainer.role || "",
        trainerAvatar: trainer.trainerAvatar || "/assets/users/default.jpg",
        phone: trainer.phone || "",
        email: trainer.email || "",
        description: trainer.description || "",
        isActive: trainer.isActive || false,
      });
      setOpenModal(true);
    } catch (error) {
      console.error("Failed to fetch trainer data:", error);
      // toast.error("Failed to load trainer data. Please try again.");
    }
  };

  const handleDeleteClick = (trainerId: string | number) => {
    setTrainerToDelete(trainerId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (trainerToDelete !== null) {
      try {
        await deleteTrainer(trainerToDelete as string);
        setDeleteDialogOpen(false);
        setTrainerToDelete(null);
        setRefresh(!refresh);
        /* toast.success("Trainer deleted successfully!", {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        }); */
      } catch (error) {
        console.error("Failed to delete trainer:", error);
        // toast.error("Failed to delete trainer. Please try again.");
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setTrainerToDelete(null);
  };

  const handleDialogSubmit = async (values: TrainerFormValues) => {
    try {
      console.log("Received values in TrainersContent:", values);

      const trainerBody = {
        firstName: values.firstName,
        lastName: values.lastName,
        role: values.role,
        phone: values.phone,
        email: values.email,
        description: values.description,
        isActive: values.isActive,
      };

      console.log("Payload being sent:", trainerBody);

      if (isEditMode && selectedTrainerId) {
        await updateTrainer(selectedTrainerId, trainerBody);
        // toast.success("Trainer updated successfully!");
      } else {
        await addTrainer(trainerBody);
        // toast.success("Trainer added successfully!");
      }
      setRefresh(!refresh);
    } catch (error) {
      console.error(
        `Failed to ${isEditMode ? "update" : "add"} trainer:`,
        error
      );
      // toast.error(
      //   `Failed to ${isEditMode ? "update" : "add"} trainer. Please try again.`
      // );
      throw error;
    }
    handleDialogClose();
  };

  const handleDialogClose = () => {
    setOpenModal(false);
    setIsEditMode(false);
    setSelectedTrainerId(null);
    setSelectedTrainerData(null);
  };

  return (
    <Box className="trainers-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="trainers-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Trainers</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box
            className="add-trainer"
            sx={{ display: "flex", gap: 1, alignItems: "center" }}
          >
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => {
                setIsEditMode(false);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ width: "14px", height: "14px" }} />
              Add Trainer
            </Button>
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 550,
              maxHeight: "calc(100vh - 550px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Trainers List</Typography>
              <PolicyFilters
                departments={[]}
                selectedDepartment={""}
                setSelectedDepartment={() => {}}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={false}
                showDepartmentFilter={false}
                showStatusFilter={false}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation=""
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              // checkboxSelection
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                // Direct approach - use model values directly
                setPage(model.page + 1);
                setPageSize(model.pageSize);
                setLimit(model.pageSize);
                // Refresh data with new pagination values
                setRefresh((prev) => !prev);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
              getRowHeight={() => "auto"} // Enable dynamic row height
              sx={{
                "& .MuiDataGrid-cell": {
                  maxHeight: "none !important",
                  overflow: "visible !important",
                  whiteSpace: "normal !important",
                  lineHeight: "1.5 !important",
                  display: "flex !important",
                  alignItems: "center !important",
                  padding: "8px 16px !important",
                },
                "& .MuiDataGrid-row": {
                  maxHeight: "none !important",
                },
              }}
            />
          </Box>
        </Paper>
      </Box>

      <AddEditTrainers
        open={openModal}
        onClose={handleDialogClose}
        onSubmit={handleDialogSubmit}
        initialValues={selectedTrainerData}
        isEditMode={isEditMode}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this trainer? This action cannot be undone."
      />
    </Box>
  );
}

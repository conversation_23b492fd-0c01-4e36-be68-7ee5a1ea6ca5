import React from "react";
import "./AddEditTrainers.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  MenuItem,
  IconButton,
  Box,
  InputLabel,
} from "@mui/material";
import { Cancel } from "@mui/icons-material";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";

interface TrainerFormValues {
  firstName: string;
  lastName: string;
  role: string;
  phone: string;
  email: string;
  description: string;
  isActive: boolean;
}

interface TrainerDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: TrainerFormValues) => void;
  initialValues?: TrainerFormValues | null;
  isEditMode?: boolean;
}

const validationSchema = Yup.object({
  firstName: Yup.string().required("First name is required"),
  lastName: Yup.string().required("Last name is required"),
  role: Yup.string().required("Role is required"),
  phone: Yup.string()
    .required("Phone is required")
    .matches(/^\d+$/, "Phone number should only contain digits")
    .length(10, "Phone number must be exactly 10 digits"),
  email: Yup.string().email("Invalid email").required("Email is required"),
  description: Yup.string().required("Description is required"),
  isActive: Yup.boolean().required("Status is required"),
});

const AddEditTrainers: React.FC<TrainerDialogProps> = ({
  open,
  onClose,
  onSubmit,
  initialValues,
  isEditMode = false,
}) => {
  const defaultValues: TrainerFormValues = {
    firstName: "",
    lastName: "",
    role: "",
    phone: "",
    email: "",
    description: "",
    isActive: true,
  };

  const handleSubmit = (values: TrainerFormValues) => {
    console.log("Submitting values from AddEditTrainers:", values);
    onSubmit(values);
  };

  const labelStyles = {
    "& .MuiFormLabel-asterisk": {
      color: "red",
    },
  };

  return (
    <Dialog
      className="dialog"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        {isEditMode ? "Edit Trainer" : "Add Trainer"}
        <IconButton onClick={onClose} sx={{ position: "absolute", right: 8 }}>
          <Cancel />
        </IconButton>
      </DialogTitle>

      <Formik
        initialValues={initialValues || defaultValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize={true}
      >
        {({ errors, touched, setFieldValue }) => (
          <Form>
            <DialogContent className="dialog-content">
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box sx={{ display: "flex", gap: 2 }}>
                  <Box sx={{ width: "50%" }}>
                    <InputLabel required sx={labelStyles}>
                      First Name
                    </InputLabel>
                    <Field
                      as={TextField}
                      fullWidth
                      name="firstName"
                      error={touched.firstName && Boolean(errors.firstName)}
                      helperText={touched.firstName && errors.firstName}
                    />
                  </Box>
                  <Box sx={{ width: "50%" }}>
                    <InputLabel required sx={labelStyles}>
                      Last Name
                    </InputLabel>
                    <Field
                      as={TextField}
                      fullWidth
                      name="lastName"
                      error={touched.lastName && Boolean(errors.lastName)}
                      helperText={touched.lastName && errors.lastName}
                    />
                  </Box>
                </Box>

                <Box sx={{ display: "flex", gap: 2 }}>
                  <Box sx={{ width: "50%" }}>
                    <InputLabel required sx={labelStyles}>
                      Role
                    </InputLabel>
                    <Field
                      as={TextField}
                      fullWidth
                      name="role"
                      error={touched.role && Boolean(errors.role)}
                      helperText={touched.role && errors.role}
                    />
                  </Box>
                  <Box sx={{ width: "50%" }}>
                    <InputLabel required sx={labelStyles}>
                      Phone
                    </InputLabel>
                    <Field
                      as={TextField}
                      fullWidth
                      name="phone"
                      error={touched.phone && Boolean(errors.phone)}
                      helperText={touched.phone && errors.phone}
                      inputProps={{
                        maxLength: 10,
                        pattern: "[0-9]*",
                        onKeyPress: (e: {
                          key: string;
                          preventDefault: () => void;
                        }) => {
                          if (!/[0-9]/.test(e.key)) {
                            e.preventDefault();
                          }
                        },
                      }}
                    />
                  </Box>
                </Box>

                <Box sx={{ width: "100%" }}>
                  <InputLabel required sx={labelStyles}>
                    Email
                  </InputLabel>
                  <Field
                    as={TextField}
                    fullWidth
                    name="email"
                    error={touched.email && Boolean(errors.email)}
                    helperText={touched.email && errors.email}
                  />
                </Box>

                <Box>
                  <InputLabel required sx={labelStyles}>
                    Description
                  </InputLabel>
                  <Field
                    as={TextField}
                    fullWidth
                    name="description"
                    multiline
                    rows={3}
                    error={touched.description && Boolean(errors.description)}
                    helperText={touched.description && errors.description}
                  />
                </Box>

                <Box>
                  <InputLabel required sx={labelStyles}>
                    Status
                  </InputLabel>
                  <Field
                    as={TextField}
                    fullWidth
                    name="isActive"
                    select
                    error={touched.isActive && Boolean(errors.isActive)}
                    helperText={touched.isActive && errors.isActive}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      const value = e.target.value === "true";
                      setFieldValue("isActive", value);
                    }}
                  >
                    <MenuItem value="true">Active</MenuItem>
                    <MenuItem value="false">Inactive</MenuItem>
                  </Field>
                </Box>
              </Box>
            </DialogContent>

            <DialogActions>
              <Button
                sx={{
                  fontWeight: "400",
                  fontSize: "14px",
                  color: "#111827",
                  border: "1px solid #E5E7EB",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                onClick={onClose}
                variant="outlined"
              >
                Cancel
              </Button>
              <Button
                sx={{
                  display: "flex",
                  gap: "8px",
                  backgroundColor: "#F26522",
                  borderColor: "#F26522",
                  color: "#FFF",
                  fontWeight: 400,
                  fontSize: "14px",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                type="submit"
                variant="contained"
              >
                {isEditMode ? "Update Trainer" : "Add Trainer"}
              </Button>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default AddEditTrainers;

/**
 * Utility functions for processing CSV and Excel files for assets
 */

// Required columns for asset data
const REQUIRED_COLUMNS = [
  "type",
  "brand",
  "category",
  "quantity",
  "serialNumber",
  "cost",
  "vendor",
  "warrentyUntil",
  "location",
];

/**
 * Process a CSV file and extract asset data
 * @param file The CSV file to process
 * @returns Array of asset data objects
 */
export const processCSVFile = async (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const csvData = event.target?.result as string;
        if (!csvData) {
          reject(new Error("Failed to read CSV file"));
          return;
        }

        // Parse CSV data
        const lines = csvData.split("\n");
        const headers = lines[0].split(",").map((header) => header.trim());

        // Validate headers
        const missingColumns = REQUIRED_COLUMNS.filter(
          (col) => !headers.includes(col)
        );
        if (missingColumns.length > 0) {
          reject(
            new Error(`Missing required columns: ${missingColumns.join(", ")}`)
          );
          return;
        }

        // Parse data rows
        const assets = [];
        for (let i = 1; i < lines.length; i++) {
          if (!lines[i].trim()) continue; // Skip empty lines

          const values = lines[i].split(",").map((value) => value.trim());
          if (values.length !== headers.length) continue; // Skip malformed lines

          const asset: Record<string, any> = {};
          headers.forEach((header, index) => {
            if (header === "serialNumber") {
              // Handle serialNumber as an array
              asset[header] = values[index].split(";").map((s) => s.trim());
            } else if (header === "quantity" || header === "cost") {
              // Convert numeric values
              asset[header] = parseFloat(values[index]);
            } else {
              asset[header] = values[index];
            }
          });

          assets.push(asset);
        }

        resolve(assets);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error("Failed to read CSV file"));
    };

    reader.readAsText(file);
  });
};

/**
 * Process an Excel file and extract asset data
 * @param file The Excel file to process
 * @returns Array of asset data objects
 */
export const processExcelFile = async (file: File): Promise<any[]> => {
  try {
    // We need to use a library like xlsx for this
    // For now, we'll use a dynamic import to avoid adding it to the bundle if not used
    const XLSX = await import("xlsx");

    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        try {
          const data = event.target?.result;
          if (!data) {
            reject(new Error("Failed to read Excel file"));
            return;
          }

          // Parse Excel data
          const workbook = XLSX.read(data, { type: "array" });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];

          // Convert to JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet);

          // Validate required columns
          if (jsonData.length > 0) {
            const firstRow = jsonData[0] as Record<string, any>;
            const missingColumns = REQUIRED_COLUMNS.filter(
              (col) => !(col in firstRow)
            );

            if (missingColumns.length > 0) {
              reject(
                new Error(
                  `Missing required columns: ${missingColumns.join(", ")}`
                )
              );
              return;
            }
          }

          // Process the data to ensure correct types
          const processedData = jsonData.map((row: any) => {
            const processedRow: Record<string, any> = { ...row };

            // Handle serialNumber as an array
            if (typeof processedRow.serialNumber === "string") {
              processedRow.serialNumber = processedRow.serialNumber
                .split(";")
                .map((s: string) => s.trim());
            } else if (processedRow.serialNumber === undefined) {
              processedRow.serialNumber = [];
            }

            // Ensure quantity and cost are numbers
            if (processedRow.quantity !== undefined) {
              processedRow.quantity = Number(processedRow.quantity);
            }

            if (processedRow.cost !== undefined) {
              processedRow.cost = Number(processedRow.cost);
            }

            return processedRow;
          });

          resolve(processedData);
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => {
        reject(new Error("Failed to read Excel file"));
      };

      reader.readAsArrayBuffer(file);
    });
  } catch (error) {
    console.error("Error processing Excel file:", error);
    throw new Error(
      "Failed to process Excel file. Make sure xlsx library is installed."
    );
  }
};

/**
 * Generate a template file for asset data
 * @returns Blob containing the template file
 */
export const generateAssetTemplateFile = async (): Promise<Blob> => {
  try {
    // We need to use a library like xlsx for this
    // For now, we'll use a dynamic import to avoid adding it to the bundle if not used
    const XLSX = await import("xlsx");

    // Create a worksheet with headers
    const worksheet = XLSX.utils.aoa_to_sheet([REQUIRED_COLUMNS]);

    // Add some example data
    XLSX.utils.sheet_add_aoa(
      worksheet,
      [
        [
          "Laptop",
          "Dell",
          "Electronics",
          1,
          "SN12345;SN67890", // Multiple serial numbers separated by semicolons
          1200,
          "Dell Inc.",
          "2025-12-31",
          "Main Office",
        ],
      ],
      { origin: "A2" }
    );

    // Create a workbook and add the worksheet
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Assets");

    // Generate the file
    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });

    return new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
  } catch (error) {
    console.error("Error generating template file:", error);
    throw new Error(
      "Failed to generate template file. Make sure xlsx library is installed."
    );
  }
};

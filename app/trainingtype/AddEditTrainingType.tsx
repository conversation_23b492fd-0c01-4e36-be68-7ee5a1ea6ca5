import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  Select,
  MenuItem,
  Box,
  IconButton,
  Typography,
  ThemeProvider,
  createTheme,
  InputLabel,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useEffect, useState } from "react";
import { Formik, Form, Field, FieldProps } from "formik";
import * as Yup from "yup";
import { getTrainingTypeById } from "../services/trainers/trainer.service";
import Loader from "@/components/Loader/Loader";
import { Cancel } from "@mui/icons-material";
import "./AddEditTrainingType.scss";
import JoditEditorWrapper from "@/components/JoditEditorWrapper";

interface AddEditTrainingDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (formData: FormData) => void;
  isEditMode: boolean;
  trainingId: string | null;
}

interface FormValues {
  type: string;
  description: string;
  isActive: boolean;
}

const theme = createTheme({
  components: {
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderColor: "#666",
            borderWidth: 1,
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: "#666",
          },
        },
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          "&.Mui-focused": {
            color: "#666",
          },
        },
      },
    },
  },
});

const validationSchema = Yup.object({
  type: Yup.string().required("Type is required"),
  description: Yup.string().required("Description is required"),
});

export default function AddEditTrainingTypeDialog({
  open,
  onClose,
  onSubmit,
  isEditMode,
  trainingId,
}: AddEditTrainingDialogProps) {
  const [initialValues, setInitialValues] = useState<FormValues>({
    type: "",
    description: "",
    isActive: true,
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isEditMode && trainingId) {
      const fetchTrainingData = async () => {
        setLoading(true);
        try {
          const response = await getTrainingTypeById(trainingId);
          const training = response.trainingType;
          console.log("Fetched training data:", training);
          setInitialValues({
            type: training.type || "",
            description: training.description || "",
            isActive: training.isActive ?? true,
          });
        } catch (error) {
          console.error("Failed to fetch training data:", error);
        } finally {
          setLoading(false);
        }
      };
      fetchTrainingData();
    } else {
      setInitialValues({
        type: "",
        description: "",
        isActive: true,
      });
      setLoading(false);
    }
  }, [isEditMode, trainingId]);

  const handleSubmitForm = (values: FormValues) => {
    const submitData = new FormData();
    Object.entries(values).forEach(([key, value]) => {
      submitData.append(key, value.toString());
    });
    onSubmit(submitData);
  };

  const labelStyles = {
    "& .MuiFormLabel-asterisk": {
      color: "red",
    },
  };

  return (
    <ThemeProvider theme={theme}>
      <Dialog
        open={open}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            onClose();
          }
        }}
        disableEscapeKeyDown
        maxWidth="sm"
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            borderRadius: 2,
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "16px !important",
          }}
        >
          <Typography
            variant="h6"
            sx={{ fontSize: "20px", fontWeight: 600, color: "#111827" }}
          >
            {isEditMode ? "Edit Training Type" : "Add Training Type"}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <Cancel sx={{ color: "#6B7280" }} />
          </IconButton>
        </DialogTitle>

        {loading ? (
          <DialogContent>
            <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
              <Loader loading={loading} />
            </Box>
          </DialogContent>
        ) : (
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmitForm}
            enableReinitialize
          >
            {(formikProps) => (
              <Form>
                <DialogContent sx={{ padding: "1rem !important" }}>
                  <Box sx={{ mb: 2 }}>
                    <InputLabel required sx={{ mb: 1, ...labelStyles }}>
                      Type
                    </InputLabel>
                    <Field name="type">
                      {({ field, meta }: FieldProps<string, FormValues>) => (
                        <TextField
                          {...field}
                          fullWidth
                          variant="outlined"
                          error={meta.touched && Boolean(meta.error)}
                          helperText={meta.touched && meta.error}
                          size="small"
                        />
                      )}
                    </Field>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <InputLabel required sx={{ mb: 1, ...labelStyles }}>
                      Description
                    </InputLabel>
                    {/* <Field name="description">
                      {({ field, meta }: FieldProps<string, FormValues>) => (
                        <TextField
                          {...field}
                          fullWidth
                          multiline
                          rows={4}
                          variant="outlined"
                          error={meta.touched && Boolean(meta.error)}
                          helperText={meta.touched && meta.error}
                          size="small"
                        />
                      )}
                    </Field> */}
                    <JoditEditorWrapper
                      value={formikProps.values.description}
                      onChange={(content) =>
                        formikProps.setFieldValue("description", content)
                      }
                      height={200}
                    />
                  </Box>

                  <Box>
                    <InputLabel required sx={{ mb: 1, ...labelStyles }}>
                      Status
                    </InputLabel>
                    <Field name="isActive">
                      {({ field }: FieldProps<boolean, FormValues>) => (
                        <FormControl fullWidth size="small">
                          <Select
                            {...field}
                            value={field.value ? "active" : "inactive"}
                            onChange={(e) => {
                              formikProps.setFieldValue(
                                "isActive",
                                e.target.value === "active"
                              );
                            }}
                          >
                            <MenuItem value="active">Active</MenuItem>
                            <MenuItem value="inactive">Inactive</MenuItem>
                          </Select>
                        </FormControl>
                      )}
                    </Field>
                  </Box>

                  <DialogActions sx={{ padding: "0px !important" }}>
                    <Button
                      sx={{
                        textTransform: "none",
                        backgroundColor: "#F8F9FA",
                        border: "1px solid #F8F9FA",
                        color: "#111827",
                      }}
                      onClick={onClose}
                      color="inherit"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      sx={{
                        bgcolor: "#ff6b35",
                        textTransform: "none",
                        "&:hover": {
                          bgcolor: "#e55a2a",
                        },
                      }}
                    >
                      {isEditMode ? "Save Changes" : "Add Training Type"}
                    </Button>
                  </DialogActions>
                </DialogContent>
              </Form>
            )}
          </Formik>
        )}
      </Dialog>
    </ThemeProvider>
  );
}

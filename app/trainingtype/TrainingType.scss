.training-container {
    .content {
        padding: 24px;

        .training-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;

            .breadcrumbs-box {
                h2 {
                    font-size: 24px;
                    font-weight: 700;
                    color: #202C4B;
                }
            }


        }

        .avatarGroup {
            padding: 10px 20px;
            .MuiAvatarGroup-avatar {
                width: 24px;
                height: 24px;
                font-size: 12px;
                background-color: #F26522;
                transition: transform 0.3s ease-in-out;

                &:hover {
                    transform: translateY(-5px);
                    z-index: 2;
                }
            }

            .avatar {
                width: 1.5rem;
                height: 1.5rem;
                transition: transform 0.3s ease-in-out;

                &:hover {
                    transform: translateY(-5px);
                }
            }

        }


        .DataGrid-container {
            border: 1px solid #E5E7EB;
            margin-top: 16px;

            .DataGrid-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px 20px;

                h5 {
                    font-size: 18px;
                    font-weight: 600;
                }

                .filter-dropdown {
                    display: flex;
                    gap: 13px;

                    button {
                        font-weight: 400;
                        font-size: 14px;
                        color: #111827;
                        border: 1px solid #E5E7EB;
                        border-radius: 5px;
                        text-transform: none;
                        padding: 8px 13.6px;
                    }

                    .sort-dropdown {
                        background-color: #F26522;
                        border-color: #F26522;
                        color: #FFF
                    }
                }
            }



            .MuiDataGrid-root {
                .MuiBox-root {
                    display: flex;

                    // SEARCH BAR FOR GRID-DATA
                    .grid-search {

                        .MuiInputBase-root {
                            border: 1px solid #E5E7EB;
                            border-radius: 5px;


                            .MuiSvgIcon-root {
                                display: none;
                            }
                        }
                    }

                    .grid-export {
                        button {
                            font-weight: 400;
                            font-size: 14px;
                            color: #111827;
                            border: 1px solid #E5E7EB;
                            border-radius: 5px;
                            text-transform: none;
                            padding: 8px 13.6px;
                        }
                    }
                }

                .MuiDataGrid-main {
                    margin-top: 10px;

                    .MuiDataGrid-virtualScroller {
                        .MuiDataGrid-topContainer {
                            .MuiDataGrid-columnHeaders {
                                .MuiDataGrid-row--borderBottom {
                                    background: #E5E7EB;

                                    .MuiDataGrid-columnHeader {
                                        .MuiDataGrid-columnHeaderDraggableContainer {
                                            .MuiDataGrid-columnHeaderTitleContainer {
                                                .MuiDataGrid-columnHeaderTitleContainerContent {
                                                    .MuiDataGrid-columnHeaderTitle {
                                                        color: #111827;
                                                        font-size: 14px;
                                                        font-weight: 600;
                                                        padding: 10px 20px;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        .MuiDataGrid-virtualScrollerContent {
                            .MuiDataGrid-virtualScrollerRenderZone {
                                .MuiDataGrid-row {
                                    .MuiDataGrid-cell {
                                        .MuiDataGrid-cell--textLeft {
                                            font-size: 14px;
                                            color: #111827;
                                            padding: 10px 20px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                .MuiDataGrid-footerContainer {
                    .MuiTablePagination-root {
                        .MuiToolbar-root {
                            display: flex;
                            justify-content: center;

                            .MuiTablePagination-spacer {
                                display: none;
                            }
                        }
                    }
                }
            }

        }

    }
}
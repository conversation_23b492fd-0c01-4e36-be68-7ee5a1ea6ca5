"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
  Typo<PERSON>,
  Paper,
} from "@mui/material";
import ReadMore from "@/components/ReadMore/ReadMore";
import React, { useCallback, useState } from "react";
import "./TrainingType.scss";
import {
  HomeOutlined,
  ControlPoint,
  EditNote,
  Delete,
  Circle,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import Loader from "@/components/Loader/Loader";
import AddEditTrainingTypeDialog from "./AddEditTrainingType";
import PolicyFilters from "@/components/policyFilter/PolicyFilters";
import { toast } from "react-toastify";
import {
  addTrainingType,
  updateTrainingType,
  deleteTrainingType,
} from "@/app/services/trainers/trainer.service";
import useFetchTrainingTypeData from "../hooks/trainingType/useFetchTrainingTypeData";
import useRoleAuth from "../hooks/useRoleAuth";
import { ROLE_GROUPS } from "../constants/roles";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";
import { useDebounce } from "../hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";

// Update the interface to match the actual data structure
interface TrainingTypeData {
  _id: string; // Changed from 'id' to '_id' to match the error message
  type: string;
  description: string;
  isActive: boolean;
}

export default function TrainingContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);

  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const [openModal, setOpenModal] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedTrainingId, setSelectedTrainingId] = useState<string | null>(
    null
  );
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [trainingToDelete, setTrainingToDelete] = useState<
    string | number | null
  >(null);

  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const currentDate = new Date();

  const [trainingTypeData, total] = useFetchTrainingTypeData({
    setIsLoading,
    refresh,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : "",
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
    search: debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined,
  }) as [TrainingTypeData[], number];

  const rows: GridRowsProp = trainingTypeData
    ? trainingTypeData.map((training: TrainingTypeData, index: number) => ({
        id: training._id || `${index + 1}`,
        type: training.type || "N/A",
        description: training.description || "N/A",
        isActive: training.isActive || false,
      }))
    : [];

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  // Restrict access to Admin roles only (Admin, SuperAdmin, Manager, HR)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // Rest of the code remains the same...
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Performance", href: "" },
    { label: "Training Type" },
  ];

  const columns: GridColDef[] = [
    { field: "type", headerName: "Type", minWidth: 144.156 },
    {
      field: "description",
      headerName: "Description",
      flex: 4,
      renderCell: (params) => (
        <Typography
          variant="body2"
          sx={{
            color: "#6B7280",
            fontSize: "14px",
            maxWidth: "900px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
        >
          <ReadMore text={params.row.description} maxChars={50} />
        </Typography>
      ),
    },
    {
      field: "isActive",
      headerName: "Status",
      minWidth: 85.25,
      // flex: 1,
      renderCell: (params) => (
        // <Box
        //   sx={{
        //     backgroundColor: params.value ? "#03C95A" : "#E70D0D",
        //     color: "#fff",
        //     borderRadius: "4px",
        //     display: "flex",
        //     textAlign: "center",
        //     maxWidth: "60px",
        //     justifyContent: "center",
        //     // margin: "15px 200px 0px 21px",
        //     fontSize: "10px",
        //     fontWeight: 500,
        //     padding: "0px 25px",
        //     lineHeight: "18px",
        //     letterSpacing: "0.5px",
        //     alignItems: "center",
        //   }}
        // >
        //   <Circle sx={{ fontSize: "6px", marginRight: "4px" }} />
        //   {params.value ? "Active" : "Inactive"}
        // </Box>

        <StatusToggle
          isActive={params.value}
          onChange={async (newStatus: boolean) => {
            try {
              await updateTrainingType(params.row.id, {
                type: params.row.type,
                description: params.row.description,
                isActive: newStatus,
              });
              setRefresh(!refresh);
              toast.success(
                `Training type status updated to ${newStatus ? "Active" : "Inactive"}`
              );
            } catch (error) {
              console.error("Failed to update training type status:", error);
              toast.error(
                "Failed to update training type status. Please try again."
              );
            }
          }}
          title={`Change status to ${params.value ? "Inactive" : "Active"}`}
        />
      ),
    },
    {
      field: "actions",
      headerName: "",
      disableExport: true,
      minWidth: 94.3125,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row.id)}
          >
            <EditNote sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row.id)}
          >
            <Delete sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  const handlePageChange = (newPage: number): void => setPage(newPage + 1);
  const handlePageSizeChange = (newPageSize: number): void => {
    setPageSize(newPageSize);
    setLimit(newPageSize);
    setPage(1);
  };

  const handleEditClick = (trainingId: string | number) => {
    setSelectedTrainingId(trainingId as string);
    setIsEditMode(true);
    setOpenModal(true);
  };

  const handleDeleteClick = (trainingId: string | number) => {
    setTrainingToDelete(trainingId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (trainingToDelete !== null) {
      try {
        await deleteTrainingType(trainingToDelete as string);
        setDeleteDialogOpen(false);
        setTrainingToDelete(null);
        setRefresh(!refresh);
        // toast.success("Training deleted successfully!");
      } catch (error) {
        console.error("Failed to delete training:", error);
        // toast.error("Failed to delete training. Please try again.");
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setTrainingToDelete(null);
  };

  const handleDialogSubmit = async (formData: FormData) => {
    try {
      const trainingBody = {
        type: formData.get("type") as string,
        description: formData.get("description") as string,
        isActive: formData.get("isActive") === "true",
      };

      if (isEditMode && selectedTrainingId) {
        await updateTrainingType(selectedTrainingId, trainingBody);
        // toast.success("Training updated successfully!");
      } else {
        await addTrainingType(trainingBody);
        // toast.success("Training added successfully!");
      }
      setRefresh(!refresh);
      setOpenModal(false);
      setIsEditMode(false);
      setSelectedTrainingId(null);
    } catch (error) {
      console.error(
        `Failed to ${isEditMode ? "update" : "add"} training:`,
        error
      );
      // toast.error(
      //   `Failed to ${isEditMode ? "update" : "add"} training. Please try again.`
      // );
    }
  };

  return (
    <Box className="training-container">
      {isLoading && <Loader loading={isLoading} />}
      <Box className="content">
        <Box className="training-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Training Type</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <Button
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 500,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => {
                setIsEditMode(false);
                setOpenModal(true);
              }}
            >
              <ControlPoint sx={{ fontSize: "14px" }} />
              Add Training type
            </Button>
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{ display: "flex", flexDirection: "column", minHeight: 550 }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Training Type List</Typography>
              <PolicyFilters
                departments={[]}
                selectedDepartment={""}
                setSelectedDepartment={() => {}}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={""}
                setSelectedDateRange={() => {}}
                setStartDate={() => {}}
                setEndDate={() => {}}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={currentDate}
                showDateRangeFilter={false}
                showDepartmentFilter={false}
                showStatusFilter={false}
                showSortByFilter={true}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => {}}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                showLeaveTypeFilter={false}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              // checkboxSelection
              rows={rows}
              columns={columns}
              rowCount={total}
              paginationMode="server"
              sortingMode="server"
              disableColumnSorting={true}
              paginationModel={{ page: page - 1, pageSize }}
              onPaginationModelChange={(model) => {
                // Direct approach - use model values directly
                setPage(model.page + 1);
                setPageSize(model.pageSize);
                setLimit(model.pageSize);
                // Refresh data with new pagination values
                setRefresh((prev) => !prev);
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
              getRowHeight={() => "auto"} // Enable dynamic row height
              sx={{
                "& .MuiDataGrid-cell": {
                  maxHeight: "none !important",
                  overflow: "visible !important",
                  whiteSpace: "normal !important",
                  lineHeight: "1.5 !important",
                  display: "flex !important",
                  alignItems: "center !important",
                  padding: "8px 16px !important",
                },
                "& .MuiDataGrid-row": {
                  maxHeight: "none !important",
                },
              }}
            />
          </Box>
        </Paper>
      </Box>

      <AddEditTrainingTypeDialog
        open={openModal}
        onClose={() => {
          setOpenModal(false);
          setIsEditMode(false);
          setSelectedTrainingId(null);
        }}
        onSubmit={handleDialogSubmit}
        isEditMode={isEditMode}
        trainingId={selectedTrainingId}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        message="Are you sure you want to delete this training type? This action cannot be undone."
      />
    </Box>
  );
}

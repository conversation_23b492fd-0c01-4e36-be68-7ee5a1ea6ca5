"use client";
import { Box, Typography } from "@mui/material";
import React, { useState, useEffect, useMemo, Suspense } from "react";
import "./ClientGrid.scss";
import { HomeOutlined, ControlPoint } from "@mui/icons-material";
import { <PERSON><PERSON> } from "@mui/material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { PeopleOutline } from "@mui/icons-material";
import AddClientForm from "../client/AddEditClient";
import CustomHeaderCard from "@/components/CustomHeaderCard/CustomHeaderCard";
import ViewToggleClient from "@/components/ViewToggleButton/ViewToggleClient";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import { toast } from "react-toastify";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import ClientCard from "@/components/ClientCard/ClientCard";
import {
  getAllClients,
  getClientById,
  deleteClient,
} from "@/app/services/clients/client.service";
import Loader from "@/components/Loader/Loader";

interface ClientsList {
  id: string;
  name: string;
  role: string;
  project: string;
  progress: number;
  company: string;
  avatar: string | undefined;
  progressColor: string;
  teamMembers: string[];
}

interface ApiClient {
  _id: string;
  clientId: string;
  firstName: string;
  lastName: string;
  userName: string;
  avatar: string;
  email: string;
  phone: string;
  company: string;
  clientDesignation: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

function ClientsGridContent() {
  const [clients, setClients] = useState<ApiClient[]>([]);
  const [totalClients, setTotalClients] = useState(0);
  const [activeCount, setActiveCount] = useState(0);
  const [inactiveCount, setInactiveCount] = useState(0);
  const [newJoiners, setNewJoiners] = useState(0);
  const [refresh, setRefresh] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [editingClient, setEditingClient] = useState<ClientsList | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deletingClientId, setDeletingClientId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // Filter states
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");

  // Fetch clients from API with startDate, endDate, and isActive
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setLoading(true);
        let isActive: boolean | undefined;
        if (selectedStatus === "Active") isActive = true;
        else if (selectedStatus === "Inactive") isActive = false;
        else isActive = undefined; // No filter for "Select Status"

        const response = await getAllClients(startDate, endDate, isActive);
        setClients(response.clients.results);
        setTotalClients(response.clients.total);
        setActiveCount(response.clients.activeCount);
        setInactiveCount(response.clients.inactiveCount);
        setNewJoiners(response.clients.newJoiners);
      } catch (error) {
        console.error("Error fetching clients:", error);
        // toast.error("Failed to fetch clients");
      } finally {
        setLoading(false);
      }
    };
    fetchClients();
  }, [refresh, startDate, endDate, selectedStatus]);

  // Filter and sort clients (only sorting is needed since isActive is handled by API)
  const filteredClients = clients.sort((a, b) => {
    const nameA = `${a.firstName} ${a.lastName}`;
    const nameB = `${b.firstName} ${b.lastName}`;
    if (selectedSortBy === "Ascending") return nameA.localeCompare(nameB);
    if (selectedSortBy === "Descending") return nameB.localeCompare(nameA);
    return 0;
  });

  // Map API data to ClientsList interface with hardcoded values for missing fields
  const profiles = useMemo<ClientsList[]>(
    () =>
      filteredClients.map((client) => ({
        id: client._id,
        name: `${client.firstName} ${client.lastName}`,
        role: client.clientDesignation || "N/A",
        project: "HRMS Managment",
        progress: 50,
        company: client.company || "N/A",
        avatar: client.avatar || undefined,
        progressColor: client.isActive ? "green" : "red",
        teamMembers: [],
      })),
    [filteredClients]
  );

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Client", href: "" },
    { label: "ClientsGrid" },
  ];

  const refreshList = () => {
    setRefresh(!refresh);
  };

  // Handle edit action
  const handleEditClick = async (id: string) => {
    try {
      const clientResponse = await getClientById(id);
      const client = clientResponse.client;
      if (client) {
        const clientData: ClientsList = {
          id: client._id,
          name: `${client.firstName} ${client.lastName}`,
          role: client.clientDesignation || "N/A",
          project: "Default Project",
          progress: 50,
          company: client.company || "N/A",
          avatar: client.avatar || undefined,
          progressColor: client.isActive ? "green" : "red",
          teamMembers: [],
        };
        setEditingClient(clientData);
        setEditModal(true);
      }
    } catch (error) {
      console.error("Error fetching client for edit:", error);
      // toast.error("Failed to fetch client data");
    }
  };

  // Handle delete action with confirmation
  const handleDeleteClick = (id: string) => {
    setDeletingClientId(id);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (deletingClientId) {
      try {
        await deleteClient(deletingClientId);

        toast.success("Client deleted successfully");
        refreshList();
      } catch (error) {
        console.error("Error deleting client:", error);
        // toast.error("Failed to delete client");
      }
    }
    setDeleteModalOpen(false);
    setDeletingClientId(null);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteModalOpen(false);
    setDeletingClientId(null);
  };

  return (
    <Box className="employee-container">
      <Box className="content">
        <Box className="employee-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Clients List</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>

          <Box className="add-policy">
            <ViewToggleClient />
            <Button
              variant="contained"
              className="add-employee-btn"
              onClick={() => setOpenModal(true)}
              disabled={loading}
            >
              <ControlPoint sx={{ fontSize: "16px" }} />
              Add Client
            </Button>
          </Box>
        </Box>

        {/* Header Cards with Dynamic Data from API */}
        <Box className="header-cards">
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Total Clients"
            value={totalClients}
            percentage="+19.01%"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Active"
            value={activeCount}
            percentage="+12.5%"
            iconBgColor="#03C95A"
            badgeBgColor="rgba(242, 101, 34, 0.1)"
            badgeTextColor="#F26522"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Inactive"
            value={inactiveCount}
            percentage="+19.01%"
            iconBgColor="#E70D0D"
            badgeBgColor="rgba(242, 101, 34, 0.1)"
            badgeTextColor="#F26522"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="New Joiners"
            value={newJoiners}
            percentage="+12.5%"
            iconBgColor="#1B84FF"
            badgeBgColor="rgba(59, 112, 128, 0.1)"
            badgeTextColor="#3B7080"
            showIcon={true}
          />
        </Box>

        {/* DataGrid Container with Filters */}
        <Box className="Client-DataGrid-container">
          <Box className="DataGrid-header">
            <Typography variant="h5">Clients Grid</Typography>
            <PolicyFilters
              departments={[]}
              designations={[]}
              selectedDepartment=""
              setSelectedDepartment={() => {}}
              selectedDesignation=""
              setSelectedDesignation={() => {}}
              selectedSortBy={selectedSortBy}
              setSelectedSortBy={setSelectedSortBy}
              selectedDateRange={selectedDateRange}
              setSelectedDateRange={setSelectedDateRange}
              setStartDate={setStartDate}
              setEndDate={setEndDate}
              selectedStatus={selectedStatus}
              setSelectedStatus={setSelectedStatus}
              setPage={() => {}}
              currentDate={currentDate}
              showDateRangeFilter={true}
              showDepartmentFilter={false}
              showStatusFilter={true}
              showSortByFilter={true}
              showDesignationFilter={false}
              selectedLeaveType="Leave Type"
              setSelectedLeaveType={() => {}}
              showLeaveTypeFilter={false}
              selectedPriority=""
              setSelectedPriority={() => {}}
            />
          </Box>
        </Box>

        {/* Profile Cards */}
        <Box className="Profile-Cards">
          {loading ? (
            <Loader loading={loading} />
          ) : (
            profiles.map((profile) => (
              <ClientCard
                key={profile.id}
                id={profile.id}
                name={profile.name}
                role={profile.role}
                project={profile.project}
                progress={profile.progress}
                company={profile.company}
                avatar={profile.avatar || ""}
                teamMembers={profile.teamMembers}
                onEdit={handleEditClick}
                onDelete={handleDeleteClick}
                loading={loading}
              />
            ))
          )}
        </Box>
      </Box>

      {/* Add Client Dialog */}
      <AddClientForm
        open={openModal}
        onClose={() => setOpenModal(false)}
        refreshList={refreshList}
      />

      {/* Edit Client Dialog */}
      {editingClient && (
        <AddClientForm
          open={editModal}
          onClose={() => {
            setEditModal(false);
            setEditingClient(null);
          }}
          refreshList={refreshList}
          isEdit
          employeeData={{
            _id: editingClient.id,
            firstName: editingClient.name.split(" ")[0],
            lastName: editingClient.name.split(" ")[1] || "",
            username:
              clients.find((c) => c._id === editingClient.id)?.userName || "",
            email: clients.find((c) => c._id === editingClient.id)?.email || "",
            password: "",
            confirmPassword: "",
            phone: clients.find((c) => c._id === editingClient.id)?.phone || "",
            company: editingClient.company,
            avatar: editingClient.avatar,
            clientDesignation:
              editingClient.role !== "N/A" ? editingClient.role : "",
          }}
        />
      )}

      <DeleteConfirmationDialog
        open={deleteModalOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Confirm Delete"
        message="You want to delete this client? This action cannot be undone."
      />
    </Box>
  );
}

function ClientsGridPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ClientsGridContent />
    </Suspense>
  );
}

export default ClientsGridPage;

"use client";

import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, Chip, IconButton, Menu, MenuItem, ListItemIcon } from "@mui/material";
import { ControlPoint, MoreVert, EditOutlined, DeleteOutlineOutlined } from "@mui/icons-material";
import { ReactNode, useState } from "react";
import { SortableContext, verticalListSortingStrategy, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

interface TaskColumnProps {
  title: string;
  count: string;
  color: string;
  taskIds: string[];
  statusId: string; // Added to identify the status
  children: ReactNode;
  onNewTaskClick?: () => void;
  onEditClick: (statusId: string) => void; // Trigger edit dialog
  onDeleteClick: (statusId: string) => void; // Trigger delete
}

const TaskColumn = ({
  title,
  count,
  color,
  taskIds,
  statusId,
  children,
  onNewTaskClick,
  onEditClick,
  onDeleteClick,
}: TaskColumnProps) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: title,
    data: { columnId: title },
  });

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    onEditClick(statusId);
    handleMenuClose();
  };

  const handleDelete = () => {
    onDeleteClick(statusId);
    handleMenuClose();
  };

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <Box
      className="task-column"
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      data-column-id={title}
    >
      <Box className="column-header">
        <Box className="column-title">
          <Box className="status-indicator" sx={{ backgroundColor: color }} />
          <Typography className="column-typo">{title}</Typography>
          <Chip label={count} size="small" className="task-count" />
        </Box>
        <IconButton size="small" onClick={handleMenuOpen}>
          <MoreVert sx={{ fontSize: "16px" }} />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
          transformOrigin={{ vertical: "top", horizontal: "right" }}
          sx={{ padding: "1rem !important" }}
        >
          <MenuItem onClick={handleEdit} sx={{ gap: 1, fontSize: "14px" }}>
            <ListItemIcon>
              <EditOutlined sx={{ fontSize: "18px", color: "#6B7280" }} />
            </ListItemIcon>
            Edit
          </MenuItem>
          <MenuItem onClick={handleDelete} sx={{ gap: 1, fontSize: "14px", color: "#DC2626" }}>
            <ListItemIcon>
              <DeleteOutlineOutlined sx={{ fontSize: "18px", color: "#DC2626" }} />
            </ListItemIcon>
            Delete
          </MenuItem>
        </Menu>
      </Box>

      <SortableContext id={title} items={taskIds} strategy={verticalListSortingStrategy}>
        <Box className="column-content">
          {children}
          <Button
            startIcon={<ControlPoint />}
            className="new-task-btn"
            fullWidth
            onClick={onNewTaskClick}
          >
            New Task
          </Button>
        </Box>
      </SortableContext>
    </Box>
  );
};

export default TaskColumn;
"use client";

import {
  <PERSON>, <PERSON><PERSON><PERSON>, Button, TextField, InputAdornment,
  Select, MenuItem, FormControl, Avatar
} from "@mui/material";
import { useState, useEffect } from "react";
import { HomeOutlined, ControlPoint, Search } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import TaskColumn from "./TaskColumn";
import TaskCard from "./TaskCard";
import {
  getAllProjects, updateTask, getTasks, getStatusBoard, deleteStatus
} from "@/app/services/projects/project.service";
import Loader from "@/components/Loader/Loader";
import AddTaskDialog from "@/components/AddTaskDialog/AddTaskDialog";
import AddBoardDialog from "./AddEditBoard";
import {
  DndContext, closestCenter, DragEndEvent,
  PointerSensor, useSensor, useSensors
} from "@dnd-kit/core";
import { toast } from "react-toastify";
import {
  SortableContext, verticalListSortingStrategy
} from "@dnd-kit/sortable";
import PolicyFilters from "@/components/policyFilter/PolicyFilters";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";

// Interfaces for API responses
interface TeamMember {
  _id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  email?: string;
  employeeId?: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Project {
  _id: string;
  projectName: string;
  isActive: boolean;
  isDeleted: boolean;
  teamMembers?: TeamMember[];
}

interface Status {
  _id: string;
  title: string;
  hexaColour: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Task {
  _id: string;
  title: string;
  dueDate: string;
  teamMembers: TeamMember[];
  status?: Status;
  priority: string;
  hours: number;
  isActive: boolean;
  isDeleted: boolean;
  projectId: Project;
  description?: string;
  createdAt?: string;
}

interface ProjectsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  projects: {
    results: Project[];
    total: number;
    activeCount: number;
    inactiveCount: number;
    page: string;
    limit: number;
    totalPages: number;
  };
  meta: {
    version: string;
    forceUpdate: boolean;
    maintenance: boolean;
    hasUpdate: boolean;
  };
}

interface TasksResponse {
  success: boolean;
  statusCode: number;
  message: string;
  task: {
    results: Task[];
    total: number;
    activeCount: number;
    inactiveCount: number;
    page: number;
    limit: number;
    totalPages: number;
    statusCounts: {
      total: number;
      inProgress: number;
      completed: number;
      onHold: number;
      Pending: number;
    };
    globalStatusCounts: {
      total: number;
      inProgress: number;
      completed: number;
      onHold: number;
      Pending: number;
      totalHours: number;
    };
  };
  meta: {
    version: string;
    forceUpdate: boolean;
    maintenance: boolean;
    hasUpdate: boolean;
  };
}

interface StatusBoardResponse {
  success: boolean;
  statusCode: number;
  message: string;
  taskBoard: {
    results: Status[];
    total: number;
    activeCount: number;
    inactiveCount: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  meta: {
    version: string;
    forceUpdate: boolean;
    maintenance: boolean;
    hasUpdate: boolean;
  };
}

interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: string | React.ReactNode;
}

interface Board {
  name: string;
  hexCode: string;
  statusId: string;
}

function Tasks() {
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: "", href: "/", icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" /> },
    { label: "Projects", href: "" },
    { label: "Task Board", href: "" },
  ];

  const [selectedPriority, setSelectedPriority] = useState<string>("All");
  const priorities: string[] = ["All", "High", "Medium", "Low"];
  const [projects, setProjects] = useState<Project[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<string>("");
  const [loadingProjects, setLoadingProjects] = useState<boolean>(true);
  const [loadingTasks, setLoadingTasks] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [openTaskModal, setOpenTaskModal] = useState<boolean>(false);
  const [openBoardModal, setOpenBoardModal] = useState<boolean>(false);
  const [editTaskId, setEditTaskId] = useState<string | null>(null);
  const [editBoardId, setEditBoardId] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] = useState<string>("Date Range");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [search, setSearch] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [boards, setBoards] = useState<Board[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<string>("All Employees");
  const [selectedDueDate, setSelectedDueDate] = useState<string>("");

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 8 } })
  );

  const fetchStatuses = async () => {
    try {
      const response: StatusBoardResponse = await getStatusBoard();
      const statuses = response.taskBoard.results.filter(
        (status) => status.isActive && !status.isDeleted
      );
      const newBoards: Board[] = statuses.map((status) => ({
        name: status.title,
        hexCode: status.hexaColour || "#ffffff",
        statusId: status._id,
      }));
      setBoards(newBoards);
    } catch (err) {
      console.error("Failed to load statuses:", err);
      setError("Failed to load task board statuses");
    }
  };

  useEffect(() => {
    fetchStatuses();
  }, []);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoadingProjects(true);
        const response: ProjectsResponse = await getAllProjects();
        const fetchedProjects = response.projects.results;
        setProjects(fetchedProjects);
        if (fetchedProjects.length > 0) {
          setSelectedProjectId(fetchedProjects[0]._id);
        }
      } catch (err) {
        setError("Failed to load projects");
        console.error(err);
      } finally {
        setLoadingProjects(false);
      }
    };
    fetchProjects();
  }, []);

  const fetchTasks = async () => {
    if (!selectedProjectId) return;
    try {
      setLoadingTasks(true);
      const status = selectedStatus === "Select Status" ? "" : selectedStatus;
      const order = selectedSortBy === "Sort By" ? "" : selectedSortBy;
      const employeeId = selectedEmployee.includes("|") ? selectedEmployee.split("|")[1] : undefined;
      const dueDate = selectedDueDate || undefined;
      const response: TasksResponse = await getTasks(
        selectedProjectId,
        order,
        startDate,
        endDate,
        status,
        search,
        undefined,
        employeeId,
        dueDate
      );
      setTasks(response.task.results);
    } catch (err) {
      setError("Failed to load tasks");
      console.error(err);
    } finally {
      setLoadingTasks(false);
    }
  };

  useEffect(() => {
    fetchTasks();
  }, [selectedProjectId, selectedStatus, selectedSortBy, startDate, endDate, search, page, selectedEmployee, selectedDueDate]);

  const refreshTasksList = () => {
    fetchTasks();
  };

  const handleNewTaskClick = () => {
    setEditTaskId(null);
    setOpenTaskModal(true);
  };

  const handleAddBoardClick = () => {
    setEditBoardId(null);
    setOpenBoardModal(true);
  };

  const handleEditBoardClick = (statusId: string) => {
    setEditBoardId(statusId);
    setOpenBoardModal(true);
  };

  const handleDeleteBoardClick = async (statusId: string) => {
    const board = boards.find((b) => b.statusId === statusId);
    if (!board) return;

    try {
      await deleteStatus(statusId);
      toast.success(`Board "${board.name}" deleted successfully`);
      fetchStatuses();
      refreshTasksList();
    } catch (error: any) {
      toast.error(error.message || "Failed to delete board");
      console.error("Error deleting board:", error);
    }
  };

  const handleCloseTaskModal = () => {
    setOpenTaskModal(false);
    setEditTaskId(null);
  };

  const handleCloseBoardModal = () => {
    setOpenBoardModal(false);
    setEditBoardId(null);
  };

  const handleBoardActionSuccess = () => {
    fetchStatuses();
    refreshTasksList();
  };

  const onDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      console.log("No valid drop target or same task");
      return;
    }

    const taskId = active.id as string;
    const sourceColumn = active.data.current?.columnId as string;
    const destColumn = over.data.current?.columnId as string;

    if (!destColumn || sourceColumn === destColumn) {
      console.log("No change in column or invalid destColumn:", { sourceColumn, destColumn });
      return;
    }

    const columnToStatusMap: { [key: string]: { id: string; title: string } } = {};
    boards.forEach((board) => {
      columnToStatusMap[board.name] = { id: board.statusId, title: board.name };
    });

    const newStatus = columnToStatusMap[destColumn];

    if (!newStatus) {
      toast.error(`Invalid column: ${destColumn}`);
      console.error(`No status ID mapped for column: ${destColumn}`);
      return;
    }

    try {
      setTasks((prev) =>
        prev.map((task) => {
          if (task._id !== taskId) return task;
          const currentStatus = task.status || {};
          return {
            ...task,
            status: {
              _id: newStatus.id,
              title: newStatus.title,
              hexaColour: (currentStatus as Status).hexaColour ?? "#ffffff",
              isActive: (currentStatus as Status).isActive ?? true,
              isDeleted: (currentStatus as Status).isDeleted ?? false,
            }
          };
        })
      );

      await updateTask(taskId, { status: newStatus.id });
      toast.success(`Task moved to ${destColumn} successfully`);
    } catch (error: any) {
      toast.error(error.message || `Failed to move task to ${destColumn}`);
      console.error("Error updating task status:", error);
      refreshTasksList();
    }
  };

  const totalTasks = tasks.length;
  const pendingTasks = tasks.filter((task) => task.status?.title === "Pending").length;
  const completedTasks = tasks.filter((task) => task.status?.title === "Completed").length;

  const filteredTasks =
    selectedPriority === "All"
      ? tasks
      : tasks.filter((task) => task.priority.toLowerCase() === selectedPriority.toLowerCase());

  const tasksByStatus: { [key: string]: Task[] } = {};
  boards.forEach((board) => {
    tasksByStatus[board.name] = [];
  });

  filteredTasks.forEach((task) => {
    const columnTitle = boards.find((board) => board.statusId === task.status?._id)?.name || "Pending";
    tasksByStatus[columnTitle].push(task);
  });

  const statusToProgress: { [key: string]: number } = {
    ToDo: 10,
    Pending: 20,
    Completed: 100,
    InProgress: 50,
  };

  if (loadingProjects) {
    return <Loader loading />;
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={onDragEnd}>
        <Box className="tasks-container">
          <Box className="content">
            <Box className="tasks-header">
              <Box className="breadcrumbs-box">
                <Typography variant="h2">Tasks</Typography>
                <BreadcrumbsComponent items={breadcrumbItems} />
              </Box>
              <Box sx={{ display: "flex", gap: 0, alignItems: "center" }}>
                <Button
                  className="add-board"
                  variant="contained"
                  startIcon={<ControlPoint sx={{ width: "16px", height: "16px" }} />}
                  onClick={handleAddBoardClick}
                >
                  Add Board
                </Button>
              </Box>
            </Box>

            <Box className="board-header">
              <FormControl sx={{ minWidth: 250 }}>
                <Select
                  labelId="project-select-label"
                  value={selectedProjectId}
                  onChange={(e) => setSelectedProjectId(e.target.value)}
                  className="board-title"
                  disabled={loadingTasks}
                >
                  {projects.map((project) => (
                    <MenuItem key={project._id} value={project._id}>
                      {project.projectName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Box className="board-stats">
                {loadingTasks ? (
                  <Loader loading />
                ) : (
                  <>
                    <Box sx={{ display: "flex", flexDirection: "row", justifyContent: "flex-start" }}>
                      {tasks.length > 0 &&
                        tasks[0].projectId?.teamMembers?.slice(0, 3).map((member) => (
                          <Avatar
                            key={member._id}
                            src={member.avatar}
                            className="task-avatar"
                            sx={{
                              width: 28,
                              height: 28,
                              border: "2px solid white",
                              marginLeft: "-5px",
                              "&:first-of-type": { marginLeft: 0 },
                              backgroundColor: "#F26522",
                            }}
                          />
                        ))}
                      {tasks.length > 0 && (tasks[0].projectId?.teamMembers?.length ?? 0) > 3 && (
                        <Avatar
                          className="task-avatar"
                          sx={{
                            width: 28,
                            height: 28,
                            border: "2px solid white",
                            marginLeft: "-5px",
                            fontSize: "0.75rem",
                            backgroundColor: "#F26522",
                          }}
                        >
                          +{(tasks[0].projectId?.teamMembers?.length ?? 0) - 3}
                        </Avatar>
                      )}
                    </Box>

                    <Box className="stat-chip">
                      <Typography variant="body2">Total Task: {totalTasks}</Typography>
                    </Box>

                    <Box className="stat-chip">
                      <Typography variant="body2">Pending: {pendingTasks}</Typography>
                    </Box>

                    <Box className="stat-chip">
                      <Typography variant="body2">Completed: {completedTasks}</Typography>
                    </Box>
                  </>
                )}

                <TextField
                  placeholder="Search Task"
                  size="small"
                  value={search}
                  onChange={(e) => {
                    setSearch(e.target.value);
                    setPage(1);
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search fontSize="small" />
                      </InputAdornment>
                    ),
                  }}
                  className="search-field"
                />
              </Box>
            </Box>

            <Box className="filter-bar">
              <Box className="priority-filter">
                <Typography variant="body2" className="metadata">Priority</Typography>
                <Box className="priority-options">
                  {priorities.map((priority) => (
                    <Button
                      key={priority}
                      variant={selectedPriority === priority ? "contained" : "text"}
                      size="small"
                      onClick={() => {
                        setSelectedPriority(priority);
                        setPage(1);
                      }}
                      className={`priority-btn ${selectedPriority === priority ? "active" : ""}`}
                    >
                      {priority}
                    </Button>
                  ))}
                </Box>
              </Box>

              <PolicyFilters
                departments={[]}
                designations={[]}
                selectedDepartment="Department"
                setSelectedDepartment={() => {}}
                selectedDesignation="Designation"
                setSelectedDesignation={() => {}}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                selectedLeaveType="Leave Type"
                setSelectedLeaveType={() => {}}
                selectedPriority={selectedPriority}
                setSelectedPriority={setSelectedPriority}
                selectedDueDate={selectedDueDate}
                setSelectedDueDate={setSelectedDueDate}
                setPage={setPage}
                currentDate={new Date()}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showDesignationFilter={false}
                showStatusFilter={false}
                showSortByFilter={false}
                showLeaveTypeFilter={false}
                showPriorityFilter={false}
                showEmployeeFilter={true}
                showDueDateFilter={true}
                optionalStatusFilter={true}
                selectedEmployee={selectedEmployee}
                setSelectedEmployee={setSelectedEmployee}
              />
            </Box>

            <Box className="task-board">
              {loadingTasks || boards.length === 0 ? (
                <Loader loading />
              ) : (
                Object.entries(tasksByStatus).map(([columnTitle, tasks]) => {
                  const board = boards.find((b) => b.name === columnTitle);
                  return (
                    <TaskColumn
                      key={columnTitle}
                      title={columnTitle}
                      count={tasks.length.toString().padStart(2, "0")}
                      color={board?.hexCode || "#ffffff"}
                      taskIds={tasks.map((task) => task._id)}
                      statusId={board?.statusId || ""}
                      onNewTaskClick={handleNewTaskClick}
                      onEditClick={handleEditBoardClick}
                      onDeleteClick={handleDeleteBoardClick}
                    >
                      <SortableContext
                        id={columnTitle}
                        items={tasks.map((task) => task._id)}
                        strategy={verticalListSortingStrategy}
                      >
                        {tasks.map((task) => (
                          <TaskCard
                            key={task._id}
                            taskId={task._id}
                            projectId={selectedProjectId || ""}
                            title={task.title}
                            progress={statusToProgress[task.status?.title || "Pending"] || 10}
                            dueDate={new Date(task.dueDate).toLocaleDateString("en-US", {
                              day: "2-digit",
                              month: "short",
                              year: "numeric",
                            })}
                            priority={task.priority}
                            commentCount={14}
                            teamMembers={task.teamMembers}
                            refreshList={refreshTasksList}
                            columnId={columnTitle}
                          />
                        ))}
                      </SortableContext>
                    </TaskColumn>
                  );
                })
              )}
            </Box>
          </Box>

          <AddTaskDialog
            open={openTaskModal}
            onClose={handleCloseTaskModal}
            refreshList={refreshTasksList}
            editTaskId={editTaskId}
            projectId={selectedProjectId || ""}
          />

          <AddBoardDialog
            open={openBoardModal}
            onClose={handleCloseBoardModal}
            onSuccess={handleBoardActionSuccess}
            existingBoards={boards.map((b) => ({ name: b.name, statusId: b.statusId }))}
            editBoardId={editBoardId}
          />
        </Box>
      </DndContext>
    </LocalizationProvider>
  );
}

export default Tasks;
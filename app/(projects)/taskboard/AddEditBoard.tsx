"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  IconButton,
  TextField,
  Button,
  Box,
  Typography,
  DialogActions,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { toast } from "react-toastify";
import { useFormik } from "formik";
import * as Yup from "yup";
import "./AddEditBoard.scss";
import Loader from "@/components/Loader/Loader";
import { useEffect, useState } from "react";
import { createStatus, updateStatus, getStatusById } from "@/app/services/projects/project.service";

interface AddBoardDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  existingBoards: { name: string; statusId: string }[];
  editBoardId?: string | null;
}

const AddBoardDialog: React.FC<AddBoardDialogProps> = ({
  open,
  onClose,
  onSuccess,
  existingBoards,
  editBoardId,
}) => {
  const [loading, setLoading] = useState<boolean>(false);

  const validationSchema = Yup.object({
    boardName: Yup.string()
      .required("Board Name is required")
      .test(
        "unique-board-name",
        "A board with this name already exists",
        (value) => {
          if (!value) return true;
          return !existingBoards.some(
            (board) =>
              board.name.toLowerCase() === value.toLowerCase() &&
              board.statusId !== editBoardId // Allow same name for the board being edited
          );
        }
      ),
    hexCode: Yup.string()
      .required("Hex Code is required")
      .matches(/^#[0-9A-Fa-f]{6}$/, "Invalid hex code format (e.g., #FF0000)"),
  });

  const formik = useFormik({
    initialValues: {
      boardName: "",
      hexCode: "#000000",
    },
    validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      try {
        if (editBoardId) {
          await updateStatus(editBoardId, {
            title: values.boardName,
            hexaColour: values.hexCode,
          });
          toast.success(`Board "${values.boardName}" updated successfully`);
        } else {
          await createStatus({
            title: values.boardName,
            hexaColour: values.hexCode,
          });
          toast.success(`Board "${values.boardName}" added successfully`);
        }
        onSuccess();
        formik.resetForm();
        onClose();
      } catch (error: any) {
        toast.error(error.message || `Failed to ${editBoardId ? "update" : "add"} board`);
        console.error(`Error ${editBoardId ? "updating" : "adding"} board:`, error);
      } finally {
        setLoading(false);
      }
    },
  });

  useEffect(() => {
    if (!open) {
      formik.resetForm();
      return;
    }

    if (editBoardId) {
      setLoading(true);
      getStatusById(editBoardId)
        .then((response) => {
          const board = response.taskBoard;
          formik.setValues({
            boardName: board.title,
            hexCode: board.hexaColour || "#000000",
          });
        })
        .catch((error) => {
          toast.error("Failed to load board data");
          console.error("Error fetching board data:", error);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [open, editBoardId]);

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
      className="board-dialog"
    >
      <DialogTitle className="dialog-title">
        <Box className="title-container">
          <Typography variant="h6" className="dialog-title-text">
            {editBoardId ? "Edit Board" : "Add New Board"}
          </Typography>
        </Box>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            backgroundColor: "#6b7280",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": { backgroundColor: "#d55a1d" },
            "& .MuiSvgIcon-root": { fontSize: "14px" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent className="dialog-content">
        {loading ? (
          <Loader loading={loading} />
        ) : (
          <form onSubmit={formik.handleSubmit}>
            <Box className="board-info-tab">
              <Box className="form-field">
                <Typography variant="subtitle1">
                  Board Name <span className="required">*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="boardName"
                  value={formik.values.boardName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.boardName && Boolean(formik.errors.boardName)}
                  helperText={formik.touched.boardName && formik.errors.boardName}
                />
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Hex Code <span className="required">*</span>
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <input
                    type="color"
                    name="hexCode"
                    value={formik.values.hexCode}
                    onChange={(e) => {
                      formik.setFieldValue("hexCode", e.target.value);
                    }}
                    style={{
                      height: "40px",
                      width: "40px",
                      border: "none",
                      background: "none",
                      cursor: "pointer",
                    }}
                  />
                  <TextField
                    fullWidth
                    variant="outlined"
                    name="hexCode"
                    value={formik.values.hexCode}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.hexCode && Boolean(formik.errors.hexCode)}
                    helperText={formik.touched.hexCode && formik.errors.hexCode}
                  />
                </Box>
              </Box>
            </Box>

            <DialogActions className="dialog-actions">
              <Button
                variant="outlined"
                onClick={onClose}
                className="cancel-button"
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                type="submit"
                className="save-button"
                disabled={loading}
              >
                {editBoardId ? "Update Board" : "Add Board"}
              </Button>
            </DialogActions>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AddBoardDialog;
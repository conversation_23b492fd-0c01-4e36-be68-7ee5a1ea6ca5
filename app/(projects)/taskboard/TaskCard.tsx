"use client";

import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  <PERSON>u,
  <PERSON>u<PERSON><PERSON>,
  IconButton,
  Avatar,
  Divider,
  ListItemIcon,
} from "@mui/material";
import { useState } from "react";
import { MoreVert, ChatBubbleOutline, CheckCircle } from "@mui/icons-material";
import AddTaskDialog from "@/components/AddTaskDialog/AddTaskDialog";
import { deleteTask } from "@/app/services/projects/project.service";
import { toast } from "react-toastify";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";

interface TeamMember {
  _id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  email?: string;
  employeeId?: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface TaskCardProps {
  title: string;
  progress: number;
  dueDate: string;
  priority: string;
  commentCount?: number;
  checkCount?: number;
  teamMembers?: TeamMember[];
  taskId: string;
  projectId: string;
  refreshList?: () => void;
  columnId: string;
}

const TaskCard = ({
  title,
  progress,
  dueDate,
  priority,
  commentCount = 14,
  checkCount = 14,
  teamMembers = [],
  taskId,
  projectId,
  refreshList,
  columnId,
}: TaskCardProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);

  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: taskId,
    data: { columnId },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    setDialogOpen(true);
    handleMenuClose();
  };

  const handleDelete = async () => {
    try {
      await deleteTask(taskId);
      toast.success("Task deleted successfully");
      if (refreshList) {
        refreshList();
      }
    } catch (error) {
      // Error handling is managed in deleteTicket
    }
    handleMenuClose();
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  const getPriorityColor = (priority: string): string => {
    switch (priority.toLowerCase()) {
      case "high":
        return "#f44336";
      case "medium":
        return "#ff9800";
      case "low":
        return "#4caf50";
      default:
        return "#f44336";
    }
  };

  const getProgressColor = (progress: number): string => {
    if (progress === 100) return "#4caf50";
    if (progress >= 60) return "#8e24aa";
    if (progress >= 35) return "#ff9800";
    return "#f44336";
  };

  return (
    <>
      <Box
        className="task-card"
        ref={setNodeRef}
        style={style}
        {...attributes}
        {...listeners}
      >
        <Box className="task-card-header">
          <Box className="task-type-priority">
            <Box
              className="priority-chip"
              sx={{
                backgroundColor: getPriorityColor(priority),
                color: "#fff",
                borderRadius: "4px",
                padding: "2px 8px",
                fontSize: "10px",
              }}
            >
              {priority}
            </Box>
          </Box>
          <IconButton onClick={handleMenuOpen}>
            <MoreVert sx={{ fontSize: "14px" }} />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
            transformOrigin={{ vertical: "top", horizontal: "right" }}
            sx={{ padding: " 1rem !important" }}
          >
            <MenuItem onClick={handleEdit} sx={{ gap: 1, fontSize: "14px" }}>
              <ListItemIcon>
                <EditOutlinedIcon sx={{ fontSize: "18px", color: "#6B7280" }} />
              </ListItemIcon>
              Edit
            </MenuItem>
            <MenuItem onClick={handleDelete} sx={{ gap: 1, fontSize: "14px", color: "#DC2626" }}>
              <ListItemIcon>
                <DeleteOutlineOutlinedIcon sx={{ fontSize: "18px", color: "#DC2626" }} />
              </ListItemIcon>
              Delete
            </MenuItem>
          </Menu>
        </Box>

        <Typography variant="subtitle1" className="task-title">
          {title}
        </Typography>

        <Box className="progress-bar-container">
          <Box
            className="progress-bar"
            style={{
              width: `${progress}%`,
              backgroundColor: getProgressColor(progress),
            }}
          />
        </Box>

        <Box className="task-card-footer">
          <Typography variant="body2" className="due-date">
            Due on: {dueDate}
          </Typography>

          <Divider className="task-divider" />

          <Box className="task-meta">
            <Box sx={{ display: "flex", flexDirection: "row", justifyContent: "flex-start" }}>
              {teamMembers.slice(0, 4).map(member => (
                <Avatar
                  key={member._id}
                  src={member.avatar}
                  className="task-avatar"
                  sx={{
                    width: 24,
                    height: 24,
                    border: "2px solid white",
                    marginLeft: "-8px",
                    "&:first-of-type": { marginLeft: 0 },
                    backgroundColor: "#F26522",
                  }}
                />
              ))}
              {teamMembers.length > 4 && (
                <Avatar
                  className="task-avatar"
                  sx={{
                    width: 24,
                    height: 24,
                    border: "2px solid white",
                    marginLeft: "-8px",
                    fontSize: "0.625rem",
                    backgroundColor: "#F26522",
                  }}
                >
                  +{teamMembers.length - 4}
                </Avatar>
              )}
            </Box>
          </Box>
        </Box>
      </Box>

      <AddTaskDialog
        open={dialogOpen}
        onClose={handleDialogClose}
        refreshList={refreshList}
        editTaskId={taskId}
        projectId={projectId}
      />
    </>
  );
};

export default TaskCard;
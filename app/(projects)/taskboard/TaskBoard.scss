.tasks-container {
  .content {
    padding: 24px;

    .tasks-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;

      .breadcrumbs-box {
        h2 {
          font-size: 24px;
          font-weight: 700;
          color: #202C4B;
        }
      }

      .add-board {
        display: flex;
        gap: 8px;
        background-color: #F26522;
        border-color: #F26522;
        color: #FFF;
        font-weight: 400;
        font-size: 14px;
        border-radius: 5px;
        text-transform: none;
        padding: 8px 13.6px;

        &:hover {
          background-color: darken(#F26522, 10%);
        }
      }
    }

    .board-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background-color: #fff;
      border-radius: 8px 8px 0 0;
      border: 1px solid #e0e0e0;
      margin-bottom: 0;

      .board-title {
        font-weight: 600;
        color: #202C4B;
      }

      .board-stats {
        display: flex;
        align-items: center;
        gap: 16px;

        .task-avatar {
          width: 28px;
          height: 28px;
          border: 2px solid white;
          font-size: 0.75rem;
          background-color: #F26522;
          transition: transform 0.3s ease-in-out;

          &:hover {
            transform: translateY(-5px);
            z-index: 2;
          }
        }

        .stat-chip {
          padding: 4px 12px;
          border-radius: 4px;
          background-color: #f5f5f5;

          p {
            font-size: 13px;
            font-weight: 500;
            color: #202C4B;
          }
        }

        .search-field {
          width: 200px;

          .MuiInputBase-root {
            border-radius: 4px;
          }
        }
      }
    }

    .filter-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #fff;
      border-left: 1px solid #e0e0e0;
      border-right: 1px solid #e0e0e0;
      border-bottom: 1px solid #e0e0e0;

      .priority-filter {
        display: flex;
        align-items: center;
        gap: 8px;

        .filter-label {
          font-weight: 500;
          color: #202C4B;
        }

        .priority-options {
          display: flex;
          gap: 2px;
          background: #F8F9FA;
          border: 0.5px solid #E0E0E0;
          padding: 8px 6px;

          .priority-btn {
            min-width: 0;
            padding: 2px 10px;
            border-radius: 4px;
            text-transform: none;
            font-size: 13px;
            color: #6B7280;
            /* Set unselected button text color */

            &.active {
              background-color: #fff;
              color: #212529;
              /* Active button text color */
            }

            /* Override default MUI hover and text variant styles */
            &:not(.active) {
              color: #6B7280;

              /* Ensure unselected buttons stay grey */
              &:hover {
                background-color: rgba(107, 114, 128, 0.1);
                /* Optional: subtle hover effect */
                color: #6B7280;
                /* Maintain grey on hover */
              }
            }
          }
        }
      }

      .filter-actions {
        display: flex;
        gap: 8px;

        .filter-btn {
          text-transform: none;
          font-size: 13px;
          color: #202C4B;
          border-color: #e0e0e0;

          .calendar-icon {
            width: 16px;
            height: 16px;
            background-color: #e0e0e0;
            border-radius: 2px;
          }
        }
      }
    }

    .task-board {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 16px;
      padding: 16px;
      background-color: #FFFFFF;
      border-left: 1px solid #e0e0e0;
      border-right: 1px solid #e0e0e0;
      border-bottom: 1px solid #e0e0e0;
      border-radius: 0 0 8px 8px;
      min-height: 600px;
      overflow-x: auto;
    }
  }
}

.task-column {
  background-color: #EDF2F4;
  border-radius: 8px;
  min-width: 250px;

  .column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1px 12px;
    background-color: #FFF;
    margin: 12px;
    // border-bottom: 1px solid #e0e0e0; /* Separator line below header */

    .column-title {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }

      .column-typo {
        font-size: 16px;
        color: #202C4B;
        font-weight: 600;
      }

      .task-count {
        background-color: #f0f0f0;
        color: #202C4B;
        font-size: 11px;
        height: 20px;
      }
    }
  }

  .column-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 0 12px 12px;

    .new-task-btn {
      color: #111827 !important;
      background-color: #FFFFFF !important;
      text-transform: none;
      font-size: 14px;
      border-radius: 8px;
      padding: 8px 13.6px;
      margin-top: 8px;
      border: 1px solid #e0e0e0;
      justify-content: center;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      .MuiButton-startIcon {
        margin-right: 8px;
      }
    }
  }
}

.task-card {

  background-color: white;
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .task-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .task-type-priority {
      display: flex;
      gap: 8px;

      // .priority-chip {
      //   font-size: 10px;
      //   height: 22px;
      // }
    }
  }

  .task-title {
    font-weight: 500;
    margin-bottom: 12px;
    color: #202C4B;
    font-size: 14px;
  }

  .progress-bar-container {
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 2px;
    margin-bottom: 8px;

    .progress-bar {
      height: 100%;
      border-radius: 8px;
    }
  }

  .task-card-footer {
    .due-date {
      color: #6B7280;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .task-divider {
      height: 1px;
      background-color: #E5E7EB;
      margin: 8px 0;
    }

    .task-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;

      .task-avatar {
        width: 24px;
        height: 24px;
        border: 2px solid #E5E7EB;
        font-size: 14px;
        background-color: #F26522;
        transition: transform 0.3s ease-in-out;

        &:hover {
          transform: translateY(-5px);
          z-index: 2;
        }
      }

      .task-stats {
        display: flex;
        gap: 8px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #757575;

          svg {
            font-size: 14px;
          }

          p {
            font-size: 12px;
          }
        }
      }
    }
  }
}
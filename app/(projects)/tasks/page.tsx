"use client";

import { <PERSON>, <PERSON><PERSON>, Typography } from "@mui/material";
import React, { useState, useEffect, Suspense } from "react";
import "./tasks.scss";
import { HomeOutlined, ControlPoint } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import Loader from "@/components/Loader/Loader";
import ProjectsCard from "@/components/ProjectsCard/ProjectsCard";
import TaskTabs from "@/components/TaskTabs/TaskTabs";
import AddTaskDialog from "@/components/AddTaskDialog/AddTaskDialog";
import {
  getAllProjects,
  getProjectById,
  getStatusBoard,
} from "@/app/services/projects/project.service";

interface Project {
  _id: string;
  title: string;
  tasks: number;
  completedTasks: number;
  deadline: string;
  value: string;
  lead: { name: string; avatar: string };
  totalHours: number;
  completion: number;
  color?: string;
  bgImage: string;
}

interface Task {
  id: string;
  title: string;
  date: string;
  completed: boolean;
  tags: string[];
  avatars: string[];
  starred: boolean;
  description: string;
  createdOn: string;
  status: { _id: string; title: string };
  teamMembers: { _id: string; firstName: string; lastName: string; avatar: string; email?: string; employeeId?: string }[];
}

interface Status {
  _id: string;
  title: string;
  hexaColour: string;
  isActive: boolean;
  isDeleted: boolean;
}

function Tasks() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProjectTasks, setSelectedProjectTasks] = useState<Task[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [tasksLoading, setTasksLoading] = useState<boolean>(false);
  const [tasksError, setTasksError] = useState<string | null>(null);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [editTaskId, setEditTaskId] = useState<string | null>(null);
  const [refresh, setRefresh] = useState<boolean>(false);
  const [statuses, setStatuses] = useState<Status[]>([]);

  const breadcrumbItems = [
    { label: "", href: "/", icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" /> },
    { label: "Projects", href: "" },
    { label: "Tasks", href: "" },
  ];

  // Fetch all statuses, handling pagination if needed
  useEffect(() => {
    const fetchStatuses = async () => {
      try {
        let allStatuses: Status[] = [];
        let page = 1;
        let totalPages = 1;

        while (page <= totalPages) {
          const response = await getStatusBoard();
          if (response.success && response.taskBoard?.results) {
            allStatuses = [...allStatuses, ...response.taskBoard.results];
            totalPages = response.taskBoard.totalPages || 1;
            page += 1;
          } else {
            console.error("Failed to load statuses");
            break;
          }
        }
        setStatuses(allStatuses);
      } catch (error) {
        console.error("Failed to fetch statuses:", error);
      }
    };
    fetchStatuses();
  }, []);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await getAllProjects();
        const apiProjects = response.projects.results;

        const mappedProjects: Project[] = apiProjects.map((project: any, index: number) => ({
          _id: project._id,
          title: project.projectName,
          tasks: project.tasks?.length || 0,
          completedTasks: project.tasks?.filter((task: any) => {
            const statusId = typeof task.status === "string" ? task.status : task.status?._id;
            const status = statuses.find(s => s._id === statusId);
            return status?.title === "Completed";
          }).length || 0,
          deadline: new Date(project.endDate).toLocaleDateString("en-US", {
            day: "2-digit",
            month: "long",
            year: "numeric",
          }),
          value: `$${project.value.toLocaleString()}`,
          lead: {
            name: `${project.projectManager.firstName} ${project.projectManager.lastName}`,
            avatar: project.projectManager.avatar || "/images/default-avatar.jpg",
          },
          totalHours: project.tasks?.reduce((sum: number, task: any) => sum + (task.hours || 0), 0) || 0,
          completion: project.tasks?.length
            ? Math.round(
                (project.tasks.filter((task: any) => {
                  const statusId = typeof task.status === "string" ? task.status : task.status?._id;
                  const status = statuses.find(s => s._id === statusId);
                  return status?.title === "Completed";
                }).length / project.tasks.length) * 100
              )
            : 0,
          color: ["#6C5DD3", "#F26522", "#D66BE2"][index % 3],
          bgImage: project.logoUrl || "/assets/default-project-logo.svg",
        }));

        setProjects(mappedProjects);
        if (mappedProjects.length > 0) {
          const firstProjectId = mappedProjects[0]._id;
          setSelectedProjectId(firstProjectId);
          fetchProjectTasks(firstProjectId);
        }
      } catch (err: any) {
        console.error("Failed to fetch projects:", err);
        setError("Failed to load projects. Please try again later.");
        setProjects([]);
      } finally {
        setLoading(false);
      }
    };

    if (statuses.length > 0) {
      fetchProjects();
    }
  }, [refresh, statuses]);

  const fetchProjectTasks = async (projectId: string) => {
    try {
      setTasksLoading(true);
      setTasksError(null);
      const response = await getProjectById(projectId);
      const project = response.project;

      const mappedTasks: Task[] = (project.tasks || []).map((task: any) => {
        const statusId = typeof task.status === "string" ? task.status : task.status?._id;
        // Map status ID to title, with temporary mapping for "Completed"
        const status = statuses.find(s => s._id === statusId) || 
          (statusId === "683af2c5e1963a2c9c50f422" ? { _id: statusId, title: "Completed" } : { _id: statusId || "", title: "Unknown" });
        return {
          id: task._id,
          title: task.title,
          date: new Date(task.dueDate).toLocaleDateString("en-US", {
            day: "2-digit",
            month: "short",
            year: "numeric",
          }),
          completed: status.title === "Completed",
          tags: [status.title, task.priority || "Unknown"],
          avatars: task.teamMembers?.map((member: any) => member.avatar) || [],
          starred: false,
          description: task.description || "No description provided.",
          createdOn: new Date(task.createdAt || Date.now()).toLocaleDateString("en-US", {
            day: "2-digit",
            month: "short",
            year: "numeric",
          }),
          status: {
            _id: statusId || "",
            title: status.title,
          },
          teamMembers: task.teamMembers?.map((member: any) => ({
            _id: member._id,
            firstName: member.firstName,
            lastName: member.lastName,
            avatar: member.avatar,
            email: member.email,
            employeeId: member.employeeId,
          })) || [],
        };
      });

      setSelectedProjectTasks(mappedTasks);
      setSelectedProjectId(projectId);
    } catch (err: any) {
      console.error("Failed to fetch project tasks:", err);
      setTasksError("Failed to load tasks for the selected project.");
      setSelectedProjectTasks([]);
    } finally {
      setTasksLoading(false);
    }
  };

  const handleProjectClick = (projectId: string) => {
    fetchProjectTasks(projectId);
  };

  const handleAddTask = () => {
    setEditTaskId(null);
    setOpenModal(true);
  };

  const handleEditTask = (taskId: string) => {
    setEditTaskId(taskId);
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setEditTaskId(null);
  };

  const refreshTasksList = () => {
    if (selectedProjectId) {
      fetchProjectTasks(selectedProjectId);
      setRefresh(!refresh);
    }
  };

  return (
    <Box className="tasks-container">
      {loading && <Loader loading={loading} />}
      <Box className="content">
        <Box className="tasks-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Tasks</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <Button
              className="add-employee"
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={handleAddTask}
              disabled={!selectedProjectId}
            >
              <ControlPoint sx={{ width: "16px", height: "16px" }} />
              Add Task
            </Button>
          </Box>
        </Box>

        <Box className="tasks-content-wrapper">
          <Box className="left-projects-cards">
            {projects.length > 0
              ? projects.map((project, index) => (
                  <ProjectsCard
                    key={index}
                    project={project}
                    onProjectClick={() => handleProjectClick(project._id)}
                    isSelected={project._id === selectedProjectId}
                  />
                ))
              : !loading && !error && <Typography>No projects available.</Typography>}
          </Box>
          <Box className="right-task-tabs">
            <TaskTabs
              tasks={selectedProjectTasks}
              loading={tasksLoading}
              error={tasksError}
              onEditTask={handleEditTask}
              refreshTasks={refreshTasksList}
              projectName={projects.find((project) => project._id === selectedProjectId)?.title || ""}
            />
          </Box>
        </Box>
      </Box>

      <AddTaskDialog
        open={openModal}
        onClose={handleCloseModal}
        refreshList={refreshTasksList}
        editTaskId={editTaskId}
        projectId={selectedProjectId || ""}
      />
    </Box>
  );
}

function TasksPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Tasks />
    </Suspense>
  );
}

export default TasksPage;
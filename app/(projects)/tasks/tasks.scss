.tasks-container {
    .content {

        padding: 24px;

        .tasks-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;

            .breadcrumbs-box {
                h2 {
                    font-size: 24px;
                    font-weight: 700;
                    color: #202C4B;
                }
            }

            .add-employee {
                display: flex;
                gap: 8px;
                background-color: #F26522;
                border-color: #F26522;
                color: #FFF;
                font-weight: 400;
                font-size: 14px;
                border-radius: 5px;
                text-transform: none;
                padding: 8px 13.6px;
            }
        }

        .tasks-content-wrapper {
            display: flex;
            margin-top: 30px;

            .left-projects-cards {
                flex: 0 0 auto;
                width: 33.33333333%;
                padding: 0px 12px;
            }

            .right-task-tabs {
                flex: 0 0 auto;
                width: 66.66666667%;
            }
        }



    }
}

// @media screen and (min-width: 1200px) and (max-width: 1600px) {
//     .employee-container {
//         .content {
//             .employee-header {
//                 display: flex;
//                 flex-direction: column;
//             }
//         }
//     }
// }

@media screen and (max-width: 1199px) {
    .employee-container {
        .content {
            .employee-header {
                display: flex;
                flex-direction: column;
            }

            .DataGrid-container {
                .DataGrid-header {
                    display: flex;
                    flex-direction: column;

                    .filters {
                        display: flex;
                        // flex-direction: column;
                        flex-wrap: wrap;
                    }
                }
            }
        }
    }

    .header-cards {
        display: flex;
        flex-direction: column !important;
    }
}

@media (max-width: 768px) {
    .employee-container {
        .content {
            padding: 16px;

            .employee-header {
                flex-direction: column;
                align-items: flex-start;

                .breadcrumbs-box {
                    margin-bottom: 10px;
                }


            }

            .DataGrid-container {
                .DataGrid-header {
                    flex-direction: column;
                    gap: 10px;

                    .filters {
                        flex-wrap: wrap;
                    }
                }

                .MuiDataGrid-root {
                    .MuiBox-root {

                        display: flex;
                        align-items: center;
                    }

                    .grid-export {
                        padding: 0px;

                        button {
                            padding: 5px 13px !important;
                        }
                    }

                    .grid-search {
                        padding: 0px;
                    }
                }


            }



            .pageSelect-dropdown {
                margin: 20px 0px;
            }
        }
    }
}
"use client";
import { Box, Typography } from "@mui/material";
import React, { useState, useEffect, useMemo, Suspense } from "react";
import "./ProjectGrid.scss";
import { HomeOutlined, ControlPoint } from "@mui/icons-material";
import { <PERSON><PERSON> } from "@mui/material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import ViewToggleProject from "@/components/ViewToggleButton/ViewToggleProject";
import PolicyFilters, { getDateRange } from "@/components/policyFilter/PolicyFilters";
import { toast } from "react-toastify";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import ProjectManagementCard from "@/components/ProjectManagementCard/ProjectManagemnetCard";
import ProjectDialog from "../projects/AddEditProject";
import { getProjectById, deleteProject, getAllProjects } from "@/app/services/projects/project.service";
import { getUsers } from "@/app/services/users.service";
import Loader from "@/components/Loader/Loader";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";

interface TeamMember {
  _id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  isActive: boolean;
  isDeleted: boolean;
  email: string;
}

interface Project {
  _id: string;
  projectId: string;
  projectName: string;
  clientId: {
    _id: string;
    firstName: string;
    lastName: string;
    avatar: string;
  };
  priority: string;
  startDate: string;
  endDate: string;
  description: string;
  logoUrl: string;
  attachmentsUrl: string[];
  teamMembers: TeamMember[];
  teamLeaders: TeamMember[];
  projectManager: TeamMember[];
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ProjectsList {
  id: string;
  name: string;
  leader: string;
  deadline: string;
  priority: string;
  status: string;
  avatar?: string;
  teamMembers: Array<{
    firstName: string;
    lastName: string;
    avatar: string;
  }>;
  teamLeaderAvatar?: string;
  tasksCompleted?: number;
  tasksTotal?: number;
  description: string;
}

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
}

function ProjectsGridContent() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [totalProjects, setTotalProjects] = useState(0);
  const [activeCount, setActiveCount] = useState(0);
  const [completedCount, setCompletedCount] = useState(0);
  const [newProjects, setNewProjects] = useState(0);
  const [refresh, setRefresh] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deletingProjectId, setDeletingProjectId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);

  // Pagination states
  const [page, setPage] = useState(1);
  const [pageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Filter states
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] = useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");

  const { roles } = useRoleAuth({
        allowedRoles: ROLE_GROUPS.USER_ROLES,
        redirectTo: "/unauthorized",
      });
  
      const isEmployee = roles.includes("Employee");

  // Fetch users for ProjectDialog
  useEffect(() => {
    const fetchUsers = async () => {
      setUsersLoading(true);
      try {
        const response = await getUsers();
        setUsers(response.users.results || []);
      } catch (error) {
        console.error("Error fetching users:", error);
        toast.error("Failed to fetch users");
      } finally {
        setUsersLoading(false);
      }
    };
    fetchUsers();
  }, []);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        let isActive: boolean | undefined;
        if (selectedStatus === "Active") isActive = true;
        else if (selectedStatus === "Inactive") isActive = false;
        else isActive = undefined;

        const response = await getAllProjects(startDate, endDate, isActive);
        setProjects(response.projects.results || []);
        setTotalProjects(response.projects.total || 0);
        setActiveCount(response.projects.activeCount || 0);
        setCompletedCount(response.projects.inactiveCount || 0);
        setNewProjects(response.projects.newProjects || 0);
        setTotalPages(response.projects.totalPages || 1);
      } catch (error) {
        console.error("Error fetching projects:", error);
        toast.error("Failed to fetch projects");
      } finally {
        setLoading(false);
      }
    };
    fetchProjects();
  }, [refresh, startDate, endDate, selectedStatus, page]);

  const filteredProjects = projects.sort((a, b) => {
    const nameA = a.projectName;
    const nameB = b.projectName;
    if (selectedSortBy === "Ascending") return nameA.localeCompare(nameB);
    if (selectedSortBy === "Descending") return nameB.localeCompare(nameA);
    return 0;
  });

  const projectProfiles = useMemo<ProjectsList[]>(
    () =>
      filteredProjects.map((project) => {
        const leader =
          project.teamLeaders?.length > 0
            ? `${project.teamLeaders[0].firstName} ${project.teamLeaders[0].lastName}`
            : project.projectManager?.length > 0
              ? `${project.projectManager[0].firstName} ${project.projectManager[0].lastName}`
              : "N/A";
        const teamLeaderAvatar = project.teamLeaders?.length > 0 ? project.teamLeaders[0].avatar : "";
        const teamMembers = project.teamMembers.map((member) => ({
          firstName: member.firstName,
          lastName: member.lastName,
          avatar: member.avatar,
        }));
        const deadline = project.endDate
          ? new Date(project.endDate).toLocaleDateString("en-US", {
              day: "2-digit",
              month: "short",
              year: "numeric",
            })
          : "N/A";
  
        return {
          id: project._id,
          name: project.projectName,
          leader,
          deadline,
          priority: project.priority || "Medium",
          status: project.isActive ? "Active" : "Inactive",
          avatar: project.logoUrl || undefined,
          teamMembers,
          teamLeaderAvatar,
          tasksCompleted: undefined,
          tasksTotal: undefined,
          description: project.description || "No description available",
        };
      }),
    [filteredProjects]
  );

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Projects", href: "" },
    { label: "ProjectsGrid" },
  ];

  const refreshList = () => {
    setRefresh(!refresh);
    setPage(1);
  };

  const handleEditClick = async (id: string) => {
    try {
      const projectResponse = await getProjectById(id);
      const project = projectResponse.project;
      if (project) {
        setEditingProject(project);
        setEditModal(true);
      }
    } catch (error) {
      console.error("Error fetching project for edit:", error);
      toast.error("Failed to fetch project data");
    }
  };

  const handleDeleteClick = (id: string) => {
    setDeletingProjectId(id);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (deletingProjectId) {
      try {
        await deleteProject(deletingProjectId);
        toast.success("Project deleted successfully");
        refreshList();
      } catch (error) {
        console.error("Error deleting project:", error);
        toast.error("Failed to delete project");
      }
    }
    setDeleteModalOpen(false);
    setDeletingProjectId(null);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteModalOpen(false);
    setDeletingProjectId(null);
  };

  // Handle page change
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  return (
    <Box className="project-container">
      <Box className="content">
        <Box className="project-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Projects List</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>

          <Box className="add-policy">
            <ViewToggleProject />
            {!isEmployee && (
            <Button
              variant="contained"
              className="add-project-btn"
              onClick={() => setOpenModal(true)}
              disabled={loading}
            >
              <ControlPoint sx={{ fontSize: "16px" }} />
              Add Project
            </Button>
            )}
          </Box>
        </Box>

        {/* DataGrid Container with Filters */}
        <Box className="Project-DataGrid-container">
          <Box className="DataGrid-header">
            <Typography variant="h5">Projects Grid</Typography>
            <PolicyFilters
              departments={[]}
              designations={[]}
              selectedDepartment=""
              setSelectedDepartment={() => { }}
              selectedDesignation=""
              setSelectedDesignation={() => { }}
              selectedSortBy={selectedSortBy}
              setSelectedSortBy={setSelectedSortBy}
              selectedDateRange={selectedDateRange}
              setSelectedDateRange={setSelectedDateRange}
              setStartDate={setStartDate}
              setEndDate={setEndDate}
              selectedStatus={selectedStatus}
              setSelectedStatus={setSelectedStatus}
              setPage={setPage}
              currentDate={currentDate}
              showDateRangeFilter={true}
              showDepartmentFilter={false}
              showStatusFilter={true}
              showSortByFilter={true}
              showDesignationFilter={false}
              selectedLeaveType="Leave Type"
              setSelectedLeaveType={() => { }}
              showLeaveTypeFilter={false}
              selectedPriority=""
              setSelectedPriority={() => {}}
            />
          </Box>
        </Box>

        {/* Project Cards */}
        <Box className="Profile-Cards" sx={{ display: "flex", flexWrap: "wrap", gap: 2 }}>
          {loading ? (
            <Loader loading={loading} />
          ) : projectProfiles.length > 0 ? (
            projectProfiles.map((profile) => (
              <ProjectManagementCard
                key={profile.id}
                id={profile.id}
                name={profile.name}
                leader={profile.leader}
                deadline={profile.deadline}
                status={profile.status}
                description={profile.description}
                avatar={profile.avatar}
                teamMembers={profile.teamMembers}
                teamLeaderAvatar={profile.teamLeaderAvatar}
                tasksCompleted={profile.tasksCompleted}
                tasksTotal={profile.tasksTotal}
                onEdit={handleEditClick}
                onDelete={handleDeleteClick}
              />
            ))
          ) : (
            <Typography variant="body1" sx={{ m: 2 }}>
              No projects found.
            </Typography>
          )}
        </Box>
      </Box>

      {/* Add Project Dialog */}
      <ProjectDialog
        open={openModal}
        onClose={() => setOpenModal(false)}
        refreshList={refreshList}
        mode="add"
        users={users}
        usersLoading={usersLoading}
      />

      {/* Edit Project Dialog */}
      <ProjectDialog
        open={editModal}
        onClose={() => {
          setEditModal(false);
          setEditingProject(null);
        }}
        refreshList={refreshList}
        projectData={editingProject}
        mode="edit"
        users={users}
        usersLoading={usersLoading}
      />

      <DeleteConfirmationDialog
        open={deleteModalOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Confirm Delete"
        message="You want to delete this project? This action cannot be undone."
      />
    </Box>
  );
}

function ProjectsGridPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ProjectsGridContent />
    </Suspense>
  );
}

export default ProjectsGridPage;
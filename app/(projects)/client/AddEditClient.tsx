import React, { useState, useRef } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import "./AddEditClient.scss";
import { toast } from "react-toastify";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  TextField,
  Box,
  Typography,
  IconButton,
  Avatar,
  Tooltip,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import {
  postClient,
  updateClient,
} from "@/app/services/clients/client.service";
import useUploadMedia from "@/app/hooks/useUploadMedia";

interface AddClientFormProps {
  open: boolean;
  onClose: () => void;
  refreshList: () => void;
  isEdit?: boolean;
  employeeData?: {
    _id: string;
    firstName: string;
    lastName: string;
    username?: string;
    email: string;
    password?: string; // Made optional since not used in edit
    confirmPassword?: string;
    phone: string;
    company: string;
    avatar?: string;
    clientDesignation?: string;
  };
}

interface ClientFormValues {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone: string;
  company: string;
  avatar: string;
  clientDesignation: string;
  avatarFile?: File | null;
}

const validationSchema = Yup.object({
  firstName: Yup.string().required("First Name is required"),
  lastName: Yup.string(),
  username: Yup.string().required("Username is required"),
  email: Yup.string()
    .email("Invalid email address")
    .required("Email is required"),
  password: Yup.string().when("isEdit", (isEdit, schema) =>
    !isEdit
      ? schema
          .min(6, "Password must be at least 6 characters")
          .required("Password is required")
      : schema
  ),
  confirmPassword: Yup.string().when("isEdit", (isEdit, schema) =>
    !isEdit
      ? schema
          .oneOf([Yup.ref("password")], "Passwords must match")
          .required("Confirm Password is required")
      : schema
  ),
  phone: Yup.string()
    .matches(/^\d{10}$/, "Phone number must be exactly 10 digits")
    .required("Phone Number is required"),
  company: Yup.string().required("Company is required"),
  clientDesignation: Yup.string()
    .required("Client Designation is required")
    .min(2, "Designation must be at least 2 characters")
    // Relax "n/a" validation for edit mode
    .when("isEdit", (isEdit, schema) =>
      !isEdit
        ? schema.test(
            "not-na",
            "Designation cannot be 'n/a' or similar",
            (value) => !value || !/^(n\/a|N\/A|n\\a|N\\A)$/i.test(value)
          )
        : schema
    ),
  avatar: Yup.string().required("Profile image is required"),
});

const AddClientForm: React.FC<AddClientFormProps> = ({
  open,
  onClose,
  refreshList,
  isEdit = false,
  employeeData,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedImage, setSelectedImage] = useState<string | undefined>(
    employeeData?.avatar || ""
  );
  const { uploadMedia } = useUploadMedia();

  const initialValues: ClientFormValues = {
    firstName: employeeData?.firstName || "",
    lastName: employeeData?.lastName || "",
    username: employeeData?.username || "",
    email: employeeData?.email || "",
    password: employeeData?.password || "",
    confirmPassword: employeeData?.confirmPassword || "",
    phone: employeeData?.phone || "",
    company: employeeData?.company || "",
    avatar: employeeData?.avatar || "",
    clientDesignation: employeeData?.clientDesignation || "", // Ensure default empty string
    avatarFile: null,
  };

  const handleImageUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
    setFieldValue: (field: string, value: any) => void
  ) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      try {
        if (file.size > 4 * 1024 * 1024) {
          throw new Error(`File ${file.name} exceeds 4MB limit`);
        }

        const { preview } = await uploadMedia(
          file,
          "avatars",
          1,
          setIsLoading,
          file.name
        );

        setSelectedImage(preview);
        setFieldValue("avatar", preview);
        setFieldValue("avatarFile", file);
        toast.success("Image uploaded successfully");
      } catch (error) {
        console.error("Image upload failed:", error);
        // toast.error(error instanceof Error ? error.message : "Failed to upload image");
      }
    }
  };

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <Dialog
      className="dialog-client"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="md"
      fullWidth
    >
      <DialogTitle className="dialog-title">
        <Typography
          variant="h6"
          sx={{ fontWeight: 600, fontSize: "20px", color: "#111827" }}
        >
          {isEdit ? "Edit Client" : "Add New Client"}
        </Typography>
        <IconButton
          color="inherit"
          onClick={onClose}
          aria-label="close"
          sx={{
            backgroundColor: "#6b7280",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": { backgroundColor: "#d55a1d" },
            "& .MuiSvgIcon-root": { fontSize: "14px" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        enableReinitialize
        onSubmit={async (values, { setSubmitting, resetForm }) => {
          setIsLoading(true);
          try {
            const payload = {
              firstName: values.firstName,
              lastName: values.lastName,
              userName: values.username,
              avatar: values.avatar,
              email: values.email,
              ...(isEdit ? {} : { password: values.password }),
              phone: values.phone,
              company: values.company,
              clientDesignation: values.clientDesignation,
            };

            if (isEdit && employeeData?._id) {
              await updateClient(employeeData._id, payload);
              toast.success("Client updated successfully");
            } else {
              await postClient(payload);
              toast.success("Client added successfully");
            }

            resetForm();
            setSelectedImage("");
            onClose();
            refreshList();
          } catch (error) {
            console.error("Error submitting client:", error);
            toast.error(
              isEdit ? "Failed to update client" : "Failed to add client"
            );
          } finally {
            setIsLoading(false);
            setSubmitting(false);
          }
        }}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          isSubmitting,
          setFieldValue,
        }) => (
          <Form>
            <DialogContent
              className="dialog-content"
              dividers
              sx={{ overflow: "auto", maxHeight: "80vh" }}
            >
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box
                  className="upload-avatar"
                  sx={{
                    display: "flex",
                    padding: "16px",
                    backgroundColor: "#F8F9FA",
                    gap: "10px",
                  }}
                >
                  <Box
                    className="avatar-logo"
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: "50%",
                      width: "80px",
                      height: "80px",
                      border: "1px solid #E5E7EB",
                    }}
                  >
                    <Avatar
                      src={selectedImage}
                      alt="Employee Avatar"
                      sx={{ width: "100%", height: "100%" }}
                    />
                  </Box>
                  <Box className="upload-button">
                    <Typography sx={{ fontSize: "14px", fontWeight: 600 }}>
                      Upload Profile Image
                    </Typography>
                    <Typography
                      sx={{ fontSize: "0.75rem", marginBottom: ".5rem" }}
                    >
                      Image should be below 4 MB
                    </Typography>
                    <Button
                      variant="contained"
                      sx={{
                        backgroundColor: "#F26522",
                        color: "#fff",
                        border: "1px solid #F26522",
                        padding: "0.25rem 0.5rem",
                        fontSize: "0.75rem",
                        borderRadius: "5px",
                        fontWeight: "600",
                        "&:hover": { backgroundColor: "#E55A1B" },
                      }}
                      onClick={handleUploadClick}
                      disabled={isLoading}
                    >
                      {isLoading ? "Uploading..." : "Upload"}
                    </Button>
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      ref={fileInputRef}
                      onChange={(e) => handleImageUpload(e, setFieldValue)}
                    />
                  </Box>
                </Box>

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label>
                      First Name <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="firstName"
                      value={values.firstName}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.firstName && Boolean(errors.firstName)}
                      helperText={touched.firstName && errors.firstName}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label>Last Name</label>
                    <TextField
                      fullWidth
                      name="lastName"
                      value={values.lastName}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.lastName && Boolean(errors.lastName)}
                      helperText={touched.lastName && errors.lastName}
                    />
                  </Box>
                </Box>

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Username <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="username"
                      value={values.username}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.username && Boolean(errors.username)}
                      helperText={touched.username && errors.username}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Email <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="email"
                      value={values.email}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.email && Boolean(errors.email)}
                      helperText={touched.email && errors.email}
                    />
                  </Box>
                </Box>

                {!isEdit && (
                  <Box
                    className="mobile-alignment"
                    sx={{ display: "flex", gap: 2 }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <label>
                        Password <span className="required">*</span>
                      </label>
                      <TextField
                        fullWidth
                        type="password"
                        name="password"
                        value={values.password}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.password && Boolean(errors.password)}
                        helperText={touched.password && errors.password}
                      />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <label>
                        Confirm Password <span className="required">*</span>
                      </label>
                      <TextField
                        fullWidth
                        type="password"
                        name="confirmPassword"
                        value={values.confirmPassword}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.confirmPassword &&
                          Boolean(errors.confirmPassword)
                        }
                        helperText={
                          touched.confirmPassword && errors.confirmPassword
                        }
                      />
                    </Box>
                  </Box>
                )}

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Phone Number <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="phone"
                      value={values.phone}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.phone && Boolean(errors.phone)}
                      helperText={touched.phone && errors.phone}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Company <span className="required">*</span>
                    </label>
                    <TextField
                      fullWidth
                      name="company"
                      value={values.company}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.company && Boolean(errors.company)}
                      helperText={touched.company && errors.company}
                    />
                  </Box>
                </Box>

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label>
                      Client Designation <span className="required">*</span>
                    </label>
                    <Tooltip
                      title={
                        isEdit
                          ? "Edit the job title (e.g., Manager, Director)."
                          : "Enter a valid job title (e.g., Manager, Director). 'n/a' is not allowed."
                      }
                      placement="top"
                    >
                      <TextField
                        fullWidth
                        name="clientDesignation"
                        value={values.clientDesignation}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.clientDesignation &&
                          Boolean(errors.clientDesignation)
                        }
                        helperText={
                          touched.clientDesignation && errors.clientDesignation
                        }
                        placeholder="e.g., Manager, Director"
                      />
                    </Tooltip>
                  </Box>
                  <Box sx={{ flex: 1 }} /> {/* Empty box for alignment */}
                </Box>
              </Box>
              <DialogActions sx={{ padding: "16px 0 0 0 !important" }}>
                <Button
                  onClick={onClose}
                  sx={{
                    fontWeight: "400",
                    fontSize: "14px",
                    color: "#111827",
                    border: "1px solid #E5E7EB",
                    borderRadius: "5px",
                    textTransform: "none",
                    padding: "8px 13.6px",
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting || isLoading}
                  sx={{
                    backgroundColor: "#F26522",
                    border: "1px solid #F26522",
                    color: "#FFF",
                    borderRadius: "5px",
                    padding: "0.5rem 0.85rem",
                    fontSize: "14px",
                    transition: "all 0.5s",
                    fontWeight: 500,
                    textTransform: "none",
                    "&:hover": { backgroundColor: "#E55A1B" },
                  }}
                >
                  {isEdit ? "Update" : "Save"}
                </Button>
              </DialogActions>
            </DialogContent>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default AddClientForm;

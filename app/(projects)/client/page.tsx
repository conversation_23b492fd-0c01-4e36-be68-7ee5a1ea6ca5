"use client";

import { <PERSON>, Divider, Avatar, Typography, Paper } from "@mui/material";
import React, { useState, useEffect, Suspense } from "react";
import "./Client.scss";
import { HomeOutlined, ControlPoint } from "@mui/icons-material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  useGridApiRef,
  GridToolbarQuickFilter,
  GridToolbarExport,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import { EditNote, Delete, PeopleOutline } from "@mui/icons-material";
import CustomHeaderCard from "@/components/CustomHeaderCard/CustomHeaderCard";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import Link from "next/link";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import AddClientForm from "./AddEditClient";
import ViewToggleClient from "@/components/ViewToggleButton/ViewToggleClient";
import {
  getClients,
  deleteClient,
  getClientById,
  updateClient,
} from "@/app/services/clients/client.service";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";

interface ClientsList {
  id: string;
  name: string;
  avatar: string;
  companyName: string;
  email: string;
  phone: string;
  isActive: string;
  clientId: string;
  clientDesignation: string;
}

function QuickSearchToolbar() {
  return (
    <Box
      sx={{
        p: 0.5,
        pb: 0,
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        padding: "10px 20px",
      }}
    >
      <GridToolbarQuickFilter
        className="grid-search"
        sx={{ textDecoration: "none" }}
        placeholder="Search"
      />
      <Box
        className="grid-export"
        sx={{ display: "flex", alignItems: "center" }}
      >
        <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
      </Box>
    </Box>
  );
}

function ClientsListContent() {
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedDepartment, setSelectedDepartment] =
    useState<string>("Department");
  const [clients, setClients] = useState<ClientsList[]>([]);
  const [totalClients, setTotalClients] = useState<number>(0);
  const [activeClients, setActiveClients] = useState<number>(0);
  const [inactiveClients, setInactiveClients] = useState<number>(0);
  const [newJoiners, setNewJoiners] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [editLoading, setEditLoading] = useState<boolean>(false); // New state for edit API call
  const [error, setError] = useState<string | null>(null);

  const apiRef = useGridApiRef();

  const fetchClients = async () => {
    setLoading(true);
    setError(null);
    try {
      const isActive =
        selectedStatus === "Active"
          ? "true"
          : selectedStatus === "Inactive"
            ? "false"
            : undefined;

      const response = await getClients(
        pageSize,
        page,
        selectedDepartment,
        selectedSortBy,
        startDate,
        endDate,
        isActive
      );

      const clientsData = response.clients.results.map((client: any) => ({
        id: client._id,
        clientId: client.clientId,
        name: `${client.firstName} ${client.lastName || ""}`.trim(),
        avatar: client.avatar || "",
        companyName: client.company || "",
        email: client.email || "",
        clientDesignation: client.clientDesignation || "",
        phone: client.phone || "",
        isActive: client.isActive ? "Active" : "Inactive",
      }));

      setClients(clientsData);
      setTotalClients(response.clients.totalClientsCount);
      setActiveClients(response.clients.activeClientsCount);
      setInactiveClients(response.clients.inactiveClientsCount);
      setNewJoiners(response.clients.newJoiners);
    } catch (err) {
      setError("Failed to fetch clients");
      console.error(err);
      // toast.error("Failed to fetch clients");
    } finally {
      setLoading(false);
    }
  };

  // Fetch clients when filters or pagination change
  useEffect(() => {
    fetchClients();
  }, [
    page,
    pageSize,
    selectedSortBy,
    selectedDateRange,
    startDate,
    endDate,
    selectedStatus,
    selectedDepartment,
  ]);

  const rows: GridRowsProp = clients.map((client: ClientsList) => ({
    id: client.id,
    clientId: client.clientId,
    name: client.name,
    avatar: client.avatar,
    companyName: client.companyName,
    email: client.email,
    phone: client.phone,
    isActive: client.isActive,
  }));

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Client", href: "" },
    { label: "ClientsList" },
  ];

  const [openModal, setOpenModal] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false);
  const [deletingClient, setDeletingClient] = useState<ClientsList | null>(
    null
  );
  const [editingClientData, setEditingClientData] = useState<any | null>(null);

  const handleEditClick = async (row: ClientsList) => {
    setEditLoading(true);
    try {
      const clientData = await getClientById(row.id);
      setEditingClientData(clientData.client); // Store the full client object
      setEditModal(true);
    } catch (error) {
      console.error("Error fetching client data:", error);
      // toast.error("Failed to load client data");
    } finally {
      setEditLoading(false);
    }
  };

  const handleDeleteClick = (row: ClientsList) => {
    setDeletingClient(row);
    setDeleteModal(true);
  };

  const handleDelete = async () => {
    if (deletingClient) {
      setLoading(true);
      try {
        await deleteClient(deletingClient.id);
        toast.success("Client deleted successfully");
        setDeleteModal(false);
        setDeletingClient(null);
        fetchClients();
      } catch (error) {
        console.error("Error deleting client:", error);
        // toast.error("Failed to delete client");
      } finally {
        setLoading(false);
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteModal(false);
    setDeletingClient(null);
  };

  const columns: GridColDef[] = [
    {
      field: "clientId",
      headerName: "Client ID",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#111827" }}>
          {params.row.clientId}
        </Typography>
      ),
    },
    {
      field: "name",
      headerName: "Client Name",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Avatar
            src={params.row.avatar}
            alt={params.row.name}
            sx={{ width: 32, height: 32 }}
          />
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            <Typography sx={{ fontSize: "14px", color: "#111827" }}>
              <Link href="#">{params.row.name}</Link>
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: "companyName",
      headerName: "Company Name",
      editable: false,
      flex: 1.5,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.companyName}
        </Typography>
      ),
    },
    {
      field: "email",
      headerName: "Email",
      editable: false,
      flex: 1.5,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.email}
        </Typography>
      ),
    },
    {
      field: "phone",
      headerName: "Phone",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {params.row.phone}
        </Typography>
      ),
    },
    {
      field: "isActive",
      headerName: "Status",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        // <Box
        //   sx={{
        //     display: "flex",
        //     alignItems: "center",
        //     gap: "3px",
        //     backgroundColor: params.value === "Active" ? "#03C95A" : "#E70D0D",
        //     color: "#fff",
        //     borderRadius: "5px",
        //     textAlign: "center",
        //     minWidth: "66px",
        //     justifyContent: "center",
        //     fontSize: "10px",
        //     fontWeight: 500,
        //     padding: "0px 5px",
        //     lineHeight: "18px",
        //   }}
        // >
        //   <Box
        //     sx={{
        //       width: "5px",
        //       height: "5px",
        //       borderRadius: "100%",
        //       backgroundColor: params.value === "Active" ? "#fff" : "#fff",
        //     }}
        //   />
        //   {params.value}
        // </Box>

        <StatusToggle
          isActive={params.row.isActive === "Active"}
          onChange={async (isActive) => {
            setEditLoading(true);
            try {
              await updateClient(params.row.id, { isActive });
              toast.success(
                `Client status updated to ${isActive ? "Active" : "Inactive"}`
              );
              fetchClients();
            } catch (error) {
              console.error("Error updating client status:", error);
              toast.error("Failed to update client status");
            } finally {
              setEditLoading(false);
            }
          }}
        />
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleEditClick(params.row as ClientsList)}
            disabled={editLoading}
          >
            <EditNote sx={{ width: "16px", height: "16px" }} />
          </IconButton>
          <IconButton
            sx={{ color: "#6B7280" }}
            onClick={() => handleDeleteClick(params.row as ClientsList)}
            disabled={editLoading}
          >
            <Delete sx={{ width: "16px", height: "16px" }} />
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Box className="client-container">
      {loading && <Loader loading={loading} />}
      <Box className="content">
        <Box className="client-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Clients List</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <ViewToggleClient />
            <Button
              className="add-employee"
              variant="contained"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={() => setOpenModal(true)}
            >
              <ControlPoint sx={{ width: "16px", height: "16px" }} />
              Add Client
            </Button>
          </Box>
        </Box>

        <Box
          className="header-cards"
          sx={{ display: "flex", width: "100%", gap: "24px" }}
        >
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Total Clients"
            value={totalClients}
            percentage="+19.01%"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Active Clients"
            value={activeClients}
            percentage="+12.5%"
            iconBgColor="#03C95A"
            badgeBgColor="rgba(242, 101, 34, 0.1)"
            badgeTextColor="#F26522"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="Inactive Clients"
            value={inactiveClients}
            percentage="+19.01%"
            iconBgColor="#E70D0D"
            badgeBgColor="rgba(242, 101, 34, 0.1)"
            badgeTextColor="#F26522"
            showIcon={true}
          />
          <CustomHeaderCard
            icon={PeopleOutline}
            title="New Clients"
            value={newJoiners}
            percentage="+12.5%"
            iconBgColor="#1B84FF"
            badgeBgColor="rgba(59, 112, 128, 0.1)"
            badgeTextColor="#3B7080"
            showIcon={true}
          />
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 500,
              maxHeight: "calc(100vh - 200px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Client List</Typography>
              <PolicyFilters
                departments={["Department"]}
                selectedDepartment={selectedDepartment}
                setSelectedDepartment={setSelectedDepartment}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={new Date()}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                showDesignationFilter={false}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => {}}
                selectedLeaveType={""}
                setSelectedLeaveType={() => {}}
                selectedPriority=""
                setSelectedPriority={() => {}}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              apiRef={apiRef}
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={totalClients}
              paginationMode="server"
              initialState={{
                pagination: { paginationModel: { pageSize: pageSize } },
              }}
              paginationModel={{ page: page - 1, pageSize: pageSize }}
              onPaginationModelChange={(model) => {
                if (model.pageSize !== pageSize) {
                  setPageSize(model.pageSize);
                  setPage(1);
                } else {
                  setPage(model.page + 1);
                }
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: QuickSearchToolbar,
              }}
            />
          </Box>
        </Paper>
      </Box>

      <AddClientForm
        open={openModal}
        onClose={() => setOpenModal(false)}
        refreshList={fetchClients}
      />
      {editingClientData && (
        <AddClientForm
          open={editModal}
          onClose={() => {
            setEditModal(false);
            setEditingClientData(null);
          }}
          refreshList={fetchClients}
          isEdit
          employeeData={{
            _id: editingClientData._id,
            firstName: editingClientData.firstName || "",
            lastName: editingClientData.lastName || "",
            username: editingClientData.userName || "",
            email: editingClientData.email || "",
            phone: editingClientData.phone || "",
            company: editingClientData.company || "",
            clientDesignation: editingClientData.clientDesignation || "",
            avatar: editingClientData.avatar || "",
          }}
        />
      )}
      <DeleteConfirmationDialog
        open={deleteModal}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleDelete}
        title="Confirm Delete"
        message={`Are you sure you want to delete the client ${deletingClient?.name || deletingClient?.id}? This action cannot be undone.`}
      />
    </Box>
  );
}

function ClientsListPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ClientsListContent />
    </Suspense>
  );
}

export default ClientsListPage;

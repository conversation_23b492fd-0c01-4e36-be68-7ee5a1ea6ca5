"use client";

import type React from "react";
import { useState, useEffect, useRef } from "react";
import {
  Di<PERSON>,
  DialogTitle,
  DialogContent,
  IconButton,
  Tabs,
  Tab,
  TextField,
  Button,
  Box,
  Typography,
  Select,
  MenuItem,
  Chip,
  FormControl,
  FormHelperText,
  DialogActions,
  Avatar,
} from "@mui/material";
import {
  Close,
  Image as ImageIcon,
  AttachFile as AttachFileIcon,
} from "@mui/icons-material";
import { toast } from "react-toastify";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  addProject,
  updateProject,
} from "@/app/services/projects/project.service";
import { getAllClients } from "@/app/services/clients/client.service";
import TextEditor from "@/components/TextEditor/TextEditor";
import Autocomplete from "@mui/material/Autocomplete";
import useUploadMedia from "@/app/hooks/useUploadMedia";
import Image from "next/image";
import "./AddEditProject.scss";

const stripHtmlTags = (html: any) => {
  const div = document.createElement("div");
  div.innerHTML = html;
  return div.textContent || div.innerText || "";
};

interface ProjectDialogProps {
  open: boolean;
  onClose: () => void;
  refreshList?: () => void;
  projectData?: any;
  mode: "add" | "edit";
  users: User[];
  usersLoading: boolean;
}

interface Client {
  _id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
}

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </Box>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

const ProjectDialog: React.FC<ProjectDialogProps> = ({
  open,
  onClose,
  refreshList,
  projectData,
  mode,
  users,
  usersLoading,
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [attachmentFiles, setAttachmentFiles] = useState<File[]>([]);
  const [attachmentPreviews, setAttachmentPreviews] = useState<string[]>([]);
  const [attachmentsUrl, setAttachmentsUrl] = useState<string[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [clientLoading, setClientLoading] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { uploadMedia } = useUploadMedia();

  const initialValues = {
    name: "",
    client: "",
    startDate: "",
    endDate: "",
    priority: "",
    value: "",
    priceType: "",
    description: "",
    status: "",
    teamMembers: [] as string[],
    teamLeaders: [] as string[],
    projectManager: "",
    logoUrl: "",
  };

  const validationSchema = Yup.object({
    name: Yup.string().required("Project Name is required"),
    client: Yup.string().required("Client is required"),
    startDate: Yup.date().required("Start Date is required").nullable(),
    endDate: Yup.date()
      .required("End Date is required")
      .nullable()
      .min(Yup.ref("startDate"), "End Date must be after Start Date"),
    priority: Yup.string().required("Priority is required"),
    value: Yup.number()
      .required("Project Value is required")
      .positive("Value must be positive"),
    priceType: Yup.string().required("Price Type is required"),
    description: Yup.string().required("Description is required"),
    status: Yup.string().required("Status is required"),
    teamMembers: Yup.array()
      .of(Yup.string())
      .min(1, "At least one team member is required")
      .required("Team Members are required"),
    teamLeaders: Yup.array()
      .of(Yup.string())
      .min(1, "At least one team leader is required")
      .required("Team Leaders are required"),
    projectManager: Yup.string().required("Project Manager is required"),
    logoUrl: Yup.string(),
  });

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      try {
        const payload = {
          projectName: values.name,
          clientId: values.client,
          startDate: new Date(values.startDate).toISOString(),
          endDate: new Date(values.endDate).toISOString(),
          priority: values.priority,
          value: parseFloat(values.value),
          priceType: values.priceType,
          description: stripHtmlTags(values.description),
          isActive: values.status === "Active",
          teamMembers: values.teamMembers,
          teamLeaders: values.teamLeaders,
          projectManager: values.projectManager,
          logoUrl: values.logoUrl || undefined,
          attachmentsUrl: attachmentsUrl.length > 0 ? attachmentsUrl : [],
        };

        let response: { message: string };
        if (mode === "edit" && projectData?._id) {
          response = await updateProject(projectData._id, payload);
          toast.success(response.message);
        } else {
          response = await addProject(payload);
          toast.success(response.message || "Project added successfully");
        }

        if (refreshList) {
          refreshList();
        }
        onClose();
      } catch (error: any) {
        console.error(
          `Error ${mode === "edit" ? "updating" : "adding"} project:`,
          error
        );
        const errorMessage =
          error.response?.data?.message ||
          `Failed to ${mode === "edit" ? "update" : "add"} project`;
        toast.error(errorMessage);
      }
    },
  });

  useEffect(() => {
    const fetchClients = async () => {
      setClientLoading(true);
      try {
        const response = await getAllClients();
        setClients(response.clients.results);
      } catch (error) {
        console.error("Failed to fetch clients:", error);
        toast.error("Failed to load clients");
      } finally {
        setClientLoading(false);
      }
    };

    if (open) {
      fetchClients();
    }
  }, [open]);

  useEffect(() => {
    if (open) {
      if (mode === "edit" && projectData) {
        formik.setValues({
          name: projectData.projectName || "",
          client: projectData.clientId?._id || "",
          startDate: projectData.startDate
            ? new Date(projectData.startDate).toISOString().split("T")[0]
            : "",
          endDate: projectData.endDate
            ? new Date(projectData.endDate).toISOString().split("T")[0]
            : "",
          priority: projectData.priority || "",
          value: projectData.value?.toString() || "",
          priceType: projectData.priceType || "",
          description: projectData.description || "",
          status: projectData.isActive ? "Active" : "Inactive",
          teamMembers:
            projectData.teamMembers?.map((member: any) =>
              typeof member === "string" ? member : member._id
            ) || [],
          teamLeaders:
            projectData.teamLeaders?.map((leader: any) =>
              typeof leader === "string" ? leader : leader._id
            ) || [],
          projectManager:
            typeof projectData.projectManager === "string"
              ? projectData.projectManager
              : projectData.projectManager?._id || "",
          logoUrl: projectData.logoUrl || "",
        });
        setSelectedImage(projectData.logoUrl || null);
        setAttachmentsUrl(projectData.attachmentsUrl || []);
        setAttachmentPreviews(projectData.attachmentsUrl || []);
        setAttachmentFiles([]);
      } else {
        formik.resetForm();
        setSelectedImage(null);
        setAttachmentFiles([]);
        setAttachmentPreviews([]);
        setAttachmentsUrl([]);
      }
      setTabValue(0);
    }

    return () => {
      attachmentPreviews.forEach((preview) => {
        if (preview?.startsWith("blob:")) {
          URL.revokeObjectURL(preview);
        }
      });
    };
  }, [open, mode, projectData]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 4 * 1024 * 1024) {
        toast.error("Image should be below 4 mb");
        return;
      }
      setIsUploading(true);
      try {
        const { preview } = await uploadMedia(
          file,
          "project_logos",
          1,
          setIsUploading,
          file.name
        );
        setSelectedImage(preview);
        formik.setFieldValue("logoUrl", preview);
        toast.success("Logo uploaded successfully");
      } catch (error) {
        console.error("Upload error:", error);
        toast.error("Failed to upload logo");
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleAttachmentChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files).filter((file) => {
        if (file.size > 4 * 1024 * 1024) {
          toast.error(`${file.name} exceeds 4 mb limit`);
          return false;
        }
        return true;
      });
      if (newFiles.length === 0) return;

      setIsUploading(true);
      try {
        const newPreviews: string[] = [];
        const newUrls: string[] = [];
        for (const file of newFiles) {
          const result = await uploadMedia(
            file,
            "project_attachments",
            1,
            setIsUploading,
            file.name
          );
          newPreviews.push(URL.createObjectURL(file));
          newUrls.push(result.preview);
        }
        setAttachmentFiles((prev) => [...prev, ...newFiles]);
        setAttachmentPreviews((prev) => [...prev, ...newPreviews]);
        setAttachmentsUrl((prev) => [...prev, ...newUrls]);
      } catch (error) {
        console.error("Attachment upload error:", error);
        toast.error("Failed to upload attachments");
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleRemoveAttachment = (index: number) => {
    if (attachmentPreviews[index]?.startsWith("blob:")) {
      URL.revokeObjectURL(attachmentPreviews[index]);
    }
    setAttachmentFiles((prev) => prev.filter((_, i) => i !== index));
    setAttachmentPreviews((prev) => prev.filter((_, i) => i !== index));
    setAttachmentsUrl((prev) => prev.filter((_, i) => i !== index));
  };

  const getUserLabel = (userId: string) => {
    const user = users.find((u) => u._id === userId);
    if (!user) return userId;
    const sameNameUsers = users.filter(
      (u) =>
        `${u.firstName} ${u.lastName}` === `${user.firstName} ${user.lastName}`
    );
    return sameNameUsers.length > 1
      ? `${user.firstName} ${user.lastName} (${user.email})`
      : `${user.firstName} ${user.lastName}`;
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
        formik.resetForm();
        setSelectedImage(null);
        setAttachmentFiles([]);
        setAttachmentPreviews([]);
        setAttachmentsUrl([]);
        setTabValue(0);
      }}
      disableEscapeKeyDown
      maxWidth="md"
      fullWidth
      className="project-dialog"
    >
      <DialogTitle className="dialog-title">
        <Box className="title-container">
          <Typography variant="h6" className="dialog-title-text">
            {mode === "edit" ? "Edit Project" : "Add Project"}
          </Typography>
          {mode === "edit" && (
            <Typography className="project-id">
              Project ID: {projectData?.projectId}
            </Typography>
          )}
        </Box>
        <IconButton
          aria-label="close"
          onClick={() => {
            onClose();
            formik.resetForm();
            setSelectedImage(null);
            setAttachmentFiles([]);
            setAttachmentPreviews([]);
            setAttachmentsUrl([]);
            setTabValue(0);
          }}
          sx={{
            backgroundColor: "#6b7280",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": { backgroundColor: "#d55a1d" },
            "& .MuiSvgIcon-root": { fontSize: "14px" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <Box className="tabs-container">
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          className="dialog-tabs"
          aria-label="project dialog tabs"
        >
          <Tab
            label="Basic Information"
            className={tabValue === 0 ? "active-tab" : ""}
            {...a11yProps(0)}
          />
          <Tab
            label="Members"
            className={tabValue === 1 ? "active-tab" : ""}
            {...a11yProps(1)}
          />
        </Tabs>
      </Box>

      <DialogContent className="dialog-content">
        <form onSubmit={formik.handleSubmit}>
          <TabPanel value={tabValue} index={0}>
            <Box className="basic-info-tab">
              <Box className="logo-upload-section">
                <Box className="image-placeholder">
                  {selectedImage ? (
                    <Avatar
                      src={selectedImage}
                      alt="Project Logo"
                      sx={{ width: "100%", height: "100%" }}
                      className="image-preview"
                    />
                  ) : (
                    <ImageIcon sx={{ fontSize: 16, color: "#6b7280" }} />
                  )}
                </Box>
                <Box className="upload-info">
                  <Typography className="upload-title">
                    Upload Project Logo
                  </Typography>
                  <Typography className="upload-hint">
                    Image should be below 4 MB
                  </Typography>
                  <Box className="upload-buttons">
                    <Button
                      variant="contained"
                      component="label"
                      className="upload-btn"
                      disabled={isUploading}
                    >
                      {isUploading ? "Uploading..." : "Upload"}
                      <input
                        type="file"
                        hidden
                        accept="image/*"
                        onChange={handleImageUpload}
                      />
                    </Button>
                    {selectedImage && (
                      <Button
                        variant="outlined"
                        component="label"
                        onClick={() => {
                          setSelectedImage(null);
                          formik.setFieldValue("logoUrl", "");
                        }}
                        className="cancel-btn"
                        disabled={isUploading}
                      >
                        Cancel
                      </Button>
                    )}
                  </Box>
                </Box>
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Project Name <span className="required">*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="name"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.name && Boolean(formik.errors.name)}
                  helperText={formik.touched.name && formik.errors.name}
                />
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Client <span className="required">*</span>
                </Typography>
                <Autocomplete
                  options={clients}
                  getOptionLabel={(option) =>
                    `${option.firstName} ${option.lastName}`
                  }
                  value={
                    clients.find(
                      (client) => client._id === formik.values.client
                    ) || null
                  }
                  onChange={(event, newValue) => {
                    formik.setFieldValue("client", newValue?._id || "");
                  }}
                  onBlur={() => formik.setFieldTouched("client", true)}
                  loading={clientLoading}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      placeholder="Select client"
                      error={
                        formik.touched.client && Boolean(formik.errors.client)
                      }
                      helperText={formik.touched.client && formik.errors.client}
                    />
                  )}
                  className="autocomplete-input"
                  disabled={clientLoading}
                  noOptionsText={
                    clientLoading ? "Loading..." : "No clients available"
                  }
                />
              </Box>

              <Box className="date-fields">
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Start Date <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    variant="outlined"
                    type="date"
                    name="startDate"
                    value={formik.values.startDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.startDate &&
                      Boolean(formik.errors.startDate)
                    }
                    helperText={
                      formik.touched.startDate && formik.errors.startDate
                    }
                    InputLabelProps={{ shrink: true }}
                  />
                </Box>
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    End Date <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    type="date"
                    variant="outlined"
                    name="endDate"
                    value={formik.values.endDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.endDate && Boolean(formik.errors.endDate)
                    }
                    helperText={formik.touched.endDate && formik.errors.endDate}
                    InputLabelProps={{ shrink: true }}
                  />
                </Box>
              </Box>

              <Box className="three-column-fields">
                <Box className="form-field third-width">
                  <Typography variant="subtitle1">
                    Priority <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="priority"
                      value={formik.values.priority}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.priority &&
                        Boolean(formik.errors.priority)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Choose</em>;
                        }
                        return selected;
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Choose</em>
                      </MenuItem>
                      <MenuItem value="High">High</MenuItem>
                      <MenuItem value="Medium">Medium</MenuItem>
                      <MenuItem value="Low">Low</MenuItem>
                    </Select>
                    {formik.touched.priority && formik.errors.priority && (
                      <FormHelperText error>
                        {formik.errors.priority}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
                <Box className="form-field third-width">
                  <Typography variant="subtitle1">
                    Project Value <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    variant="outlined"
                    name="value"
                    value={formik.values.value}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.value && Boolean(formik.errors.value)}
                    helperText={formik.touched.value && formik.errors.value}
                    type="number"
                  />
                </Box>
                <Box className="form-field third-width">
                  <Typography variant="subtitle1">
                    Price Type <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    variant="outlined"
                    name="priceType"
                    value={formik.values.priceType}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.priceType &&
                      Boolean(formik.errors.priceType)
                    }
                    helperText={
                      formik.touched.priceType && formik.errors.priceType
                    }
                  />
                </Box>
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Description <span className="required">*</span>
                </Typography>
                <TextEditor
                  value={formik.values.description}
                  onChange={(newContent: any) =>
                    formik.setFieldValue("description", newContent)
                  }
                />
                {formik.touched.description && formik.errors.description && (
                  <FormHelperText error>
                    {formik.errors.description}
                  </FormHelperText>
                )}
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">Upload Files</Typography>
                <Box className="upload-files-section">
                  <Button
                    variant="outlined"
                    component="label"
                    className="file-upload-btn"
                    disabled={isUploading}
                    startIcon={<AttachFileIcon />}
                  >
                    {isUploading ? "Uploading..." : "Choose Files"}
                    <input
                      type="file"
                      multiple
                      hidden
                      onChange={handleAttachmentChange}
                    />
                  </Button>
                </Box>

                {attachmentPreviews.length > 0 && (
                  <Box className="uploaded-files-section">
                    <Box className="file-previews">
                      {attachmentPreviews.map((preview, index) => (
                        <Box key={index} className="file-preview-item">
                          {attachmentFiles[index]?.type.startsWith("image/") ? (
                            preview.startsWith("blob:") ? (
                              <Image
                                className="file-preview-image"
                                src={preview}
                                alt={attachmentFiles[index]?.name}
                                width={100}
                                height={100}
                                style={{ objectFit: "cover" }}
                              />
                            ) : (
                              <Image
                                className="file-preview-image"
                                src={preview}
                                alt={attachmentFiles[index]?.name}
                                width={100}
                                height={100}
                                style={{ objectFit: "cover" }}
                              />
                            )
                          ) : (
                            <Box className="file-preview-placeholder">
                              <AttachFileIcon className="file-icon" />
                              <Button
                                variant="text"
                                className="view-file-btn"
                                href={attachmentsUrl[index]}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                View File
                              </Button>
                            </Box>
                          )}
                          <IconButton
                            size="small"
                            className="remove-file-btn"
                            onClick={() => handleRemoveAttachment(index)}
                          >
                            <Close />
                          </IconButton>
                        </Box>
                      ))}
                    </Box>
                  </Box>
                )}
              </Box>
            </Box>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Box className="members-tab">
              <Box className="form-field">
                <Typography variant="subtitle1">
                  Team Members <span className="required">*</span>
                </Typography>
                <Autocomplete
                  multiple
                  options={users}
                  getOptionLabel={(option) => getUserLabel(option._id)}
                  value={users?.filter((user) =>
                    formik.values.teamMembers.includes(user._id)
                  )}
                  onChange={(event, newValue) => {
                    const newIds = newValue.map((user) => user._id);
                    formik.setFieldValue("teamMembers", newIds);
                  }}
                  onBlur={() => formik.setFieldTouched("teamMembers", true)}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => {
                      const { key, ...otherProps } = getTagProps({ index });
                      return (
                        <Chip
                          key={option._id}
                          label={getUserLabel(option._id)}
                          deleteIcon={<Close />}
                          sx={{
                            margin: "2px",
                            borderRadius: "4px",
                            "& .MuiChip-label": {
                              fontSize: "12px",
                            },
                          }}
                          {...otherProps}
                        />
                      );
                    })
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      placeholder="Add team members"
                      error={
                        formik.touched.teamMembers &&
                        Boolean(formik.errors.teamMembers)
                      }
                      helperText={
                        formik.touched.teamMembers && formik.errors.teamMembers
                      }
                      className="custom-autocomplete"
                    />
                  )}
                  className="autocomplete-input"
                  disabled={usersLoading}
                  noOptionsText={
                    usersLoading ? "Loading..." : "No users available"
                  }
                  sx={{
                    width: "100%",
                  }}
                />
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Team Leaders <span className="required">*</span>
                </Typography>
                <Autocomplete
                  multiple
                  options={users}
                  getOptionLabel={(option) => getUserLabel(option._id)}
                  value={users?.filter((user) =>
                    formik.values.teamLeaders.includes(user._id)
                  )}
                  onChange={(event, newValue) => {
                    const newIds = newValue.map((user) => user._id);
                    formik.setFieldValue("teamLeaders", newIds);
                  }}
                  onBlur={() => formik.setFieldTouched("teamLeaders", true)}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => {
                      const { key, ...otherProps } = getTagProps({ index });
                      return (
                        <Chip
                          key={option._id}
                          label={getUserLabel(option._id)}
                          deleteIcon={<Close />}
                          sx={{
                            margin: "2px",
                            borderRadius: "4px",
                            "& .MuiChip-label": {
                              fontSize: "12px",
                            },
                          }}
                          {...otherProps}
                        />
                      );
                    })
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      placeholder="Add team leaders"
                      error={
                        formik.touched.teamLeaders &&
                        Boolean(formik.errors.teamLeaders)
                      }
                      helperText={
                        formik.touched.teamLeaders && formik.errors.teamLeaders
                      }
                      className="custom-autocomplete"
                    />
                  )}
                  className="autocomplete-input"
                  disabled={usersLoading}
                  noOptionsText={
                    usersLoading ? "Loading..." : "No users available"
                  }
                />
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Project Manager <span className="required">*</span>
                </Typography>
                <Autocomplete
                  options={users}
                  getOptionLabel={(option) => getUserLabel(option._id)}
                  value={
                    users?.find(
                      (user) => user._id === formik.values.projectManager
                    ) || null
                  }
                  onChange={(event, newValue) => {
                    formik.setFieldValue("projectManager", newValue?._id || "");
                  }}
                  onBlur={() => formik.setFieldTouched("projectManager", true)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      placeholder="Select project manager"
                      error={
                        formik.touched.projectManager &&
                        Boolean(formik.errors.projectManager)
                      }
                      helperText={
                        formik.touched.projectManager &&
                        formik.errors.projectManager
                      }
                    />
                  )}
                  className="autocomplete-input"
                  disabled={usersLoading}
                  noOptionsText={
                    usersLoading ? "Loading..." : "No users available"
                  }
                />
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Status <span className="required">*</span>
                </Typography>
                <FormControl fullWidth variant="outlined">
                  <Select
                    name="status"
                    value={formik.values.status}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.status && Boolean(formik.errors.status)
                    }
                    displayEmpty
                    renderValue={(selected) => {
                      if (!selected) {
                        return <em>Select</em>;
                      }
                      return selected;
                    }}
                  >
                    <MenuItem disabled value="">
                      <em>Select</em>
                    </MenuItem>
                    <MenuItem value="Active">Active</MenuItem>
                    <MenuItem value="Inactive">Inactive</MenuItem>
                  </Select>
                  {formik.touched.status && formik.errors.status && (
                    <FormHelperText error>
                      {formik.errors.status}
                    </FormHelperText>
                  )}
                </FormControl>
              </Box>
            </Box>
          </TabPanel>

          <DialogActions className="dialog-actions">
            <Button
              variant="outlined"
              onClick={() => {
                onClose();
                formik.resetForm();
                setSelectedImage(null);
                setAttachmentFiles([]);
                setAttachmentPreviews([]);
                setAttachmentsUrl([]);
                setTabValue(0);
              }}
              className="cancel-button"
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              type="submit"
              className="save-button"
              disabled={isUploading}
            >
              Save
            </Button>
          </DialogActions>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ProjectDialog;

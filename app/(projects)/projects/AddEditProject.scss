.project-dialog {
  .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px !important;

    .title-container {
      display: flex;
      align-items: center;
      gap: 12px;

      .dialog-title-text {
        font-size: 20px;
        color: #111827;
        font-weight: 600;
      }

      .project-id {
        color: #212529;
        font-weight: normal;
        font-size: 14px;
      }
    }
  }

  .tabs-container {
    border-bottom: 1px solid #e0e0e0;

    .dialog-tabs {
      .MuiTab-root {
        min-width: 120px;
        text-transform: none;
        font-weight: 500;
        color: #6b7280;
        font-size: 14px;
        padding: 12px 16px;
      }

      .active-tab {
        color: #f26522;
      }

      .MuiTabs-indicator {
        background-color: #f26522;
      }
    }
  }

  .dialog-content {
    padding: 0px !important;
    max-height: 90vh;
    overflow-y: auto;

    .basic-info-tab,
    .members-tab {
      display: flex;
      flex-direction: column;
    }

    .logo-upload-section {
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: #f8f9fa;
      gap: 16px;
      margin-bottom: 1.5rem !important;

      .image-placeholder {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #e0e0e0;

        .image-preview {
          border-radius: 50%;
        }
      }

      .upload-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .upload-title {
          font-size: 14px;
          font-weight: 600;
          color: #111827;
        }

        .upload-hint {
          font-size: 12px;
          color: #6b7280;
          // margin-bottom: 8px;
        }

        .upload-buttons {
          display: flex;
          align-items: center; /* Ensure vertical alignment */
          gap: 8px;
        
          .upload-btn {
            background-color: #f26522;
            color: #fff;
            text-transform: none;
            font-size: 14px;
            padding: 6px 12px !important; /* Increased padding for better fit */
            border-radius: 4px;
            min-width: 90px; /* Prevent resizing when text changes to "Uploading..." */
            display: flex;
            align-items: center;
            justify-content: center;
        
            &:hover {
              background-color: #e55a1b;
            }
        
            &:disabled {
              background-color: #ccc;
              color: #666;
            }
          }
        
          .cancel-btn {
            color: #000;
            border: 1px solid #e0e0e0 !important; /* Add a border for consistency */
            text-transform: none;
            font-size: 14px;
            padding: 6px 12px !important; /* Match padding with upload-btn */
            border-radius: 4px;
        
            &:hover {
              background-color: rgba(242, 101, 34, 0.1);
              color: #e55a1b;
            }
        
            &:disabled {
              color: #ccc;
              border-color: #ccc !important;
            }
          }
        }
      }
    }

    .uploaded-files-section {
      margin-top: 16px;

      .uploaded-files-title {
        font-size: 16px;
        color: #111827;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .file-previews {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
      }

      .file-preview-item {
        position: relative;
        width: 100px;
        height: 100px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f9f9f9;

        .file-preview-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .file-preview-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          padding: 8px;
          width: 100%;
          height: 100%;
          gap: 4px;

          .file-icon {
            font-size: 24px;
            color: #6b7280;
          }

          .file-name {
            color: #6b7280;
            font-size: 12px;
            word-break: break-all;
          }

          .view-file-btn {
            color: #f26522;
            text-transform: none;
            font-size: 12px;
            padding: 2px 8px;

            &:hover {
              background-color: rgba(242, 101, 34, 0.1);
            }
          }
        }

        .remove-file-btn {
          position: absolute;
          top: 4px;
          right: 4px;
          background-color: rgba(0, 0, 0, 0.5);
          color: white;
          padding: 2px;

          &:hover {
            background-color: rgba(0, 0, 0, 0.7);
          }

          .MuiSvgIcon-root {
            font-size: 16px;
          }
        }
      }
    }

    .date-fields {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .half-width {
        flex: 1;
        min-width: 200px;
      }
    }

    .three-column-fields {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      // margin-bottom: 16px;

      .third-width {
       
        flex: 1;
        min-width: 150px;
      }
    }

    .form-field {
      margin-bottom: 16px;
      width: 100%;

      .required {
        color: red;
      }


      .custom-autocomplete {
        .MuiInputBase-root {
          padding: 8px !important;
          display: flex;
          flex-wrap: wrap;
          height: auto !important;
          min-height: 38px !important;
          max-height: none !important;
        }

        .MuiInputBase-input {
          padding: 0 !important;
          height: auto !important;
          // min-height: 38px !important;
        }

        .MuiAutocomplete-endAdornment {
          position: absolute;
          right: 9px;
          top: 50%;
          transform: translateY(-50%);
        }

        .MuiAutocomplete-tag {
          margin: 3px;
        }
      }

      .autocomplete-input {
        width: 100%;

        .MuiOutlinedInput-root {
          height: auto;
          min-height: 38px;
        }
      }
    }

    .dialog-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      // margin-top: 24px;
      margin: 0px !important;

      .cancel-button {
        color: #666;
        border-color: #ccc;
        text-transform: none;
      }

      .save-button {
        background-color: #f26522;
        text-transform: none;

        &:hover {
          background-color: #e55a1b;
          /* Fallback for darken(#f26522, 10%) */
        }
      }
    }
  }
}

.file-upload-btn {
  display: flex  !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: flex-start !important;
  gap: 8px !important;
  padding: 6px 16px !important;
  margin: 0 !important;
  border-color: #e0e0e0 !important;
  color: #6b7280 !important;
  text-transform: none !important;
  font-size: 14px !important;
  
  .MuiButton-startIcon {
    margin: 0 !important;
    display: flex !important;
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.04) !important;
    border-color: #d0d0d0 !important;
  }
  
  &:disabled {
    color: #ccc !important;
    border-color: #e0e0e0 !important;
  }
}

.upload-files-section {
  margin-bottom: 16px;
}

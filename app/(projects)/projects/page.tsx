"use client";

import {
  <PERSON>,
  Divider,
  Avatar,
  Typography,
  Paper,
  IconButton,
  Button,
  AvatarGroup,
  MenuItem,
  Select,
} from "@mui/material";
import React, { useState, useEffect, Suspense, useCallback } from "react";
import "./Projects.scss";
import {
  HomeOutlined,
  EditNote,
  Delete,
  ControlPoint,
  KeyboardArrowDown,
} from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { CustomDataGrid } from "@/components/CustomDataGrid/CustomDataGrid";
import {
  GridRowsProp,
  GridColDef,
  useGridApiRef,
} from "@mui/x-data-grid";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import ViewToggleProject from "@/components/ViewToggleButton/ViewToggleProject";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";
import {
  getProjects,
  getProjectById,
  deleteProject,
  updateProjectPriority,
  updateProject
} from "@/app/services/projects/project.service";
import { getUsers } from "@/app/services/users.service";
import ProjectDialog from "./AddEditProject";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import StatusChangeConfirmation from "@/components/StatusChangeConfirmation/StatusChangeConfirmation";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";
import ProjectDetailsDialog from "./ProjectDetailsDialog";
import { useDebounce } from "@/app/hooks/debounce/useDebounce";
import { QuickSearchToolbar } from "@/components/QuickSearchToolbar/QuickSearchToolbar";
import StatusToggle from "@/components/SwitchToggleStatus/SwitchToggleStatus";


interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string;
}

interface ProjectsList {
  id: string;
  projectId: string;
  name: string;
  client: string;
  team: { _id: string; name: string; avatar: string }[];
  teamLeaders: { _id: string; name: string; avatar: string }[];
  projectManager: string;
  deadline: string;
  priority: string;
  status: string;
  avatar?: string;
}

function ProjectsListContent() {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");
  const [selectedPriority, setSelectedPriority] =
    useState<string>("Select Priority");
  const [projects, setProjects] = useState<ProjectsList[]>([]);
  const [totalProjects, setTotalProjects] = useState<number>(0);
  const [activeProjects, setActiveProjects] = useState<number>(0);
  const [completedProjects, setCompletedProjects] = useState<number>(0);
  const [newProjects, setNewProjects] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [editLoading, setEditLoading] = useState<boolean>(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [deletingProject, setDeletingProject] = useState<ProjectsList | null>(
    null
  );
  const [users, setUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState<boolean>(false);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [dialogMode, setDialogMode] = useState<"add" | "edit">("add");
  const [editingProjectData, setEditingProjectData] = useState<any>(null);

  const [priorityDialogOpen, setPriorityDialogOpen] = useState<boolean>(false);
  const [changingProject, setChangingProject] = useState<ProjectsList | null>(
    null
  );
  const [newPriority, setNewPriority] = useState<string>("");
  const [refresh, setRefresh] = useState(false);

  const { roles } = useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const isEmployee = roles.includes("Employee");

  const handlePriorityChange = async () => {
    if (changingProject && newPriority) {
      setLoading(true);
      try {
        await updateProjectPriority(changingProject.id, newPriority);
        toast.success("Priority updated successfully");
        setPriorityDialogOpen(false);
        setChangingProject(null);
        setNewPriority("");
        fetchProjects();
      } catch (error) {
        console.error("Error updating priority:", error);
        toast.error("Failed to update priority");
      } finally {
        setLoading(false);
      }
    }
  };

  const handleClosePriorityDialog = () => {
    setPriorityDialogOpen(false);
    setChangingProject(null);
    setNewPriority("");
  };

  const handlePrioritySelect = (project: ProjectsList, value: string) => {
    setChangingProject(project);
    setNewPriority(value);
    setPriorityDialogOpen(true);
  };

  const apiRef = useGridApiRef();

  // Fetch users
  const fetchUsers = async () => {
    setUsersLoading(true);
    try {
      const response = await getUsers();
      setUsers(response.users.results);
    } catch (error) {
      console.error("Failed to fetch users:", error);
      toast.error("Failed to load users");
    } finally {
      setUsersLoading(false);
    }
  };

  // Fetch projects
  const fetchProjects = async () => {
    setLoading(true);
    try {
      const status = selectedStatus === "Status" ? undefined : selectedStatus;
      const priority =
        selectedPriority === "Select Priority" ? undefined : selectedPriority;

      const search = debouncedSearchQuery.length >= 3 ? debouncedSearchQuery : undefined;

      const response = await getProjects(
        pageSize,
        page,
        "Department",
        selectedSortBy,
        startDate,
        endDate,
        status,
        search
      );

      const projectsData = response.projects.results.map((project: any) => {
        return {
          id: project._id,
          projectId: project.projectId,
          name: project.projectName,
          client: project.clientId?.firstName || "N/A",
          team:
            project.teamMembers?.map((member: any) => ({
              _id: member._id,
              name: `${member.firstName} ${member.lastName}`.trim(),
              avatar: member.avatar || "",
            })) || [],
          teamLeaders:
            project.teamLeaders?.map((leader: any) => ({
              _id: leader._id,
              name: `${leader.firstName} ${leader.lastName}`.trim(),
              avatar: leader.avatar || "",
            })) || [],
          projectManager:
            project.projectManager && typeof project.projectManager === "object"
              ? `${project.projectManager.firstName} ${project.projectManager.lastName}`.trim()
              : "N/A",
          deadline: project.endDate || "",
          priority: project.priority || "",
          status: project.isActive ? "Active" : "Inactive",
          avatar: project.logoUrl || project.clientId?.avatar || "",
        };
      });

      setProjects(projectsData);
      setTotalProjects(response.projects.total);
      setActiveProjects(response.projects.activeCount);
      setCompletedProjects(response.projects.inactiveCount);
      setNewProjects(response.projects.newProjects || 0);
    } catch (err) {
      toast.error("Failed to fetch projects");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(event.target.value);
    },
    []
  );

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    fetchProjects();
  }, [
    page,
    pageSize,
    selectedSortBy,
    selectedDateRange,
    startDate,
    endDate,
    selectedStatus,
    selectedPriority,
    users,
  ]);

  const handleOpenAddDialog = () => {
    setDialogMode("add");
    setEditingProjectData(null);
    setDialogOpen(true);
  };

  const handleEditClick = async (row: ProjectsList) => {
    setEditLoading(true);
    try {
      const projectData = await getProjectById(row.id);
      setEditingProjectData(projectData.project);
      setDialogMode("edit");
      setDialogOpen(true);
    } catch (error) {
      console.error("Error fetching project data:", error);
      toast.error("Failed to load project data");
    } finally {
      setEditLoading(false);
    }
  };

  const handleDeleteClick = (row: ProjectsList) => {
    setDeletingProject(row);
    setDeleteDialogOpen(true);
  };

  const handleDelete = async () => {
    if (deletingProject) {
      setLoading(true);
      try {
        await deleteProject(deletingProject.id);
        toast.success("Project deleted successfully");
        setDeleteDialogOpen(false);
        setDeletingProject(null);
        fetchProjects();
      } catch (error) {
        console.error("Error deleting project:", error);
        toast.error("Failed to delete project");
      } finally {
        setLoading(false);
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeletingProject(null);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingProjectData(null);
  };

  const handleProjectClick = async (projectId: string) => {
    try {
      const response = await getProjectById(projectId);
      setSelectedProject(response.project);
      setDetailsDialogOpen(true);
    } catch (error) {
      console.error("Error fetching project details:", error);
      toast.error("Failed to load project details");
    }
  };

  const rows: GridRowsProp = projects.map((project: ProjectsList) => ({
    id: project.id,
    projectId: project.projectId,
    name: project.name,
    client: project.client,
    team: project.team,
    teamLeaders: project.teamLeaders,
    projectManager: project.projectManager,
    deadline: project.deadline,
    priority: project.priority,
    status: project.status,
    avatar: project.avatar,
  }));

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Projects", href: "" },
    { label: "ProjectsList" },
  ];

  const columns: GridColDef[] = [
    {
      field: "projectId",
      headerName: "Project ID",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#111827" }}>
          {params.row.projectId}
        </Typography>
      ),
    },
    {
      field: "name",
      headerName: "Project Name",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => handleProjectClick(params.row.id)}
          >
            <Typography sx={{ fontSize: "14px", color: "#111827" }}>
              {params.row.name}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: "teamLeaders",
      headerName: "Leader",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
          {params.row.teamLeaders.map((leader: any) => (
            <Box
              key={leader._id}
              sx={{ display: "flex", alignItems: "center", gap: 1 }}
            >
              <Avatar
                src={leader.avatar}
                alt={leader.name}
                sx={{ width: 24, height: 24 }}
              />
              <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
                {leader.name}
              </Typography>
            </Box>
          ))}
        </Box>
      ),
    },
    {
      field: "team",
      headerName: "Team",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <AvatarGroup
          max={5}
          sx={{
            "& .MuiAvatar-root": { width: 24, height: 24 },
          }}
        >
          {params.row.team.map((member: any) => (
            <Avatar
              key={member._id}
              src={member.avatar}
              alt={member.name}
              sx={{ width: 24, height: 24 }}
            />
          ))}
        </AvatarGroup>
      ),
    },
    {
      field: "deadline",
      headerName: "Deadline",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
          {new Date(params.row.deadline).toLocaleDateString()}
        </Typography>
      ),
    },
    {
      field: "priority",
      headerName: "Priority",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: "3px", alignItems: "center" }}>
          <Select
            value={params.row.priority}
            onChange={(event: any) =>
              handlePrioritySelect(
                params.row as ProjectsList,
                event.target.value
              )
            }
            sx={{
              minWidth: "85px",
              width: "100px",
              height: "30px",
              borderRadius: "4px",
              "& .MuiSelect-select": {
                padding: "2px 5px",
                display: "flex",
                alignItems: "center",
              },
              "& .MuiOutlinedInput-notchedOutline": {
                border: "1px solid #E5E7EB",
              },
              "&:hover .MuiOutlinedInput-notchedOutline": {
                border: "1px solid #D1D5DB",
              },
              "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                border: "1px solid #D1D5DB",
              },
              "& .MuiSelect-icon": {
                display: "none",
              },
            }}
            renderValue={(value: string) => (
              <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <Box
                  sx={{
                    width: "8px",
                    height: "8px",
                    borderRadius: "50%",
                    backgroundColor:
                      value === "High"
                        ? "#FF0000" // Red for High
                        : value === "Medium"
                          ? "#FFC107" // Yellow for Medium
                          : "#00FF00", // Green for Low
                  }}
                />
                <Typography sx={{ fontSize: "14px", color: "#111827" }}>
                  {value} <KeyboardArrowDown />
                </Typography>
              </Box>
            )}
          >
            <MenuItem value="High">
              <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <Box
                  sx={{
                    width: "8px",
                    height: "8px",
                    borderRadius: "50%",
                    backgroundColor: "#FF0000",
                  }}
                />
                <Typography sx={{ fontSize: "14px" }}>High</Typography>
              </Box>
            </MenuItem>
            <MenuItem value="Medium">
              <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <Box
                  sx={{
                    width: "8px",
                    height: "8px",
                    borderRadius: "50%",
                    backgroundColor: "#FFC107",
                  }}
                />
                <Typography sx={{ fontSize: "14px", fontWeight: 400 }}>
                  Medium
                </Typography>
              </Box>
            </MenuItem>
            <MenuItem value="Low">
              <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <Box
                  sx={{
                    width: "8px",
                    height: "8px",
                    borderRadius: "50%",
                    backgroundColor: "#00FF00",
                  }}
                />
                <Typography sx={{ fontSize: "14px" }}>Low</Typography>
              </Box>
            </MenuItem>
          </Select>
        </Box>
      ),
    },
    {
      field: "status",
      headerName: "Status",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        // <Box
        //   sx={{
        //     display: "flex",
        //     alignItems: "center",
        //     gap: "3px",
        //     backgroundColor: params.value === "Active" ? "#03C95A" : "#E70D0D",
        //     color: "#fff",
        //     borderRadius: "5px",
        //     textAlign: "center",
        //     minWidth: "66px",
        //     justifyContent: "center",
        //     fontSize: "10px",
        //     fontWeight: 500,
        //     padding: "0px 5px",
        //     lineHeight: "18px",
        //   }}
        // >
        //   <Box
        //     sx={{
        //       width: "5px",
        //       height: "5px",
        //       borderRadius: "100%",
        //       backgroundColor: "#fff",
        //     }}
        //   />
        //   {params.value}
        // </Box>
        <StatusToggle
          isActive={params.row.status === "Active"}
          onChange={async (newStatus) => {
            try {
              setLoading(true);
              await updateProject(params.row.id, { isActive: newStatus});
              fetchProjects();
              toast.success(
                `Project status updated to ${newStatus ? "Active" : "Inactive"}!`
              );
            } catch (error) {
              console.error("Failed to update project status:", error);
              toast.error("Failed to update status. Please try again.");
            } finally {
              setLoading(false);
            }
          }}
        />
      ),
    },
    {
      field: "actions",
      headerName: "",
      editable: false,
      disableExport: true,
      flex: 1,
      renderCell: (params) =>
        !isEmployee && (
          <Box sx={{ display: "flex", gap: 1 }}>
            <IconButton
              sx={{ color: "#6B7280" }}
              onClick={() => handleEditClick(params.row as ProjectsList)}
              disabled={editLoading || loading}
            >
              <EditNote sx={{ width: "16px", height: "16px" }} />
            </IconButton>
            <IconButton
              sx={{ color: "#6B7280" }}
              onClick={() => handleDeleteClick(params.row as ProjectsList)}
              disabled={editLoading || loading}
            >
              <Delete sx={{ width: "16px", height: "16px" }} />
            </IconButton>
          </Box>
        ),
    },
  ];

  return (
    <Box className="project-container">
      {loading && <Loader loading={loading} />}
      <Box className="content">
        <Box className="project-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Projects List</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <ViewToggleProject />
            {!isEmployee && (
              <Button
                className="add-project"
                variant="contained"
                sx={{
                  display: "flex",
                  gap: "8px",
                  backgroundColor: "#F26522",
                  borderColor: "#F26522",
                  color: "#FFF",
                  fontWeight: 400,
                  fontSize: "14px",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                onClick={handleOpenAddDialog}
              >
                <ControlPoint sx={{ width: "16px", height: "16px" }} />
                Add Project
              </Button>
            )}
          </Box>
        </Box>

        <Paper>
          <Box
            className="DataGrid-container"
            sx={{
              display: "flex",
              flexDirection: "column",
              minHeight: 400,
              maxHeight: "calc(100vh - 200px)",
            }}
          >
            <Box className="DataGrid-header">
              <Typography variant="h5">Project List</Typography>
              <PolicyFilters
                departments={[]}
                selectedDepartment={""}
                setSelectedDepartment={() => { }}
                selectedSortBy={selectedSortBy}
                setSelectedSortBy={setSelectedSortBy}
                selectedDateRange={selectedDateRange}
                setSelectedDateRange={setSelectedDateRange}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                setPage={setPage}
                currentDate={new Date()}
                showDateRangeFilter={true}
                showDepartmentFilter={false}
                showStatusFilter={true}
                showSortByFilter={true}
                showDesignationFilter={false}
                designations={[]}
                selectedDesignation={""}
                setSelectedDesignation={() => { }}
                selectedLeaveType={""}
                setSelectedLeaveType={() => { }}
                selectedPriority=""
                setSelectedPriority={() => { }}
              />
            </Box>
            <Divider />
            <CustomDataGrid
              apiRef={apiRef}
              disableRowSelectionOnClick
              disableColumnFilter
              disableColumnSelector
              disableDensitySelector
              rows={rows}
              columns={columns}
              rowCount={totalProjects}
              paginationMode="server"
              initialState={{
                pagination: { paginationModel: { pageSize: pageSize } },
              }}
              paginationModel={{ page: page - 1, pageSize: pageSize }}
              onPaginationModelChange={(model) => {
                if (model.pageSize !== pageSize) {
                  setPageSize(model.pageSize);
                  setPage(1);
                } else {
                  setPage(model.page + 1);
                }
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              slots={{
                pagination: CustomPagination,
                toolbar: () => (
                  <QuickSearchToolbar
                    onSearchChange={handleSearchChange}
                    searchQuery={searchQuery}
                  />
                ),
              }}
            />
          </Box>
        </Paper>

        <ProjectDialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          refreshList={fetchProjects}
          projectData={editingProjectData}
          mode={dialogMode}
          users={users}
          usersLoading={usersLoading}
        />

        {selectedProject && (
          <ProjectDetailsDialog
            open={detailsDialogOpen}
            onClose={() => setDetailsDialogOpen(false)}
            project={selectedProject}
          />
        )}

        <DeleteConfirmationDialog
          open={deleteDialogOpen}
          title="Delete Project"
          message={`Are you sure you want to delete the project "${deletingProject?.name || ""}"? This action cannot be undone.`}
          onClose={handleCloseDeleteDialog}
          onConfirm={handleDelete}
        />

        <StatusChangeConfirmation
          open={priorityDialogOpen}
          title="Change Priority"
          message={`Are you sure you want to change the priority of project "${changingProject?.name || ""}" to ${newPriority}?`}
          onClose={handleClosePriorityDialog}
          onConfirm={handlePriorityChange}
        />
      </Box>
    </Box>
  );
}

function ProjectsListPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ProjectsListContent />
    </Suspense>
  );
}

export default ProjectsListPage;

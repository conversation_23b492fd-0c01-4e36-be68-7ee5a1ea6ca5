import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>,
  Avatar,
  <PERSON>rid,
  <PERSON>vider,
  Box,
  Chip,
  Stack,
  Paper,
} from "@mui/material";
import Tree from "react-d3-tree";
import { Cancel } from "@mui/icons-material";

interface Member {
  _id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
}

interface Project {
  projectName: string;
  projectId: string;
  description: string;
  value: number;
  priority: string;
  priceType: string;
  startDate: string;
  endDate: string;
  logoUrl: string;
  clientId: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    company: string;
    avatar: string;
  };
  teamMembers: Member[];
  teamLeaders: Member[];
  projectManager: Member;
}

interface Props {
  open: boolean;
  onClose: () => void;
  project: Project;
}

const ProjectDetailsDialog: React.FC<Props> = ({ open, onClose, project }) => {
  const buildTreeData = () => {
    return {
      name: `${project.projectManager.firstName} ${project.projectManager.lastName}`,
      attributes: { role: "Project Manager" },
      children: project.teamLeaders.map((leader) => ({
        name: `${leader.firstName} ${leader.lastName}`,
        attributes: { role: "Team Leader" },
        children: project.teamMembers.map((member) => ({
          name: `${member.firstName} ${member.lastName}`,
          attributes: { role: "Team Member" },
        })),
      })),
    };
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth="lg"
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          {/* <Avatar
            src={project.logoUrl}
            alt="logo"
            sx={{ width: 56, height: 56 }}
          /> */}
          <Box>
            <Typography variant="h6">
              {project.projectName} ({project.projectId})
            </Typography>
          </Box>
        </Stack>
        <Cancel onClick={onClose} sx={{ fontSize: "20x", color: "#6B7280" }} />
      </DialogTitle>

      <DialogContent dividers>
        <Box>
          <label>Description</label>
          <Typography variant="body2" color="text.secondary">
            {project.description}
          </Typography>
        </Box>
        <Grid container spacing={3}>
          {/* Project Info */}
          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Project Info
              </Typography>
              <Stack spacing={1}>
                <Typography>
                  <b>Priority:</b>{" "}
                  <Chip label={project.priority} color="primary" size="small" />
                </Typography>
                {/* <Typography>
                  <b>Value:</b> ₹{project.value.toLocaleString()}
                </Typography> */}
                {/* <Typography>
                  <b>Price Type:</b> {project.priceType}
                </Typography> */}
                <Typography>
                  <b>Start Date:</b>{" "}
                  {new Date(project.startDate).toLocaleDateString()}
                </Typography>
                <Typography>
                  <b>End Date:</b>{" "}
                  {new Date(project.endDate).toLocaleDateString()}
                </Typography>
              </Stack>
            </Paper>
          </Grid>

          {/* Client Info */}
          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                👤 Client Details
              </Typography>
              <Stack direction="row" spacing={2} alignItems="center">
                <Avatar src={project.clientId.avatar} alt="client" />
                <Box>
                  <Typography>
                    {project.clientId.firstName} {project.clientId.lastName}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {project.clientId.company}
                  </Typography>
                </Box>
              </Stack>
              <Typography mt={1}>
                <b>Email:</b> {project.clientId.email}
              </Typography>
              <Typography>
                <b>Phone:</b> {project.clientId.phone}
              </Typography>
            </Paper>
          </Grid>

          {/* Hierarchy Tree */}
          <Grid item xs={12}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                🧭 Team Hierarchy
              </Typography>
              <Box sx={{ width: "100%", height: 400 }}>
                <Tree
                  data={buildTreeData()}
                  orientation="vertical"
                  translate={{ x: 300, y: 100 }}
                  // collapsible={false}
                  pathFunc="step"
                  nodeSize={{ x: 200, y: 100 }}
                />
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  );
};

export default ProjectDetailsDialog;

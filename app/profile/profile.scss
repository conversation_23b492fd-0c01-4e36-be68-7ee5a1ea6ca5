.profile-container {

    .MuiInputBase-root {
        padding: 5.5px 14px !important;
        height: 36px !important;
        box-sizing: border-box;

        &.MuiInputBase-multiline {
            height: auto !important;
            padding: 5.5px 14px !important;
        }

        .MuiInputBase-input {
            padding: 0 !important;
            height: 100% !important;
            box-sizing: border-box;
        }
    }

    .MuiAutocomplete-inputRoot {
        padding: 5.5px 14px !important;
        height: 36px !important;
        box-sizing: border-box;

        .MuiAutocomplete-input {
            padding: 0 !important;
            height: 100% !important;
        }
    }

    .MuiTextField-root,
    .MuiAutocomplete-root {
        margin-top: 8px !important;
        margin-bottom: 8px !important;
    }

    .content {
        padding: 24px;

        .profile-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;

            .breadcrumbs-box {
                h2 {
                    font-size: 24px;
                    font-weight: 700;
                    color: #202C4B;
                }
            }


        }
    }

    .profile-details {
        label {
            flex: 0 0 auto;
            width: 33.33333333%;
            font-size: 14px;
            font-weight: 500;
            color: rgb(32, 44, 75);
        }


    }

    .address-info {
        label {
            flex: 0 0 auto;
            width: 33.33333333%;
            font-size: 14px;
            font-weight: 500;
            color: rgb(32, 44, 75);
        }


    }

    .change-password {
        label {
            flex: 0 0 auto;
            width: 41.66666667%;
            font-size: 14px;
            font-weight: 500;
            color: rgb(32, 44, 75);
        }
    }
}
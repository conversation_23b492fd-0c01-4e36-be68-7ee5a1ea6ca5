const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";
import axios, { AxiosError } from 'axios';

export const getLeave = async (
  limit: number,
  page: number,
  departmentName: string,
  order: string,
  empId?: string,
  startDate?: string,
  endDate?: string,
  isActive?: string,
  leaveType?: string, // New parameter
  search?: string // Optional search parameter
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE}`;
    
    // Construct query parameters
    const queryParams = new URLSearchParams();

    // Always append these parameters
    queryParams.append("limit", limit.toString());
    queryParams.append("page", page.toString());
    queryParams.append("departmentName", departmentName === "Department" ? "" : departmentName);
    queryParams.append("order", order === "Sort By" ? "" : order);
    

    // Append optional parameters if provided
    if (empId) queryParams.append("empId", empId);
    if (startDate) queryParams.append("startDate", startDate);
    if (endDate) queryParams.append("endDate", endDate);
    if (isActive) queryParams.append("isActive", isActive);
    if (leaveType && leaveType !== "Leave Type") queryParams.append("leaveType", leaveType); // Add leaveType
    if (search) queryParams.append("search", search); // Add search parameter

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    console.log("Fetching LEAVE with URL:", fullUrl);

    const response = await base.get(fullUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching LEAVE:", error);
    throw error;
  }
};


//add leave 
interface LeaveBody {
    empId: string;
    leaveType: string;
    leaveFrom: string;
    leaveTo: string;
    leaveTime: string;
    remainingDays: number;
    leaveStatus: string;
    reason: string;
  }
  interface LeaveErrorResponse {
    error: {
      errors: string;
    };
  }

  export const getAllLeave = async () => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE}`;
      const response = await base.get(apiUrl);
      console.log("LEAVE Fetching successfully");
      return response.data;
    } catch (error) {
      console.error("Error Fetching LEAVE:", error);
      throw error;
    }
  };

export const addLeave = async (body: LeaveBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE}`;
    const response = await base.post(apiUrl, body);
    console.log("LEAVE added successfully");
    return response.data;
  } catch (error: unknown) {
    console.error("Error adding LEAVE:", error);

    const errorMessage = (error as AxiosError<LeaveErrorResponse>).response?.data?.error?.errors ||
      (error as Error).message ||
      "Failed to add leave";

    toast.error(errorMessage);
    throw error;
  }
};

//update leave
export const updateLeave = async (id: string, body: LeaveBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE}/${id}`;
    await base.put(apiUrl, body);
    console.log("LEAVE updated successfully");
  } catch (error) {
    console.error("Error updating LEAVE:", error);
    throw error;
  }
};

//get leave by id
export const getLeaveById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching LEAVE by ID:", error);
    throw error;
  }
};

//delete leave
export const deleteLeave = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE}/${id}`;
    const response = await base.delete(apiUrl);
    console.log("LEAVE deleted successfully");
  } catch (error) {
    console.error("Error deleting LEAVE:", error);
    throw error;
  }
};

//approve leave by id 
export const approveLeaveById = async (leaveId: string, status: "Pending" | "Approved" | "Declined") => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE}/${leaveId}/approve?status=${status}`;
    const response = await base.patch(apiUrl);
    console.log(`LEAVE ${status} successfully`);
    toast.success(response.data.message);
  } catch (error) {
    console.error(`Error ${status} LEAVE by ID:`, error);
    throw error;
  }
};




//employee leave section 

//get employee leave from employee id
export const getEmployeeLeaveById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching LEAVE by ID:", error);
    throw error;
  }
};

//GET EMPLOYEE LEAVE
export const getEmpLeaveById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE_EMP}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching LEAVE by ID:", error);
    throw error;
  }
};

//post
export const addEmployeeLeave = async (body: LeaveBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE}`;
    const response = await base.post(apiUrl, body);
    console.log("LEAVE added successfully");
    return response.data;
  } catch (error) {
    console.error("Error adding LEAVE:", error);
    throw error;
  }
};

//update leave
export const updateEmployeeLeave = async (id: string, body: LeaveBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE}/${id}`;
    await base.put(apiUrl, body);
    console.log("LEAVE updated successfully");
  } catch (error) {
    console.error("Error updating LEAVE:", error);
    throw error;
  }
};


export const deleteEmployeeLeave = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE}/${id}`;
    await base.delete(apiUrl);
    console.log("LEAVE deleted successfully");
  } catch (error) {
    console.error("Error deleting LEAVE:", error);
    throw error;
  }
};

// Leave setting servicess 

//post leave seetting
export interface LeaveSettingBody {
  leaveType: string;
  policyName: string;
  days: number;
  designations: string[];
}

export const addLeaveSetting = async (body: LeaveSettingBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE_SETTING}`;
    const response = await base.post(apiUrl, body);
    console.log("LEAVE setting added successfully");
    return response.data;
  } catch (error) {
    console.error("Error adding LEAVE setting:", error);
    throw error;
  }
};

//get all leave setting without paginationtion
export const getAllLeaveSettings = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE_SETTING}`;
    const response = await base.get(apiUrl);
    return response.data;
  }
  catch (error) {
    console.error("Error fetching LEAVE settings:", error);
    throw error;
  }
};

//get leave seeting by id

export const getLeaveSettingById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE_SETTING}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching LEAVE setting by ID:", error);
    throw error;
  }
};
//update leave setting
export const updateLeaveSetting = async (id: string, body: LeaveSettingBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LEAVE_SETTING}/${id}`;
    await base.put(apiUrl, body);
    console.log("LEAVE setting updated successfully");
  } catch (error) {
    console.error("Error updating LEAVE setting:", error);
    throw error;
  }
}

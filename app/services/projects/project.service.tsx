const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

//GET PROJECTS
export const getProjects = async (
    limit: number = 10,
    page: number = 1,
    departmentName: string = "Department",
    order: string = "Sort By",
    startDate?: string,
    endDate?: string,
    status?: string,
    search?: string
) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROJECTS}`;

        // Construct query parameters
        const queryParams = new URLSearchParams({
            limit: limit.toString(),
            page: page.toString(),
            departmentName: departmentName === "Department" ? "" : departmentName,
            order: order === "Sort By" ? "" : order,
        });

        if (startDate) queryParams.append("startDate", startDate);
        if (endDate) queryParams.append("endDate", endDate);
        if (status === "Active") queryParams.append("isActive", "true");
        else if (status === "Completed" || status === "Inactive") queryParams.append("isActive", "false");
        if (search) queryParams.append("search", search);

        console.log("Fetching projects with params:", {
            status,
            queryParams: queryParams.toString(),
            url: `${apiUrl}?${queryParams.toString()}`,
        });

        const response = await base.get(`${apiUrl}?${queryParams.toString()}`);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching projects:", error);
        throw error;
    }
};

//get project by id
export const getProjectById = async (id: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROJECTS}/${id}`;
        const response = await base.get(apiUrl);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching project by ID:", error);
        throw error;
    }
};

//ADD PROJECTS
export const addProject = async (data: any) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROJECTS}`;
        const response = await base.post(apiUrl, data);
        return response.data;
    } catch (error: any) {
        console.error("Error posting project:", error);
        throw error;
    }
};

//update project
export const updateProject = async (id: string, data: any) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROJECTS}/${id}`;
        const response = await base.put(apiUrl, data);
        return response.data;
    } catch (error: any) {
        console.error("Error updating project:", error);
        // toast.error("Failed to update project. Please try again.");
        throw error;
    }
};

//delete project
export const deleteProject = async (id: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROJECTS}/${id}`;
        const response = await base.delete(apiUrl);
        return response.data;
    } catch (error: any) {
        console.error("Error deleting project:", error);
        throw error;
    }
};


//get all projects
export const getAllProjects = async (startDate?: string, endDate?: string, isActive?: boolean) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROJECTS}`;
        // Construct query parameters

        const queryParams = new URLSearchParams();
        if (startDate) queryParams.append("startDate", startDate);
        if (endDate) queryParams.append("endDate", endDate);
        if (isActive !== undefined) queryParams.append("isActive", isActive ? "true" : "false");

        const response = await base.get(`${apiUrl}?${queryParams.toString()}`);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching projects:", error);
        throw error;
    }
}

//Update priority
export const updateProjectPriority = async (id: string, priority: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROJECTS}/${id}/priority`;
        const response = await base.patch(apiUrl, { priority });
        return response.data;
    } catch (error: any) {
        console.error("Error updating project priority:", error);
        throw error;
    }
}


// Add Task
export const addTask = async (data: any) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TASK}`;
        const response = await base.post(apiUrl, data);
        return response.data;
    } catch (error: any) {
        console.error("Error posting project:", error);
        throw error;
    }
};

// update task by id
export const updateTask = async (id: string, data: any) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TASK}/${id}`;
        const response = await base.patch(apiUrl, data);
        return response.data;
    } catch (error: any) {
        console.error("Error updating project:", error);
        throw error;
    }
}

// get task by id
export const getTaskById = async (id: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TASK}/${id}`;
        const response = await base.get(apiUrl);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching task by ID:", error);
        throw error;
    }
}


// Delete task by id
export const deleteTask = async (id: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TASK}/${id}`;
        const response = await base.delete(apiUrl);
        return response.data;
    } catch (error: any) {
        console.error("Error deleting task:", error);
        throw error;
    }
}

//get all Task by filteration
export const getTasks = async (
    projectId: string,
    order: string = "Sort By",
    startDate?: string,
    endDate?: string,
    status?: string,
    search?: string,
    id?: string,
    empId?: string,
    dueDate?: string
) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TASK}`;

        // Construct query parameters
        const queryParams = new URLSearchParams({
            projectId,
            order: order === "Sort By" ? "" : order,
        });

        if (startDate) queryParams.append("startDate", startDate);
        if (endDate) queryParams.append("endDate", endDate);
        if (status === "Active") queryParams.append("isActive", "true");
        else if (status === "Completed" || status === "Inactive") queryParams.append("isActive", "false");
        if (search) queryParams.append("search", search);
        if (id) queryParams.append("id", id);
        if (empId) queryParams.append("empId", empId);
        if (dueDate) queryParams.append("dueDate", dueDate);

        console.log("Fetching projects with params:", {
            status,
            queryParams: queryParams.toString(),
            url: `${apiUrl}?${queryParams.toString()}`,
        });

        const response = await base.get(`${apiUrl}?${queryParams.toString()}`);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching projects:", error);
        throw error;
    }
};


//get all status board 
export const getStatusBoard = async () => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.STATUS_BOARD}`;
        const response = await base.get(`${apiUrl}`);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching status board:", error);
        throw error;
    }
};


// add status board
export const createStatus = async (data: any) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.STATUS_BOARD}`;
        const response = await base.post(apiUrl, data);
        return response.data;
    } catch (error: any) {
        console.error("Error posting status board:", error);
        throw error;
    }
}

// update status board
export const updateStatus = async (id: string, data: any) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.STATUS_BOARD}/${id}`;
        const response = await base.put(apiUrl, data);
        return response.data;
    } catch (error: any) {
        console.error("Error updating status board:", error);
        throw error;
    }
}
// delete status board
export const deleteStatus = async (id: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.STATUS_BOARD}/${id}`;
        const response = await base.delete(apiUrl);
        return response.data;
    } catch (error: any) {
        console.error("Error deleting status board:", error);
        throw error;
    }
}

//get id
export const getStatusById = async (id: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.STATUS_BOARD}/${id}`;
        const response = await base.get(apiUrl);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching status by ID:", error);
        throw error;
    }
}
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL_DEVELOPMENT;
import { API_ENDPOINTS } from "@/constants/endpoints";
import { base2 } from "../middlewares/interceptors";

interface PreSignedUrlParams {
  location: string;
  type: string;
  count: number;
  fileName?: string;
}

export const getPreSignedUrl = async (prams: PreSignedUrlParams) => {
  try {
    let apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GET_PRE_SIGNED_URL}?location=${prams.location}&type=${prams.type}&count=${prams.count}`;
    if (prams.fileName) {
      apiUrl += `&fileName=${prams.fileName}`;
    }
    const response = await base2.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching pre-signed URL:", error);
    throw error;
  }
};

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";

//get all goals type
export const getAllGoalsType = async (
    limit?: number,
    page?: number,
    order?: string,
    startDate?: string,
    endDate?: string,
    isActive?: string,
    search?: string
  ) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GOAL_TYPE}`;
  
      // Construct query parameters
      const queryParams = new URLSearchParams();
  
      // Only add pagination parameters if they are provided
      if (limit) queryParams.append("limit", limit.toString());
      if (page) queryParams.append("page", page.toString());
  
      if (order) queryParams.append("order", order);
      if (startDate) queryParams.append("startDate", startDate);
      if (endDate) queryParams.append("endDate", endDate);
      if (isActive) queryParams.append("isActive", isActive);
      if (search) queryParams.append("search", search);
  
      // Construct the full URL
      const fullUrl = queryParams.toString()
        ? `${apiUrl}?${queryParams.toString()}`
        : apiUrl;
  
      console.log("Fetching Goals with URL:", fullUrl);
  
      const response = await base.get(fullUrl);
  
      return response.data;
    } catch (error) {
      console.error("Error fetching Goals:", error);
      throw error;
    }
  };

// add goal type
export const addGoalType = async (data: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GOAL_TYPE}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error adding Goal:", error);
    throw error;
  }
}  

// update goal type
export const updateGoalType = async (id: string, data: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GOAL_TYPE}/${id}`;
    const response = await base.patch(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error updating Goal:", error);
    throw error;
  }
}
// delete goal type
export const deleteGoalType = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GOAL_TYPE}/${id}`;
    const response = await base.delete(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error deleting Goal:", error);
    throw error;
  }
}
// get goal type by id
export const getGoalTypeById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GOAL_TYPE}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching Goal by ID:", error);
    throw error;
  }
}
// get all goals type
export const getAllGoalType = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GOAL_TYPE}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching Goal Types:", error);
    throw error;
  }
}

// get all goals
export const getAllGoals = async (
  limit?: number,
  page?: number,
  order?: string,
  startDate?: string,
  endDate?: string,
  isActive?: string,
  search?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GOAL}`;
    // Construct query parameters
    const queryParams = new URLSearchParams();

    // Only add pagination parameters if they are provided
    if (limit) queryParams.append("limit", limit.toString());
    if (page) queryParams.append("page", page.toString());

    if (order) queryParams.append("order", order);
    if (startDate) queryParams.append("startDate", startDate);
    if (endDate) queryParams.append("endDate", endDate);
    if (isActive) queryParams.append("isActive", isActive);
    if (search) queryParams.append("search", search);

    // Construct the full URL
    const fullUrl = queryParams.toString()
      ? `${apiUrl}?${queryParams.toString()}`
      : apiUrl;

    console.log("Fetching Goals with URL:", fullUrl);

    const response = await base.get(fullUrl);

    return response.data;
  } catch (error) {
    console.error("Error fetching Goals:", error);
    throw error;
  }
}

// add goal
export const addGoal = async (data: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GOAL}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error adding Goal:", error);
    throw error;
  }
}
// update goal
export const updateGoal = async (id: string, data: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GOAL}/${id}`;
    const response = await base.patch(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error updating Goal:", error);
    throw error;
  }
}
// delete goal
export const deleteGoal = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GOAL}/${id}`;
    const response = await base.delete(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error deleting Goal:", error);
    throw error;
  }
}
// get goal by id
export const getGoalById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.GOAL}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching Goal by ID:", error);
    throw error;
  }
}

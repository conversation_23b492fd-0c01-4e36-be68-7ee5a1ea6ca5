const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

export const getPromotion = async (
  limit?: number,
  page?: number,
  departmentName?: string,
  order?: string,
  startDate?: string,
  endDate?: string,
  isActive?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROMOTION}`;
    const queryParams = new URLSearchParams();

    // Only add pagination parameters if they are provided
    if (limit) queryParams.append("limit", limit.toString());
    if (page) queryParams.append("page", page.toString());

    // Add other parameters
    if (departmentName && departmentName !== "Department")
      queryParams.append("departmentName", departmentName);
    if (order) queryParams.append("order", order);
    if (startDate) queryParams.append("startDate", startDate);
    if (endDate) queryParams.append("endDate", endDate);
    if (isActive) queryParams.append("isActive", isActive);

    const fullUrl = queryParams.toString()
      ? `${apiUrl}?${queryParams.toString()}`
      : apiUrl;

    console.log("Fetching designations with URL:", fullUrl);

    const response = await base.get(fullUrl);

    // Sort designations alphabetically by designationName
    /*  if (response.data && response.data.designations && response.data.designations.results) {
      response.data.designations.results = [...response.data.designations.results].sort(
        (a, b) => a.designationName.localeCompare(b.designationName)
      );
    } */

    return response.data;
  } catch (error) {
    console.error("Error fetching designations:", error);
    throw error;
  }
};

export const getAllPromotion = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROMOTION}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching Promotions:", error);
    throw error;
  }
};

// get by id
export const getPromotionById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROMOTION}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching Promotion by ID:", error);
    throw error;
  }
};

export const addPromotion = async (data: {
  empId: string;
  currentDesignation: string;
  newDesignation: string;
  promotionDate: string;
}) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROMOTION}`;
    const response = await base.post(apiUrl, data);
    toast.success("Promotion added successfully");
    return response.data;
  } catch (error) {
    console.error("Error adding Promotion:", error);
    toast.error("Failed to add Promotion");
    throw error;
  }
};

export const updatePromotion = async (
  id: string,
  data: {
    empId: string;
    currentDesignation: string;
    newDesignation: string;
    promotionDate: string;
  }
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROMOTION}/${id}`;
    const response = await base.put(apiUrl, data);
    toast.success("Promotion updated successfully");
    return response.data;
  } catch (error) {
    console.error("Error updating Promotion:", error);
    toast.error("Failed to update Promotion");
    throw error;
  }
};

export const deletePromotion = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROMOTION}/${id}`;
    const response = await base.delete(apiUrl);
    toast.success("Promotion deleted successfully");
    return response.data;
  } catch (error) {
    console.error("Error deleting Promotion:", error);
    toast.error("Failed to delete Promotion");
    throw error;
  }
};

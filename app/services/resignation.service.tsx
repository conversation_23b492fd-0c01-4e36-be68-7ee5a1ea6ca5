const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

export const getAllResignations = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.RESIGNATION}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching Resignations:", error);
    throw error;
  }
};

export const getResignations = async (
  limit: number,
  page: number,
  departmentName: string,
  order: string,
  startDate?: string,
  endDate?: string,
  isActive?: string,
  search?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.RESIGNATION}`;

    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      departmentName: departmentName === "Department" ? "" : departmentName,
      order: order,
      search: search || "",
    });

    if (startDate) {
      queryParams.append("startDate", startDate);
    }
    if (endDate) {
      queryParams.append("endDate", endDate);
    }
    if (isActive) {
      queryParams.append("isActive", isActive);
    }
    if (search) {
      queryParams.append("search", search);
    }

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    console.log("Fetching resignation with URL:", fullUrl);

    const response = await base.get(fullUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching resignation:", error);
    throw error;
  }
};

export const deleteResignation = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.RESIGNATION}/${id}`;
    console.log(apiUrl);
    const response = await base.delete(apiUrl);
    console.log("resignation deleted successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error deleting resignation:", error);
    throw error;
  }
};

interface resignationBody {
  empId?: string;
  noticeDate?: string;
  resignationDate?: string;
  reason?: string;
}

export const addResignation = async (body: resignationBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.RESIGNATION}`;
    console.log(apiUrl);
    const response = await base.post(apiUrl, body);
    console.log("resignation added successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error adding resignation:", error);
    throw error;
  }
};

// Update Policies
export const updateResignation = async (id: string, body: resignationBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.RESIGNATION}/${id}`;
    console.log(apiUrl);
    const response = await base.put(apiUrl, body);
    console.log("resignation updated successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error updating resignation:", error);
    throw error;
  }
};

//get policy by id
export const getResignationById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.RESIGNATION}/${id}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching resignation by id:", error);
    throw error;
  }
};

//approve resignation by id 
export const approveResignationById = async (resignationId: string, status: "Pending" | "Approved" | "Declined") => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.RESIGNATION}/employee/${resignationId}/status?status=${status}`;
    const response = await base.patch(apiUrl);
    console.log(`Resignation ${status} successfully`);
    toast.success(response.data.message || `Resignation ${status} successfully`);
    return response.data;
  } catch (error) {
    console.error(`Error ${status} Resignation by ID:`, error);
    throw error;
  }
};

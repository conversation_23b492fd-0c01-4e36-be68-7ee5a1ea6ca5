"use client";
import Loader from "@/components/Loader/Loader";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState, useMemo } from "react";

const AuthGuard = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const publicPaths = useMemo(
    () => [
      "/login",
      "/login/",
      "/signup",
      "/signup/",
      "/forget-password",
      "/forget-password/",
      "/reset-password",
      "/reset-password/",
      "/success",
      "/success/",
    ],
    []
  );

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem("token");

        // Increase delay to ensure proper token validation
        await new Promise((resolve) => setTimeout(resolve, 1000));

        if (token) {
          setIsAuthenticated(true);
          // Only redirect from login page, don't force redirect to dashboard
          if (pathname === "/login") {
            await router.push("/");
          }
        } else {
          setIsAuthenticated(false);
          if (!publicPaths.includes(pathname)) {
            await router.push("/login/");
          }
        }
      } catch (error) {
        console.error("Auth check error:", error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [pathname, router, publicPaths]);

  // Don't render anything while loading
  if (isLoading) {
    return <Loader loading={true} />;
  }

  // Only block access to protected routes when not authenticated
  if (!isAuthenticated && !publicPaths.includes(pathname)) {
    return null;
  }

  return <>{children}</>;
};

export default AuthGuard;
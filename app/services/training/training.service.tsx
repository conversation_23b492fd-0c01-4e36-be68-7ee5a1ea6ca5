const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

export const getTraining = async (
  limit: number,
  page: number,
  departmentName: string,
  order: string,
  startDate?: string,
  endDate?: string,
  isActive?: string,
  search?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINING}`;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      departmentName: departmentName === "Department" ? "" : departmentName,
      order: order,
    });

    if (startDate) {
      queryParams.append("startDate", startDate);
    }
    if (endDate) {
      queryParams.append("endDate", endDate);
    }
    if (isActive) {
      queryParams.append("isActive", isActive);
    }
    if (search) {
      queryParams.append("search", search);
    }

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    console.log("Fetching training with URL:", fullUrl);

    const response = await base.get(fullUrl);
    
    // Sort trainings alphabetically by trainingType
    /* if (response.data && response.data.trainings && response.data.trainings.results) {
      response.data.trainings.results = [...response.data.trainings.results].sort(
        (a, b) => a.trainingType.localeCompare(b.trainingType)
      );
    } */
    
    return response.data;
  } catch (error) {
    console.error("Error fetching training:", error);
    throw error;
  }
};

interface TrainingBody {
  trainingTypeId: string;
  trainerId: string;
  employeesId: string[];
  trainingCost: number;
  startDate: string;
  endDate: string;
  description: string;
  isActive: boolean;
}

export const addTraining = async (body: TrainingBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINING}`;
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error adding training type:", error);
    throw error;
  }
};

export const updateTraining = async (id: string, body: TrainingBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINING}/${id}`;
    const response = await base.put(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error updating TRAINING:", error);
    throw error;
  }
};

export const deleteTraining = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINING}/${id}`;
    const response = await base.delete(apiUrl);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error deleting TRAINING:", error);
    throw error;
  }
};

//get trainer by id
export const getTrainingById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINING}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error getting TRAINING by id:", error);
    throw error;
  }
};

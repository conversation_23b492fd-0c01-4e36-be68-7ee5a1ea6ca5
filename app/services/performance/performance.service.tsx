// import axios from "axios";
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";



//get all performanec review
export const getAllPerformanceReview = async (
  limit: number = 10,
  page: number = 1,
  departmentName: string = "Department",
  order: string = "Sort By",
  startDate?: string,
  endDate?: string,
  isActive?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE_REVIEW}`;
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      departmentName: departmentName === "Department" ? "" : departmentName,
      order: order === "Sort By" ? "" : order,
    });
    if (startDate) queryParams.append("startDate", startDate);
    if (endDate) queryParams.append("endDate", endDate);
    if (isActive) queryParams.append("isActive", isActive);

    console.log("Fetching performance review with params:", {
      startDate,
      endDate,
      url: `${apiUrl}?${queryParams.toString()}`,
    });

    const response = await base.get(`${apiUrl}?${queryParams.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error("Error fetching performance review:", error);
    throw error;
  }
};

// Fetch Performance Indicator
export const getPerformanceIndicator = async (
  limit: number,
  page: number,
  departmentName: string,
  order: string,
  startDate?: string,
  endDate?: string,
  search?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE}`;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      departmentName: departmentName === "Department" ? "" : departmentName,
      order: order,
      search: search || "",
    });

    if (startDate) {
      queryParams.append("startDate", startDate);
    }
    if (endDate) {
      queryParams.append("endDate", endDate);
    }
    if (search) {
      queryParams.append("search", search);
    }

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    console.log("Fetching policies with URL:", fullUrl);

    const response = await base.get(fullUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching policies:", error);
    throw error;
  }
};

//add performance indicator
interface PerformanceIndicator {
  designationId: string;
  customerExperience: string;
  marketing: string;
  management: string;
  administration: string;
  presentationSkill: string;
  qualityWork: string;
  efficiency: string;
  integrity: string;
  professionalism: string;
  teamWork: string;
  criticalThinking: string;
  conflictManagement: string;
  attendence: string;
  meetDedline: string;
  isActive: boolean;
}

interface CompetencyItem {
  competencyKey: string;
  setValue: string;
}

interface PerformanceAppraisalPayload {
  empId: string;
  appraisalDate: string;
  designationId: string;
  competencies: CompetencyItem[];
}

//add performance indicator
export const addPerformanceIndicator = async (data: PerformanceIndicator) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE}`;
    const response = await base.post(apiUrl, data);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error adding performance indicator:", error);
    throw error;
  }
};

//update performance indicator
export const updatePerformanceIndicator = async (
  id: string,
  data: PerformanceIndicator
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE}/${id}`;
    const response = await base.put(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error updating performance indicator:", error);
    throw error;
  }
};

//delete performance indicator
export const deletePerformanceIndicator = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE}/${id}`;
    const response = await base.delete(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error deleting performance indicator:", error);
    throw error;
  }
};

//get performance indicator by id
export const getPerformanceIndicatorById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching performance indicator by id:", error);
    throw error;
  }
};

export const getPerformanceIndicatorByDesignationId = async (designationId: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE}/designation/${designationId}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching performance indicator by Designation ID:", error);
    throw error;
  }
};

//performance appraisal section

// Fetch Performance Appraisal

export const getPerformanceAppraisal = async (
  limit: number,
  page: number,
  order: string,
  startDate?: string,
  endDate?: string,
  search?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE_APPRAISAL}`;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      order: order,
      search: search ?? ""
    });

    if (startDate) {
      queryParams.append("startDate", startDate);
    }
    if (endDate) {
      queryParams.append("endDate", endDate);
    }
    if (search) {
      queryParams.append("search", search);
    }

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    console.log("Fetching policies with URL:", fullUrl);

    const response = await base.get(fullUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching policies:", error);
    throw error;
  }
};

// Add Performance Appraisal
export const addPerformanceAppraisal = async (
  data: PerformanceAppraisalPayload
) => {
  try {
    const response = await base.post(
      `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE_APPRAISAL}`,
      data
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

//update performance appraisal
export const updatePerformanceAppraisal = async (
  id: string,
  data: PerformanceAppraisalPayload
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE_APPRAISAL}/${id}`;
    const response = await base.patch(apiUrl, data);
    return response.data;
  } catch (error) {
    throw error;
  }
};

//get performance appraisal by id
export const getPerformanceAppraisalById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE_APPRAISAL}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    throw error;
  }
};

//delete performance appraisal
export const deletePerformanceAppraisal = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE_APPRAISAL}/${id}`;
    const response = await base.delete(apiUrl);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Performance Review Section

// POst Professional Excellence

export interface ProfessionalExcellencePayload {
  empId: string;
  professionalExcellence: {
    keyResultArea: string;
    keyPerformanceIndicator: string;
    weightage: number;
    percentAchievSelf: number;
    percentAchievRo: number;
  }[];
}

export const postProfessionalExcellence = async (
  data: ProfessionalExcellencePayload
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROFESSIONAL_EXCELLENCE}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting professional excellence:", error);
    throw error;
  }
};

//Personal excellence
export interface PersonalExcellence {
  empId: string;
  personalExcellence: {
    personalKeyResultArea: string;
    personalKeyPerformanceIndicator: string;
    personalWeightage: number;
    personalPercentAchievSelf: number;
    personalPercentAchievRo: number;
  }[];
}

export const postPersonalExcellence = async (data: PersonalExcellence) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERSONAL_EXCELLENCE}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting personal excellence:", error);
    throw error;
  }
};

//specialachievment
export interface SpecialAchievement {
  empId: string;
  specialAchievements: {
    specialAcheivmentCommentBySelf: string;
    specialAcheivmentCommentByRo: string;
    specialAcheivmentCommentByHod: string;
  }[];
}

export const postSpecialAchievement = async (data: SpecialAchievement) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.SPECIAL_ACHIEVEMENT}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting special achievement:", error);
    throw error;
  }
};

//commentonrole
export interface CommentOnRole {
  empId: string;
  commentOnRole: {
    commentOnRoleBySelf: string;
    commentOnRoleByRo: string;
    commentOnRoleByHod: string;
  }[];
}

export const postCommentOnRole = async (data: CommentOnRole) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.COMMENT_ON_ROLE}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting comment on role:", error);
    throw error;
  }
};

//reviewbyrole
interface ReviewOnRole {
  commentOnRolestrength: string;
  commentOnRoleAreaForImprove: string;
}

interface ReviewOnRolePayload {
  empId: string;
  reviewOnRole: ReviewOnRole[];
}

export const postReviewOnRole = async (data: ReviewOnRolePayload) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.COMMENT_ON_REVIEW_ROLE}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting comment on role by RO:", error);
    throw error;
  }
};

//reporting officer
interface CommentOnRoleByRo {
  commentOnRolestrengthRo: string;
  commentOnRoleAreaForImproveRo: string;
}

interface CommentOnRolePayloadByRo {
  empId: string;
  commentOnRoleByRo: CommentOnRoleByRo[];
}

export const postCommentOnRoleByRo = async (data: CommentOnRolePayloadByRo) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.COMMENT_ON_ROLE_BY_RO}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting comment on role by RO:", error);
    throw error;
  }
};

//reviewbyhod
interface CommentOnRoleByHod {
  commentOnRolestrengthHod: string;
  commentOnRoleAreaForImproveHod: string;
}

interface CommentOnRolePayloadByHod {
  empId: string;
  commentOnRoleByHod: CommentOnRoleByHod[];
}

export const postCommentOnRoleByHod = async (
  data: CommentOnRolePayloadByHod
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.COMMENT_ON_ROLE_BY_HOD}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting comment on role by HOD:", error);
    throw error;
  }
};

// ASSESMENT NY ROLE MANAGER
export interface AssessmentByRo {
  assessmentByRo: string;
  assessmentByRoAnswer: string;
  assessmentByRoDetails: string;
}

export interface AssessmentByRoPayload {
  empId: string;
  assessmentByRo: AssessmentByRo[];
}

export const postAssessmentByRo = async (data: AssessmentByRoPayload) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSESSMENT_BY_RO}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting assessment by RO:", error);
    throw error;
  }
};

// ASSESMENT NY HOD
export interface AssessmentByHod {
  overallParameterByHod: string;
  availablePointByHod: string;
  pointScoredByHod: string;
  roComment: string;
}

export interface AssessmentByHodPayload {
  empId: string;
  assessmentByHod: AssessmentByHod[];
}

export const postAssessmentByHod = async (data: AssessmentByHodPayload) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSESSMENT_BY_HOD}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting assessment by HOD:", error);
    throw error;
  }
};

//performance approval
export interface PerformanceApproval {
  userRole: string;
  userName: string;
  userSignature: string;
  userDate: string;
}

export interface PerformanceApprovalPayload {
  empId: string;
  performanceApprovals: PerformanceApproval[];
}

export const postPerformanceApproval = async (
  data: PerformanceApprovalPayload
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE_APPROVAL}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting performance approval:", error);
    throw error;
  }
};

//Personal goals
export interface PersonalGoals {
  personalGoalsAchievedLastYear: string;
  personalGoalsSetCurrentYear: string;
}

export interface PersonalGoalsPayload {
  empId: string;
  personalGoals: PersonalGoals[];
}

export const postPersonalGoals = async (data: PersonalGoalsPayload) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERSONAL_GOALS}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting personal goals:", error);
    throw error;
  }
};

//Pertaining Updates
export interface PersonalUpdates {
  personalUpdateslastYear: string;
  personalUpdateslastYearStatus: string;
  personalUpdateslastYearDetails: string;
  personalUpdatesCurrentYear: string;
  personalUpdatesCurrentYearStatus: string;
  personalUpdatesCurrentYearDetails: string;
}

export interface PersonalUpdatesPayload {
  empId: string;
  personalUpdates: PersonalUpdates[];
}

export const postPersonalUpdates = async (data: PersonalUpdatesPayload) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERSONAL_UPDATES}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting personal updates:", error);
    throw error;
  }
};

//training

export interface PerformanceTraining {
  performanceTrainingBySelf: string;
  performanceTrainingByRo: string;
  performanceTrainingByHod: string;
}

export interface PerformanceTrainingPayload {
  empId: string;
  performanceTraining: PerformanceTraining[];
}

export const postPerformanceTraining = async (
  data: PerformanceTrainingPayload
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE_TRAINING}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting performance training:", error);
    throw error;
  }
};

//other
export interface PerformanceOthers {
  otherCommentsBySelf: string;
  otherCommentsByRo: string;
  otherCommentsByHod: string;
}

export interface PerformanceOthersPayload {
  empId: string;
  performanceOthers: PerformanceOthers[];
}

export const postPerformanceOthers = async (data: PerformanceOthersPayload) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE_OTHERS}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting performance others:", error);
    throw error;
  }
};

//professional goals
export interface ProfessionalGoals {
  professionalGoalsAcheiveLastYearBySelf: string;
  professionalGoalsAcheiveLastYearByRo: string;
  professionalGoalsAcheiveLastYearByHod: string;
  professionalGoalsAcheiveForthComingYearBySelf: string;
  professionalGoalsAcheiveForthComingYearByRo: string;
  professionalGoalsAcheiveForthComingYearByHod: string;
}

export interface ProfessionalGoalsPayload {
  empId: string;
  professionalGoals: ProfessionalGoals[];
}

export const postProfessionalGoals = async (data: ProfessionalGoalsPayload) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PROFESSIONAL_GOALS}`;
    const response = await base.post(apiUrl, data);
    return response.data;
  } catch (error) {
    console.error("Error posting professional goals:", error);
    throw error;
  }
};

//get preformance review by employee id
export const getPerformanceReviewByEmployeeId = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERFORMANCE_REVIEW_BY_EMPLOYEE_ID}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching performance review by employee id:", error);
    throw error;
  }
};

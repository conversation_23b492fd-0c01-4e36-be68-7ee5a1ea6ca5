const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";


//Get all jobs with pagination
export const getAllJobs = async (
    limit?: number,
    page?: number,
    order?: string,
    startDate?: string,
    endDate?: string,
    isActive?: string,
    search?: string
  ) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.JOB}`;
  
      // Construct query parameters
      const queryParams = new URLSearchParams();
  
      // Only add pagination parameters if they are provided
      if (limit) queryParams.append("limit", limit.toString());
      if (page) queryParams.append("page", page.toString());
  
      if (order) queryParams.append("order", order);
      if (startDate) queryParams.append("startDate", startDate);
      if (endDate) queryParams.append("endDate", endDate);
      if (isActive) queryParams.append("isActive", isActive);
      if (search) queryParams.append("search", search);
  
      // Construct the full URL
      const fullUrl = queryParams.toString()
        ? `${apiUrl}?${queryParams.toString()}`
        : apiUrl;
  
      console.log("Fetching departments with URL:", fullUrl);
  
      const response = await base.get(fullUrl);
  
      return response.data;
    } catch (error) {
      console.error("Error fetching departments:", error);
      throw error;
    }
  };

  //Get all jobs without pagination
  export const getAllJob = async (
    order?: string,
    startDate?: string,
    endDate?: string,
    isActive?: string
  ) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.JOB}`;
  
      // Construct query parameters
      const queryParams = new URLSearchParams();
  
      if (order) queryParams.append("order", order);
      if (startDate) queryParams.append("startDate", startDate);
      if (endDate) queryParams.append("endDate", endDate);
      if (isActive) queryParams.append("isActive", isActive);
  
      // Construct the full URL
      const fullUrl = queryParams.toString()
        ? `${apiUrl}?${queryParams.toString()}`
        : apiUrl;
  
      const response = await base.get(fullUrl);
  
      return response.data;
    } catch (error) {
      console.error("Error fetching jobs:", error);
      throw error;
    }
  };

  //get all jobs
  export const getAllJobWithoutPagination = async () => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.JOB}`;
      const response = await base.get(apiUrl);
      return response.data;
    } catch (error) {
      console.error("Error fetching jobs:", error);
      throw error;
    }
  };


  //delete job
  export const deleteJob = async (id: string) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.JOB}/${id}`;
      const response = await base.delete(apiUrl);
      return response.data;
    } catch (error) {
      console.error("Error deleting job:", error);
      throw error;
    }
  }

  //Add job
  export const addJob = async (data: any) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.JOB}`;
      const response = await base.post(apiUrl, data);
      return response.data;
    } catch (error) {
      console.error("Error adding job:", error);
      throw error;
    }
  }
  //Get job by id
  export const getJobById = async (id: string) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.JOB}/${id}`;
      const response = await base.get(apiUrl);
      return response.data;
    } catch (error) {
      console.error("Error fetching job by ID:", error);
      throw error;
    }
  }
  //Update job
  export const updateJob = async (id: string, data: any) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.JOB}/${id}`;
      const response = await base.put(apiUrl, data);
      return response.data;
    } catch (error) {
      console.error("Error updating job:", error);
      throw error;
    }
  }
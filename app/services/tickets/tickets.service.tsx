const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";


//get all tickets
export const getAllTickets = async (
    limit?: number,
    page?: number,
    order?: string,
    startDate?: string,
    endDate?: string,
    isActive?: string,
    priority?: string,
    status?: string,
  ) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS}`;
  
      // Construct query parameters
      const queryParams = new URLSearchParams();
  
      // Only add pagination parameters if they are provided
      if (limit) queryParams.append("limit", limit.toString());
      if (page) queryParams.append("page", page.toString());
  
      if (order) queryParams.append("order", order);
      if (startDate) queryParams.append("startDate", startDate);
      if (endDate) queryParams.append("endDate", endDate);
      if (isActive) queryParams.append("isActive", isActive);
      if (priority) queryParams.append("priority", priority);
      if (status) queryParams.append("status", status);
  
      // Construct the full URL
      const fullUrl = queryParams.toString()
        ? `${apiUrl}?${queryParams.toString()}`
        : apiUrl;
  
      console.log("Fetching Tickets with URL:", fullUrl);
  
      const response = await base.get(fullUrl);
  
      return response.data;
    } catch (error) {
      console.error("Error fetching departments:", error);
      throw error;
    }
  };

// delete ticket
export const deleteTicket = async (ticketId: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS}/${ticketId}`;
    const response = await base.delete(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error deleting ticket:", error);
    toast.error(error instanceof Error ? error.message : "An unexpected error occurred");
    throw error;
  }
}

//Add tickets
export const addTicket = async (ticket: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS}`;
    const response = await base.post(apiUrl, ticket);
    return response.data;
  } catch (error) {
    console.error("Error adding ticket:", error);
    throw error;
  }
}

//Update ticket
export const updateTicket = async (ticketId: string, ticket: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS}/${ticketId}`;
    const response = await base.put(apiUrl, ticket);
    return response.data;
  } catch (error) {
    console.error("Error updating ticket:", error);
    toast.error(error instanceof Error ? error.message : "An unexpected error occurred");
    throw error;
  }
}
//Get ticket by id
export const getTicketById = async (ticketId: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS}/${ticketId}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching ticket by ID:", error);
    toast.error(error instanceof Error ? error.message : "An unexpected error occurred");
    throw error;
  }
}


// add comment
export const addCommentToTicket = async (ticketId: string, comment: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS}/${ticketId}/comment`;
    const response = await base.post(apiUrl, comment);
    return response.data;
  } catch (error) {
    console.error("Error adding comment to ticket:", error);
    throw error;
  }
}


// get Ticket Category
export const getAllTicketsCategory = async (
    limit?: number,
    page?: number,
    order?: string,
    startDate?: string,
    endDate?: string,
    isActive?: string,
    search?: string,
  ) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS_CATEGORY}`;
  
      // Construct query parameters
      const queryParams = new URLSearchParams();
  
      // Only add pagination parameters if they are provided
      if (limit) queryParams.append("limit", limit.toString());
      if (page) queryParams.append("page", page.toString());
  
      if (order) queryParams.append("order", order);
      if (startDate) queryParams.append("startDate", startDate);
      if (endDate) queryParams.append("endDate", endDate);
      if (isActive) queryParams.append("isActive", isActive);
      if (search) queryParams.append("search", search);
  
      // Construct the full URL
      const fullUrl = queryParams.toString()
        ? `${apiUrl}?${queryParams.toString()}`
        : apiUrl;
  
      console.log("Fetching Tickets with URL:", fullUrl);
  
      const response = await base.get(fullUrl);
  
      return response.data;
    } catch (error) {
      console.error("Error fetching departments:", error);
      throw error;
    }
  };

// get Ticket Category by id
export const getTicketCategoryById = async (ticketCategoryId: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS_CATEGORY}/${ticketCategoryId}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching ticket category by ID:", error);
    toast.error(error instanceof Error ? error.message : "An unexpected error occurred");
    throw error;
  }
}

// add Ticket Category
export const addTicketCategory = async (ticketCategory: any) => { 
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS_CATEGORY}`;
    const response = await base.post(apiUrl, ticketCategory);
    return response.data;
  } catch (error) {
    console.error("Error adding ticket category:", error);
    throw error;
  }
}
// update Ticket Category
export const updateTicketCategory = async (ticketCategoryId: string, ticketCategory: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS_CATEGORY}/${ticketCategoryId}`;
    const response = await base.put(apiUrl, ticketCategory);
    return response.data;
  } catch (error) {
    console.error("Error updating ticket category:", error);
    toast.error(error instanceof Error ? error.message : "An unexpected error occurred");
    throw error;
  }
}
// delete Ticket Category
export const deleteTicketCategory = async (ticketCategoryId: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS_CATEGORY}/${ticketCategoryId}`;
    const response = await base.delete(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error deleting ticket category:", error);
    toast.error(error instanceof Error ? error.message : "An unexpected error occurred");
    throw error;
  }
}

//get all ticket categories
export const getAllTicketCategories = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS_CATEGORY}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching all ticket categories:", error);
    throw error;
  }
}

export const getAllCategoryTicketsCount = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TICKETS_CATEGORY_COUNT}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching all category tickets count:", error);
    toast.error(error instanceof Error ? error.message : "An unexpected error occurred");
    throw error;
  }
}
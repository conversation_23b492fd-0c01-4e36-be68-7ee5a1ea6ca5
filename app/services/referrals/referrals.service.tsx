const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

export const getAllReferrals = async (
    limit?: number,
    page?: number,
    order?: string,
    startDate?: string,
    endDate?: string,
    isActive?: string,
    search?: string
  ) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.REFERRAL}`;
  
      // Construct query parameters
      const queryParams = new URLSearchParams();
  
      // Only add pagination parameters if they are provided
      if (limit) queryParams.append("limit", limit.toString());
      if (page) queryParams.append("page", page.toString());
  
      if (order) queryParams.append("order", order);
      if (startDate) queryParams.append("startDate", startDate);
      if (endDate) queryParams.append("endDate", endDate);
      if (isActive) queryParams.append("isActive", isActive);
      if (search) queryParams.append("search", search);
  
      // Construct the full URL
      const fullUrl = queryParams.toString()
        ? `${apiUrl}?${queryParams.toString()}`
        : apiUrl;
  
      console.log("Fetching departments with URL:", fullUrl);
  
      const response = await base.get(fullUrl);
  
      return response.data;
    } catch (error) {
      console.error("Error fetching departments:", error);
      throw error;
    }
  };

  //post referral
  export const addReferral = async (data: any) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.REFERRAL}`;
      const response = await base.post(apiUrl, data);
      return response.data;
    } catch (error) {
      console.error("Error creating referral:", error);
      throw error;
    }
  };

  //get referral by id
  export const getReferralById = async (id: string) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.REFERRAL}/${id}`;
      const response = await base.get(apiUrl);
      return response.data;
    } catch (error) {
      console.error("Error fetching referral by ID:", error);
      throw error;
    }
  };

  //update referral
  export const updateReferral = async (id: string, data: any) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.REFERRAL}/${id}`;
      const response = await base.patch(apiUrl, data);
      return response.data;
    } catch (error) {
      console.error("Error updating referral:", error);
      toast.error("Error updating referral");
      throw error;
    }
  };
  //delete referral
  export const deleteReferral = async (id: string) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.REFERRAL}/${id}`;
      const response = await base.delete(apiUrl);
      return response.data;
    } catch (error) {
      console.error("Error deleting referral:", error);
      toast.error("Error deleting referral");
      throw error;
    }
  };

  //Status change
  export const changeReferralStatus = async (id: string, data: any) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.REFERRAL}/${id}/status`;
      const response = await base.patch(apiUrl, data);
      return response.data;
    } catch (error) {
      console.error("Error changing referral status:", error);
      throw error;
    }
  };
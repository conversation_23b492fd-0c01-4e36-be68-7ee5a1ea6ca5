// import axios from "axios";
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

// Fetch Holiday
export const getHolidays = async () => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.HOLIDAY}`;
        console.log(apiUrl);
        const response = await base.get(apiUrl);
        return response.data;
    } catch (error) {
        console.error("Error fetching holiday:", error);
        throw error;
    }
};

interface HolidayPayload {
    title: string;
    date: string;
    description: string;
    isActive: boolean;
}

// post a new department
export const postHoliday = async (payload: HolidayPayload) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.HOLIDAY}`;
        console.log("POST Request to:", apiUrl);

        const response = await base.post(apiUrl, payload);

        console.log("Holiday Created:", response.data);
        toast.success(response.data.message)
        return response.data;
    } catch (error) {
        console.error("Error posting holiday:", error);
        throw error;
    }
};

// edit and update holiday
export const editHoliday = async (
    holidayId: string,
    title?: string,
    date?: string,
    description?: string,
    status?: boolean // Ensure status is a boolean
) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.HOLIDAY}/${holidayId}`;
        console.log("PUT Request to:", apiUrl);

        const payload: { title?: string; date?: string; description?: string; isActive?: boolean } = { title, date, description };
        if (status !== undefined) {
            payload.isActive = status; // Ensure boolean value
        }

        const response = await base.put(apiUrl, payload);

        // Log full response for debugging
        console.log("API Response:", response.data);

        // Extract `updatedHoliday` instead of `holiday`
        if (!response?.data?.updatedHoliday) {
            throw new Error(`Invalid API response: Expected 'updatedHoliday', got ${JSON.stringify(response.data)}`);
        }

        console.log("Holiday Updated Successfully:", response.data.updatedHoliday);
        toast.success(response.data.message);
        return response.data.updatedHoliday; // Return updated holiday data
    } catch (error) {
        console.error("Error editing holiday:", error);
        throw error;
    }
};









// delete a holiday
export const deleteHoliday = async (holidayId: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.HOLIDAY}/${holidayId}`;
        console.log("DELETE Request to:", apiUrl);

        const response = await base.delete(apiUrl);

        console.log("Holiday Deleted:", response.data);
        toast.success(response.data.message);       
        return response.data;
    } catch (error) {
        console.error("Error deleting holiday:", error);
        throw error;
    }
};

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

export const getTrainers = async (
  limit: number,
  page: number,
  departmentName: string,
  order: string,
  startDate?: string,
  endDate?: string,
  isActive?: string,
  search?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINERS}`;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      departmentName: departmentName === "Department" ? "" : departmentName,
      order: order,
      search: search || "",
    });

    if (startDate) {
      queryParams.append("startDate", startDate);
    }
    if (endDate) {
      queryParams.append("endDate", endDate);
    }
    if (isActive) {
      queryParams.append("isActive", isActive);
    }
    if (search) {
      queryParams.append("search", search);
    }

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    console.log("Fetching department with URL:", fullUrl);

    const response = await base.get(fullUrl);
    
    // Sort trainers alphabetically by firstName
   /*  if (response.data && response.data.trainers && response.data.trainers.results) {
      response.data.trainers.results = [...response.data.trainers.results].sort(
        (a, b) => a.firstName.localeCompare(b.firstName)
      );
    } */
    
    return response.data;
  } catch (error) {
    console.error("Error fetching department:", error);
    throw error;
  }
};

interface trainerBody {
  firstName: string;
  lastName: string;
  role: string;
  phone: string;
  email: string;
  description: string;
  isActive: boolean;
}

//add trainer
export const addTrainer = async (body: trainerBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINERS}`;
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error adding trainer:", error);
    throw error;
  }
};

export const updateTrainer = async (id: string, body: trainerBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINERS}/${id}`;
    const response = await base.put(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error updating trainer:", error);
    throw error;
  }
};

export const deleteTrainer = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINERS}/${id}`;
    const response = await base.delete(apiUrl);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error deleting trainer:", error);
    throw error;
  }
};

//get trainer by id
export const getTrainerById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINERS}/${id}`;
    const response = await base.get(apiUrl);
    //toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error getting trainer by id:", error);
    throw error;
  }
};

//training type
export const getTrainingType = async (
  limit: number,
  page: number,
  departmentName: string,
  order: string,
  startDate?: string,
  endDate?: string,
  isActive?: string,
  search?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINING_TYPE}`;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      departmentName: departmentName === "Department" ? "" : departmentName,
      order: order,
    });

    if (startDate) {
      queryParams.append("startDate", startDate);
    }
    if (endDate) {
      queryParams.append("endDate", endDate);
    }
    if (isActive) {
      queryParams.append("isActive", isActive);
    }
    if (search) {
      queryParams.append("search", search);
    }

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    console.log("Fetching department with URL:", fullUrl);

    const response = await base.get(fullUrl);
    
    // Sort training types alphabetically by type
    /* if (response.data && response.data.trainingTypes && response.data.trainingTypes.results) {
      response.data.trainingTypes.results = [...response.data.trainingTypes.results].sort(
        (a, b) => a.type.localeCompare(b.type)
      );
    } */
    
    return response.data;
  } catch (error) {
    console.error("Error fetching department:", error);
    throw error;
  }
};

// reaning crud operatopn service

interface trainingTypeBody {
  type: string;
  description: string;
  isActive: boolean;
}

export const addTrainingType = async (body: trainingTypeBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINING_TYPE}`;
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error adding training type:", error);
    throw error;
  }
};

export const updateTrainingType = async (
  id: string,
  body: trainingTypeBody
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINING_TYPE}/${id}`;
    const response = await base.put(apiUrl, body);
    // toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error updating trainer:", error);
    throw error;
  }
};

export const deleteTrainingType = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINING_TYPE}/${id}`;
    const response = await base.delete(apiUrl);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error deleting trainer:", error);
    throw error;
  }
};

//get trainer by id
export const getTrainingTypeById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TRAINING_TYPE}/${id}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error getting trainer by id:", error);
    throw error;
  }
};

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

export const addTimesheet = async (body: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TIME_SHEET}`;
    const response = await base.post(apiUrl, body);
    return response.data;
  } catch (error) {
    console.error("Error adding ticket:", error);
    throw error;
  }
}

//update timesheet
export const updateTimesheet = async (id: string, body: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TIME_SHEET}/${id}`;
    const response = await base.put(apiUrl, body);
    return response.data;
  } catch (error) {
    console.error("Error updating ticket:", error);
    toast.error(error instanceof Error ? error.message : "An unexpected error occurred");
    throw error;
  }
}

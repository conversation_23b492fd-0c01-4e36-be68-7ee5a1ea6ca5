const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";
import {
  TimesheetEntry,
  TimesheetCreateRequest,
  TimesheetUpdateRequest,
  TimesheetResponse
} from "@/app/types/timesheet";

// Create a new timesheet entry
export const createTimesheet = async (body: TimesheetCreateRequest): Promise<TimesheetResponse> => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TIME_SHEET}`;
    const response = await base.post(apiUrl, body);
    toast.success("Timesheet entry created successfully");
    return response.data;
  } catch (error) {
    console.error("Error creating timesheet:", error);
    toast.error("Failed to create timesheet entry");
    throw error;
  }
};

// Update an existing timesheet entry
export const updateTimesheet = async (id: string, body: TimesheetUpdateRequest): Promise<TimesheetResponse> => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TIME_SHEET}/${id}`;
    const response = await base.put(apiUrl, body);
    toast.success("Timesheet entry updated successfully");
    return response.data;
  } catch (error) {
    console.error("Error updating timesheet:", error);
    toast.error("Failed to update timesheet entry");
    throw error;
  }
};

// Get timesheet entries by employee ID
export const getTimesheetsByEmployeeId = async (empId: string): Promise<TimesheetEntry[]> => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TIME_SHEET}?empId=${empId}`;
    const response = await base.get(apiUrl);
    return response.data.data || [];
  } catch (error) {
    console.error("Error fetching timesheets:", error);
    toast.error("Failed to fetch timesheet entries");
    throw error;
  }
};

// Get timesheet entries by employee ID and date range
export const getTimesheetsByDateRange = async (
  empId: string,
  startDate: string,
  endDate: string
): Promise<TimesheetEntry[]> => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TIME_SHEET}?empId=${empId}&startDate=${startDate}&endDate=${endDate}`;
    const response = await base.get(apiUrl);
    return response.data.data || [];
  } catch (error) {
    console.error("Error fetching timesheets by date range:", error);
    toast.error("Failed to fetch timesheet entries");
    throw error;
  }
};

// Delete a timesheet entry
export const deleteTimesheet = async (id: string): Promise<void> => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TIME_SHEET}/${id}`;
    await base.delete(apiUrl);
    toast.success("Timesheet entry deleted successfully");
  } catch (error) {
    console.error("Error deleting timesheet:", error);
    toast.error("Failed to delete timesheet entry");
    throw error;
  }
};

// Get a single timesheet entry by ID
export const getTimesheetById = async (id: string): Promise<TimesheetEntry> => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TIME_SHEET}/${id}`;
    const response = await base.get(apiUrl);
    return response.data.data;
  } catch (error) {
    console.error("Error fetching timesheet:", error);
    toast.error("Failed to fetch timesheet entry");
    throw error;
  }
};

// Legacy function for backward compatibility
export const addTimesheet = createTimesheet;

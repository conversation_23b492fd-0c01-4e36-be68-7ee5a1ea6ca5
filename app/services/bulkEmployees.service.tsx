import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { processCSVFile, processExcelFile, generateTemplateFile } from "@/app/utils/fileProcessing";
import { toast } from "react-toastify";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

interface BulkUploadResponse {
  success: boolean;
  message: string;
  data?: any;
  errors?: any[];
}

/**
 * Upload and process a bulk employee file (CSV or Excel)
 * @param file The file to upload and process
 * @returns Response with success status and message
 */
export const uploadBulkEmployees = async (file: File): Promise<BulkUploadResponse> => {
  try {
    // Process the file based on its type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    let employeesData: any[] = [];

    if (fileExtension === 'csv') {
      employeesData = await processCSVFile(file);
    } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
      employeesData = await processExcelFile(file);
    } else {
      return {
        success: false,
        message: 'Unsupported file format. Please upload a CSV or Excel file.'
      };
    }

    // Validate the data
    if (!employeesData || employeesData.length === 0) {
      return {
        success: false,
        message: 'No valid data found in the file. Please check the file format.'
      };
    }

    // Make API calls to add/update employees
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.USERS}/bulk`;
    
    // For now, we'll simulate the API call since the endpoint might not exist yet
    // In a real implementation, you would uncomment the following code:
    
    /*
    const response = await base.post(apiUrl, {
      employees: employeesData
    });
    
    return {
      success: response.data.success,
      message: response.data.message,
      data: response.data.data,
      errors: response.data.errors
    };
    */
    
    // Simulate API call for now
    console.log('Employees data to be sent to API:', employeesData);
    
    // Process each employee individually as a fallback if bulk endpoint doesn't exist
    const results = await Promise.all(
      employeesData.map(async (employee) => {
        try {
          const singleApiUrl = `${API_BASE_URL}${API_ENDPOINTS.USERS}`;
          const response = await base.post(singleApiUrl, employee);
          return {
            success: true,
            data: response.data
          };
        } catch (error: any) {
          console.error('Error adding employee:', error);
          return {
            success: false,
            error: error.response?.data?.message || 'Failed to add employee'
          };
        }
      })
    );
    
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    
    if (failureCount === 0) {
      return {
        success: true,
        message: `Successfully added ${successCount} employees.`
      };
    } else {
      return {
        success: successCount > 0,
        message: `Added ${successCount} employees. Failed to add ${failureCount} employees.`,
        errors: results.filter(r => !r.success).map(r => r.error)
      };
    }
  } catch (error) {
    console.error('Error processing bulk employees:', error);
    return {
      success: false,
      message: 'An error occurred while processing the file.'
    };
  }
};

/**
 * Download a template file for employee data
 */
export const downloadEmployeeTemplate = async (): Promise<void> => {
  try {
    // Generate the template file
    const templateBlob = await generateTemplateFile();
    
    // Create a download link and trigger the download
    const url = URL.createObjectURL(templateBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'employee_template.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading template:', error);
    toast.error('Failed to download template file');
    throw error;
  }
};

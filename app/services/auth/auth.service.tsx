const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

//signup section 
interface SignupBody {
  firstName: string, lastName: string, email: string, password: string, roles: string
}

export const addSignupSection = async (body: SignupBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.SIGNUP}`;

    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

interface LoginBody {
  email:string,
  password:string
}
  
  //login section 
  export const addLoginSection = async (body: LoginBody) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.LOGIN}`;
  
      const response = await base.post(apiUrl, body);
      return response.data;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  };


  //FORGET PASSWORD 
  interface ForgetPasswordBody {
    email: string;
  }
  export const addForgetPasswordSection = async (body: ForgetPasswordBody) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.FORGET_PASSWORD}`;
      const response = await base.post(apiUrl, body);
      return response.data;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  };


  //reset password
  interface ResetPasswordBody {
    token: string;
    newPassword: string;
  }
  
  export const resetPassword = async (body: ResetPasswordBody) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.RESET_PASSWORD}`;
      const response = await base.post(apiUrl, body);
      return response.data;
    } catch (error) {
      console.error("Error:", error);
      throw error;
    }
  };

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";

// Get Version
export const getVersion = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.VERSION}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching Version:", error);
    throw error;
  }
};

interface CompanyBody {
  companyName: string;
}

// Update Company
export const updateCompany = async (body: CompanyBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.COMPANY}`;
    console.log(apiUrl);
    const response = await base.post(apiUrl, body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Get Company
export const getCompany = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.COMPANY}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching Company:", error);
    throw error;
  }
};

interface NoticePeriodBody {
  noticePeriodDays: number;
}

// update NoticePeriod Settings
export const updateNoticePeriodSettings = async (body: NoticePeriodBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.NOTICE_PERIOD}`;
    console.log(apiUrl);
    const response = await base.put(apiUrl, body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Get NoticePeriod Settings
export const getNoticePeriod = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.NOTICE_PERIOD}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching NoticePeriod:", error);
    throw error;
  }
};

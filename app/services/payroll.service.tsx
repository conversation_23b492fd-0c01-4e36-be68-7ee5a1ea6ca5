const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

export const getPayrollItem = async (isDefault?: boolean) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLLITEM}?isDefault=${isDefault}`;
    // console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    // console.error("Error fetching Payroll Items:", error);
    throw error;
  }
};

export const getPayrollWithPagination = async (
  limit: number = 10,
  page: number = 1,
  order: string = "desc",
  startDate?: string,
  endDate?: string,
  isActive?: string,
  type?: string // "Additions", "Overtime", or "Deductions"
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLLITEM}`;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      order: order,
    });

    if (startDate) queryParams.append("startDate", startDate);
    if (endDate) queryParams.append("endDate", endDate);
    if (isActive) queryParams.append("isActive", isActive);
    if (type) queryParams.append("type", type);

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    // console.log("Fetching payroll items with URL:", fullUrl);

    const response = await base.get(fullUrl);
    return response.data;
  } catch (error) {
    // console.error("Error fetching payroll items:", error);
    throw error;
  }
};

// Add PayrollItems
export const addPayrollItem = async (body: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLLITEM}`;
    // console.log(apiUrl);
    const response = await base.post(apiUrl, body);
    // console.log("Payroll Item added successfully", response.data.message);
    // toast.success(response.data.message);
    return response.data;
  } catch (error) {
    // console.error("Error adding Payroll Item:", error);
    throw error;
  }
};

//get PayrollItem by id
export const getPayrollItemById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLLITEM}/${id}`;
    // console.log(apiUrl);
    const response = await base.get(apiUrl);
    // toast.success(response.data.message)
    return response.data;
  } catch (error) {
    //  console.error("Error fetching Payroll Item by id:", error);
    throw error;
  }
};

// Update Payroll Item
export const updatePayrollItem = async (id: string, body: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLLITEM}/${id}`;
    const response = await base.put(apiUrl, body);
    // console.log("PayrollItem updated successfully");
    // toast.success(response.data.message);
  } catch (error) {
    // console.error("Error updating PayrollItem:", error);
    throw error;
  }
};

// delete PayrollItem with id
export const deletePayrollItem = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLLITEM}/${id}`;
    //console.log(apiUrl);
    const response = await base.delete(apiUrl);
    //console.log("Payroll Item deleted successfully");
    toast.success(response.data.message);
  } catch (error) {
    // console.error("Error deleting Payroll Item:", error);
    throw error;
  }
};

//Add Payroll
export const addPayroll = async (body: FormData) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLL}`;
    const payload = {
      empId: body.get("empId"),
      netSalary: Number(body.get("netSalary")),
      isActive: body.get("isActive") === "true",
      isDeleted: body.get("isDeleted") === "false",
      earnings: Array.from(body.entries())
        .filter(([key]) => key.startsWith("earnings["))
        .reduce(
          (acc, [key]) => {
            const match = key.match(/earnings\[(\d+)\]\.name/);
            if (match) {
              const index = match[1];
              acc[body.get(`earnings[${index}].name`) as string] = Number(
                body.get(`earnings[${index}].amount`)
              );
            }
            return acc;
          },
          {} as { [key: string]: number }
        ),
      deductions: Array.from(body.entries())
        .filter(([key]) => key.startsWith("deductions["))
        .reduce(
          (acc, [key]) => {
            const match = key.match(/deductions\[(\d+)\]\.name/);
            if (match) {
              const index = match[1];
              acc[body.get(`deductions[${index}].name`) as string] = Number(
                body.get(`deductions[${index}].amount`)
              );
            }
            return acc;
          },
          {} as { [key: string]: number }
        ),
      additions: Array.from(body.entries())
        .filter(([key]) => key.startsWith("additions["))
        .reduce(
          (acc, [key]) => {
            const match = key.match(/additions\[(\d+)\]\.name/);
            if (match) {
              const index = match[1];
              acc[body.get(`additions[${index}].name`) as string] = Number(
                body.get(`additions[${index}].amount`)
              );
            }
            return acc;
          },
          {} as { [key: string]: number }
        ),
      overtime: Array.from(body.entries())
        .filter(([key]) => key.startsWith("overtime["))
        .reduce(
          (acc, [key]) => {
            const match = key.match(/overtime\[(\d+)\]\.name/);
            if (match) {
              const index = match[1];
              acc[body.get(`overtime[${index}].name`) as string] = Number(
                body.get(`overtime[${index}].amount`)
              );
            }
            return acc;
          },
          {} as { [key: string]: number }
        ),
    };
    const response = await base.post(apiUrl, payload);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error adding payroll:", error);
    throw error;
  }
};

// get payrole
export const getPayRoll = async (
  limit: number = 10,
  page: number = 1,
  order: string = "desc",
  startDate?: string,
  search?: string,
  endDate?: string,
  isActive?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLL}`;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      order: order,
      search: search || "",
    });

    if (startDate) queryParams.append("startDate", startDate);
    if (endDate) queryParams.append("endDate", endDate);
    if (isActive) queryParams.append("isActive", isActive);
    if (search) queryParams.append("search", search);

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    // console.log("Fetching payroll items with URL:", fullUrl);

    const response = await base.get(fullUrl);
    return response.data;
  } catch (error) {
    // console.error("Error fetching payroll items:", error);
    throw error;
  }
};

//Delete payrole
export const deletePayRoll = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLL}/${id}`;
    console.log(apiUrl);
    const response = await base.delete(apiUrl);
    console.log("Payroll Item deleted successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error deleting Payroll Item:", error);
    throw error;
  }
};

//get Payroll by id
export const getPayrollById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLL}/${id}`;
    // console.log(apiUrl);
    const response = await base.get(apiUrl);
    // toast.success(response.data.message)
    return response.data;
  } catch (error) {
    // console.error("Error fetching Payroll Item by id:", error);
    throw error;
  }
};
// Update Payroll
export const updatePayroll = async (id: string, body: FormData) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLL}/${id}`;
    const payload = {
      empId: body.get("empId"),
      netSalary: Number(body.get("netSalary")),
      isActive: body.get("isActive") === "true",
      isDeleted: body.get("isDeleted") === "false",
      earnings: Array.from(body.entries())
        .filter(([key]) => key.startsWith("earnings["))
        .reduce(
          (acc, [key]) => {
            const match = key.match(/earnings\[(\d+)\]\.name/);
            if (match) {
              const index = match[1];
              acc[body.get(`earnings[${index}].name`) as string] = Number(
                body.get(`earnings[${index}].amount`)
              );
            }
            return acc;
          },
          {} as { [key: string]: number }
        ),
      deductions: Array.from(body.entries())
        .filter(([key]) => key.startsWith("deductions["))
        .reduce(
          (acc, [key]) => {
            const match = key.match(/deductions\[(\d+)\]\.name/);
            if (match) {
              const index = match[1];
              acc[body.get(`deductions[${index}].name`) as string] = Number(
                body.get(`deductions[${index}].amount`)
              );
            }
            return acc;
          },
          {} as { [key: string]: number }
        ),
      additions: Array.from(body.entries())
        .filter(([key]) => key.startsWith("additions["))
        .reduce(
          (acc, [key]) => {
            const match = key.match(/additions\[(\d+)\]\.name/);
            if (match) {
              const index = match[1];
              acc[body.get(`additions[${index}].name`) as string] = Number(
                body.get(`additions[${index}].amount`)
              );
            }
            return acc;
          },
          {} as { [key: string]: number }
        ),
      overtime: Array.from(body.entries())
        .filter(([key]) => key.startsWith("overtime["))
        .reduce(
          (acc, [key]) => {
            const match = key.match(/overtime\[(\d+)\]\.name/);
            if (match) {
              const index = match[1];
              acc[body.get(`overtime[${index}].name`) as string] = Number(
                body.get(`overtime[${index}].amount`)
              );
            }
            return acc;
          },
          {} as { [key: string]: number }
        ),
    };
    const response = await base.put(apiUrl, payload);
    return response.data;
  } catch (error: any) {
    console.error("Error updating Payroll:", error);

    throw error;
  }
};
// get PayrollItem for employee
export const getPayrollItemForEmployee = async (employeeId: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PAYROLL}/emp/${employeeId}`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    throw error;
  }
};

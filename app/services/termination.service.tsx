// import axios from "axios";
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

export const getAllTerminations = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TERMINATION}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching Termination:", error);
    throw error;
  }
};

// Fetch Terminations
export const getTermination = async (
  limit: number,
  page: number,
  departmentName: string,
  order: string,
  startDate?: string,
  endDate?: string,
  isActive?: string,
  search?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TERMINATION}`;

    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      departmentName: departmentName === "Department" ? "" : departmentName,
      order: order,
      search: search || "",
    });

    if (startDate) {
      queryParams.append("startDate", startDate);
    }
    if (endDate) {
      queryParams.append("endDate", endDate);
    }
    if (isActive) {
      queryParams.append("isActive", isActive);
    }
    if (search) {
      queryParams.append("search", search);
    }

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    console.log("Fetching terminations with URL:", fullUrl);

    const response = await base.get(fullUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching terminations:", error);
    throw error;
  }
};

// Add Terminations
interface terminationBody {
  terminationEmp?: string;
  type?: string;
  noticeDate?: string;
  reason?: string;
  terminationDate?: string;
}

export const addTermination = async (body: terminationBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TERMINATION}`;
    console.log(apiUrl);
    const response = await base.post(apiUrl, body);
    console.log("Termination added successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error adding termination:", error);
    throw error;
  }
};

// Update Terminations
export const updateTermination = async (id: string, body: terminationBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TERMINATION}/${id}`;
    
    // Ensure dates are in ISO format
    const payload = {
      ...body,
      noticeDate: body.noticeDate ? new Date(body.noticeDate).toISOString() : undefined,
      terminationDate: body.terminationDate ? new Date(body.terminationDate).toISOString() : undefined
    };
    
    console.log("Update termination API URL:", apiUrl);
    console.log("Update termination payload:", payload);
    
    const response = await base.put(apiUrl, payload);
    console.log("Termination update response:", response.data);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error updating termination:", error);
    throw error;
  }
};

// Get Termination by ID
export const getTerminationById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TERMINATION}/${id}`;
    console.log("Fetching termination by ID:", apiUrl);
    const response = await base.get(apiUrl);
    console.log("Termination data response:", response.data);
    return response.data; // Return the full response
  } catch (error) {
    console.error("Error fetching termination by ID:", error);
    throw error;
  }
};

// delete terminations with id
export const deleteTermination = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.TERMINATION}/${id}`;
    console.log(apiUrl);
    const response = await base.delete(apiUrl);
    console.log("Termination deleted successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error deleting termination:", error);
    throw error;
  }
};

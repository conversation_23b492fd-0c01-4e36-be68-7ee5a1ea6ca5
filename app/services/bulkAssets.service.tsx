import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { processCSVFile, processExcelFile, generateAssetTemplateFile } from "@/app/utils/assetFileProcessing";
import { toast } from "react-toastify";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

interface BulkUploadResponse {
  success: boolean;
  message: string;
  data?: any;
  errors?: any[];
}

/**
 * Upload and process a bulk asset file (CSV or Excel)
 * @param file The file to upload and process
 * @returns Response with success status and message
 */
export const uploadBulkAssets = async (file: File): Promise<BulkUploadResponse> => {
  try {
    // Process the file based on its type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    let assetsData: any[] = [];

    if (fileExtension === 'csv') {
      assetsData = await processCSVFile(file);
    } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
      assetsData = await processExcelFile(file);
    } else {
      return {
        success: false,
        message: 'Unsupported file format. Please upload a CSV or Excel file.'
      };
    }

    // Validate the data
    if (!assetsData || assetsData.length === 0) {
      return {
        success: false,
        message: 'No valid data found in the file. Please check the file format.'
      };
    }

    // Make API calls to add/update assets
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSET}/bulk`;
    
    // For now, we'll simulate the API call since the endpoint might not exist yet
    // In a real implementation, you would uncomment the following code:
    
    /*
    const response = await base.post(apiUrl, {
      assets: assetsData
    });
    
    return {
      success: response.data.success,
      message: response.data.message,
      data: response.data.data,
      errors: response.data.errors
    };
    */
    
    // Simulate API call for now
    console.log('Assets data to be sent to API:', assetsData);
    
    // Process each asset individually as a fallback if bulk endpoint doesn't exist
    const results = await Promise.all(
      assetsData.map(async (asset) => {
        try {
          const formData = new FormData();
          
          // Add all asset properties to the FormData
          Object.keys(asset).forEach(key => {
            if (key !== 'assetImage' && key !== 'serialNumber') {
              formData.append(key, asset[key]);
            }
          });
          
          // Handle serialNumber array
          if (asset.serialNumber && Array.isArray(asset.serialNumber)) {
            asset.serialNumber.forEach((serial: string, index: number) => {
              formData.append(`serialNumber[${index}]`, serial);
            });
          } else if (asset.serialNumber) {
            formData.append('serialNumber[0]', asset.serialNumber);
          }
          
          // Handle assetImage array if present
          if (asset.assetImage && Array.isArray(asset.assetImage)) {
            asset.assetImage.forEach((imageUrl: string, index: number) => {
              formData.append(`assetImage[${index}]`, imageUrl);
            });
          }
          
          const singleApiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSET}`;
          const response = await base.post(singleApiUrl, formData);
          return {
            success: true,
            data: response.data
          };
        } catch (error: any) {
          console.error('Error adding asset:', error);
          return {
            success: false,
            error: error.response?.data?.message || 'Failed to add asset'
          };
        }
      })
    );
    
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    
    if (failureCount === 0) {
      return {
        success: true,
        message: `Successfully added ${successCount} assets.`
      };
    } else {
      return {
        success: successCount > 0,
        message: `Added ${successCount} assets. Failed to add ${failureCount} assets.`,
        errors: results.filter(r => !r.success).map(r => r.error)
      };
    }
  } catch (error) {
    console.error('Error processing bulk assets:', error);
    return {
      success: false,
      message: 'An error occurred while processing the file.'
    };
  }
};

/**
 * Download a template file for asset data
 */
export const downloadAssetTemplate = async (): Promise<void> => {
  try {
    // Generate the template file
    const templateBlob = await generateAssetTemplateFile();
    
    // Create a download link and trigger the download
    const url = URL.createObjectURL(templateBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'asset_template.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading template:', error);
    toast.error('Failed to download template file');
    throw error;
  }
};

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";

export const getClients = async (
    limit: number = 10,
    page: number = 1,
    departmentName: string = "Department",
    order: string = "Sort By",
    startDate?: string,
    endDate?: string,
    isActive?: string
) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.CLIENTS}`;

        // Construct query parameters
        const queryParams = new URLSearchParams({
            limit: limit.toString(),
            page: page.toString(),
            departmentName: departmentName === "Department" ? "" : departmentName,
            order: order === "Sort By" ? "" : order,
        });

        if (startDate) queryParams.append("startDate", startDate);
        if (endDate) queryParams.append("endDate", endDate);
        if (isActive) queryParams.append("isActive", isActive);

        console.log("Fetching clients with params:", {
            startDate,
            endDate,
            url: `${apiUrl}?${queryParams.toString()}`,
        });

        const response = await base.get(`${apiUrl}?${queryParams.toString()}`);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching clients:", error);
        throw error;
    }
};

//get all clients
export const getAllClients = async (startDate?: string, endDate?: string, isActive?: boolean) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.CLIENTS}`;

        const queryParams = new URLSearchParams();
        if (startDate) queryParams.append("startDate", startDate);
        if (endDate) queryParams.append("endDate", endDate);
        if (isActive !== undefined) queryParams.append("isActive", isActive ? "true" : "false");

        const response = await base.get(`${apiUrl}?${queryParams.toString()}`);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching clients:", error);
        throw error;
    }
}


//post client
export const postClient = async (data: any) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.CLIENTS}`;
        const response = await base.post(apiUrl, data);
        return response.data;
    } catch (error: any) {
        console.error("Error posting client:", error);
        throw error;
    }
};
//get client by id
export const getClientById = async (id: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.CLIENTS}/${id}`;
        const response = await base.get(apiUrl);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching client by ID:", error);
        throw error;
    }
}
//update client
export const updateClient = async (id: string, data: any) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.CLIENTS}/${id}`;
        const response = await base.put(apiUrl, data);
        return response.data;
    } catch (error: any) {
        console.error("Error updating client:", error);
        throw error;
    }
}
//delete client
export const deleteClient = async (id: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.CLIENTS}/${id}`;
        const response = await base.delete(apiUrl);
        return response.data;
    }
    catch (error: any) {
        console.error("Error deleting client:", error);
        throw error;
    }
}
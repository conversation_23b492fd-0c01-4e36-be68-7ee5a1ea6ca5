const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";
import { AxiosError } from "axios";


export const getAttendance = async (
  limit: number,
  page: number,
  departmentName: string,
  order: string,
  startDate?: string,
  endDate?: string,
  isActive?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ATTENDANCE}`;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      departmentName: departmentName === "Department" ? "" : departmentName,
      order: order,
    });

    // Always add startDate and endDate if they exist
    if (startDate) {
      queryParams.append("startDate", startDate);
    }
    if (endDate) {
      queryParams.append("endDate", endDate);
    }
    if (isActive) {
      queryParams.append("isActive", isActive);
    }

    console.log('Fetching attendance with params:', {
      startDate,
      endDate,
      url: `${apiUrl}?${queryParams.toString()}`
    });

    const response = await base.get(`${apiUrl}?${queryParams.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching attendance:', error);
    throw error;
  }
};

interface AttendanceBody {
  empId?: string;
  date?: string;
  status?: string;
  checkIn?: string;
  checkOut?: string;
  break?: number;
  late?: number;
}

export const addAttendance = async (body: AttendanceBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ATTENDANCE}`;
    console.log(apiUrl);
    const response = await base.post(apiUrl, body);
    console.log("Attendance added successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error adding attendance:", error);
    throw error;
  }
};

export const updateAttendance = async (id: string, body: AttendanceBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ATTENDANCE}/${id}`;
    console.log(apiUrl);
    const response = await base.put(apiUrl, body);
    console.log("Attendance updated successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error updating attendance:", error);
    throw error;
  }
};

export const getAttendanceById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ATTENDANCE}/${id}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching attendance by id:", error);
    throw error;
  }
};

export const getAttendanceByEmpId = async (
  empId: string,
  limit?: number,
  page?: number,
  order?: string,
  startDate?: string,
  endDate?: string,
  isActive?: string
) => {
  try {
    const queryParams = new URLSearchParams();

    // Add empId to query params
    queryParams.append('empId', empId);

    if (limit) queryParams.append('limit', limit.toString());
    if (page) queryParams.append('page', page.toString());
    if (order) queryParams.append('order', order);
    if (startDate) queryParams.append('startDate', startDate);
    if (endDate) queryParams.append('endDate', endDate);
    if (isActive) queryParams.append('isActive', isActive);

    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ATTENDANCE}?${queryParams.toString()}`;
    console.log("Fetching attendance with URL:", apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error('Error fetching attendance by employee ID:', error);
    throw error;
  }
};

//Attendance Summary
export const getAttendanceByDate = async (startDate: string, endDate: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ATTENDANCE_SUMMARY}`;
    const queryParams = new URLSearchParams({
      startDate,
      endDate,
    });
    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    console.log("Fetching ATTENDANCE with URL:", fullUrl);
    const response = await base.get(fullUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching ATTENDANCE by date:", error);
    throw error;
  }
};

//Get yearly summary of attendance
export const getYearlyAttendanceSummary = async (empId: string, year: number) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ATTENDANCE}/yearly-summary/${empId}/${year}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching yearly attendance summary:", error);
    throw error;
  }
};

export const deleteAttendance = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ATTENDANCE}/${id}`;
    console.log(apiUrl);
    const respone = await base.delete(apiUrl);
    console.log("Attendance deleted successfully");
    toast.success(respone.data.message);
  } catch (error) {
    console.error("Error deleting attendance:", error);
    throw error;
  }
};
interface ErrorResponse {
  error?: {
    errors?: string | string[] | object; // Adjust based on your API's error format
  };
}
//punch in and out
export const punchIn = async (empId: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PUNCH_IN}/${empId}`;
    console.log(apiUrl);
    await base.patch(apiUrl);
    console.log("Punched in successfully");
  } catch (error) {
    console.error("Error punching in:", error);

    // Type the error as AxiosError with ErrorResponse
    const axiosError = error as AxiosError<ErrorResponse>;
    let errorMessage = "Failed to punch in. Please try again.";
    if (axiosError.response?.data?.error?.errors) {
      const errors = axiosError.response.data.error.errors;
      if (typeof errors === 'string') {
        errorMessage = errors;
      } else if (Array.isArray(errors)) {
        errorMessage = errors.join(', ');
      } else if (typeof errors === 'object') {
        errorMessage = JSON.stringify(errors);
      }
    }

    toast.error(errorMessage);
    throw error;
  }
};

//punch in and out
export const punchOut = async (empId: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PUNCH_OUT}/${empId}`;
    console.log(apiUrl);
    await base.patch(apiUrl);
    console.log("Punched out successfully");
  } catch (error) {
    console.error("Error punching out:", error);
    throw error;
  }
};

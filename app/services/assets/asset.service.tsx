const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";

// Post assets
export const addAsset = async (body: FormData) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSET}`;
        console.log("Posting to:", apiUrl);
        const response = await base.post(apiUrl, body);
        return response.data;
    } catch (error: any) {
        console.error("Error posting ASSET:", error);
        throw error;
    }
};

// Update asset
export const updateAsset = async (id: string, body: FormData) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSET}/${id}`;
        console.log("Updating at:", apiUrl);
        const response = await base.put(apiUrl, body);
        return response.data;
    } catch (error: any) {
        console.error("Error updating ASSET:", error);
        throw error;
    }
};

// Delete asset
export const deleteAsset = async (id: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSET}/${id}`;
        console.log("Deleting at:", apiUrl);
        const response = await base.delete(apiUrl);
        return response.data;
    } catch (error: any) {
        console.error("Error deleting ASSET:", error);
        throw error;
    }
};

// Get assets
export const getAssets = async (
    limit: number = 10,
    page: number = 1,
    departmentName: string = "Department",
    order: string = "Sort By",
    startDate?: string,
    endDate?: string,
    isActive?: string,
    search?: string
) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSET}`;

        // Construct query parameters
        const queryParams = new URLSearchParams({
            limit: limit.toString(),
            page: page.toString(),
            departmentName: departmentName === "Department" ? "" : departmentName,
            order: order === "Sort By" ? "" : order,
            search: search || "",
        });

        if (startDate) queryParams.append("startDate", startDate);
        if (endDate) queryParams.append("endDate", endDate);
        if (isActive) queryParams.append("isActive", isActive);
        if (search) queryParams.append("search", search);

        console.log("Fetching assets with params:", {
            startDate,
            endDate,
            url: `${apiUrl}?${queryParams.toString()}`,
        });

        const response = await base.get(`${apiUrl}?${queryParams.toString()}`);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching assets:", error);
        throw error;
    }
};

//get assests by id
export const getAssetById = async (id: string) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSET}/${id}`;
        console.log("Fetching asset by ID:", apiUrl);
        const response = await base.get(apiUrl);
        return response.data;
    } catch (error: any) {
        console.error("Error fetching asset by ID:", error);
        throw error;
    }
};


//assests assign
export const assignAsset = async (body: {
    empId: string;
    serialNumber: string;
    assignedDate: string;
}) => {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSET_ASSIGN}`;
        console.log("Assigning at:", apiUrl);
        const response = await base.post(apiUrl, body);
        return response.data;
    } catch (error: any) {
        console.error("Error assigning ASSET:", error);
        throw error;
    }
};

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";


export const getUserswithdepartmenetId = async (departmenetId: string)=> {
    try {
        const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.USERS}?departmentId=${departmenetId}`;
        console.log(apiUrl);
        const response = await base.get(apiUrl);
        return response.data;
    } catch (error) {
        console.error("Error fetching users:", error);
        throw error;
    }
};

export const getUser = async (
  limit: number,
  page: number,
  departmentName: string,
  designationName: string,
  order: string,
  startDate?: string,
  endDate?: string,
  isActive?: string,
  search?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.USERS}`;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      departmentName: departmentName === "Department" ? "" : departmentName,
      designationName: designationName === "Designation" ? "" : designationName,
      order: order,
    });

    if (startDate) {
      queryParams.append("startDate", startDate);
    }
    if (endDate) {
      queryParams.append("endDate", endDate);
    }
    if (isActive) {
      queryParams.append("isActive", isActive);
    }
    if (search) {
      queryParams.append("search", search);
    }

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    console.log("Fetching department with URL:", fullUrl);

    const response = await base.get(fullUrl);
    
    // Sort users alphabetically by firstName
   /*  if (response.data && response.data.users && response.data.users.results) {
      response.data.users.results = [...response.data.users.results].sort(
        (a, b) => a.firstName.localeCompare(b.firstName)
      );
    } */
    
    return response.data;
  } catch (error) {
    console.error("Error fetching department:", error);
    throw error;
  }
};

// get employee by id
export const getUserById = async (userId: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.USERS}/${userId}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching holiday:", error);
    throw error;
  }
};

// get department user count
export const getDepartmentUserCount = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.USERS}/department-user-count`;
    // console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching department user count:", error);
    throw error;
  }
};

// get designation user count
export const getDesignationUserCount = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.USERS}/designation-user-count`;
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching designation user count:", error);
    throw error;
  }
};

//getuser
export const getUsers = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.USERS}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching users:", error);
    throw error;
  }
};


// add user
interface User {
  roles: string;
  firstName: string;
  lastName: string;
  // employeeId: string;
  joiningDate: string;
  email: string;
  password: string;
  // confirmPassword: string;
  phone: string;
  pan: string;
  company: string;
  departmentId: string;
  designationId: string;
  about: string;
  avatar: string | null;
}

// add an employee
export const addUser = async (body: User) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.USERS}`;

    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

// Delete a user
export const deleteEmployee = async (userId: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.USERS}/${userId}`;
    console.log("DELETE Request to:", apiUrl);

    const response = await base.delete(apiUrl);

    console.log("Employee Deleted:", response.data);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error deleting employee:", error);
    throw error;
  }
};

// Edit an employee data
export const editUser = async (userId: string, body: Partial<any>) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.USERS}/${userId}`;

    const response = await base.put(apiUrl, body);

    console.log("Employee updated:", response.data);
    toast.success(response.data.message);
    return response.data;
  } catch (error: any) {
    // console.error("Error updating employee:", error?.response?.data?.error);
    toast.error(error?.response?.data?.error)
    throw error;
  }
};

//ADD Personal info
interface PersonalInfo {
  userId: string;
  passportNumber: string;
  passportExpiry: string;
  nationality: string;
  religion: string;
  maritalStatus: string;
  employementSpouse: string;
  childrens: number;
}
export const addPersonalInfo = async (body: PersonalInfo) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERSONAL_INFO}`;
    console.log("POST Request to:", apiUrl);
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

//update personal info
export const updatePersonalInfo = async (
  userId: string,
  body: PersonalInfo
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.PERSONAL_INFO}/${userId}`;
    console.log("PUT Request to:", apiUrl);
    const response = await base.put(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

//Add emergency contact
interface EmergencyContact {
  userId: string;
  name: string;
  relationship: string;
  phone1: string;
  phone2: string;
}
export const addEmergencyContact = async (body: EmergencyContact) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.EMERGENCY_CONTACT}`;
    console.log("POST Request to:", apiUrl);
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

//update emergenct contact
export const updateEmergencyContact = async (
  id: string,
  body: EmergencyContact
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.EMERGENCY_CONTACT}/${id}`;
    console.log("PUT Request to:", apiUrl);
    const response = await base.put(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

//ADD BANK
interface Bank {
  userId?: string;
  bankName: string;
  accountNumber: string;
  ifsc: string;
  address: string;
}
export const addBank = async (body: Bank) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.BANK}`;
    console.log("POST Request to:", apiUrl);
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

//UPDATE BANK
export const updateBank = async (id: string, body: Bank) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.BANK}/${id}`;
    console.log("PUT Request to:", apiUrl);
    const response = await base.put(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

interface Family {
  userId: string;
  name: string;
  relationship: string;
  phone: string;
  dob: string;
}

export const addFamily = async (body: Family) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.FAMILY}`;
    console.log("POST Request to:", apiUrl);
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

export const updateFamily = async (id: string, body: Family) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.FAMILY}/${id}`;
    console.log("PUT Request to:", apiUrl);
    const response = await base.put(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

export const addExperience = async (body: {
  previousCompany: string;
  designation: string;
  startDate: string;
  endDate: string;
}) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.EXPERIENCE}`;
    console.log("POST Request to:", apiUrl);
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

export const updateExperience = async (
  id: string,
  body: {
    previousCompany: string;
    designation: string;
    startDate: string;
    endDate: string;
  }
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.EXPERIENCE}/${id}`;
    console.log("PUT Request to:", apiUrl);
    const response = await base.put(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

interface Education {
  course: string;
  startDate: string;
  endDate: string;
}

export const addEducation = async (body: Education) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.EDUCATION}`;
    console.log("POST Request to:", apiUrl);
    const response = await base.post(apiUrl, body);
      toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

export const updateEducation = async (id: string, body: Education) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.EDUCATION}/${id}`;
    console.log("PUT Request to:", apiUrl);
    const response = await base.put(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

/* interface Asset {
  userId: string;
  assetImage?: string;
  brand: string;
  type: string;
  serialNumber: string;
  category: string;
  assignedDate: string;
  cost: string;
  vendor: string;
  warrentyTo: string;
  location: string;
}
// post assets
export const postAssets = async (body: Asset) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSETS}`;
    console.log(apiUrl);
    const response = await base.post(apiUrl, body);
    return response.data;
  } catch (error) {
    console.error("Error posting ASSETS:", error);
    throw error;
  }
}; */

// Update
/* export const updateAssets = async (id: string, body: Asset) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSETS}/${id}`;
    console.log("PUT Request to:", apiUrl);
    const response = await base.put(apiUrl, body);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
}; */

//add assessts issue
interface AssetsIssue {
  assetId: string;
  issueDescription: string;
}

export const addAssetsIssue = async (body: AssetsIssue) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSETS_ISSUE}`;
    console.log("POST Request to:", apiUrl);
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

export const updateAssetsIssue = async (id: string, body: AssetsIssue) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSETS_ISSUE}/${id}`;
    console.log("PUT Request to:", apiUrl);
    const response = await base.put(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

//add bank statuaray

export const addBankStatutory = async (body: {
  userId: string;
  salaryBasis: string;
  salaryCurrency: string;
  paymentType: string;
  pfContribution: string;
  pfNumber: string;
  pfRate: string;
  pfAdditionalRate: string;
  totalRate: string;
  esiContribution: string;
  esiNumber: string;
  esiRate: string;
  esiAdditionalRate: string;
}) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.BANK_STATUTORY}`;
    console.log("POST Request to:", apiUrl);
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};

//UPDATE BANK STATUUTAORY
export const updateBankStatutory = async (
  id: string,
  body: {
    userId: string;
    salaryBasis: string;
    salaryCurrency: string;
    paymentType: string;
    pfContribution: string;
    pfNumber: string;
    pfRate: string;
    pfAdditionalRate: string;
    totalRate: string;
    esiContribution: string;
    esiNumber: string;
    esiRate: string;
    esiAdditionalRate: string;
  }
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.BANK_STATUTORY}/${id}`;
    console.log("PUT Request to:", apiUrl);
    const response = await base.put(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
};
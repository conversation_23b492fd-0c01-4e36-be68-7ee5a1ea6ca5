// import axios from "axios";
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

export const getAllPolicies = async () => {
  try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.POLICIES}`;
      console.log(apiUrl);
      const response = await base.get(apiUrl);
      return response.data;
  } catch (error) {
      console.error("Error fetching Policies :", error);
      throw error;
  }
};

// Fetch Policies
export const getPolicies = async (
  limit: number,
  page: number,
  departmentName: string,
  order: string,
  startDate?: string,
  endDate?: string,
  search?: string   
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.POLICIES}`;
    
    // Construct query parameters
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString(),
      departmentName: departmentName === "Department" ? "" : departmentName, 
      order: order, 
    });

    if (startDate) {
      queryParams.append("startDate", startDate);
    }
    if (endDate) {
      queryParams.append("endDate", endDate);
    }
    if (search) {
      queryParams.append("search", search);
    }

    const fullUrl = `${apiUrl}?${queryParams.toString()}`;
    console.log("Fetching policies with URL:", fullUrl);

    const response = await base.get(fullUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching policies:", error);
    throw error;
  }
};

// Add Policies
interface policyBody {
  policyName: string;
  implementationDate: string;
  departmentId: string;
  description: string;
  // policyDocument: string;
}

export const addPolicy = async (body: policyBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.POLICIES}`;
    console.log(apiUrl);
    const response = await base.post(apiUrl, body);
    console.log("API Response:", response);
    console.log("Policy added successfully");
    if (response.data && response.data.message) {
      console.log("Success Message:", response.data.message);
      // Assuming you have a toast function available
      toast.success(response.data.message);
    }
  } catch (error) {
    console.error("Error adding policy:", error);
    throw error;
  }
};

// Update Policies
export const updatePolicy = async (id: string, body: policyBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.POLICIES}/${id}`;
    console.log(apiUrl);
    await base.put(apiUrl, body);
    const response = await base.put(apiUrl, body);
    console.log("Policy updated successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error updating policy:", error);
    throw error;
  }
};

//get policy by id
export const getPolicyById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.POLICIES}/${id}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error fetching policy by id:", error);
    throw error;
  }
};

// delete policies with id
export const deletePolicy = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.POLICIES}/${id}`;
    console.log(apiUrl);
    const response =await base.delete(apiUrl);
    console.log("Policy deleted successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error deleting policy:", error);
    throw error;
  }
};

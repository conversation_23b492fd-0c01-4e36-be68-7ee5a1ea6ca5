const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

// Fetch Department
export const getDepartments = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DEPARTMENT}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);

    // Sort departments alphabetically by departmentName
    if (
      response.data &&
      response.data.departments &&
      response.data.departments.results
    ) {
      response.data.departments.results = [
        ...response.data.departments.results,
      ].sort((a, b) => a.departmentName.localeCompare(b.departmentName));
    }

    return response.data;
  } catch (error) {
    console.error("Error fetching departments:", error);
    throw error;
  }
};

export const getDepartment = async (
  limit?: number,
  page?: number,
  departmentName?: string,
  order?: string,
  startDate?: string,
  endDate?: string,
  isActive?: string,
  search?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DEPARTMENT}`;

    // Construct query parameters
    const queryParams = new URLSearchParams();

    // Only add pagination parameters if they are provided
    if (limit) queryParams.append("limit", limit.toString());
    if (page) queryParams.append("page", page.toString());

    // Add other parameters
    if (departmentName && departmentName !== "Department")
      queryParams.append("departmentName", departmentName);
    if (order) queryParams.append("order", order);
    if (startDate) queryParams.append("startDate", startDate);
    if (endDate) queryParams.append("endDate", endDate);
    if (isActive) queryParams.append("isActive", isActive);
    if (search) queryParams.append("search", search);

    // Construct the full URL
    const fullUrl = queryParams.toString()
      ? `${apiUrl}?${queryParams.toString()}`
      : apiUrl;

    console.log("Fetching departments with URL:", fullUrl);

    const response = await base.get(fullUrl);

    // Sort departments alphabetically by departmentName
    /* if (
      response.data &&
      response.data.departments &&
      response.data.departments.results
    ) {
      response.data.departments.results = [
        ...response.data.departments.results,
      ].sort((a, b) => a.departmentName.localeCompare(b.departmentName));
    } */

    return response.data;
  } catch (error) {
    console.error("Error fetching departments:", error);
    throw error;
  }
};

// Define the interface for department body
export interface DepartmentBody {
  departmentHead: string;
  departmentName: string;
  isActive: boolean;
}

// Add Departments
export const addDepartment = async (body: DepartmentBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DEPARTMENT}`;
    console.log(apiUrl);
    const response = await base.post(apiUrl, body);
    console.log("Department added successfully", response.data.message);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error adding department:", error);
    throw error;
  }
};

// Update Department
export const updateDepartment = async (id: string, body: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DEPARTMENT}/${id}`;
    const response = await base.put(apiUrl, body);
    console.log("Department updated successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error updating department:", error);
    throw error;
  }
};

//get department by id
export const getDepartmentById = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DEPARTMENT}/${id}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    // toast.success(response.data.message)
    return response.data;
  } catch (error) {
    console.error("Error fetching department by id:", error);
    throw error;
  }
};

// delete policies with id
export const deleteDepartment = async (id: string) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DEPARTMENT}/${id}`;
    console.log(apiUrl);
    const response = await base.delete(apiUrl);
    console.log("Department deleted successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error deleting department:", error);
    throw error;
  }
};

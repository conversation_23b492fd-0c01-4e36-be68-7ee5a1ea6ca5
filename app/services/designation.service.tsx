const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

export interface DesignationBody {
  designationName: string;
  departmentId: string;
  isActive: boolean;
}

// Get Designations with filters
export const getDesignations = async (
  limit?: number,
  page?: number,
  departmentName?: string,
  order?: string,
  startDate?: string,
  endDate?: string,
  isActive?: string,
  search?: string,
  departmentId?: string
) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DESIGNATION}`;
    const queryParams = new URLSearchParams();
    
    // Only add pagination parameters if they are provided
    if (limit) queryParams.append("limit", limit.toString());
    if (page) queryParams.append("page", page.toString());
    
    // Add other parameters
    if (departmentName && departmentName !== "Department") 
      queryParams.append("departmentName", departmentName);
    if (order) queryParams.append("order", order);
    if (startDate) queryParams.append("startDate", startDate);
    if (endDate) queryParams.append("endDate", endDate);
    if (isActive) queryParams.append("isActive", isActive);
    if (search) queryParams.append("search", search);
    if (departmentId) queryParams.append("departmentId", departmentId);

    const fullUrl = queryParams.toString() 
      ? `${apiUrl}?${queryParams.toString()}` 
      : apiUrl;
    
    console.log("Fetching designations with URL:", fullUrl);

    const response = await base.get(fullUrl);
    
    // Sort designations alphabetically by designationName
   /*  if (response.data && response.data.designations && response.data.designations.results) {
      response.data.designations.results = [...response.data.designations.results].sort(
        (a, b) => a.designationName.localeCompare(b.designationName)
      );
    } */
    
    return response.data;
  } catch (error) {
    console.error("Error fetching designations:", error);
    throw error;
  }
};

// Fetch Designation without pagination
export const getAllDesignations = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DESIGNATION}`;
    const response = await base.get(apiUrl);
    
    // Sort designations alphabetically by designationName
    if (response.data && response.data.designations && response.data.designations.results) {
      response.data.designations.results = [...response.data.designations.results].sort(
        (a, b) => a.designationName.localeCompare(b.designationName)
      );
    }
    
    return response.data;
  } catch (error) {
    console.error("Error fetching all designations:", error);
    throw error;
  }
};


//post designation
export const addDesignation = async (body: DesignationBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DESIGNATION}`;
    const response = await base.post(apiUrl, body);
    console.log("Designation added successfully:", response.data);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error adding designations:", error);
    throw error;
  }
};

//update designation
export const updateDesignation = async (id: string, body: DesignationBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DESIGNATION}/${id}`;
    const response = await base.put(apiUrl, body);
    console.log("Designation updated successfully");
    toast.success(response.data.message);
  } catch (error) {
    console.error("Error updating designations:", error);
    throw error;
  }
};
  
  //get policy by id
  export const getDesignationById = async (id: string) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DESIGNATION}/${id}`;
      console.log("Fetching designation from:", apiUrl);
      const response = await base.get(apiUrl);
      console.log("Designation data received:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching designation by id:", error);
      throw error;
    }
  };
  
  // delete policies with id
  export const deleteDesignation = async (id:string) => {
    try {
      const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.DESIGNATION}/${id}`;
      console.log(apiUrl);
      const response = await base.delete(apiUrl);
      console.log("Policy deleted successfully");
      toast.success(response.data.message);
    } catch (error) {
      console.error("Error deleting designations:", error);
      throw error;
    }
  };
  

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

export interface ChangePasswordBody {
  newPassword: string;
  confirmNewPassword: string;
}
export const changePassword = async (body: ChangePasswordBody) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.CHANGE_PASSWORD}`;
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error changing password:", error);
    throw error;
  }
};

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
import { API_ENDPOINTS } from "@/constants/endpoints";
import base from "@/app/middlewares/interceptors";
import { toast } from "react-toastify";

// post assets
export const postAssets = async (body: any) => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSETS}`;
    console.log(apiUrl);
    const response = await base.post(apiUrl, body);
    toast.success(response.data.message);
    return response.data;
  } catch (error) {
    console.error("Error posting ASSETS:", error);
    throw error;
  }
};

export const getAssets = async () => {
  try {
    const apiUrl = `${API_BASE_URL}${API_ENDPOINTS.ASSETS}`;
    console.log(apiUrl);
    const response = await base.get(apiUrl);
    return response.data;
  } catch (error) {
    console.error("Error Getting ASSETS:", error);
    throw error;
  }
};

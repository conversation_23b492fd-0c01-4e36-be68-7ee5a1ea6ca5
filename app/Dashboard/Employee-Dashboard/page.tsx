"use client";

import * as React from "react";
import { useState } from "react";
import "./Employee-Dashboard.scss";
import { Box, Card, IconButton, Typography } from "@mui/material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import { AccessTime, Circle, Close, HomeOutlined } from "@mui/icons-material";
import ProfileCard from "./EmployeeCards/ProfileCard/ProfileCard";
import LeaveDetails from "./EmployeeCards/LeaveDetails/LeaveDetails";
import LeaveSummary from "./EmployeeCards/LeaveSummary/LeaveSummary";
import AttendanceCard from "./EmployeeCards/AttendancePunchCard/AttendancePunchCard";
import Projects from "./EmployeeCards/Projects/Projects";
import BirthdayAndHoliday from "./EmployeeCards/BirthdayAndHoliday/BirthdayAndHoliday";
import PerformanceChart from "./EmployeeCards/PerformanceChart/PerformanceChart";
import TasksComponent from "./EmployeeCards/TasksComponent/TasksComponent";
import MySkillsComponent from "./EmployeeCards/MySkillsComponent/MySkillsComponent";
import CustomPagination from "@/components/CustomPagination/CustomPagination";
import AttendancePunchCard from "@/components/AttendancePunchCard/AttendancePunchCard";
import CustomHoursCard from "@/components/CustomHoursCard/CustomHoursCard";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";
import useFetchAttendanceByEmpId from "@/app/hooks/attendance/useFetchAttendanceByEmpId";
import Loader from "@/components/Loader/Loader";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useAuthStore from "@/store/authStore";

// Quick Search Toolbar Component

// Utility function to format ISO date to DD MMM YYYY
const formatDate = (isoDate?: string) => {
  if (!isoDate || isoDate === "N/A") return "N/A";
  const date = Date.parse(isoDate);
  if (isNaN(date)) return "N/A";
  const parsedDate = new Date(date);
  const day = parsedDate.getDate();
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const month = monthNames[parsedDate.getMonth()];
  const year = parsedDate.getFullYear();
  return `${day} ${month} ${year}`;
};

const formatDateTime = (isoDate?: string) => {
  if (!isoDate || isoDate === "N/A") return "N/A";
  const date = new Date(isoDate);
  const hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const ampm = hours >= 12 ? "PM" : "AM";
  const formattedHours = hours % 12 || 12;
  return `${formattedHours}:${minutes} ${ampm}`;
};

const formatDateForApi = (dateStr: string) => {
  if (!dateStr) return undefined;

  try {
    // Assuming dateStr is in format "MM/DD/YYYY"
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return undefined;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Date formatting error:", error);
    return undefined;
  }
};

// Utility function to get today's date in YYYY-MM-DD format
const getTodayDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = (today.getMonth() + 1).toString().padStart(2, "0");
  const day = today.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const formatTimeLabel = (date: Date) => {
  const hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const ampm = hours >= 12 ? "PM" : "AM";
  const formattedHours = hours % 12 || 12;
  return `${formattedHours}:${minutes} ${ampm}`;
};

// Interface to match API response
interface AttendanceData {
  _id: string;
  break?: number;
  late?: number;
  productionHours?: number;
  avatar?: string;
  employeeName?: string;
  departmentName?: string;
  date?: string;
  checkIn?: string;
  checkOut?: string;
  status?: string;
  totalWorkingHours?: number;
}

export default function EmployeeDashboard() {
  const [isLoading, setIsLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);

  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");

  const { employeeId } = useAuthStore();

  // Handler for punch actions to trigger refetch
  const handlePunch = () => {
    setTimeout(() => {
      setRefreshKey((prev) => prev + 1);
    }, 1000);
  };

  const [attendanceData, attendanceTotal] = useFetchAttendanceByEmpId({
    empId: employeeId || "",
    setIsLoading,
    refresh: refreshKey,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : undefined,

    startDate: startDate ? formatDateForApi(startDate) : undefined,
    endDate: endDate ? formatDateForApi(endDate) : undefined,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
  });

  // Restrict access to Employee roles
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  // Add console.log to debug the response
  console.log("API Response:", attendanceData);

  // Memoized rows for DataGrid

  //  const isMobile = useMediaQuery("(max-width:768px)");

  const handlePaginationModelChange = (model: {
    page: number;
    pageSize: number;
  }) => {
    setPage(model.page + 1);
    setPageSize(model.pageSize);
    setLimit(model.pageSize);
  };

  // Filter today's attendance record
  const todayDate = getTodayDate();
  const todayAttendance = attendanceData?.results?.find(
    (item: AttendanceData) => item.date?.split("T")[0] === todayDate
  );

  // Use API-provided values directly, no conversion
  const totalWorkingHoursToday = todayAttendance?.totalWorkingHours || 0;
  const productionHoursToday = todayAttendance?.productionHours || 0;
  const breakHoursToday = todayAttendance?.break
    ? todayAttendance.break / 60
    : 0;

  // Calculate weekly and monthly totals from all records (optional)
  const totalWorkingHoursWeek =
    attendanceData?.results?.reduce(
      (total: number, item: AttendanceData): number =>
        total + (Number(item.totalWorkingHours) || 0),
      0
    ) || 0;
  const totalWorkingHoursMonth = totalWorkingHoursWeek;

  const visualizationWindowHours = 12;

  const startTime = todayAttendance?.checkIn
    ? new Date(todayAttendance.checkIn)
    : new Date();
  startTime.setMinutes(0, 0, 0);

  const timeLabels: string[] = [];
  for (let i = 0; i <= visualizationWindowHours; i++) {
    const time = new Date(startTime);
    time.setHours(startTime.getHours() + i);
    timeLabels.push(formatTimeLabel(time));
  }

  const totalVisualizationHours = visualizationWindowHours;
  const productiveWidth =
    (Math.abs(productionHoursToday) / totalVisualizationHours) * 100;
  const breakWidth = (breakHoursToday / totalVisualizationHours) * 100;
  const remainingWidth = Math.max(0, 100 - productiveWidth - breakWidth);

  // Handle error state
  if (!attendanceData && !isLoading) {
    return (
      <Box className="attendance-container">
        <Typography color="error">Error loading attendance data</Typography>
      </Box>
    );
  }
  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Dashboard", href: "" },
    { label: "Employee-Dashboard" },
  ];

  return (
    <Box className="employee-dashboard-container">
      <Box className="content">
        <Box className="employee-dashboard-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2"> Employee Dashboard</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
        </Box>

        {/* <Card
          className="leave-approval-message"
          sx={{
            padding: "0.625rem 2.25rem 0.625rem 0.85rem",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            borderRadius: "4px",
            fontSize: "0.8125rem",
            backgroundColor: "#EDF2F4 !important",
            color: "#3B7080 !important",
            marginBottom: "1.5rem ",
          }}
        >
          Your Leave Request on“24th April 2024”has been Approved!!!
          <IconButton>
            <Close />
          </IconButton>
        </Card> */}

        <Box className="profile-row" sx={{ display: "flex", mb: "1.5rem" }}>
          <Box
            className="profile-card"
            sx={{
              flex: "0 0 auto",
              width: "33.33333333%",
              padding: "0px 12px",
            }}
          >
            <ProfileCard />
          </Box>
          <Box
            className="leave-details"
            sx={{
              flex: "0 0 auto",
              width: "41.66666667%",
              padding: "0px 12px",
            }}
          >
            <LeaveDetails />
          </Box>
          <Box
            className="leave-summary"
            sx={{
              flex: "0 0 auto",
              width: "25%",
              padding: "0px 12px",
            }}
          >
            <LeaveSummary />
          </Box>
        </Box>

        <Box sx={{ display: "flex", mb: "1.5rem" }}>
          {isLoading && <Loader loading={isLoading} />}
          {/* <AttendanceCard
            totalWorkingHoursToday={totalWorkingHoursToday.toFixed(2)}
            totalWorkingHoursWeek={totalWorkingHoursWeek.toFixed(2)}
            totalWorkingHoursMonth={totalWorkingHoursMonth.toFixed(2)}
            productionHoursToday={productionHoursToday.toFixed(2)}
            breakHoursToday={breakHoursToday}
          /> */}

          {/* Employee Punch In & Out Card */}
          <Box
            className="AttendanceCard-wrapper"
            sx={{ display: "flex", gap: 3, width: "100%" }}
          >
            <Box
              className="AttendancePunchCard-container"
              sx={{ flex: "1 1 30%", maxWidth: "30%" }}
            >
              <AttendancePunchCard onPunch={handlePunch} />
            </Box>
            <Box
              sx={{
                flex: "1 1 70%",
                display: "flex",
                flexDirection: "column",
                gap: 2,
              }}
            >
              <Box
                className="CustomHoursCard-container"
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  gap: 3,
                  flexWrap: "wrap",
                }}
              >
                <CustomHoursCard
                  icon={
                    <AccessTime style={{ color: "#fff", fontSize: "16px" }} />
                  }
                  value={parseFloat(totalWorkingHoursToday.toFixed(2))}
                  total={9}
                  title="Total Hours Today"
                  subtitle="This Week"
                  // percentage={5}
                  color="#F57C00"
                  sx={{ flex: 1 }}
                />
                <CustomHoursCard
                  icon={
                    <AccessTime style={{ color: "#fff", fontSize: "16px" }} />
                  }
                  value={parseFloat(totalWorkingHoursWeek.toFixed(2))}
                  total={40}
                  title="Total Hours Week"
                  subtitle="Last Week"
                  color="#2E2E2E"
                  sx={{ flex: 1 }}
                />
                <CustomHoursCard
                  icon={
                    <AccessTime style={{ color: "#fff", fontSize: "16px" }} />
                  }
                  value={parseFloat(totalWorkingHoursMonth.toFixed(2))}
                  total={98}
                  title="Total Hours Month"
                  subtitle="Last Month"
                  color="#1976D2"
                  sx={{ flex: 1 }}
                />
              </Box>

              {/* Working Hours Summary */}
              <Box
                className="working-hours-summary"
                sx={{
                  borderRadius: 2,
                  backgroundColor: "#fff",
                  padding: "37px",
                  boxShadow: "0px 4px 8px rgba(0,0,0,0.1)",
                  marginTop: "6px",
                }}
              >
                <Box
                  className="working-hours-cards"
                  sx={{ display: "flex", gap: 9 }}
                >
                  <Box className="working-hours-card">
                    <Typography color="textSecondary">
                      <Circle
                        sx={{
                          color: "#E8E9EA",
                          width: "7px",
                          height: "7px",
                          marginRight: ".25rem",
                        }}
                      />
                      Total Working Hours
                    </Typography>
                    <Typography variant="h6">
                      {totalWorkingHoursToday.toFixed(2)} Hrs
                    </Typography>
                  </Box>
                  <Box className="working-hours-card">
                    <Typography color="textSecondary">
                      <Circle
                        sx={{
                          color: "#03C95A",
                          width: "7px",
                          height: "7px",
                          marginRight: ".25rem",
                        }}
                      />
                      Productive Hours
                    </Typography>
                    <Typography variant="h6">
                      {productionHoursToday.toFixed(2)} Hrs
                    </Typography>
                  </Box>
                  <Box className="working-hours-card">
                    <Typography color="textSecondary">
                      <Circle
                        sx={{
                          color: "#FFC107",
                          width: "7px",
                          height: "7px",
                          marginRight: ".25rem",
                        }}
                      />
                      Break Hours
                    </Typography>
                    <Typography variant="h6">
                      {breakHoursToday.toFixed(2)} Hrs
                    </Typography>
                  </Box>
                </Box>

                {/* Time Distribution Visualization */}
                <Box
                  sx={{
                    mt: 2,
                    display: "flex",
                    flexDirection: "column",
                    width: "100%",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Box
                    sx={{
                      width: "100%",
                      display: "flex",
                      justifyContent: "flex-start",
                      gap: 0,
                      mt: 1,
                    }}
                  >
                    {totalWorkingHoursToday > 0 ? (
                      <>
                        <Box
                          sx={{
                            width: `${productiveWidth}%`,
                            height: 20,
                            bgcolor: "#03C95A",
                            borderTopLeftRadius: 4,
                            borderBottomLeftRadius: 4,
                            minWidth: productionHoursToday > 0 ? "5%" : "0%",
                          }}
                        />
                        <Box
                          sx={{
                            width: `${breakWidth}%`,
                            height: 20,
                            bgcolor: "#FFC107",
                            borderRadius: 0,
                            minWidth: breakHoursToday > 0 ? "3%" : "0%",
                          }}
                        />
                        <Box
                          sx={{
                            width: `${remainingWidth}%`,
                            height: 20,
                            bgcolor: "#E8E9EA",
                            borderTopRightRadius: 4,
                            borderBottomRightRadius: 4,
                            flexGrow: 1,
                          }}
                        />
                      </>
                    ) : (
                      <Box
                        sx={{
                          width: "100%",
                          height: 20,
                          bgcolor: "#E8E9EA",
                          borderRadius: 4,
                        }}
                      />
                    )}
                  </Box>

                  <Box
                    className="time-labels"
                    sx={{
                      display: "flex",
                      width: "100%",
                      justifyContent: "space-between",
                      gap: 0,
                      mt: 1,
                    }}
                  >
                    {timeLabels.map((label, index) => (
                      <Typography
                        key={index}
                        sx={{ fontSize: "0.75rem", color: "#6B7280" }}
                      >
                        {label}
                      </Typography>
                    ))}
                  </Box>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* <Box sx={{ display: "flex", mb: "1.5rem", background: "transparent" }}>
        <Box
          sx={{
            flex: "0 0 auto",
            width: "50%",
            background: "#fff",
            padding: "0px 12px",
          }}
        >
          <Projects />
        </Box>

        <Box
          sx={{
            flex: "0 0 auto",
            width: "50%",
            padding: "0px 12px",
          }}
        >
          <TasksComponent />
        </Box>
      </Box>

      <Box sx={{ display: "flex", mb: "1.5rem" }}>
        <Box
          sx={{ flex: "0 0 auto", width: "41.66666667%", padding: "0px 12px" }}
        >
          <PerformanceChart />
        </Box>
        <Box
          sx={{ flex: "0 0 auto", width: "33.33333333%", padding: "0px 12px" }}
        >
          <MySkillsComponent />
        </Box>
        <Box sx={{ flex: "0 0 auto", width: "25%", padding: "0px 12px" }}>
          <BirthdayAndHoliday />
        </Box>
      </Box> */}
    </Box>
  );
}

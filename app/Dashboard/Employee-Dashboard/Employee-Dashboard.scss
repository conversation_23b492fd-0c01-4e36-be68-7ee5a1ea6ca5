.employee-dashboard-container {
    .content {
        padding: 24px;

        .employee-dashboard-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;

            .breadcrumbs-box {
                h2 {
                    font-size: 24px;
                    font-weight: 700;
                    color: #202C4B;
                }
            }


        }

        .welcome-card {
            margin-bottom: 1.5rem;
            background-color: #FFF;
            transition: all 0.5sease-in-out;
            position: relative;
            border-radius: 5px;
            border: 1px solid #E5E7EB;
            box-shadow: 0px 1px 1px 1px rgba(198, 198, 198, 0.2);
            color: inherit;
        }

        .avatar-style {
            position: relative;
            height: 2.625rem;
            width: 2.625rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: #FFF;
            font-weight: 500;
        }


    }
}

@media screen and (min-width: 1200px) and (max-width: 1600px) {
    .profile-row {
        display: flex;
        flex-direction: column;

        .profile-card {
            width: 100%;
        }

        .leave-details {
            width: 100%;
        }

        .leave-summary {
            width: 100%;
        }
    }
}

@media screen and (max-width: 1199px) {
    .profile-row {
        display: flex;
        flex-direction: column;

        .profile-card {
            width: 100%;
        }

        .leave-details {
            width: 100%;
        }

        .leave-summary {
            width: 100%;
        }
    }
}

@media screen and (max-width: 767px) {
    .employee-dashboard-container {
        .content {
            padding: 16px;

            .employee-dashboard-header {
                flex-direction: column;
                align-items: flex-start;

                .breadcrumbs-box {
                    margin-bottom: 16px;
                }
            }

            .profile-row {
                display: flex;
                flex-direction: column;
                gap: 24px;

                .profile-card {
                    width: 100%;
                    margin-bottom: 0px;
                }

                .leave-details {
                    width: 100%;
                }

                .leave-summary {
                    width: 100%;
                }
            }

            .AttendanceCard {
                display: flex;
                flex-direction: column;
                width: 100%;

                .CustomHoursCards {
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                }

                .working-hours-summary {
                    .working-hours-cards {
                        display: flex;
                        flex-direction: column !important;
                    }

                    .working-hours-card {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                }

                .time-labels {
                    p {
                        font-size: 0.2rem;
                    }
                }
            }
        }
    }
}
"use client";

import React, { useState } from "react";
import { Box, Typography } from "@mui/material";
import { AccessTime, Circle } from "@mui/icons-material";
import AttendancePunchCard from "@/components/AttendancePunchCard/AttendancePunchCard";
import CustomHoursCard from "@/components/CustomHoursCard/CustomHoursCard";
import useFetchAttendanceByEmpId from "@/app/hooks/attendance/useFetchAttendanceByEmpId";
import useAuthStore from "@/store/authStore";
import PolicyFilters, {
  getDateRange,
} from "@/components/policyFilter/PolicyFilters";

interface AttendanceCardProps {
  totalWorkingHoursToday: number;
  totalWorkingHoursWeek: number;
  totalWorkingHoursMonth: number;
  productionHoursToday: number;
  breakHoursToday: number;
}

interface AttendanceData {
  _id: string;
  break?: number;
  late?: number;
  productionHours?: number;
  avatar?: string;
  employeeName?: string;
  departmentName?: string;
  date?: string;
  checkIn?: string;
  checkOut?: string;
  status?: string;
  totalWorkingHours?: number;
}

const AttendanceCard: React.FC<AttendanceCardProps> = ({
  totalWorkingHoursToday,
  totalWorkingHoursWeek,
  totalWorkingHoursMonth,
  productionHoursToday,
  breakHoursToday,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState(10);
  const [limit, setLimit] = useState(10);
  const currentDate = new Date();
  const {
    startDate: initialStartDate,
    endDate: initialEndDate,
    rangeStr: initialRangeStr,
  } = getDateRange("Last 7 Days", currentDate);
  const [selectedSortBy, setSelectedSortBy] = useState<string>("Sort By");
  const [selectedDateRange, setSelectedDateRange] =
    useState<string>(initialRangeStr);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const [selectedStatus, setSelectedStatus] = useState<string>("Select Status");

  const formatDateForApi = (dateStr: string) => {
    if (!dateStr) return undefined;

    try {
      // Assuming dateStr is in format "MM/DD/YYYY"
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return undefined;

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");

      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error("Date formatting error:", error);
      return undefined;
    }
  };

  const { employeeId } = useAuthStore();
  const [attendanceData, attendanceTotal] = useFetchAttendanceByEmpId({
    empId: employeeId || "",
    setIsLoading,
    refresh: refreshKey,
    limit,
    page,
    order:
      selectedSortBy === "Ascending"
        ? "asc"
        : selectedSortBy === "Descending"
          ? "desc"
          : undefined,

    startDate: startDate ? formatDateForApi(startDate) : undefined,
    endDate: endDate ? formatDateForApi(endDate) : undefined,
    isActive:
      selectedStatus === "Active"
        ? "true"
        : selectedStatus === "Inactive"
          ? "false"
          : undefined,
  });

  const getTodayDate = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, "0");
    const day = today.getDate().toString().padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const todayDate = getTodayDate();
  const todayAttendance = attendanceData?.results?.find(
    (item: AttendanceData) => item.date?.split("T")[0] === todayDate
  );
  const visualizationWindowHours = 12;
  const startTime = todayAttendance?.checkIn
    ? new Date(todayAttendance.checkIn)
    : new Date();
  startTime.setMinutes(0, 0, 0);

  const formatTimeLabel = (date: Date) => {
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const ampm = hours >= 12 ? "PM" : "AM";
    const formattedHours = hours % 12 || 12;
    return `${formattedHours}:${minutes} ${ampm}`;
  };
  const timeLabels: string[] = [];
  for (let i = 0; i <= visualizationWindowHours; i++) {
    const time = new Date(startTime);
    time.setHours(startTime.getHours() + i);
    timeLabels.push(formatTimeLabel(time));
  }

  const totalVisualizationHours = visualizationWindowHours;
  const productiveWidth =
    (Math.abs(productionHoursToday) / totalVisualizationHours) * 100;
  const breakWidth = (breakHoursToday / totalVisualizationHours) * 100;
  const remainingWidth = Math.max(0, 100 - productiveWidth - breakWidth);
  return (
    <Box
      className="AttendanceCard"
      sx={{ display: "flex", gap: 2, width: "100%" }}
    >
      <Box sx={{ flex: "1 1 30%", width: "100%" }}>
        <AttendancePunchCard />
      </Box>
      <Box
        sx={{
          flex: "1 1 90%",
          display: "flex",
          flexDirection: "column",
          gap: 2,
        }}
      >
        <Box
          className="CustomHoursCards"
          sx={{
            display: "flex",
            justifyContent: "space-between",
            gap: "16px",
            flexWrap: "wrap",
            height: "100%",
          }}
        >
          <CustomHoursCard
            icon={<AccessTime style={{ color: "#fff", fontSize: "16px" }} />}
            value={totalWorkingHoursToday}
            total={9}
            title="Total Hours Today"
            subtitle="This Week"
            // percentage={5}
            color="#F57C00"
            sx={{ flex: 1 }}
          />
          <CustomHoursCard
            icon={<AccessTime style={{ color: "#fff", fontSize: "16px" }} />}
            value={totalWorkingHoursWeek}
            total={40}
            title="Total Hours Week"
            subtitle="Last Week"
            // percentage={7}
            color="#2E2E2E"
            sx={{ flex: 1 }}
          />
          <CustomHoursCard
            icon={<AccessTime style={{ color: "#fff", fontSize: "16px" }} />}
            value={totalWorkingHoursMonth}
            total={98}
            title="Total Hours Month"
            subtitle="Last Month"
            // percentage={-8}
            color="#1976D2"
            sx={{ flex: 1 }}
          />
        </Box>

        {/* Working Hours Summary */}
        <Box
          className="working-hours-summary"
          sx={{
            borderRadius: 2,
            backgroundColor: "#fff",
            padding: "37px",
            boxShadow: "0px 4px 8px rgba(0,0,0,0.1)",
            marginTop: "6px",
          }}
        >
          <Box className="working-hours-cards" sx={{ display: "flex", gap: 9 }}>
            <Box className="working-hours-card">
              <Typography color="textSecondary">
                <Circle
                  sx={{
                    color: "#E8E9EA",
                    width: "7px",
                    height: "7px",
                    marginRight: ".25rem",
                  }}
                />
                Total Working Hours
              </Typography>
              <Typography variant="h6">
                {totalWorkingHoursToday.toFixed(2)} Hrs
              </Typography>
            </Box>
            <Box className="working-hours-card">
              <Typography color="textSecondary">
                <Circle
                  sx={{
                    color: "#03C95A",
                    width: "7px",
                    height: "7px",
                    marginRight: ".25rem",
                  }}
                />
                Productive Hours
              </Typography>
              <Typography variant="h6">
                {productionHoursToday.toFixed(2)} Hrs
              </Typography>
            </Box>
            <Box className="working-hours-card">
              <Typography color="textSecondary">
                <Circle
                  sx={{
                    color: "#FFC107",
                    width: "7px",
                    height: "7px",
                    marginRight: ".25rem",
                  }}
                />
                Break Hours
              </Typography>
              <Typography variant="h6">
                {breakHoursToday.toFixed(2)} Hrs
              </Typography>
            </Box>
          </Box>

          {/* Time Distribution Visualization */}
          <Box
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: "column",
              width: "100%",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Box
              sx={{
                width: "100%",
                display: "flex",
                justifyContent: "flex-start",
                gap: 0,
                mt: 1,
              }}
            >
              {totalWorkingHoursToday > 0 ? (
                <>
                  <Box
                    sx={{
                      width: `${productiveWidth}%`,
                      height: 20,
                      bgcolor: "#03C95A",
                      borderTopLeftRadius: 4,
                      borderBottomLeftRadius: 4,
                      minWidth: productionHoursToday > 0 ? "5%" : "0%",
                    }}
                  />
                  <Box
                    sx={{
                      width: `${breakWidth}%`,
                      height: 20,
                      bgcolor: "#FFC107",
                      borderRadius: 0,
                      minWidth: breakHoursToday > 0 ? "3%" : "0%",
                    }}
                  />
                  <Box
                    sx={{
                      width: `${remainingWidth}%`,
                      height: 20,
                      bgcolor: "#E8E9EA",
                      borderTopRightRadius: 4,
                      borderBottomRightRadius: 4,
                      flexGrow: 1,
                    }}
                  />
                </>
              ) : (
                <Box
                  sx={{
                    width: "100%",
                    height: 20,
                    bgcolor: "#E8E9EA",
                    borderRadius: 4,
                  }}
                />
              )}
            </Box>

            <Box
              className="time-labels"
              sx={{
                display: "flex",
                width: "100%",
                justifyContent: "space-between",
                gap: 0,
                mt: 1,
              }}
            >
              {timeLabels.map((label, index) => (
                <Typography
                  key={index}
                  sx={{ fontSize: "0.75rem", color: "#6B7280" }}
                >
                  {label}
                </Typography>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default AttendanceCard;

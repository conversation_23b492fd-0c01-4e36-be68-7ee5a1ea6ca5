import React from "react";
import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  Box,
  Avatar,
  IconButton,
  Grid,
  Chip,
  Divider,
  Menu,
  MenuItem,
  Button,
  AvatarGroup,
} from "@mui/material";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import GroupIcon from "@mui/icons-material/Group";

interface ProjectCardProps {
  title: string;
  leader: string;
  deadline: string;
  tasks: string;
  timeSpent: number;
  totalTime: number;
  members: { name: string; avatar: string }[];
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  title,
  leader,
  deadline,
  tasks,
  timeSpent,
  totalTime,
  members,
}) => {
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);
  const open = Boolean(anchorEl);

  const handleMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  return (
    <Card
      sx={{
        backgroundColor: "#FFF",
        transition: "all 0.5sease-in-out",
        position: "relative",
        borderRadius: "5px",
        border: "1px solid #E5E7EB",
      }}
    >
      <CardContent sx={{ padding: "1.25rem", flex: "1 1 auto" }}>
        <Box
          sx={{
            marginBottom: "1rem",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography
            sx={{
              fontSize: "14px",
              fontWeight: "600",
              color: "#202C4B",
            }}
            variant="h6"
          >
            {title}
          </Typography>
          <IconButton size="small" onClick={handleMenuOpen}>
            <MoreVertIcon sx={{ fontSize: "16px" }} />
          </IconButton>
          <Menu anchorEl={anchorEl} open={open} onClose={handleMenuClose}>
            <MenuItem onClick={handleMenuClose}>Edit</MenuItem>
            <MenuItem onClick={handleMenuClose}>Delete</MenuItem>
          </Menu>
        </Box>
        <Box
          display="flex"
          alignItems="center"
          mt={1}
          sx={{ marginBottom: "1rem" }}
        >
          <Avatar
            sx={{ width: "42px", height: "42px" }}
            alt={leader}
            src="/avatar.jpg"
          />
          <Box sx={{ marginLeft: ".5rem" }}>
            <Typography
              sx={{ fontWeight: "400", fontSize: "14px", color: "#202C4B" }}
              variant="body1"
            >
              {leader}
            </Typography>
            <Typography sx={{ fontSize: "0.8125rem" }} variant="caption">
              Project Leader
            </Typography>
          </Box>
        </Box>
        <Box display="flex" alignItems="center" gap={1} mt={1}>
          <Box
            sx={{
              width: "42px",
              height: "42px",
              borderRadius: "50%",
              bgcolor: "#FEF0E9",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              color: "#F26522 !important",
            }}
          >
            <CalendarMonthIcon fontSize="small" />
          </Box>
          <Typography variant="body2">{deadline}</Typography>
        </Box>
        <Box
           sx={{
            borderRadius: "4px",
            border: "1px solid #E5E7EB ",
            backgroundColor: "#F8F9FA !important",
            padding: ".5rem",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "1rem",
          }}
        >
          <Box>
            <GroupIcon />
            {`Tasks: ${tasks}`}
          </Box>
          <Box display="flex" gap={0.5}>
            <AvatarGroup max={3} className="avatarGroup">
              {members.slice(0, 3).map((member, index) => (
                <Avatar
                  key={index}
                  alt={member.name}
                  src={member.avatar}
                  sx={{ width: 24, height: 24 }}
                />
              ))}
            </AvatarGroup>
          </Box>
        </Box>
        <Box
          sx={{
            borderRadius: "4px",
            border: "1px solid #E5E7EB ",
            backgroundColor: "#F8F9FA !important",
            padding: ".5rem",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "1rem",
          }}
        >
          <Typography variant="body2">Time Spent</Typography>
          <Typography variant="h6">{`${timeSpent}/${totalTime} Hrs`}</Typography>
        </Box>
        <Box
          sx={{
            borderRadius: "4px",
            backgroundColor: "#B9CBD1",
            padding: ".5rem",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography variant="body2">Time Spent</Typography>
          <Typography variant="h6">{`${timeSpent}/${totalTime} Hrs`}</Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

const Projects = () => {
  return (
    <Box
      sx={{
        borderRadius: "5px",
        boxShadow: "0px 1px 1px 1px rgba(198, 198, 198, 0.2)",
        height: "100%",
      }}
    >
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            // marginBottom: ".5rem",
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Projects
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
          >
            Ongoing Projects
          </Button>
        </Box>
      </Box>

      <Box
        sx={{
          padding: "1.25rem",
          borderTop: "1px solid #E5E7EB",
          display: "flex",
        }}
      >
        {[1, 2].map((_, index) => (
          <Box key={index} sx={{ padding: "0px 12px", width: "100%" }}>
            <ProjectCard
              title="Office Management"
              leader="Anthony Lewis"
              deadline="14 Jan 2024"
              tasks="6/10"
              timeSpent={65}
              totalTime={120}
              members={[
                { name: "John", avatar: "/avatar1.jpg" },
                { name: "Sara", avatar: "/avatar2.jpg" },
                { name: "Mike", avatar: "/avatar3.jpg" },
                { name: "Kate", avatar: "/avatar4.jpg" },
              ]}
            />
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default Projects;

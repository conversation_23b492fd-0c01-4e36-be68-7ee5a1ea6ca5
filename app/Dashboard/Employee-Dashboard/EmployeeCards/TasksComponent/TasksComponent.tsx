import React from "react";
import "./TasksComponent.scss";
import {
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  Grid,
  Checkbox,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Button,
  AvatarGroup,
} from "@mui/material";
import {
  DragIndicator,
  Star,
  StarOutline,
  MoreVert,
  Circle,
} from "@mui/icons-material";

const tasks = [
  {
    title: "Patient appointment booking",
    status: "Onhold",
    statusColorBg: "rgb(255, 219, 236)",
    textColor: "rgb(253, 57, 149) !important",
    priority: true,
    members: ["A", "B", "C"],
  },
  {
    title: "Appointment booking with payment",
    status: "Inprogress",
    statusColorBg: "rgb(247, 238, 249)",
    textColor: "rgb(171, 71, 188) !important",
    priority: false,
    members: ["D", "E"],
  },
  {
    title: "Patient and Doctor video conferencing",
    status: "Completed",
    statusColorBg: "rgba(3, 201, 90, 0.1)",
    textColor: "rgb(3, 201, 90) !important",
    priority: false,
    members: ["F", "G", "H", "I"],
  },
  {
    title: "Private chat module",
    status: "Pending",
    statusColorBg: "rgb(237, 242, 244)",
    textColor: "rgb(59, 112, 128) !important",
    priority: true,
    members: ["J", "K", "L"],
  },
  {
    title: "Go-Live and Post-Implementation Support",
    status: "Inprogress",
    statusColorBg: "rgb(247, 238, 249) !important",
    textColor: "rgb(171, 71, 188) !important",
    priority: false,
    members: ["M", "N", "O", "P"],
  },
];

const TasksComponent = () => {
  return (
    <Card
      elevation={3}
      sx={{
        borderRadius: "5px",
        boxShadow: "0px 1px 1px 1px rgba(198, 198, 198, 0.2)",
        height: "100%",
      }}
    >
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            // marginBottom: ".5rem",
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Tasks
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
          >
            All Projects
          </Button>
        </Box>
      </Box>

      <CardContent>
        {tasks.map((task, index) => (
          <Grid item key={index}>
            <Card
              variant="outlined"
              sx={{
                display: "flex",
                alignItems: "center",
                padding: 1,
                marginY: 0.5,
              }}
            >
              <IconButton>
                <DragIndicator />
              </IconButton>
              <Checkbox />
              <IconButton>
                {task.priority ? <Star color="warning" /> : <StarOutline />}
              </IconButton>
              <Typography
                variant="body1"
                sx={{
                  flexGrow: 1,
                  fontSize: "0.875rem !important",
                  fontWeight: "600",
                  color: "rgb(32, 44, 75)",
                }}
              >
                {task.title}
              </Typography>
              <Chip
                icon={<Circle sx={{ fontSize: 8, color: task.textColor }} />}
                label={task.status}
                sx={{
                  backgroundColor: task.statusColorBg,
                  color: task.textColor,
                  marginRight: 1,
                  fontWeight: "600",
                  letterSpacing: "0.5px",
                  padding: "0.25rem 0.45rem !important",
                  borderRadius: "4px",
                  fontSize: "10.5px",
                }}
              />
              <AvatarGroup className="avatarGroup">
                {task.members.map((member, idx) => (
                  <Avatar
                    key={idx}
                    sx={{ width: 24, height: 24, marginLeft: 0.5 }}
                  >
                    {member}
                  </Avatar>
                ))}
              </AvatarGroup>
              {/* <IconButton>
                <MoreVert />
              </IconButton> */}
            </Card>
          </Grid>
        ))}
      </CardContent>
    </Card>
  );
};

export default TasksComponent;

import React from "react";
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  But<PERSON>,
} from "@mui/material";
import {
  Line<PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
} from "recharts";
import { CalendarMonth } from "@mui/icons-material";

const data = [
  { month: "Jan", value: 20000 },
  { month: "Feb", value: 25000 },
  { month: "Mar", value: 30000 },
  { month: "Apr", value: 32000 },
  { month: "May", value: 45000 },
  { month: "Jun", value: 60000 },
  { month: "Jul", value: 60000 },
];

const PerformanceChart = () => {
  return (
    <Card
      sx={{
        borderRadius: "5px",
        boxShadow: "0px 1px 1px 1px rgba(198, 198, 198, 0.2)",
        height: "100%",
      }}
    >
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            // marginBottom: ".5rem",
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Performance
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            startIcon={<CalendarMonth />}
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
          >
            2024
          </Button>
        </Box>
      </Box>
      <CardContent
        sx={{
          padding: "1.25rem",
          borderTop: "1px solid #E5E7EB",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
          <Typography
            variant="h4"
            sx={{
              mr: 1,
              fontSize: "20px",
              fontWeight: "600",
            }}
          >
            98%
          </Typography>
          <Chip label="12%" color="success" size="small" />
          <Typography>vs last years</Typography>
        </Box>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart
            data={data}
            margin={{ top: 20, right: 30, left: 0, bottom: 0 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Area
              type="monotone"
              dataKey="value"
              stroke="#00C49F"
              fill="#00C49F"
              fillOpacity={0.2}
            />
            <Line
              type="monotone"
              dataKey="value"
              stroke="#00C49F"
              strokeWidth={2}
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default PerformanceChart;

"use client";
import "./LeaveSummary.scss";
import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Menu,
  MenuItem,
} from "@mui/material";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import useAuthStore from "@/store/authStore";
import AddEditLeaveEmployee from "@/app/Attendance/leaves/leaves-employee/AddEditLeaveEmployee";
import { addEmployeeLeave } from "@/app/services/leave/leave.service";
import { getEmpLeaveById } from "@/app/services/leave/leave.service";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import type { LeaveEmployee } from "@/app/types/leave";

// Define interface for the API response structure
interface LeaveSummaryData {
  totalLeavesAllowed: number;
  totalLeavesTaken: number;
  pendingRequests: number; // Used for "Request"
  totalRemaining?: number;
  totalRejected?: number;
}

const LeaveSummary = () => {
  const employeeId = useAuthStore((state) => state.employeeId);
  const [openModal, setOpenModal] = useState(false);
  const [leaveData, setLeaveData] = useState<LeaveSummaryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [year, setYear] = useState(new Date().getFullYear());
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  // Generate last 3 years including current year
  const years = [
    new Date().getFullYear(),
    new Date().getFullYear() - 1,
    new Date().getFullYear() - 2,
  ];

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleYearSelect = (selectedYear: number) => {
    setYear(selectedYear);
    handleClose();
  };

  // Fetch leave data when component mounts or employeeId changes
  useEffect(() => {
    const fetchLeaveData = async () => {
      if (employeeId) {
        try {
          setLoading(true);
          const response = await getEmpLeaveById(employeeId); // Fetch leave data by employee ID
          console.log("Leave response:", response); // Debug log

          // Check if response and leaves exist
          if (response && response.success && response.leaves) {
            // Calculate total leaves allowed by summing up the 'total' from each leave type
            const totalAllowed = response.leaves.leaveSummary.reduce(
              (sum: number, item: { total: number }) => sum + item.total,
              0
            );

            // Calculate total leaves taken by summing up the 'taken' from each leave type
            const totalTaken = response.leaves.leaveSummary.reduce(
              (sum: number, item: { taken: number }) => sum + item.taken,
              0
            );

            // Calculate total remaining leaves by summing up the 'remaining' from each leave type
            const totalRemaining = response.leaves.leaveSummary.reduce(
              (sum: number, item: { remaining: number }) =>
                sum + item.remaining,
              0
            );

            // Get pending requests directly from the response
            const pendingRequests = response.leaves.pendingRequests || 0;

            // For rejected, we don't have direct data in the response, so we'll set it to 0
            // You might need to adjust this if the API provides rejected count in the future
            const totalRejected = 0;

            setLeaveData({
              totalLeavesAllowed: totalAllowed,
              totalLeavesTaken: totalTaken,
              pendingRequests: pendingRequests,
              totalRemaining: totalRemaining,
              totalRejected: totalRejected,
            });
          } else {
            console.error("Invalid response structure:", response);
            setLeaveData({
              totalLeavesAllowed: 0,
              totalLeavesTaken: 0,
              pendingRequests: 0,
              totalRemaining: 0,
              totalRejected: 0,
            });
          }
        } catch (error) {
          console.error("Failed to fetch leave summary:", error);
          // toast.error("Failed to load leave summary");
          setLeaveData({
            totalLeavesAllowed: 0,
            totalLeavesTaken: 0,
            pendingRequests: 0,
            totalRemaining: 0,
            totalRejected: 0,
          });
        } finally {
          setLoading(false);
        }
      }
    };

    fetchLeaveData();
  }, [employeeId, year]);

  // Handle form submission for adding a new leave
  const handleFormSubmit = async (values: LeaveEmployee): Promise<void> => {
    if (!employeeId) {
      // toast.error("Employee ID not found. Please log in.");
      return;
    }

    try {
      const leaveBody = {
        empId: employeeId,
        leaveType: values.leaveType,
        leaveFrom: values.from,
        leaveTo: values.to,
        leaveTime: values.leaveTime,
        remainingDays: values.remainingDays,
        leaveStatus: values.status,
        reason: values.reason,
      };

      await addEmployeeLeave(leaveBody);
      toast.success("Leave added successfully");
      setOpenModal(false);
      // Optionally refetch leave data after adding a new leave
      const response = await getEmpLeaveById(employeeId);
      const leaves = response.leaves;
      setLeaveData({
        totalLeavesAllowed: leaves.totalLeavesAllowed,
        totalLeavesTaken: leaves.totalLeavesTaken,
        pendingRequests: leaves.pendingRequests,
        totalRemaining: leaves.totalRemaining,
        totalRejected: leaves.totalRejected,
      });
    } catch (error) {
      console.error("Failed to save leave:", error);
      // toast.error("Failed to save leave");
    }
  };

  if (loading) {
    return <Loader loading={loading} />;
  }

  return (
    <>
      <Card
        sx={{
          borderRadius: "5px",
          boxShadow: "0px 1px 1px 1px rgba(198, 198, 198, 0.2)",
          height: "100%",
        }}
      >
        <Box
          sx={{
            borderColor: "#E5E7EB",
            position: "relative",
            background: "transparent",
            padding: "1rem 1.25rem 1rem",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography
            sx={{
              fontSize: "16px",
              fontWeight: "600",
              color: "#202C4B",
            }}
            variant="h6"
          >
            Leave Summary
          </Typography>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Button
              className="header-year-calendar"
              startIcon={<CalendarMonthIcon sx={{ fontSize: "12px" }} />}
              size="small"
              variant="outlined"
              onClick={handleClick}
              aria-controls={open ? "year-menu" : undefined}
              aria-haspopup="true"
              aria-expanded={open ? "true" : undefined}
            >
              {year}
            </Button>
            <Menu
              id="year-menu"
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              MenuListProps={{
                "aria-labelledby": "year-button",
              }}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "right",
              }}
              transformOrigin={{
                vertical: "top",
                horizontal: "right",
              }}
            >
              {years.map((yearOption) => (
                <MenuItem
                  className="menu-item"
                  key={yearOption}
                  onClick={() => handleYearSelect(yearOption)}
                  selected={yearOption === year}
                  sx={{
                    fontSize: "14px",
                    minHeight: "32px",
                    "&.Mui-selected": {
                      backgroundColor: "#F26522",
                      color: "#fff",
                      "&:hover": {
                        backgroundColor: "#d55a1d",
                      },
                    },
                  }}
                >
                  {yearOption}
                </MenuItem>
              ))}
            </Menu>
          </Box>
        </Box>
        <CardContent
          sx={{
            padding: "1.25rem",
            borderTop: "1px solid #E5E7EB",
          }}
        >
          <Box justifyContent="space-between">
            <Box sx={{ display: "flex" }}>
              <Box
                sx={{
                  flex: "0 0 auto",
                  width: "50%",
                  padding: "0px 12px",
                  marginBottom: "1.5rem",
                }}
              >
                <Typography variant="body2">Total Leaves</Typography>
                <Typography
                  variant="h6"
                  sx={{ fontSize: "18px", fontWeight: "600", color: "#202C4B" }}
                >
                  {leaveData?.totalLeavesAllowed || 0}
                </Typography>
              </Box>
              <Box
                sx={{
                  flex: "0 0 auto",
                  width: "50%",
                  padding: "0px 12px",
                  marginBottom: "1.5rem",
                }}
              >
                <Typography variant="body2">Taken</Typography>
                <Typography
                  variant="h6"
                  sx={{ fontSize: "18px", fontWeight: "600", color: "#202C4B" }}
                >
                  {leaveData?.totalLeavesTaken || 0}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: "flex" }}>
              <Box
                sx={{
                  flex: "0 0 auto",
                  width: "50%",
                  padding: "0px 12px",
                  marginBottom: "1.5rem",
                }}
              >
                <Typography variant="body2">Available Leaves</Typography>
                <Typography
                  variant="h6"
                  sx={{ fontSize: "18px", fontWeight: "600", color: "#202C4B" }}
                >
                  {leaveData?.totalRemaining || 0}
                </Typography>
              </Box>
              <Box
                sx={{
                  flex: "0 0 auto",
                  width: "50%",
                  padding: "0px 12px",
                  marginBottom: "1.5rem",
                }}
              >
                <Typography variant="body2">Request</Typography>
                <Typography
                  variant="h6"
                  sx={{ fontSize: "18px", fontWeight: "600", color: "#202C4B" }}
                >
                  {leaveData?.pendingRequests || 0}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: "flex" }}>
              <Box
                sx={{
                  flex: "0 0 auto",
                  width: "100%",
                  padding: "0px 12px",
                  marginBottom: "1.5rem",
                }}
              >
                <Typography sx={{ marginBottom: ".25rem" }} component="span">
                  Rejected Leaves
                </Typography>
                <Typography
                  variant="h6"
                  sx={{ fontSize: "18px", fontWeight: "600", color: "#202C4B" }}
                >
                  {leaveData?.totalRejected || 0}
                </Typography>
              </Box>
              {/* <Box
                sx={{
                  flex: "0 0 auto",
                  width: "50%",
                  padding: "0px 12px",
                  marginBottom: "1.5rem",
                }}
              >
                <Typography variant="body2">Loss of Pay</Typography>
                <Typography
                  variant="h6"
                  sx={{ fontSize: "18px", fontWeight: "600", color: "#202C4B" }}
                >
                  2
                </Typography>
              </Box> */}
            </Box>
          </Box>
          <Button
            variant="contained"
            fullWidth
            sx={{
              mt: 2,
              background: "#252a30 !important",
              border: "1px solid rgb(14.8054054054, 16.6, 18.3945945946)",
              color: "#FFF",
              borderRadius: "5px",
              padding: "0.5rem 0.85rem",
              fontSize: "14px",
              fontWeight: "500",
              transition: "all 0.5s",
              textTransform: "none",
            }}
            onClick={() => setOpenModal(true)}
          >
            Apply New Leave
          </Button>
        </CardContent>
      </Card>

      {/* AddEditLeaveEmployee Dialog */}
      <AddEditLeaveEmployee
        open={openModal}
        onClose={() => setOpenModal(false)}
        onSubmit={handleFormSubmit}
        employeeId={employeeId || ""}
      />
    </>
  );
};

export default LeaveSummary;

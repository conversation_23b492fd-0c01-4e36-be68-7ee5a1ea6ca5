"use client";

import React, { useState, useEffect } from "react";
import { Box, Typography, Paper, Avatar } from "@mui/material";
import { Circle } from "@mui/icons-material";
import useAuthStore from "@/store/authStore";
import { getUserById } from "@/app/services/users.service";
import Loader from "@/components/Loader/Loader";

interface UserData {
  _id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  email: string;
  phone: string;
  designationName?: string;
  departmentName?: string;
  joiningDate?: string;
  roName?: string;
  departmentId?: {
    _id: string;
    departmentName: string;
    isActive: boolean;
    isDeleted: boolean;
  };
  designationId?: {
    _id: string;
    designationName: string;
    isActive: boolean;
    isDeleted: boolean;
  };
}

const ProfileCard = () => {
  const { employeeId: authEmployeeId } = useAuthStore();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch user data when component mounts or employeeId changes
  useEffect(() => {
    const fetchUserData = async () => {
      if (authEmployeeId) {
        try {
          setLoading(true);
          const response = await getUserById(authEmployeeId);
          setUserData(response.user);
        } catch (error) {
          console.error("Failed to fetch user data:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchUserData();
  }, [authEmployeeId]);

  // Format the joining date (e.g., "15 Jan 2024")
  const formatJoiningDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
  };

  // Helper function to get department name
  const getDepartmentName = (): string => {
    if (!userData) return "N/A";
    
    if (userData.departmentId && typeof userData.departmentId === 'object') {
      return userData.departmentId.departmentName || "N/A";
    }
    
    return userData.departmentName || "N/A";
  };

  // Helper function to get designation name
  const getDesignationName = (): string => {
    if (!userData) return "N/A";
    
    if (userData.designationId && typeof userData.designationId === 'object') {
      return userData.designationId.designationName || "N/A";
    }
    
    return userData.designationName || "N/A";
  };

  if (loading) {
    return <Loader loading={loading} />; // Use your Loader component
  }

  if (!userData) {
    return <Typography>No user data available</Typography>; // Fallback if no data
  }

  return (
    <Paper
      elevation={3}
      sx={{
        marginBottom: "1.5rem",
        backgroundColor: "#FFF",
        transition: "all 0.5s ease-in-out",
        position: "relative",
        borderRadius: "5px",
        border: "1px solid #E5E7EB",
        boxShadow: "0px 1px 1px 1px rgba(198, 198, 198, 0.2)",
        color: "inherit",
        height: "100%",
      }}
    >
      <Box
        sx={{
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          backgroundColor: "#212529 !important",
          border: "1px solid #212529 !important",
          color: "#FFF",
        }}
      >
        <Box sx={{ color: "#FFF", display: "flex", alignItems: "center" }}>
          <Avatar
            alt={`${userData.firstName} ${userData.lastName}`}
            src={userData.avatar}
            sx={{
              width: 41,
              height: 41,
              borderRadius: "50%",
              marginRight: ".5rem",
            }}
          />
          <Box>
            <Typography
              sx={{
                color: "#FFF !important",
                fontSize: "16px",
                fontWeight: "600",
                marginBottom: ".25rem",
              }}
            >
              {`${userData.firstName} ${userData.lastName}`}
            </Typography>
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Typography sx={{ fontSize: "0.75rem" }}>
                {getDesignationName()}
              </Typography>
              <Typography
                component="span"
                sx={{
                  marginRight: ".25rem !important",
                  marginLeft: ".25rem !important",
                  color: "#F26522 !important",
                }}
              >
                <Circle sx={{ width: "8px", height: "8px" }} />
              </Typography>
              <Typography sx={{ fontSize: "0.75rem" }}>
                {getDepartmentName()}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>

      <Box sx={{ padding: "1.25rem" }}>
        <Box
          sx={{ display: "flex", alignItems: "center", marginBottom: "1rem" }}
        >
          <Box>
            <Typography
              variant="caption"
              sx={{ fontSize: "0.8125rem", marginBottom: ".25rem" }}
            >
              Phone Number
            </Typography>
            <Typography sx={{ color: "#111827 !important" }}>
              {userData.phone || "N/A"}
            </Typography>
          </Box>
        </Box>

        <Box
          sx={{ display: "flex", alignItems: "center", marginBottom: "1rem" }}
        >
          <Box>
            <Typography
              variant="caption"
              sx={{ fontSize: "0.8125rem", marginBottom: ".25rem" }}
            >
              Email Address
            </Typography>
            <Typography>{userData.email || "N/A"}</Typography>
          </Box>
        </Box>

        {/* <Box
          sx={{ display: "flex", alignItems: "center", marginBottom: "1rem" }}
        >
          <Box>
            <Typography
              variant="caption"
              sx={{ fontSize: "0.8125rem", marginBottom: ".25rem" }}
            >
              Report Office
            </Typography>
            <Typography>{userData.roName || "N/A"}</Typography>
          </Box>
        </Box> */}

        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Box>
            <Typography
              variant="caption"
              sx={{ fontSize: "0.8125rem", marginBottom: ".25rem" }}
            >
              Joined on
            </Typography>
            <Typography>{formatJoiningDate(userData.joiningDate)}</Typography>
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

export default ProfileCard;

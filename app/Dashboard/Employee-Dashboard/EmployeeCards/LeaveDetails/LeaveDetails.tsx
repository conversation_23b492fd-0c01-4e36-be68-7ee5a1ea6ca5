"use client";

import React, { useState, useEffect } from "react";
import "./LeaveDetails.scss";
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Menu,
  MenuItem,
} from "@mui/material";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from "recharts";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { Circle } from "@mui/icons-material";
import useAuthStore from "@/store/authStore";
import { getYearlyAttendanceSummary } from "@/app/services/attendance.service";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";

// Define interface for the API response structure
interface AttendanceSummary {
  onTime: number;
  late: number;
  absent: number;
  leave: number;
}

const LeaveDetails = () => {
  const employeeId = useAuthStore((state) => state.employeeId);
  const [attendanceData, setAttendanceData] =
    useState<AttendanceSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [year, setYear] = useState(new Date().getFullYear());
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleYearSelect = (selectedYear: number) => {
    setYear(selectedYear);
    setAnchorEl(null);
  };

  // Generate last 3 years including current year
  const years = [
    new Date().getFullYear(),
    new Date().getFullYear() - 1,
    new Date().getFullYear() - 2,
  ];

  useEffect(() => {
    const fetchAttendanceSummary = async () => {
      if (employeeId) {
        try {
          setLoading(true);
          const response = await getYearlyAttendanceSummary(employeeId, year);
          const summary = response.attendanceSummary;
          setAttendanceData({
            onTime: summary.onTime,
            late: summary.late,
            absent: summary.absent,
            leave: summary.leave,
          });
        } catch (error) {
          console.error("Failed to fetch attendance summary:", error);
          // toast.error("Failed to load attendance summary");
        } finally {
          setLoading(false);
        }
      }
    };

    fetchAttendanceSummary();
  }, [employeeId, year]);

  useEffect(() => {
    console.log("Attendance Data:", attendanceData);
  }, [attendanceData]);

  const chartData = attendanceData
    ? [
        { name: "On Time", value: attendanceData.onTime, color: "#003b47" },
        {
          name: "Late Attendance",
          value: attendanceData.late,
          color: "#00b969",
        },
        { name: "Work From Home", value: 0, color: "#ffa000" },
        { name: "Absent", value: attendanceData.absent, color: "#d50000" },
        { name: "Sick Leave", value: attendanceData.leave, color: "#ff5722" },
      ]
    : [
        { name: "On Time", value: 0, color: "#003b47" },
        { name: "Late Attendance", value: 0, color: "#00b969" },
        { name: "Work From Home", value: 0, color: "#ffa000" },
        { name: "Absent", value: 0, color: "#d50000" },
        { name: "Sick Leave", value: 0, color: "#ff5722" },
      ];

  if (loading) {
    return <Loader loading={loading} />;
  }

  return (
    <Card
      sx={{
        borderRadius: "5px",
        boxShadow: "0px 1px 1px 1px rgba(198, 198, 198, 0.2)",
        height: "100%",
      }}
    >
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Attendance Detail
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            className="header-year-calendar"
            startIcon={<CalendarMonthIcon sx={{ fontSize: "12px" }} />}
            size="small"
            variant="outlined"
            onClick={handleClick}
            aria-controls={open ? "year-menu" : undefined}
            aria-haspopup="true"
            aria-expanded={open ? "true" : undefined}
          >
            {year}
          </Button>
          <Menu
            id="year-menu"
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            MenuListProps={{
              "aria-labelledby": "year-button",
            }}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
          >
            {years.map((yearOption) => (
              <MenuItem
              className="menu-item"
                key={yearOption}
                onClick={() => handleYearSelect(yearOption)}
                selected={yearOption === year}
                sx={{
                  fontSize: "14px",
                  minHeight: "32px",
                  "&.Mui-selected": {
                    backgroundColor: "#F26522",
                    color: "#fff",
                    "&:hover": {
                      backgroundColor: "#d55a1d",
                    },
                  },
                }}
              >
                {yearOption}
              </MenuItem>
            ))}
          </Menu>
        </Box>
      </Box>
      <CardContent
        sx={{
          padding: "1.25rem",
          borderTop: "1px solid #E5E7EB",
        }}
      >
        <Box display="flex" gap={2} alignItems="center">
          <Box>
            {chartData.map((entry) => (
              <Box
                key={entry.name}
                display="flex"
                alignItems="center"
                gap={1}
                marginBottom="1rem"
              >
                <Circle style={{ color: entry.color, fontSize: "8px" }} />
                <Typography variant="body2">
                  <strong>{`${entry.value}`}</strong> {entry.name}
                </Typography>
              </Box>
            ))}
          </Box>
          <ResponsiveContainer width="70%" height={250}>
            <PieChart>
              <Pie
                data={chartData}
                innerRadius={50}
                outerRadius={70}
                dataKey="value"
                nameKey="name"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </Box>
      </CardContent>
    </Card>
  );
};

export default LeaveDetails;

import React from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
} from "@mui/material";

const BirthdayCard = () => {
  return (
    <Card
      sx={{
        borderRadius: 3,
        boxShadow: 3,
        backgroundImage: "url(/assets/card-bg-05.webp)",
        backgroundPosition: "top",
        backgroundRepeat: "no-repeat",
        backgroundSize: "contain",
        width: "100%",
        color: "white",
        mb: 2,
        backgroundColor: "#212529 !important",
        border: "1px solid #212529 !important",
      }}
    >
      <CardContent sx={{ textAlign: "center", padding: "1.25rem !important" }}>
        <Typography variant="h6">Team Birthday</Typography>
        <Avatar
          src="/avatar.jpg"
          alt="Andrew Jermia"
          sx={{ width: 58, height: 58, mt: 2, mx: "auto" }}
        />
        <Typography variant="body1" sx={{ mt: 1 }}>
          <PERSON>
        </Typography>
        <Typography variant="caption" display="block">
          iOS Developer
        </Typography>
        <Button
          variant="contained"
          sx={{
            mt: 2,
            backgroundColor: "#F26522",
            border: "1px solid #F26522",
            color: "#FFF",
            padding: "0.25rem 0.5rem",
            fontSize: "0.75rem",
            borderRadius: "5px",
            fontWeight: 500,
            textTransform: "none",
          }}
        >
          Send Wishes
        </Button>
      </CardContent>
    </Card>
  );
};

interface InfoCardProps {
  title: string;
  description: string;
  color: string;
  textColor: string;
}

const InfoCard = ({ title, description, color, textColor }: InfoCardProps) => {
  return (
    <Card
      sx={{
        borderRadius: 3,
        boxShadow: 3,
        backgroundColor: color,
        mb: 1,
      }}
    >
      <CardContent
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "1rem",
          color: textColor, // Fixed textColor usage
        }}
      >
        <Box>
          <Typography variant="body1">{title}</Typography>
          <Typography variant="caption">{description}</Typography>
        </Box>
        <Button
          variant="contained"
          size="small"
          sx={{
            backgroundColor: "#FFF",
            borderColor: "#FFF",
            color: "#111827",
            padding: "0.25rem 0.5rem",
            fontSize: "0.75rem",
            borderRadius: "5px",
            textTransform: "none",
          }}
        >
          View All
        </Button>
      </CardContent>
    </Card>
  );
};

const BirthdayAndHoliday = () => {
  return (
    <Box sx={{ height: "100%" }}>
      <BirthdayCard />
      <InfoCard
        title="Leave Policy"
        description="Last Updated: Today"
        color="#45858C"
        textColor="#FFF"
      />
      <InfoCard
        title="Next Holiday"
        description="Diwali, 15 Sep 2025"
        color="#FFCC00"
        textColor="#202C4B"
      />
    </Box>
  );
};

export default BirthdayAndHoliday;

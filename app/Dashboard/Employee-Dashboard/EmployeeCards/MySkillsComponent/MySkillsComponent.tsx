import React from "react";
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  IconButton,
  Button,
} from "@mui/material";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";

const skills = [
  { name: "Figma", updated: "15 May 2025", progress: 95, color: "#ff6d00" },
  { name: "HTML", updated: "12 May 2025", progress: 85, color: "#00c853" },
  { name: "CSS", updated: "12 May 2025", progress: 70, color: "#9c27b0" },
  { name: "Wordpress", updated: "15 May 2025", progress: 61, color: "#2196f3" },
  {
    name: "Javascript",
    updated: "13 May 2025",
    progress: 58,
    color: "#424242",
  },
];

const MySkillsComponent = () => {
  return (
    <Paper
      elevation={3}
      sx={{
        borderRadius: "5px",
        boxShadow: "0px 1px 1px 1px rgba(198, 198, 198, 0.2)",
        height: "100%",
      }}
    >
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            // marginBottom: ".5rem",
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          My Skills
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            startIcon={<CalendarMonthIcon />}
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
          >
            2024
          </Button>
        </Box>
      </Box>

      <Box
        sx={{
          padding: "1.25rem",
          borderTop: "1px solid #E5E7EB",
        }}
      >
        {skills.map((skill, index) => (
          <Box
            key={index}
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            mt={2}
            p={1}
            borderRadius={2}
            sx={{ backgroundColor: "#f5f5f5" }}
          >
            <Box>
              <Typography
                sx={{
                  marginBottom: ".25rem!important",
                  fontWeight: "500!important",
                  fontSize: "14px!important",
                  color: "#202C4B!important",
                }}
                variant="body1"
              >
                {skill.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Updated: {skill.updated}
              </Typography>
            </Box>
            <Box display="flex" alignItems="center">
              <CircularProgress
                variant="determinate"
                value={skill.progress}
                sx={{ color: skill.color, marginRight: "8px" }}
              />
              <Typography variant="body1" fontWeight="bold">
                {skill.progress}%
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Paper>
  );
};

export default MySkillsComponent;

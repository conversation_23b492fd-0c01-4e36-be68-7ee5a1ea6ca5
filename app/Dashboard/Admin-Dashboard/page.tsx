"use client";

import * as React from "react";
import { useEffect, useState } from "react";
import "./Admin-Dashboard.scss";
import { toast } from "react-toastify";
import {
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  Typography,
} from "@mui/material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import {
  Group,
  HomeOutlined,
  EventAvailable,
  Apartment,
  WorkOutline,
  EventBusy,
  Logout,
  Cancel,
  Gavel,
  PersonAddAlt1,
  ControlPoint,
} from "@mui/icons-material";
import Link from "next/link";
import CustomAttendanceCards from "./AdminCards/CustomAttendanceCards/CustomAttendanceCards";
import EmployeesByDepartment from "./AdminCards/EmployeesByDepartment/EmployeesByDepartment";
import EmployeeStatus from "./AdminCards/EmployeeStatus/EmployeeStatus";
import AttendanceOverview from "./AdminCards/AttendanceOverview/AttendanceOverview";
import ClockInOutComponent from "./AdminCards/ClockInOutComponent/ClockInOutComponent";
import JobsApplicantsComponent from "./AdminCards/JobsApplicantsComponent/JobsApplicantsComponent";
import EmployeesComponent from "./AdminCards/EmployeesComponent/EmployeesComponent";
import TodoComponent from "./AdminCards/TodoComponent/TodoComponent";
import SalesOverviewComponent from "./AdminCards/SalesOverviewComponent/SalesOverviewComponent";
import InvoicesComponent from "./AdminCards/InvoicesComponent/InvoicesComponent";
import ProjectsTable from "./AdminCards/ProjectsTable/ProjectsTable";
import RecentActivities from "./AdminCards/RecentActivities/RecentActivities";
import Schedules from "./AdminCards/Schedules/Schedules";
import Birthdays from "./AdminCards/Birthdays/Birthdays";
import TasksStatistics from "./AdminCards/TasksStatistics/TasksStatistics";
import useAuthStore from "@/store/authStore";
import { getUserById, getUsers } from "@/app/services/users.service";
import { getAttendanceByDate } from "@/app/services/attendance.service";
import useFetchLeaveData from "@/app/hooks/leaves/useFetchLeaveData";
import { getDepartments } from "@/app/services/department.service";
import { getAllDesignations } from "@/app/services/designation.service";
import {
  getAllResignations,
  getResignations,
} from "@/app/services/resignation.service";
import {
  getAllTerminations,
  getTermination,
} from "@/app/services/termination.service";
import { getAllPolicies } from "@/app/services/policies.service";
import Footer from "@/components/Footer/Footer";
import AddEditLeaveAdmin from "@/app/Attendance/leaves/leaves-admin/AddEditLeaveAdmin";
import { addLeave, updateLeave } from "@/app/services/leave/leave.service";
import { ROLE_GROUPS } from "@/app/constants/roles";
import useRoleAuth from "@/app/hooks/useRoleAuth";

interface UserData {
  _id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  email: string;
  departmentName?: string;
  designationId?: string;
}

interface LeaveAdminRecord {
  id: string;
  employee: string;
  leaveType: string;
  from: string;
  to: string;
  days: string;
  avatar: string;
  department: string;
  status: LeaveStatus;
  leaveTime?: string;
  reason?: string;
  approvedBy?: string | null;
}

type LeaveStatus = "Pending" | "Approved" | "Declined";

interface LeaveFormValues {
  empId: string;
  leaveType: string;
  fromDate: string | null;
  toDate: string | null;
  leaveTime: "Full Day" | "Half Day";
  noOfDays: number;
  remainingDays: number;
  reason: string;
}

interface AttendanceData {
  totalAttendance: number;
  totalEmployees : number;
  presentCount: number;
  presentPercentage: string;
  absentCount: number;
  absentPercentage: string;
  absentEmployees: {
    id: string;
    firstName: string;
    avatar: string;
    departmentName: string;
    designationName: string;
  }[];
  onLeaveCount: number;
  onLeavePercentage: string;
  lateLoginCount: number;
  latePercentage: string;
}

interface DepartmentData {
  totalDepartment: number;
  activeDepartment: number;
}

interface DesignationData {
  totalDesignation: number;
  activeDesignation: number;
}

interface ResignationData {
  totalResignation: number;
  activeResignation: number;
}

interface TerminationData {
  totalTermination: number;
  activeTermination: number;
}

interface PoliciesData {
  totalPolicies: number;
  activePolicies: number;
}

interface EmployeeData {
  totalNewJoiners: number;
  totalUsers: number;
  results: UserData[];
}

export default function AdminDashboard() {
  const { employeeId } = useAuthStore();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [allUsers, setAllUsers] = useState<UserData[]>([]); // New state for all users
  const [loading, setLoading] = useState(true);
  const [loadingLeaves, setLoadingLeaves] = useState(false);
  const [attendanceData, setAttendanceData] = useState<AttendanceData | null>(
    null
  );
  const [departmentData, setDepartmentData] = useState<DepartmentData | null>(
    null
  );
  const [openModal, setOpenModal] = useState(false);
  const [editModal, setEditModal] = useState(false);
  const [selectedRow, setSelectedRow] = useState<LeaveAdminRecord | null>(null);
  const [refresh, setRefresh] = useState(false);

  const [designationData, setDesignationData] =
    useState<DesignationData | null>(null);
  const [resignationData, setResignationData] =
    useState<ResignationData | null>(null);
  const [terminationData, setTerminationData] =
    useState<TerminationData | null>(null);
  const [policiesData, setPoliciesData] = useState<PoliciesData | null>(null);
  const [newJoinersData, setNewJoinersData] = useState<EmployeeData | null>(
    null
  );

  // const router = useRouter();

  // Restrict access to admin roles only (Admin, SuperAdmin, Manager, HR)
  useRoleAuth({
    allowedRoles: ROLE_GROUPS.ADMIN_ROLES,
    redirectTo: "/unauthorized",
  });

  // Use the useFetchLeaveData hook
  const [leaveData, total, leaveSummary] = useFetchLeaveData({
    setIsLoading: setLoadingLeaves,
    refresh,
    limit: 10,
    page: 1,
  });

  const [attendanceDateRange, setAttendanceDateRange] = useState({
    startDate: new Date().toISOString().split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  const handleAttendanceDateChange = (startDate: string, endDate: string) => {
    setAttendanceDateRange({ startDate, endDate });
    // You can also fetch new data here if needed
  };

  useEffect(() => {
    const fetchUserData = async () => {
      if (employeeId) {
        try {
          const response = await getUserById(employeeId);
          setUserData(response?.user);
        } catch (error) {
          console.error("Failed to fetch user data:", error);
        }
      }
    };

    const fetchAllUsers = async () => {
      try {
        const response = await getUsers();
        if (response && response.users?.results) {
          setAllUsers(response.users.results);
          setNewJoinersData({
            totalNewJoiners: response.users.newJoiners,
            totalUsers: response.users.total,
            results: response.users.results,
          });
        }
      } catch (error) {
        console.error("Failed to fetch all users:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
    fetchAllUsers();
  }, [employeeId]);

  useEffect(() => {
    const fetchAttendanceData = async () => {
      const todayDate = new Date().toISOString().split("T")[0];
      try {
        const response = await getAttendanceByDate(todayDate, todayDate);
        console.log("Attendance API Response:", response);
        if (response && response.attendenceSummary) {
          setAttendanceData({
            totalAttendance: response.attendenceSummary.totalAttendance,
            totalEmployees: response.attendenceSummary.totalEmployees,
            presentCount: response.attendenceSummary.present.count,
            presentPercentage: response.attendenceSummary.present.percentage,
            absentCount: response.attendenceSummary.absent.count,
            absentPercentage: response.attendenceSummary.absent.percentage,
            absentEmployees: response.attendenceSummary.absent.employees,
            onLeaveCount: response.attendenceSummary.onLeave.count,
            onLeavePercentage: response.attendenceSummary.onLeave.percentage,
            lateLoginCount:
              response.attendenceSummary.lateLogins?.current?.count || 0,
            latePercentage:
              response.attendenceSummary.lateLogins?.current?.percentage ||
              "0.00%",
          });
        }
      } catch (error) {
        console.error("Failed to fetch attendance data:", error);
      }
    };

    fetchAttendanceData();
  }, []);

  useEffect(() => {
    const fetchDepartmentData = async () => {
      try {
        const response = await getDepartments();
        console.log("Departments Response", response);
        if (response && response.departments) {
          setDepartmentData({
            totalDepartment: response.departments.total,
            activeDepartment: response.departments.activeCount,
          });
        }
      } catch (error) {
        console.error("Failed to fetch Departments Data:", error);
      }
    };
    fetchDepartmentData();
  }, []);

  useEffect(() => {
    const fetchDesignationData = async () => {
      try {
        const response = await getAllDesignations();
        if (response && response.designations) {
          setDesignationData({
            totalDesignation: response.designations.total,
            activeDesignation: response.designations.activeCount,
          });
        }
      } catch (error) {
        console.log("failed to fetch Designations Data:", error);
      }
    };
    fetchDesignationData();
  }, []);

  useEffect(() => {
    const fetchResignationData = async () => {
      try {
        const response = await getAllResignations();
        console.log("Resignations Response", response);
        if (response && response.resignations) {
          setResignationData({
            totalResignation: response.resignations.total,
            activeResignation: response.resignations.activeCount,
          });
        }
      } catch (error) {
        console.log("failed to fetch Resignations Data:", error);
      }
    };
    fetchResignationData();
  }, []);

  useEffect(() => {
    const fetchTerminationData = async () => {
      try {
        const response = await getAllTerminations();
        if (response && response.terminations) {
          setTerminationData({
            totalTermination: response.terminations.total,
            activeTermination: response.terminations.activeCount,
          });
        }
      } catch (error) {
        console.error("Failed to fetch Terminations Data:", error);
      }
    };
    fetchTerminationData();
  }, []);

  useEffect(() => {
    const fetchPoliciesData = async () => {
      try {
        const response = await getAllPolicies();
        console.log("Policiess Response", response);
        if (response && response.policies) {
          setPoliciesData({
            totalPolicies: response.policies.total,
            activePolicies: response.policies.activeCount,
          });
        }
      } catch (error) {
        console.error("Failed to fetch Policies Data:", error);
      }
    };
    fetchPoliciesData();
  }, []);

  const handleFormSubmit = async (values: LeaveFormValues) => {
    const leaveBody = {
      empId: values.empId,
      leaveType: values.leaveType,
      leaveFrom: values.fromDate,
      leaveTo: values.toDate,
      leaveTime: values.leaveTime || "N/A",
      remainingDays: values.remainingDays,
      leaveStatus: "Pending",
      reason: values.reason,
    };

    try {
      if (editModal && selectedRow) {
        await updateLeave(selectedRow.id, {
          ...leaveBody,
          leaveFrom: leaveBody.leaveFrom || "",
          leaveTo: leaveBody.leaveTo || "",
        });
        toast.success("Leave request updated successfully");
        setRefresh((prev) => !prev);
        setEditModal(false);
        setSelectedRow(null);
      } else {
        await addLeave({
          ...leaveBody,
          leaveFrom: leaveBody.leaveFrom || "",
          leaveTo: leaveBody.leaveTo || "",
        });
        toast.success("Leave request submitted successfully");
        setRefresh((prev) => !prev);
        setOpenModal(false);
      }
    } catch (err) {
      console.error(
        `Failed to ${editModal ? "update" : "submit"} leave request:`,
        err
      );
      // toast.error(
      //   `Failed to ${editModal ? "update" : "submit"} leave request. Please try again.`
      // );
    }
  };

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Dashboard", href: "" },
    { label: "Admin-Dashboard" },
  ];

  return (
    <>
      <Box className="admin-dashboard-container">
        <Box className="content">
          <Box className="admin-dashboard-header">
            <Box className="breadcrumbs-box">
              <Typography variant="h2">Dashboard</Typography>
              <BreadcrumbsComponent items={breadcrumbItems} />
            </Box>
          </Box>

          {/* Welcome Card  */}
          <Box sx={{ padding: "0px 12px" }}>
            <Card className="welcome-card">
              <CardContent
                className="welcome-card-content"
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  padding: "1.25rem !important",
                }}
              >
                <Box sx={{ display: "flex" }}>
                  <Avatar
                    alt={
                      userData
                        ? `${userData?.firstName || ""} ${userData?.lastName || ""}`
                        : "Admin"
                    }
                    src={userData?.avatar || ""}
                    sx={{ width: "3.6rem", height: "3.6rem" }}
                  />
                  <Box sx={{ marginLeft: "1rem" }}>
                    <Typography
                      variant="h3"
                      component="h2"
                      sx={{
                        marginBottom: ".5rem",
                        fontSize: "20px",
                        fontWeight: 600,
                        color: "#202C4B",
                      }}
                    >
                      {loading
                        ? "Loading..."
                        : `Welcome Back, ${userData?.firstName || "User"}`}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      You have &nbsp;
                      <Link
                        style={{
                          color: "#F26522",
                          textDecoration: "underline",
                        }}
                        href="/Attendance/leaves/leaves-admin/"
                      >
                        {loadingLeaves ? "..." : leaveSummary.pendingRequests}{" "}
                      </Link>
                      &nbsp; Pending Approvals & &nbsp;
                      <Link
                        style={{
                          color: "#F26522",
                          textDecoration: "underline",
                        }}
                        href="/Attendance/leaves/leaves-admin/"
                      >
                        {loadingLeaves ? "..." : leaveSummary.totalLeaves}{" "}
                      </Link>
                      &nbsp; Leave Requests
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ display: "flex", gap: 2 }}>
                  {/* <Button
                sx={{
                  textTransform: "none",
                  backgroundColor: "#3B7080",
                  border: "1px solid #3B7080",
                  color: "#FFF",
                  padding: "0.35rem 0.85rem",
                  fontSize: "0.813rem",
                  borderRadius: "5px",
                  transition: "all 0.5s",
                  fontWeight: "500",
                  marginRight: ".5rem",
                  marginBottom: ".5rem",
                }}
              >
                Add Project
              </Button> */}
                  <Button
                    sx={{
                      textTransform: "none",
                      backgroundColor: "#F26522",
                      border: "1px solid #F26522",
                      color: "#FFF",
                      padding: "0.35rem 0.85rem",
                      fontSize: "0.813rem",
                      fontWeight: "500",
                      gap: "8px",
                    }}
                    onClick={() => setOpenModal(true)}
                  >

                    <ControlPoint sx={{ fontSize: "14px" }} />
                    Add Leave
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Box>

          <Box className="Counter-Graph" sx={{ display: "flex" }}>
            {/* Attendance Cards */}
            <Box
              className="attendance-cards"
              style={{
                display: "flex",
                flexWrap: "wrap",
                maxWidth: "100%",
              }}
            >
              <Box className="cards" sx={{ display: "flex", flexWrap: "wrap" }}>
                <CustomAttendanceCards
                  title="Attendance Overview"
                  current={attendanceData?.presentCount || 0}
                  total={attendanceData?.totalEmployees || 0}
                  // change={0.0}
                  // positive={true}
                  color="orange"
                  icon={
                    <Avatar
                      className="avatar-style"
                      sx={{
                        backgroundColor: "#F26522 !important",
                        border: "1px solid #F26522 !important",
                      }}
                    >
                      <EventAvailable />
                    </Avatar>
                  }
                  linkPath="/Attendance/attendance-admin"
                />
                <CustomAttendanceCards
                  title="Departments"
                  current={departmentData?.activeDepartment || 0}
                  total={departmentData?.totalDepartment || 0}
                  // change={0.0}
                  // positive={true}
                  color="teal"
                  icon={
                    <Avatar
                      className="avatar-style"
                      sx={{
                        backgroundColor: "#3B7080 !important",
                        border: "1px solid #3B7080 !important",
                      }}
                    >
                      <Apartment />
                    </Avatar>
                  }
                  linkPath="/employees/department"
                />
                <CustomAttendanceCards
                  title="No of Designations"
                  current={designationData?.activeDesignation || 0}
                  total={designationData?.totalDesignation || 0}
                  // change={0.0}
                  // positive={true}
                  color="blue"
                  icon={
                    <Avatar
                      className="avatar-style"
                      sx={{
                        backgroundColor: "#1B84FF !important",
                        border: "1px solid #1B84FF !important",
                      }}
                    >
                      <WorkOutline />
                    </Avatar>
                  }
                  linkPath="/employees/designation"
                />
                <CustomAttendanceCards
                  title="No of Resignations"
                  current={resignationData?.activeResignation || 0}
                  total={resignationData?.totalResignation || 0}
                  // change={0.0}
                  // positive={true}
                  color="blue"
                  icon={
                    <Avatar
                      className="avatar-style"
                      sx={{
                        backgroundColor: "#FD3995 !important",
                        border: "1px solid #FD3995 !important",
                      }}
                    >
                      <Logout />
                    </Avatar>
                  }
                  linkPath="/resignation"
                />
                <CustomAttendanceCards
                  title="No of Terminations"
                  total={terminationData?.totalTermination || 0}
                  current={terminationData?.activeTermination || 0}
                  // change={0.0}
                  // positive={true}
                  color="purple"
                  icon={
                    <Avatar
                      className="avatar-style"
                      sx={{
                        backgroundColor: "#AB47BC !important",
                        border: "1px solid #AB47BC !important",
                      }}
                    >
                      <Cancel />
                    </Avatar>
                  }
                  linkPath="/termination"
                />
                <CustomAttendanceCards
                  title="Active Leaves"
                  current={leaveSummary?.pendingRequests || 0}
                  total={leaveSummary?.totalLeaves || 0}
                  // change={0.0}
                  // positive={true}
                  color="blue"
                  icon={
                    <Avatar
                      className="avatar-style"
                      sx={{
                        backgroundColor: "#E70D0D !important",
                        border: "1px solid #E70D0D !important",
                      }}
                    >
                      <EventBusy />
                    </Avatar>
                  }
                  linkPath="/Attendance/leaves/leaves-admin"
                />
                <CustomAttendanceCards
                  title="Total Policies"
                  total={policiesData?.totalPolicies || 0}
                  current={policiesData?.activePolicies || 0}
                  // change={0.0}
                  // positive={true}
                  color="blue"
                  icon={
                    <Avatar
                      className="avatar-style"
                      sx={{
                        backgroundColor: "#03C95A !important",
                        border: "1px solid #03C95A !important",
                      }}
                    >
                      <Gavel />
                    </Avatar>
                  }
                  linkPath="/employees/policies"
                />
                <CustomAttendanceCards
                  title="New Joiners"
                  current={newJoinersData?.totalNewJoiners || 0}
                  total={newJoinersData?.totalUsers || 0}
                  // change={0.0}
                  // positive={true}
                  color="blue"
                  icon={
                    <Avatar
                      className="avatar-style"
                      sx={{
                        backgroundColor: "#212529 !important",
                        border: "1px solid #212529 !important",
                      }}
                    >
                      <PersonAddAlt1 />
                    </Avatar>
                  }
                  linkPath="/employees/employeesList"
                />
              </Box>
            </Box>

            {/* Employees By Department */}
            <Box
              className="employees-by-department"
              sx={{
                display: "flex !important",
                flex: "0 0 auto",
                width: "45.33333333%",
                maxWidth: "100%",
                padding: "0px 12px",
              }}
            >
              <EmployeesByDepartment />
            </Box>
          </Box>

          <Box
            className="attendance-row"
            sx={{ display: "flex", mb: "1.5rem" }}
          >
            <Box
              className="employee-status"
              sx={{
                flex: "0 0 auto",
                width: "33.33333333%",
                padding: "0px 12px",
              }}
            >
              <EmployeeStatus />
            </Box>
            <Box
              className="Attendance-Overview"
              sx={{
                flex: "0 0 auto",
                width: "33.33333333%",
                padding: "0px 12px",
              }}
            >
              <AttendanceOverview
                totalAttendance={attendanceData?.totalEmployees || 0}
                attendanceData={[
                  {
                    id: "Present",
                    value: parseFloat(attendanceData?.presentPercentage || "0"),
                    color: "#03C95A",
                  },
                  {
                    id: "Late",
                    value: parseFloat(attendanceData?.latePercentage || "0"),
                    color: "#3B7080",
                  },
                  {
                    id: "On Leave",
                    value: parseFloat(attendanceData?.onLeavePercentage || "0"),
                    color: "#FFC107",
                  },
                  {
                    id: "Absent",
                    value: parseFloat(attendanceData?.absentPercentage || "0"),
                    color: "#E70D0D",
                  },
                ]}
                absentees={(attendanceData?.absentEmployees || []).map(
                  (employee) => ({
                    name: employee?.firstName || "Unknown",
                    image: employee?.avatar || "/assets/users/default.jpg",
                  })
                )}
                onDateRangeChange={handleAttendanceDateChange}
                initialDateRange={attendanceDateRange}
              />
            </Box>
            <Box
              className="clock-in-out"
              sx={{
                flex: "0 0 auto",
                width: "33.33333333%",
                padding: "0px 12px",
              }}
            >
              <ClockInOutComponent />
            </Box>
          </Box>

          <Box className="employee-row" sx={{ display: "flex", mb: "1.5rem" }}>
            {/* <Box
            sx={{
              flex: "0 0 auto",
              width: "33.33333333%",
              padding: "0px 12px",
            }}
          >
            <JobsApplicantsComponent />
          </Box> */}

            <Box
              className="employees-component"
              sx={{
                flex: "0 0 auto",
                width: "33.33333333%",
                padding: "0px 12px",
              }}
            >
              <EmployeesComponent />
            </Box>

            {/* <Box
            sx={{
              flex: "0 0 auto",
              width: "33.33333333%",
              padding: "0px 12px",
            }}
          >
            <TodoComponent />
          </Box> */}
          </Box>

          <Box sx={{ display: "flex", mb: "1.5rem" }}>
            {/* <Box
            sx={{
              flex: "0 0 auto",
              width: "58.33333333%",
              padding: "0px 12px",
            }}
          >
            <SalesOverviewComponent />
          </Box> */}

            {/* <Box
            sx={{
              flex: "0 0 auto",
              width: "41.66666667%",
              padding: "0px 12px",
            }}
          >
            <InvoicesComponent />
          </Box> */}
          </Box>

          <AddEditLeaveAdmin
            open={openModal || editModal}
            onClose={() => {
              setOpenModal(false);
              setEditModal(false);
              setSelectedRow(null);
            }}
            onSubmit={handleFormSubmit}
            initialValues={
              selectedRow
                ? {
                    empId:
                      leaveData?.find((l) => l._id === selectedRow.id)
                        ?.employeeId || "",
                    leaveType: selectedRow.leaveType,
                    fromDate:
                      selectedRow.from.split("T")[0] || selectedRow.from,
                    toDate: selectedRow.to.split("T")[0] || selectedRow.to,
                    leaveTime:
                      selectedRow.leaveTime === "N/A"
                        ? "Full Day"
                        : (selectedRow.leaveTime as "Full Day" | "Half Day"),
                    noOfDays: parseInt(selectedRow.days, 10),
                    remainingDays: 0,
                    reason: selectedRow.reason || "",
                  }
                : undefined
            }
            userData={allUsers.map((user) => ({
              ...user,
              firstName: user.firstName || "",
              lastName: user.lastName || "",
              designationId: user.designationId || "",
            }))}
          />
        </Box>
      </Box>
    </>
  );
}

.admin-dashboard-container {
    .content {
        padding: 24px;

        .admin-dashboard-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;

            .breadcrumbs-box {
                h2 {
                    font-size: 24px;
                    font-weight: 700;
                    color: #202C4B;
                }
            }


        }

        .welcome-card {
            margin-bottom: 1.5rem;
            // background-color: #FFF;
            transition: all 0.5sease-in-out;
            position: relative;
            border-radius: 5px;
            // border: 1px solid #E5E7EB;
            // box-shadow: 0px 1px 1px 1px rgba(198, 198, 198, 0.2);
            color: inherit;
            box-shadow: none;
        }

        .avatar-style {
            position: relative;
            height: 2.625rem;
            width: 2.625rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: #FFF;
            font-weight: 500;
        }


    }
}

@media screen and (min-width: 1200px) and (max-width: 1600px) {
    .Counter-Graph {
        display: flex;
        flex-direction: column;
    }

    .employees-by-department {
        width: 100% !important;
    }

    .attendance-row {
        display: flex;
        flex-direction: column;
        gap: 24px !important;

        .employee-status {
            width: 100% !important;
        }

        .Attendance-Overview {
            width: 100% !important;
        }

        .clock-in-out {
            width: 100% !important;
        }
    }

    .employee-row {
        .employees-component {
            width: 100% !important;
        }
    }

}

@media screen and (max-width: 1200px) {

    .Counter-Graph {
        display: flex;
        flex-direction: column;
    }

    .employees-by-department {
        width: 100% !important;
    }

    .attendance-row {
        display: flex;
        flex-direction: column;
        gap: 24px !important;


        .employee-status {
            width: 100% !important;
        }

        .Attendance-Overview {
            width: 100% !important;
        }

        .clock-in-out {
            width: 100% !important;
        }
    }

    .employee-row {
        .employees-component {
            width: 100% !important;
        }
    }
}

@media screen and (max-width: 768px) {

    .welcome-card {
        .welcome-card-content {
            display: flex;
            flex-direction: column;
        }
    }

    .Counter-Graph {
        .attendance-cards {
            .cards {
                display: flex;
                flex-direction: column;
                flex-wrap: nowrap;
                width: 100%;

                .custom-attendance-card {
                    width: 100% !important;
                    // margin-bottom: 1rem;
                }
            }
        }
    }
}
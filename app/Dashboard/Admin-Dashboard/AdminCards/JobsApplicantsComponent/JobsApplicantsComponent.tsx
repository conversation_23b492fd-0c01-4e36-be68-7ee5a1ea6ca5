import React, { useState } from "react";
import "./JobsApplicantsComponent.scss";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  Button,
  Tabs,
  Tab,
  <PERSON>,
  Stack,
  IconButton,
} from "@mui/material";
import { OpenInNew } from "@mui/icons-material";

const jobOpenings = [
  {
    title: "Senior IOS Developer",
    openings: 25,
    iconImages: "/assets/apple.svg",
  },
  {
    title: "Junior PHP Developer",
    openings: 20,
    iconImages: "/assets/php.svg",
  },
  {
    title: "Junior React Developer",
    openings: 30,
    iconImages: "/assets/react.svg",
  },
  {
    title: "Senior Laravel Developer",
    openings: 40,
    iconImages: "/assets/laravel-icon.svg",
  },
];

const jobApplicants = [
  {
    name: "<PERSON>",
    experience: "5+ Years",
    location: "USA",
    role: "UI/UX Designer",
    bgcolor: "#3B7080",
  },
  {
    name: "<PERSON>",
    experience: "4+ Years",
    location: "USA",
    role: "Python Developer",
    bgcolor: "#1B84FF",
  },
  {
    name: "<PERSON>",
    experience: "6+ Years",
    location: "USA",
    role: "Android Developer",
    bgcolor: "#FD3995",
  },
  {
    name: "Doglas Martini",
    experience: "2+ Years",
    location: "USA",
    role: "React Developer",
    bgcolor: "#AB47BC",
  },
];

const JobsApplicantsComponent = () => {
  const [tab, setTab] = useState(0);

  const handleTabChange = (
    event: React.SyntheticEvent,
    newValue: number
  ): void => {
    setTab(newValue);
  };

  return (
    <Card sx={{ borderRadius: 2, boxShadow: 3, height: "100%" }}>
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            // marginBottom: ".5rem",
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Jobs Applicants
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
          >
            View All
          </Button>
        </Box>
      </Box>
      <CardContent
        sx={{
          padding: "1.5rem",
          borderTop: "1px solid #E5E7EB",
        }}
      >
        <Tabs className="tabs" value={tab} onChange={handleTabChange} centered>
          <Tab className="tab" label="Openings" />
          <Tab className="tab" label="Applicants" />
        </Tabs>

        {tab === 0 && (
          <Box mt={2}>
            {jobOpenings.map((job, index) => (
              <Stack
                key={index}
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                p={1}
              >
                <Box sx={{ display: "flex" }}>
                  <Avatar
                    sx={{ borderRadius: "4px", width: "20px", height: "20px" }}
                    src={job.iconImages}
                  />
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      marginLeft: ".5rem ",
                    }}
                  >
                    <Typography
                      sx={{
                        color: "#111827",
                        cursor: "pointer",
                        textDecoration: "none",
                        WebkitTransition: "all 0.5sease",
                        MsTransition: "all 0.5s ease",
                        transition: "all 0.5sease",
                        fontWeight: "500",
                      }}
                      variant="body1"
                    >
                      {job.title}
                    </Typography>
                    <Typography sx={{ fontSize: "0.75rem" }} variant="body2">
                      No of Openings: {job.openings}
                    </Typography>
                  </Box>
                </Box>
                <IconButton size="small">
                  <OpenInNew fontSize="small" />
                </IconButton>
              </Stack>
            ))}
          </Box>
        )}

        {tab === 1 && (
          <Box mt={2}>
            {jobApplicants.map((applicant, index) => (
              <Stack
                key={index}
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                p={1}
              >
                <Stack direction="row" spacing={1} alignItems="center">
                  <Avatar>{applicant.name.charAt(0)}</Avatar>
                  <Box>
                    <Typography variant="subtitle1">
                      {applicant.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Exp: {applicant.experience} • {applicant.location}
                    </Typography>
                  </Box>
                </Stack>
                <Chip
                  sx={{
                    fontSize: "10px",
                    fontWeight: "500",
                    padding: "0px 5px",
                    lineHeight: "18px",
                    backgroundColor: applicant.bgcolor,
                    color: "#fff",
                    borderRadius: "4px",
                  }}
                  label={applicant.role}
                  size="small"
                />
              </Stack>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default JobsApplicantsComponent;

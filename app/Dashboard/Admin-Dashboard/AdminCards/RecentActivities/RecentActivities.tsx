import React from "react";
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  Avatar,
  ListItemText,
  IconButton,
  Chip,
  <PERSON>ton,
} from "@mui/material";
import {
  NotificationsNone as ActivityIcon,
  ArrowForward as ViewAllIcon,
  Add as AddIcon,
  Comment as CommentIcon,
  CheckCircle as ApproveIcon,
  Lock as RequestIcon,
  Download as DownloadIcon,
  DoneAll as CompleteIcon,
} from "@mui/icons-material";

const RecentActivities = () => {
  const activities = [
    {
      user: "<PERSON> Morgan",
      action: "Added New Project HRMS Dashboard",
      time: "05:30 PM",
      icon: <AddIcon color="primary" />,
    },
    {
      user: "Jay Ze",
      action: "Commented on Uploaded Document",
      time: "05:00 PM",
      icon: <CommentIcon color="info" />,
    },
    {
      user: "<PERSON>",
      action: "Approved Task Projects",
      time: "05:30 PM",
      icon: <ApproveIcon color="success" />,
    },
    {
      user: "<PERSON>",
      action: "Requesting Access to Module Tickets",
      time: "06:00 PM",
      icon: <RequestIcon color="warning" />,
    },
    {
      user: "<PERSON> Zeen",
      action: "Downloaded App Reports",
      time: "06:30 PM",
      icon: <DownloadIcon color="secondary" />,
    },
    {
      user: "Hendry Daniel",
      action: "Completed New Project HMS",
      time: "05:30 PM",
      icon: <CompleteIcon color="success" />,
    },
  ];

  return (
    <Card sx={{ borderRadius: 2, boxShadow: 3, height: "100%" }}>
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            // marginBottom: ".5rem",
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Recent Activities
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
          >
            View All
          </Button>
        </Box>
      </Box>
      <CardContent
        sx={{
          padding: "1.25rem",
          borderTop: "1px solid #E5E7EB",
        }}
      >
        <List sx={{ width: "100%", bgcolor: "background.paper" }}>
          {activities.map((activity, index) => (
            <React.Fragment key={index}>
              <ListItem alignItems="flex-start" sx={{ }}>
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: "grey.100" }}>{activity.icon}</Avatar>
                </ListItemAvatar>
                <ListItemText
                  sx={{ display: "flex", justifyContent: "space-between" }}
                  primary={
                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                      <Typography
                        sx={{
                          color: "#111827",
                          cursor: "pointer",
                          textDecoration: "none",
                          transition: "all 0.5sease",
                          fontSize: "14px",
                          fontWeight: "600",
                        }}
                        component="span"
                        fontWeight="bold"
                      >
                        {activity.user}
                      </Typography>
                      <Typography
                        sx={{ fontSize: "0.8125rem" }}
                        variant="body2"
                        color="text.primary"
                      >
                        {activity.action}
                      </Typography>
                    </Box>
                  }
                  secondary={
                    <Typography
                      sx={{}}
                      variant="caption"
                      color="text.secondary"
                    >
                      {activity.time}
                    </Typography>
                  }
                />
              </ListItem>
              {/* {index < activities.length - 1 && (
                <Divider variant="inset" component="li" />
              )} */}
            </React.Fragment>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

export default RecentActivities;

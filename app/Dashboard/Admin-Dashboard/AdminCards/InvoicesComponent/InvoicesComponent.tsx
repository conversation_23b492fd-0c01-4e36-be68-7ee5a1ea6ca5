import React from "react";
import {
  Box,
  Typography,
  Paper,
  Button,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
} from "@mui/material";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";

const invoices = [
  {
    name: "Redesign Website",
    id: "#INVO02",
    company: "Logistics",
    payment: 3560,
    status: "Unpaid",
    avatar: "https://i.pravatar.cc/50?img=1",
  },
  {
    name: "Module Completion",
    id: "#INVO05",
    company: "Yip Corp",
    payment: 4175,
    status: "Unpaid",
    avatar: "https://i.pravatar.cc/50?img=2",
  },
  {
    name: "Change on Emp Module",
    id: "#INVO03",
    company: "Ignis LLP",
    payment: 6985,
    status: "Unpaid",
    avatar: "https://i.pravatar.cc/50?img=3",
  },
  {
    name: "Changes on the Board",
    id: "#INVO02",
    company: "Ignis LLP",
    payment: 1457,
    status: "Unpaid",
    avatar: "https://i.pravatar.cc/50?img=4",
  },
  {
    name: "Hospital Management",
    id: "#INVO06",
    company: "HCL Corp",
    payment: 6458,
    status: "Paid",
    avatar: "https://i.pravatar.cc/50?img=5",
  },
];

const InvoicesComponent = () => {
  return (
    <Paper elevation={3} sx={{ borderRadius: 2, boxShadow: 3, height: "100%" }}>
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            // marginBottom: ".5rem",
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Invoices
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            startIcon={<CalendarTodayIcon />}
            size="small"
            variant="outlined"
          >
            This Week
          </Button>
        </Box>
      </Box>

      <List sx={{ padding: "1.25rem", paddingTop: ".5rem" }}>
        {invoices.map((invoice, index) => (
          <ListItem
            key={index}
            sx={{ display: "flex", justifyContent: "space-between" }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                minWidth: "250px",
                maxWidth: "250px",
              }}
            >
              <ListItemAvatar>
                <Avatar src={invoice.avatar} />
              </ListItemAvatar>
              <ListItemText
                primary={invoice.name}
                secondary={`${invoice.id} • ${invoice.company}`}
              />
            </Box>
            <Box textAlign="right">
              <Typography sx={{ fontSize: "0.8125rem" }} variant="body1">
                Payment
              </Typography>
              <Typography
                sx={{
                  fontWeight: "500 !important",
                  fontSize: "14px",
                  color: "#202C4B",
                }}
                variant="h6"
                color="primary"
              >
                ${invoice.payment}
              </Typography>
            </Box>
            <Chip
              label={invoice.status}
              size="small"
              sx={{
                color: invoice.status === "Paid" ? "#03C95A" : "#E70D0D",
                backgroundColor:
                  invoice.status === "Paid" ? "#D2F5E1" : "#FAE7E7",
                fontSize: "10px",
                fontWeight: "500",
                padding: "0px 5px",
                lineHeight: "18px",
                borderRadius: "4px",
              }}
            />
          </ListItem>
        ))}
        <Box sx={{ display: "flex", width: "100%", padding: "0px 1.5rem" }}>
          <Button
            sx={{
              width: "100%",
              alignItems: "center",
              textTransform: "none",
              backgroundColor: "#F8F9FA",
              border: "1px solid #F8F9FA",
              color: "#111827",
              padding: "0.35rem 0.85rem",
              fontSize: "0.813rem",
            }}
            variant="contained"
          >
            View All
          </Button>
        </Box>
      </List>
    </Paper>
  );
};

export default InvoicesComponent;

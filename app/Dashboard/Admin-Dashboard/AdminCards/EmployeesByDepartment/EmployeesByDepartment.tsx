import React, { useState, useEffect } from "react";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import { <PERSON><PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { PieChart } from "@mui/x-charts/PieChart";
import { Box, Card, CardContent, Typography, Button } from "@mui/material";
import { getDepartmentUserCount } from "@/app/services/users.service";
import useFetchDepartmentData from "@/app/hooks/department/useFetchDepartmentData";
import Loader from "@/components/Loader/Loader";

// First, update the color palette function to use the same colors consistently
const getColorFromPalette = (index: number) => {
  const colors = [
    "#2196f3", // blue
    "#f44336", // red
    "#4caf50", // green
    "#ff9800", // orange
    "#9c27b0", // purple
    "#795548", // brown
    "#607d8b", // grey
    "#3f51b5", // indigo
    "#009688", // teal
    "#ffc107", // amber
    "#e91e63", // pink
    "#673ab7", // deep purple
  ];
  return colors[index % colors.length];
};

const EmployeesByDepartment = () => {
  const [departmentData, setDepartmentData] = useState<
    { department: string; employees: number }[]
  >([]);
  const [loading, setLoading] = useState(true);

  // Use the custom hook to fetch all departments
  const [allDepartments] = useFetchDepartmentData({
    setIsLoading: setLoading,
    refresh: false,
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch employee count data
        const userCountResponse = await getDepartmentUserCount();
        const userCountData = userCountResponse.userCount || [];

        if (!allDepartments) return;

        // Create a map of employee counts by department name from getDepartmentUserCount
        const employeeCountMap: Map<string, number> = new Map(
          userCountData.map(
            (item: { departmentName: string; numberOfEmployees: number }) => [
              item.departmentName,
              item.numberOfEmployees,
            ]
          )
        );

        // Transform all departments from useFetchDepartmentData, matching with employee counts
        const transformedData = allDepartments.map((dept) => ({
          department: dept.departmentName,
          employees: Number(employeeCountMap.get(dept.departmentName) || 0), // Use count from API or 0
        }));

        // Ensure we have at least one department with employees for the chart to render
        if (
          transformedData.length > 0 &&
          transformedData.every((item) => item.employees === 0)
        ) {
          // If all departments have 0 employees, add a dummy value to the first one
          // This ensures the chart will render
          transformedData[0].employees = 1;
          console.log("Added dummy value to ensure chart renders");
        }

        setDepartmentData(transformedData);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching department data:", error);
        // If API fails, show all departments with 0 employees
        if (allDepartments) {
          const fallbackData = allDepartments.map((dept) => ({
            department: dept.departmentName,
            employees: 0,
          }));

          // Add a dummy value to ensure chart renders
          if (fallbackData.length > 0) {
            fallbackData[0].employees = 1;
          }

          setDepartmentData(fallbackData);
        }
        setLoading(false);
      }
    };

    fetchData();
  }, [allDepartments]);

  return (
    <Card
      sx={{
        marginBottom: "1.5rem",
        backgroundColor: "#FFF",
        transition: "all 0.5s ease-in-out",
        position: "relative",
        borderRadius: "5px",
        border: "1px solid #E5E7EB",
        boxShadow: "0px 1px 1px 1px rgba(198, 198, 198, 0.2)",
        color: "inherit",
        flex: "1 1 auto !important",
        // width: "100%",
      }}
    >
      {/* card header */}
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          alignItems: "center",
          justifyContent: "space-between !important",
          flexWrap: "wrap",
          display: "flex",
          borderBottom: "1px solid #E5E7EB",
        }}
      >
        <Typography variant="h6" className="dialog-heading-text">
          Employees By Department
        </Typography>
      </Box>

      <CardContent
        sx={{ padding: "1.5rem", flex: "1 1 auto", height: "350px" }}
      >
        {loading ? (
          <Loader loading={loading} />
        ) : departmentData.length > 0 &&
          departmentData.some((item) => item.employees > 0) ? (
          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Box sx={{ width: "55%" }}>
              <PieChart
                series={[
                  {
                    data: departmentData
                      .filter((item) => item.employees > 0) // Only include departments with employees
                      .sort((a, b) => b.employees - a.employees)
                      .map((item, index) => ({
                        id: item.department,
                        value: item.employees,
                        label: item.department,
                        color: getColorFromPalette(index),
                      })),
                    innerRadius: 30,
                    outerRadius: 100,
                    paddingAngle: 1,
                    cornerRadius: 5,
                    startAngle: 0,
                    endAngle: 360,
                    cx: 150,
                    cy: 150,
                  },
                ]}
                height={300}
                width={400}
                margin={{ top: 10, bottom: 10, left: 10, right: 10 }}
                slotProps={{
                  legend: {
                    hidden: true, // Hide default legend
                  },
                }}
              />
            </Box>
            <Box
              sx={{
                width: "45%",
                display: "flex",
                flexDirection: "column",
                gap: 1,
                padding: 2,
                maxHeight: 280,
                overflowY: "auto",
                "&::-webkit-scrollbar": {
                  width: "6px",
                },
                "&::-webkit-scrollbar-track": {
                  background: "#f1f1f1",
                  borderRadius: "10px",
                },
                "&::-webkit-scrollbar-thumb": {
                  background: "#c1c1c1",
                  borderRadius: "10px",
                },
                "&::-webkit-scrollbar-thumb:hover": {
                  background: "#a8a8a8",
                },
              }}
            >
              {/* Display all departments in the legend */}
              {departmentData
                .sort((a, b) => b.employees - a.employees)
                .map((item, index) => (
                  <Box
                    key={item.department}
                    sx={{
                      display: "flex",
                      alignItems: "flex-start",
                      gap: 1,
                      fontSize: "0.875rem",
                      minHeight: "24px",
                      marginBottom: "4px",
                    }}
                  >
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: "50%",
                        bgcolor: `${getColorFromPalette(index)}`,
                        flexShrink: 0,
                        marginTop: "3px",
                      }}
                    />
                    <Typography
                      variant="body2"
                      sx={{
                        wordBreak: "break-word",
                        lineHeight: "1.2",
                      }}
                    >
                      {item.department} ({item.employees})
                    </Typography>
                  </Box>
                ))}
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100%",
            }}
          >
            <Typography variant="body1" color="text.secondary">
              {departmentData.length === 0
                ? "No departments available"
                : "No employees assigned to departments"}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default EmployeesByDepartment;

import React, { useState, useEffect, useMemo } from "react";
import "./AttendanceOverview.scss";
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Avatar,
  AvatarGroup,
  Menu,
  MenuItem,
} from "@mui/material";
import { Pie<PERSON>hart } from "@mui/x-charts";
import Link from "next/link";
import { getAttendanceByDate } from "@/app/services/attendance.service";


interface Absentee {
  name: string;
  image: string;
}

interface AttendanceData {
  totalAttendance: number;
  totalEmployees: number;
  presentCount: number;
  presentPercentage: string;
  absentCount: number;
  absentPercentage: string;
  absentEmployees: {
    id: string;
    firstName: string;
    avatar: string;
    departmentName: string;
    designationName: string;
  }[];
  onLeaveCount: number;
  onLeavePercentage: string;
  lateLoginCount: number;
  latePercentage: string;
}

interface AttendanceOverviewProps {
  totalAttendance: number;
  attendanceData: { id: string; value: number; color: string }[];
  absentees: Absentee[];
  onDateRangeChange?: (startDate: string, endDate: string) => void;
  initialDateRange?: { startDate: string; endDate: string };
}

const AttendanceOverview = ({
  totalAttendance,
  attendanceData: initialAttendanceData,
  absentees: initialAbsentees,
  onDateRangeChange,
  initialDateRange,
}: AttendanceOverviewProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTime, setSelectedTime] = useState("Today");
  const [attendanceInfo, setAttendanceInfo] = useState<AttendanceData | null>(null);

  // Initialize with provided date range or default to today
  const [currentDateRange, setCurrentDateRange] = useState({
    startDate: initialDateRange?.startDate || new Date().toISOString().split('T')[0],
    endDate: initialDateRange?.endDate || new Date().toISOString().split('T')[0]
  });

  // Create dynamic attendance data based on fetched info
  const currentAttendanceData = useMemo(() => {
    if (!attendanceInfo) return initialAttendanceData;
    
    return [
      {
        id: "Present",
        value: parseFloat(attendanceInfo.presentPercentage || "0"),
        color: "#03C95A",
      },
      {
        id: "Late",
        value: parseFloat(attendanceInfo.latePercentage || "0"),
        color: "#3B7080",
      },
      {
        id: "On Leave",
        value: parseFloat(attendanceInfo.onLeavePercentage || "0"),
        color: "#FFC107",
      },
      {
        id: "Absent",
        value: parseFloat(attendanceInfo.absentPercentage || "0"),
        color: "#E70D0D",
      },
    ];
  }, [attendanceInfo, initialAttendanceData]);

  // Create dynamic absentees list based on fetched info
  const currentAbsentees = useMemo(() => {
    if (!attendanceInfo?.absentEmployees?.length) return initialAbsentees;
    
    return attendanceInfo.absentEmployees.map(employee => ({
      name: employee.firstName || "Unknown",
      image: employee.avatar || "/assets/users/default.jpg",
    }));
  }, [attendanceInfo, initialAbsentees]);

  useEffect(() => {
    const fetchAttendanceData = async () => {
      try {
        const response = await getAttendanceByDate(
          currentDateRange.startDate, 
          currentDateRange.endDate
        );
        console.log("Attendance API Response:", response);
        if (response && response.attendenceSummary) {
          setAttendanceInfo({
            totalAttendance: response.attendenceSummary.totalAttendance,
            totalEmployees: response.attendenceSummary.totalEmployees,
            presentCount: response.attendenceSummary.present.count,
            presentPercentage: response.attendenceSummary.present.percentage,
            absentCount: response.attendenceSummary.absent.count,
            absentPercentage: response.attendenceSummary.absent.percentage,
            absentEmployees: response.attendenceSummary.absent.employees,
            onLeaveCount: response.attendenceSummary.onLeave.count,
            onLeavePercentage: response.attendenceSummary.onLeave.percentage,
            lateLoginCount: response.attendenceSummary.lateLogins?.current?.count || 0,
            latePercentage: response.attendenceSummary.lateLogins?.current?.percentage || "0.00%",
          });
        }
      } catch (error) {
        console.error("Failed to fetch attendance data:", error);
      }
    };

    fetchAttendanceData();
  }, [currentDateRange.startDate, currentDateRange.endDate]);

  // Add handlers for the dropdown
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleTimeSelect = (value: string) => {
    setSelectedTime(value);
    handleClose();
    
    const today = new Date();
    let startDate = today.toISOString().split('T')[0];
    const endDate = today.toISOString().split('T')[0];

    if (value === 'This Week') {
      const first = today.getDate() - today.getDay();
      startDate = new Date(today.setDate(first)).toISOString().split('T')[0];
    } else if (value === 'This Month') {
      startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
    }

    // Update local state
    setCurrentDateRange({ startDate, endDate });
    
    // Notify parent component
    if (onDateRangeChange) {
      onDateRangeChange(startDate, endDate);
    }
  };

  return (
    <Card sx={{ borderRadius: 2, boxShadow: 3, height: "100%" }}>
      {/* Card header with title and date range selector */}
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h6" className="dialog-heading-text">Attendance Overview</Typography>
        <Box>
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem !important",
              fontSize: "0.75rem !important",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
            onClick={handleClick}
          >
            {selectedTime}
          </Button>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
          >
            <MenuItem onClick={() => handleTimeSelect('Today')}>Today</MenuItem>
            <MenuItem onClick={() => handleTimeSelect('This Week')}>This Week</MenuItem>
            <MenuItem onClick={() => handleTimeSelect('This Month')}>This Month</MenuItem>
          </Menu>
        </Box>
      </Box>
      <CardContent
        sx={{
          padding: "1.5rem",
          paddingTop: "0",
          borderTop: "1px solid #E5E7EB",
        }}
      >
        <Box
          sx={{
            mt: 2,
            width: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <PieChart
            series={[
              {
                data: currentAttendanceData,
                innerRadius: 90,
                outerRadius: 150,
                paddingAngle: 5,
                cornerRadius: 10,
                startAngle: -110,
                endAngle: 110,
                cx: 210,
                cy: 150,
              },
            ]}
            height={200}
            width={430}
          />
          <Box
            sx={{
              textAlign: "center",
            }}
          >
            <Typography
              sx={{ fontSize: "0.8125rem", marginBottom: ".25rem" }}
              variant="h6"
            >
              Total Attendance
            </Typography>
            <Typography
              sx={{ fontSize: "20px", fontWeight: "600", color: "#202C4B" }}
              variant="h6"
            >
              {attendanceInfo?.totalEmployees || totalAttendance}
            </Typography>
          </Box>
        </Box>
        <Box sx={{ mt: 2 }}>
          <Typography sx={{ marginBottom: "1rem" }} variant="body2">
            Status
          </Typography>
          {currentAttendanceData.map((item) => (
            <Box
              key={item.id}
              display="flex"
              justifyContent="space-between"
              sx={{ mt: 1 }}
            >
              <Box display="flex" alignItems="center">
                <Box
                  sx={{
                    bgcolor: item.color,
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    mr: 1,
                  }}
                ></Box>
                <Typography>{item.id}</Typography>
              </Box>
              <Typography>{`${item.value.toFixed(2)}%`}</Typography>
            </Box>
          ))}
        </Box>
        <Box
          sx={{
            borderRadius: "0.3125rem",
            color: "#6B7280",
            backgroundColor: "#F8F9FA !important",
            border: "1px solid #F8F9FA !important",
            padding: ".5rem",
            paddingBottom: "0",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Typography sx={{ marginBottom: ".5rem", marginRight: ".5rem" }}>
              Total Absentees
            </Typography>
            <AvatarGroup
              className="avatarGroup"
              max={5}
              sx={{ display: "flex", gap: "-10px", marginBottom: ".5rem" }}
            >
              {currentAbsentees.map((absentee: Absentee, index: number) => (
                <Avatar
                  key={index}
                  className="avatar"
                  alt={absentee.name}
                  src={absentee.image}
                />
              ))}
            </AvatarGroup>
          </Box>
          <Link
            style={{
              color: "#F26522 !important",
              fontSize: "0.8125rem",
              textDecoration: "underline",
              marginBottom: ".5rem",
            }}
            href={{
              pathname: "/Attendance/attendance-admin",
              query: {
                department : "IT",
              }
            }}
          >
            View Detail
          </Link>
        </Box>
      </CardContent>
    </Card>
  );
};

export default AttendanceOverview;

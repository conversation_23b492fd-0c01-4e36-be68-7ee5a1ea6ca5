import React from "react";
import {
  Box,
  Typography,
  Paper,
  MenuItem,
  Select,
} from "@mui/material";
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";

const data = [
  { month: "Jan", income: 40, expenses: 80 },
  { month: "Feb", income: 20, expenses: 90 },
  { month: "Mar", income: 30, expenses: 70 },
  { month: "Apr", income: 60, expenses: 60 },
  { month: "May", income: 80, expenses: 50 },
  { month: "Jun", income: 100, expenses: 40 },
  { month: "Jul", income: 90, expenses: 50 },
  { month: "Aug", income: 80, expenses: 60 },
  { month: "Sep", income: 80, expenses: 60 },
  { month: "Oct", income: 70, expenses: 50 },
  { month: "Nov", income: 10, expenses: 100 },
  { month: "Dec", income: 80, expenses: 60 },
];

const SalesOverviewComponent = () => {
  return (
    <Paper elevation={3} sx={{ borderRadius: 2, boxShadow: 3, height: "100%" }}>
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            // marginBottom: ".5rem",
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Sales Overview
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Select
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
            defaultValue="All Departments"
          >
            <MenuItem value="All Departments">All Departments</MenuItem>
            <MenuItem value="Finance">Finance</MenuItem>
            <MenuItem value="HR">HR</MenuItem>
            <MenuItem value="Marketing">Marketing</MenuItem>
          </Select>
        </Box>
      </Box>

      <Box
        sx={{
          padding: "1.5rem",
          borderTop: "1px solid #E5E7EB",
        }}
      >
        <Typography variant="body2" align="right" color="textSecondary">
          Last Updated at 11:30 PM
        </Typography>
        <ResponsiveContainer width="100%" height={300}>
          <ReBarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="income" fill="#f97316" name="Income" />
            <Bar dataKey="expenses" fill="#e5e7eb" name="Expenses" />
          </ReBarChart>
        </ResponsiveContainer>
      </Box>
    </Paper>
  );
};

export default SalesOverviewComponent;

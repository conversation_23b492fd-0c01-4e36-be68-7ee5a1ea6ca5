import React, { useState } from "react";
import "./ClockInOutComponent.scss";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  Button,
  IconButton,
  Stack,
  Menu,
  MenuItem,
} from "@mui/material";
import { Circle, ScheduleSendOutlined } from "@mui/icons-material";
import useFetchAttendanceData from "@/app/hooks/attendance/useFetchAttendanceData";

const ClockInOutComponent = () => {
  const today = new Date().toISOString().split("T")[0];

  const [startDate, setStartDate] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [endDate, setEndDate] = useState(
    new Date().toISOString().split("T")[0]
  );

  const [attendanceData, total] = useFetchAttendanceData({
    setIsLoading: undefined,
    refresh: false,
    limit: 1000,
    page: 1,
    startDate: startDate,
    endDate: endDate,
  });

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTime, setSelectedTime] = useState("Today");

  // Add these functions inside your component
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleTimeSelect = (value: string) => {
    setSelectedTime(value);
    handleClose();

    const today = new Date();
    let newStartDate;
    const newEndDate = today.toISOString().split("T")[0];

    if (value === "Today") {
      // For "Today", both start and end dates should be today
      newStartDate = today.toISOString().split("T")[0];
    } else if (value === "This Week") {
      // Get the first day of the current week (Sunday)
      const first = today.getDate() - today.getDay();
      const firstDayOfWeek = new Date(today);
      firstDayOfWeek.setDate(first);
      newStartDate = firstDayOfWeek.toISOString().split("T")[0];
    } else if (value === "This Month") {
      // Get the first day of the current month
      newStartDate = new Date(today.getFullYear(), today.getMonth(), 1)
        .toISOString()
        .split("T")[0];
    }

    setStartDate(newStartDate || today.toISOString().split("T")[0]);
    setEndDate(newEndDate);
  };

  // Format time from ISO string to readable format
  const formatTime = (isoString?: string) => {
    if (!isoString) return "N/A";
    return new Date(isoString).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const displayedAttendance = attendanceData || [];
  console.log("Attendance Data:", displayedAttendance);

  return (
    <Card sx={{ borderRadius: 2, boxShadow: 3,height: "100%" }}>
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            marginBottom: ".5rem",
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Clock-In/Out
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          {/* <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              marginRight: ".5rem",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
          >
            All Departments
          </Button> */}
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem !important",
              fontSize: "0.75rem !important",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
            onClick={handleClick}
          >
            {selectedTime}
          </Button>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
          >
            <MenuItem onClick={() => handleTimeSelect("Today")}>Today</MenuItem>
            <MenuItem onClick={() => handleTimeSelect("This Week")}>
              This Week
            </MenuItem>
            <MenuItem onClick={() => handleTimeSelect("This Month")}>
              This Month
            </MenuItem>
          </Menu>
        </Box>
      </Box>
      <CardContent
        sx={{
          padding: "1.5rem",
          borderTop: "1px solid #E5E7EB",
          display: "flex",
          flexDirection: "column",
          height: "520px", // Set a fixed height
          justifyContent: "space-between",
        }}
      >
        <Box
          sx={{
            flex: 1,
            overflowY: "auto", // Add vertical scrollbar when content overflows
            marginBottom: "1rem",
            // Add custom scrollbar styling
            "&::-webkit-scrollbar": {
              width: "6px",
            },
            "&::-webkit-scrollbar-track": {
              background: "#f1f1f1",
              borderRadius: "10px",
            },
            "&::-webkit-scrollbar-thumb": {
              background: "#888",
              borderRadius: "10px",
            },
            "&::-webkit-scrollbar-thumb:hover": {
              background: "#555",
            },
          }}
        >
          {displayedAttendance.length > 0 ? (
            displayedAttendance.map((employee, index) => (
              <Box key={index} mb={2}>
                <Card variant="outlined" sx={{ p: 1, borderRadius: 3 }}>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <Avatar
                      src={employee.avatar}
                      sx={{
                        position: "relative",
                        height: "2.625rem",
                        width: "2.625rem",
                        display: "inline-flex",
                        alignItems: "center",
                        justifyContent: "center",
                        color: "#FFF",
                        fontWeight: "500",
                      }}
                    >
                      {!employee.avatar && employee.employeeName?.charAt(0)}
                    </Avatar>
                    <Box flexGrow={1}>
                      <Typography
                        sx={{
                          fontSize: "0.875rem",
                          fontWeight: "500",
                          color: "#202C4B",
                        }}
                        variant="subtitle1"
                      >
                        {employee.employeeName}
                      </Typography>
                      <Typography
                        sx={{ fontSize: "0.8125rem" }}
                        variant="body2"
                        color="text.secondary"
                      >
                        {employee.departmentName}
                      </Typography>
                    </Box>
                    <IconButton size="small">
                      <ScheduleSendOutlined fontSize="small" />
                    </IconButton>
                    <Chip
                      className="chip"
                      label={formatTime(employee.checkIn)}
                      sx={{
                        backgroundColor:
                          employee.status === "Present" ? "#03C95A" : "#E70D0D",
                        color: "#FFF",
                      }}
                      size="small"
                      icon={
                        <Circle
                          sx={{
                            width: "6px",
                            height: "6px",
                            color: "#FFF !important",
                          }}
                        />
                      }
                    />
                  </Stack>
                  {(employee.checkIn || employee.checkOut) && (
                    <Box
                      mt={1}
                      sx={{
                        border: "1px solid #E5E7EB",
                        borderRadius: "0.3125rem",
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        padding: "0.5rem 0.75rem",
                        color: "#202C4B",
                      }}
                    >
                      <Box sx={{ display: "flex", flexDirection: "column" }}>
                        <Typography
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            marginBottom: ".25rem",
                          }}
                          variant="body2"
                        >
                          <Circle
                            sx={{
                              color: "#03C95A !important",
                              width: "6px",
                              height: "6px",
                              marginRight: ".25rem",
                            }}
                          />
                          Clock In:
                        </Typography>
                        <Typography
                          sx={{ display: "flex", alignItems: "center" }}
                          variant="body2"
                        >
                          {formatTime(employee.checkIn)}
                        </Typography>
                      </Box>
                      <Box sx={{ display: "flex", flexDirection: "column" }}>
                        <Typography
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            marginBottom: ".25rem",
                          }}
                          variant="body2"
                        >
                          <Circle
                            sx={{
                              color: "#E70D0D !important",
                              width: "6px",
                              height: "6px",
                              marginRight: ".25rem",
                            }}
                          />
                          Clock Out:
                        </Typography>
                        <Typography
                          sx={{ display: "flex", alignItems: "center" }}
                          variant="body2"
                        >
                          {formatTime(employee.checkOut)}
                        </Typography>
                      </Box>
                      <Box sx={{ display: "flex", flexDirection: "column" }}>
                        <Typography
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            marginBottom: ".25rem",
                          }}
                          variant="body2"
                        >
                          <Circle
                            sx={{
                              color: "#FFC107 !important",
                              width: "6px",
                              height: "6px",
                              marginRight: ".25rem",
                            }}
                          />
                          Production:
                        </Typography>
                        <Typography variant="body2">
                          {employee.productionHours?.toFixed(2) || "0.00"} Hrs
                        </Typography>
                      </Box>
                    </Box>
                  )}
                </Card>
              </Box>
            ))
          ) : (
            <Typography>No attendance data available</Typography>
          )}
        </Box>

        <Box
          sx={{
            display: "flex",
            width: "100%",
            // padding: "0px 1.5rem",
          }}
        >
          <Button
          className="view-all-button"
            variant="contained"
            onClick={() => {
              window.location.href = "/Attendance/attendance-admin";
            }}
          >
            View All Attendance
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ClockInOutComponent;

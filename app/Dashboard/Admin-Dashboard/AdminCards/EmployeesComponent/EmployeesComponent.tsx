import React, { useState } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
} from "@mui/material";
import useFetchUsersData from "@/app/hooks/users/useFetchUsersData";
import { useRouter } from "next/navigation";

// Add interface for user data
interface UserData {
  _id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  departmentId?: string | { _id: string; departmentName: string };
  departmentName?: string;
  designationId?: string | { _id: string; designationName: string };
  designationName?: string;
}

const EmployeesComponent = () => {
  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();

  // Using the custom hook with default parameters
  const [userData, total, activeCount, inactiveCount, newJoiners] =
    useFetchUsersData({
      setIsLoading,
      refresh: false, // You can add a refresh mechanism if needed
      limit: 5,
      page: 1,
    });

  // Map chip colors based on department (you can customize this further)
  const getChipStyles = (departmentName: string) => {
    const styles = {
      IT: { backgroundColor: "#BBDEFB", color: "#1976D2" },
      Sales: { backgroundColor: "#EDF2F4", color: "#000000" },
    };
    return (
      styles[departmentName as keyof typeof styles] || {
        backgroundColor: "#E5E7EB",
        color: "#111827",
      }
    );
  };

  // Helper function to get department name
  const getDepartmentName = (user: UserData): string => {
    if (user.departmentId && typeof user.departmentId === 'object') {
      return user.departmentId.departmentName || 'N/A';
    }
    return user.departmentName || 'N/A';
  };

  // Helper function to get designation name
  const getDesignationName = (user: UserData): string => {
    if (user.designationId && typeof user.designationId === 'object') {
      return user.designationId.designationName || 'N/A';
    }
    return user.designationName || 'N/A';
  };

  return (
    <Card sx={{ borderRadius: 2, boxShadow: 3, height: "100%" }}>
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Employees ({total})
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem !important",
              fontSize: "0.75rem !important",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
            onClick={() => {
              window.location.href = "/employees/employeesList"; // Replace with your desired route
            }}
          >
            View All
          </Button>
        </Box>
      </Box>
      <CardContent
        sx={{
          padding: "0",
          borderTop: "1px solid #E5E7EB",
        }}
      >
        <TableContainer>
          <Table>
            <TableHead sx={{ background: "#E5E7EB" }}>
              <TableRow>
                <TableCell
                  sx={{
                    fontWeight: "600",
                    borderColor: "#E9EDF4",
                    background: "#E5E7EB",
                    fontSize: "14px",
                    color: "#111827",
                    padding: "8px 20px !important",
                  }}
                >
                  Name
                </TableCell>
                <TableCell
                  sx={{
                    fontWeight: "600",
                    borderColor: "#E9EDF4",
                    background: "#E5E7EB",
                    fontSize: "14px",
                    color: "#111827",
                    padding: "8px 20px !important",
                  }}
                >
                  Department
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={2} align="center">
                    <Typography>Loading...</Typography>
                  </TableCell>
                </TableRow>
              ) : userData && userData.length > 0 ? (
                userData.map((user: any) => {
                  const departmentName = getDepartmentName(user);
                  const designationName = getDesignationName(user);
                  const chipStyles = getChipStyles(departmentName);
                  
                  return (
                    <TableRow key={user._id}>
                      <TableCell
                        sx={{
                          padding: "8px 20px!important",
                          borderBottom: "1px solid #E9EDF4",
                          fontSize: "14px",
                          color: "#111827",
                          fontWeight: "500",
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <Box display="flex" alignItems="center" gap={1}>
                          <Avatar
                            sx={{ width: "42px", height: "42px" }}
                            src={user.avatar}
                          />
                          <Box>
                            <Box
                              component="span"
                              sx={{
                                color: "#111827",
                                fontWeight: "500",
                                fontSize: "14px",
                                cursor: "pointer",
                                textDecoration: "none",
                              }}
                              onClick={() =>
                                router.push(
                                  `/employees/employeesDetails?id=${user._id}`
                                )
                              }
                            >
                              {user.firstName} {user.lastName}
                            </Box>

                            <Typography
                              sx={{
                                fontSize: ".75rem",
                                color: "#6B7280",
                              }}
                              variant="body2"
                              color="textSecondary"
                            >
                              {designationName}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          sx={{
                            fontSize: "10px",
                            fontWeight: "500",
                            padding: "0px 5px",
                            lineHeight: "18px",
                            backgroundColor: chipStyles.backgroundColor,
                            color: chipStyles.color,
                            borderRadius: "4px",
                          }}
                          label={departmentName}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={2} align="center">
                    <Typography>No employees found</Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );
};

export default EmployeesComponent;

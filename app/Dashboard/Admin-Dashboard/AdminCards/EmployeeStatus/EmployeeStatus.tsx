import React, { useEffect, useState } from "react";
import "./EmployeeStatus.scss";
import "../shared.scss";
import {
  Box,
  Typography,
  Card,
  CardContent,
  Avatar,
  Button,
  Stack,
  Divider,
} from "@mui/material";
import { CalendarToday, WorkspacePremium } from "@mui/icons-material";
import { getUsers } from "@/app/services/users.service";
import { useRouter } from "next/navigation";

interface UserData {
  totalUsers: number;
  fullTime: number;
  contract: number;
  probation: number;
  wfh: number;
  users?: {
    total: number;
    statistics: {
      fullTimeCount: number;
      contractCount: number;
      probationCount: number;
      wfhCount: number;
    };
  };
}

const EmployeeStatus = () => {
  const router = useRouter();
  const [userData, setUserData] = useState<UserData | null>(null);

  // Calculate percentages based on actual data
  const calculatePercentage = (count: number, total: number): number => {
    return total > 0 ? Math.round((count / total) * 100) : 0;
  };

  // Get status data with dynamic values
  const getStatusData = () => {
    const total = userData?.totalUsers || 0;
    return [
      {
        name: "Fulltime",
        percentage: calculatePercentage(userData?.fullTime || 0, total),
        count: userData?.fullTime || 0,
        color: "#FFC107",
      },
      {
        name: "Contract",
        percentage: calculatePercentage(userData?.contract || 0, total),
        count: userData?.contract || 0,
        color: "#3B7080",
      },
      {
        name: "Probation",
        percentage: calculatePercentage(userData?.probation || 0, total),
        count: userData?.probation || 0,
        color: "#E70D0D",
      },
      {
        name: "WFH",
        percentage: calculatePercentage(userData?.wfh || 0, total),
        count: userData?.wfh || 0,
        color: "#FD3995",
      },
    ];
  };

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await getUsers();
        console.log("Users Response", response);

        if (response?.users) {
          setUserData({
            totalUsers: response.users.total,
            fullTime: response.users.statistics.fullTimeCount,
            contract: response.users.statistics.contractCount,
            probation: response.users.statistics.probationCount,
            wfh: response.users.statistics.wfhCount,
          });
        }
      } catch (error) {
        console.error("Failed to fetch user data:", error);
        setUserData({
          totalUsers: 0,
          fullTime: 0,
          contract: 0,
          probation: 0,
          wfh: 0,
        });
      }
    };

    fetchUserData();
  }, []);

  const statusData = getStatusData();

  return (
    <Card
      sx={{
        borderRadius: 2,
        boxShadow: 3,
        height: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: "1px solid #E5E7EB",
        }}
      >
        <Typography variant="h6" className="dialog-heading-text">Employee Status</Typography>
      </Box>
      <CardContent
        sx={{
          padding: "1.5rem !important",
          display: "flex",
          flexDirection: "column",
          flex: 1,
          overflow: "auto",
        }}
      >
        <Box sx={{ flex: 1 }}>
          {/* Total Employees with Bar Chart */}
          <Box>
            <Card
              sx={{
                boxShadow: "none !important",
              }}
            >
              <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontSize: "0.8125rem",
                    marginBottom: "1rem",
                    color: "#6B7280",
                  }}
                >
                  Total Employee
                </Typography>
                <Typography
                  variant="h4"
                  sx={{
                    marginBottom: "1rem",
                    fontSize: "20px",
                    fontWeight: "600",
                  }}
                >
                  {userData?.totalUsers || 0}
                </Typography>
              </Box>

              {/* Horizontal Bar Chart */}
              <Box sx={{ mb: "1rem" }}>
                <Box
                  sx={{
                    display: "flex",
                    height: 24,
                    borderRadius: 12,
                    overflow: "hidden",
                    backgroundColor: "#f5f5f5",
                  }}
                >
                  {statusData.map((item, index) => (
                    <Box
                      key={index}
                      sx={{
                        width: `${item.percentage}%`,
                        backgroundColor: item.color,
                      }}
                    />
                  ))}
                </Box>
              </Box>

              {/* Legend */}
              <Stack
                sx={{
                  border: "1px solid #E5E7EB",
                  marginBottom: "0",
                  display: "flex",
                  flexDirection: "row",
                  flexWrap: "wrap",
                }}
              >
                {statusData.map((item, index) => (
                  <Box
                    key={index}
                    sx={{
                      flex: "0 0 auto",
                      width: "50%",
                      border: "1px solid #E5E7EB",
                      padding: ".5rem",
                    }}
                  >
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <Box
                        sx={{
                          width: 12,
                          height: 12,
                          bgcolor: item.color,
                          borderRadius: "50%",
                          mr: 1,
                        }}
                      />
                      <Typography
                        sx={{ fontSize: "0.8125rem" }}
                        variant="body2"
                      >
                        {item.name}
                        <Typography
                          component="span"
                          sx={{ color: "#111827", fontSize: "0.8125rem" }}
                        >
                          ({item.percentage}%)
                        </Typography>
                      </Typography>
                    </Box>
                    <Typography
                      sx={{ fontSize: "36px" }}
                      variant="body2"
                      fontWeight="bold"
                    >
                      {item.count}
                    </Typography>
                  </Box>
                ))}
              </Stack>
            </Card>
          </Box>

          {/* Top Performer */}
          {/* <Box sx={{ padding: "1.5rem" }}>
            <Typography
              sx={{
                marginBottom: ".5rem",
                fontSize: "14px",
                fontWeight: "600",
                color: "#202C4B",
              }}
              variant="h6"
            >
              Top Performer
            </Typography>

            <Card
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                backgroundColor: "#FEF0E9",
                borderColor: "#F26522",
                border: "1px solid #F26522",
                borderRadius: "0.3125rem",
                padding: ".5rem ",
                marginBottom: "1.5rem",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <WorkspacePremium
                    sx={{ color: "#F26522 ", marginright: ".5rem " }}
                  />

                  <Avatar
                    alt="John Doe"
                    src="https://via.placeholder.com/150"
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: "50%",
                      marginRight: ".5rem",
                    }}
                  />
                </Box>

                <Box>
                  <Typography
                    sx={{
                      color: "#F26522",
                      transition: "all 0.5sease",
                      marginbottom: ".25rem",
                      fontSize: "0.875rem",
                    }}
                    variant="h6"
                  >
                    Daniel Esbella
                  </Typography>
                  <Typography sx={{ fontSize: "0.8125rem" }}>
                    IOS Developer
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ textAlign: "right" }}>
                <Typography sx={{ fontSize: "0.8125rem" }}>
                  Performance
                </Typography>
                <Typography
                  sx={{ color: "#F26522", fontSize: "16px", fontWeight: "600" }}
                  variant="h5"
                >
                  99%
                </Typography>
              </Box>
            </Card>
          </Box> */}
        </Box>

        <Box
          sx={{
            display: "flex",
            width: "100%",
            marginTop: "auto",
            // borderTop: "1px solid #E5E7EB",
            padding: "1.5rem 0 0",
          }}
        >
          <Button
            className="view-all-employee-button"
            variant="contained"
            onClick={() => router.push("/employees/employeesList")}
          >
            View All Employees
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default EmployeeStatus;

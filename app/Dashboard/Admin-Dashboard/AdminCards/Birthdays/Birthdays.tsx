import React from "react";
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  ListItem,
  ListItemAvatar,
  Avatar,
  ListItemText,
  Button,
  Stack,
} from "@mui/material";
import {
  Cake as CakeIcon,
  Send as SendIcon,
  Today as TodayIcon,
  Event as EventIcon,
  CalendarToday as CalendarIcon,
} from "@mui/icons-material";

interface BirthdayCardProps {
  name: string;
  role: string;
  dateType: string;
  showActions?: boolean;
}

const BirthdayCard: React.FC<BirthdayCardProps> = ({
  name,
  role,
  dateType,
  showActions = true,
}) => (
  <Card
    variant="outlined"
    sx={{ border: "1px solid #E5E7EB", backgroundColor: "#F8F9FA !important" }}
  >
    <CardContent sx={{ display: "flex", padding: "0.5rem !important" }}>
      <ListItem disableGutters sx={{ padding: "0" }}>
        <ListItemAvatar>
          <Avatar sx={{ bgcolor: "secondary.light" }}>
            <CakeIcon />
          </Avatar>
        </ListItemAvatar>
        <ListItemText
          primary={name}
          secondary={role}
          primaryTypographyProps={{ fontWeight: "bold" }}
          secondaryTypographyProps={{ color: "text.secondary" }}
        />
      </ListItem>

      {showActions && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center", // Ensure proper vertical alignment
          }}
        >
          <Button
            size="small"
            variant="outlined"
            startIcon={<SendIcon sx={{ width: "10px", height: "10px" }} />}
            sx={{
              mr: 1,
              backgroundColor: "#3B7080",
              border: "1px solid #3B7080",
              color: "#FFF",
              padding: "0.25rem 0.5rem",
              fontSize: "0.6rem",
              textTransform: "none",
            }}
          >
            Send
          </Button>
        </Box>
      )}
    </CardContent>
  </Card>
);

const Birthdays = () => {
  return (
    <Box sx={{ borderRadius: 2, boxShadow: 3, height: "100%" }}>
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            // marginBottom: ".5rem",
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Birthdays
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
          >
            View All
          </Button>
        </Box>
      </Box>

      <Box
        sx={{
          padding: "1.25rem",
          borderTop: "1px solid #E5E7EB",
        }}
      >
        {/* Today Section */}
        <Box sx={{ mb: "1rem" }}>
          <Box display="flex" alignItems="center">
            <Typography
              sx={{
                marginBottom: ".5rem",
                fontSize: "14px",
                fontWeight: "600",
                color: "#202C4B",
              }}
              variant="h6"
              fontWeight="medium"
            >
              Today
            </Typography>
          </Box>
          <BirthdayCard
            name="Andrew Jermia"
            role="iOS Developer"
            dateType="today"
          />
        </Box>

        {/* <Divider sx={{ my: 3 }}>
          <Chip label="Upcoming" size="small" />
        </Divider> */}

        {/* Tomorrow Section */}
        <Box sx={{ mb: "1rem" }}>
          <Box display="flex" alignItems="center">
            <Typography
              sx={{
                marginBottom: ".5rem",
                fontSize: "14px",
                fontWeight: "600",
                color: "#202C4B",
              }}
              variant="h6"
              fontWeight="medium"
            >
              Tomorrow
            </Typography>
          </Box>
          <Stack spacing={2}>
            <BirthdayCard
              name="Mary Zeen"
              role="UI/UX Designer"
              dateType="tomorrow"
            />
            <BirthdayCard
              name="Antony Lewis"
              role="Android Developer"
              dateType="tomorrow"
            />
          </Stack>
        </Box>

        {/* Future Date Section */}
        <Box sx={{}}>
          <Box display="flex" alignItems="center">
            <Typography
              sx={{
                marginBottom: ".5rem",
                fontSize: "14px",
                fontWeight: "600",
                color: "#202C4B",
              }}
              variant="h6"
              fontWeight="medium"
            >
              25 Jan 2025
            </Typography>
          </Box>
          <BirthdayCard
            name="Doglas Martini"
            role=".Net Developer"
            dateType="future"
          />
        </Box>
      </Box>
    </Box>
  );
};

export default Birthdays;

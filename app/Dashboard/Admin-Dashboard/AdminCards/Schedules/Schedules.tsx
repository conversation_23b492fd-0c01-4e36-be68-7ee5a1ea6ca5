import React from "react";
import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  Di<PERSON><PERSON>,
  But<PERSON>,
  <PERSON>,
  Box,
  Stack,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  AvatarGroup,
} from "@mui/material";
import {
  CalendarToday as CalendarIcon,
  Schedule as TimeIcon,
  VideoCall as MeetingIcon,
  DesignServices as DesignIcon,
  Code as CodeIcon,
} from "@mui/icons-material";

interface ScheduleCardProps {
  title: string;
  role: string;
  date: string;
  time: string;
  icon: React.ReactNode;
}

const ScheduleCard: React.FC<ScheduleCardProps> = ({
  title,
  role,
  date,
  time,
  icon,
}) => (
  <Card
    sx={{
      borderRadius: "0.3125rem ",
      color: "#6B7280",
      backgroundColor: "#F8F9FA !important",
      border: "1px solid #F8F9FA !important",
      padding: "1rem",
      marginBottom: "1.5rem",
    }}
  >
    <Box display="flex" alignItems="center" mb={1}>
      <Box>
        <Typography
          sx={{
            fontSize: "10px",
            fontWeight: "500",
            padding: "0px 5px",
            lineHeight: "18px",
            background: "#3B7080",
            color: "#FFF",
            letterSpacing: "0.5px",
            borderRadius: "4px",
            display: "inline-block",
          }}
          variant="subtitle2"
        >
          {role}
        </Typography>
        <Typography
          sx={{
            fontSize: "14px",
            fontWeight: "600",
            color: "#202C4B",
            marginBottom: ".5rem",
          }}
          variant="h6"
          fontWeight="bold"
        >
          {title}
        </Typography>
      </Box>
    </Box>

    <List dense sx={{ display: "flex" }}>
      <ListItem disableGutters>
        <ListItemIcon sx={{ minWidth: 20 }}>
          <CalendarIcon
            sx={{ width: "13px", height: "13px" }}
            fontSize="small"
            color="action"
          />
        </ListItemIcon>
        <ListItemText
          sx={{ fontSize: "0.8125rem", marginBottom: ".25rem" }}
          primary={date}
        />
      </ListItem>
      <ListItem disableGutters>
        <ListItemIcon sx={{ minWidth: 20 }}>
          <TimeIcon
            sx={{ width: "13px", height: "13px" }}
            fontSize="small"
            color="action"
          />
        </ListItemIcon>
        <ListItemText primary={time} />
      </ListItem>
    </List>

    <Divider />

    <Box mt={2} sx={{ display: "flex", justifyContent: "space-between" }}>
      <AvatarGroup
        className="avatarGroup"
        max={5}
        sx={{ display: "flex", gap: "-10px", marginBottom: ".5rem" }}
      >
        <Avatar
          className="avatar"
          alt="Remy Sharp"
          src="/assets/users/user-01.jpg"
        />
        <Avatar
          className="avatar"
          alt="Remy Sharp"
          src="/assets/users/user-10.jpg"
        />
        <Avatar
          className="avatar"
          alt="Remy Sharp"
          src="/assets/users/user-11.jpg"
        />
        <Avatar
          className="avatar"
          alt="Remy Sharp"
          src="/assets/users/user-01.jpg"
        />
        <Avatar
          className="avatar"
          alt="Remy Sharp"
          src="/assets/users/user-01.jpg"
        />
        <Avatar
          className="avatar"
          alt="Remy Sharp"
          src="/assets/users/user-10.jpg"
        />
      </AvatarGroup>
      <Button
        variant="contained"
        // startIcon={<MeetingIcon />}
        size="small"
        sx={{
          backgroundColor: "#F26522",
          border: "1px solid #F26522",
          color: "#FFF",
          padding: "0.25rem 0.5rem",
          fontSize: "0.6rem",
          borderRadius: "5px",
          fontWeight: "500",
          textTransform: "none",
          marginBottom: ".5rem"
        }}
      >
        Join Meeting
      </Button>
    </Box>
  </Card>
);

const Schedules = () => {
  const schedules = [
    {
      title: "Interview Candidates - UI/UX Designer",
      role: "UI/UX Designer",
      date: "Thu, 15 Feb 2025",
      time: "01:00 PM - 02:20 PM",
      icon: <DesignIcon />,
    },
    {
      title: "Interview Candidates - IOS Developer",
      role: "IOS Developer",
      date: "Thu, 15 Feb 2025",
      time: "02:00 PM - 04:20 PM",
      icon: <CodeIcon />,
    },
  ];

  return (
    <Box sx={{ borderRadius: 2, boxShadow: 3, height: "100%" }}>
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            // marginBottom: ".5rem",
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Schedules
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
          >
            View All
          </Button>
        </Box>
      </Box>

      <Stack
        spacing={2}
        sx={{
          padding: "1.5rem",
          borderTop: "1px solid #E5E7EB",
        }}
      >
        {schedules.map((schedule, index) => (
          <React.Fragment key={index}>
            <ScheduleCard {...schedule} />
            {index < schedules.length - 1 && (
              <Divider sx={{ my: 1 }}>
                <Chip label="Up Next" size="small" />
              </Divider>
            )}
          </React.Fragment>
        ))}
      </Stack>
    </Box>
  );
};

export default Schedules;

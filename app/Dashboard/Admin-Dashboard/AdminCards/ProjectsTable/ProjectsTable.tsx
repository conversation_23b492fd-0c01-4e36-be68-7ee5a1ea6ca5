import React from "react";
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Chip,
  Box,
  Button,
  Avatar,
  AvatarGroup,
} from "@mui/material";
import {
  Circle as CircleIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Circle,
} from "@mui/icons-material";

const ProjectsTable = () => {
  const projects = [
    {
      id: "PRO-001",
      name: "Office Management App",
      hours: "15/255 Hrs",
      deadline: "12 Sep 2024",
      priority: "High",
      team: [
        { name: "<PERSON>", avatar: "https://via.placeholder.com/40" },
        { name: "<PERSON>", avatar: "https://via.placeholder.com/40" },
        { name: "<PERSON>", avatar: "https://via.placeholder.com/40" },
      ],
    },
    {
      id: "PRO-002",
      name: "Clinic Management",
      hours: "15/255 Hrs",
      deadline: "24 Oct 2024",
      priority: "Low",
      team: [
        { name: "<PERSON>", avatar: "https://via.placeholder.com/40" },
        { name: "<PERSON>", avatar: "https://via.placeholder.com/40" },
      ],
    },
    {
      id: "PRO-003",
      name: "Educational Platform",
      hours: "40/255 Hrs",
      deadline: "18 Feb 2024",
      priority: "Medium",
      team: [
        { name: "<PERSON>", avatar: "https://via.placeholder.com/40" },
        { name: "Grace", avatar: "https://via.placeholder.com/40" },
        { name: "Hank", avatar: "https://via.placeholder.com/40" },
      ],
    },
    {
      id: "PRO-004",
      name: "Chat & Call Mobile App",
      hours: "35/155 Hrs",
      deadline: "19 Feb 2024",
      priority: "High",
      team: [
        { name: "Alice", avatar: "https://via.placeholder.com/40" },
        { name: "Bob", avatar: "https://via.placeholder.com/40" },
        { name: "Charlie", avatar: "https://via.placeholder.com/40" },
      ],
    },
    {
      id: "PRO-005",
      name: "Travel Planning Website",
      hours: "50/235 Hrs",
      deadline: "18 Feb 2024",
      priority: "Medium",
      team: [
        { name: "David", avatar: "https://via.placeholder.com/40" },
        { name: "Eve", avatar: "https://via.placeholder.com/40" },
      ],
    },
    {
      id: "PRO-006",
      name: "Service Booking Software",
      hours: "40/255 Hrs",
      deadline: "20 Feb 2024",
      priority: "Low",
      team: [
        { name: "Frank", avatar: "https://via.placeholder.com/40" },
        { name: "Grace", avatar: "https://via.placeholder.com/40" },
        { name: "Hank", avatar: "https://via.placeholder.com/40" },
      ],
    },
    {
      id: "PRO-008",
      name: "Travel Planning Website",
      hours: "15/255 Hrs",
      deadline: "17 Oct 2024",
      priority: "Medium",
      team: [
        { name: "Alice", avatar: "https://via.placeholder.com/40" },
        { name: "Bob", avatar: "https://via.placeholder.com/40" },
        { name: "Charlie", avatar: "https://via.placeholder.com/40" },
      ],
    },
  ];

  interface Project {
    id: string;
    name: string;
    team: string;
    deadline: string;
    priority: string;
  }

  const getPriorityChip = (priority: string): React.JSX.Element => {
    switch (priority.toLowerCase()) {
      case "high":
        return (
          <Chip
            icon={
              <Circle
                sx={{ width: "8px", height: "6px", color: "#FFF !important" }}
              />
            }
            label="High"
            sx={{
              backgroundColor: "#E70D0D",
              color: "#FFF!important",
              borderRadius: "4px",
            }}
            size="small"
          />
        );
      case "medium":
        return (
          <Chip
            icon={
              <Circle
                sx={{ width: "8px", height: "6px", color: "#FFF !important" }}
              />
            }
            label="Medium"
            sx={{
              backgroundColor: "#FD3995",
              color: "#FFF !important",
              borderRadius: "4px",
            }}
            size="small"
          />
        );
      case "low":
        return (
          <Chip
            icon={
              <Circle
                sx={{ width: "8px", height: "6px", color: "#FFF !important" }}
              />
            }
            label="Low"
            sx={{
              backgroundColor: "#03C95A",
              color: "#fff",
              borderRadius: "4px",
            }}
            size="small"
          />
        );
      default:
        return <Chip label={priority} size="small" />;
    }
  };

  return (
    <Box sx={{ borderRadius: 2, boxShadow: 3, height: "100%" }}>
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Projects
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
          >
            This Week
          </Button>
        </Box>
      </Box>
      <TableContainer component={Paper} elevation={3}>
        <Table sx={{ minWidth: 650 }} aria-label="projects table">
          <TableHead sx={{ backgroundColor: "#f5f5f5" }}>
            <TableRow>
              <TableCell>
                <strong>ID</strong>
              </TableCell>
              <TableCell>
                <strong>Name</strong>
              </TableCell>
              <TableCell>
                <strong>Team</strong>
              </TableCell>
              <TableCell>
                <strong>Hours</strong>
              </TableCell>
              <TableCell>
                <strong>Deadline</strong>
              </TableCell>
              <TableCell>
                <strong>Priority</strong>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {projects.map((project) => (
              <TableRow
                key={project.id}
                sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
              >
                <TableCell component="th" scope="row">
                  {project.id}
                </TableCell>
                <TableCell>{project.name}</TableCell>
                <TableCell
                  sx={{ display: "flex", justifyContent: "flex-start" }}
                >
                  <AvatarGroup max={3} className="avatarGroup">
                    {project.team.map((member, index) => (
                      <Avatar
                        key={index}
                        alt={member.name}
                        src={member.avatar}
                      />
                    ))}
                  </AvatarGroup>
                </TableCell>
                <TableCell>{project.hours}</TableCell>
                <TableCell>{project.deadline}</TableCell>
                <TableCell>{getPriorityChip(project.priority)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default ProjectsTable;

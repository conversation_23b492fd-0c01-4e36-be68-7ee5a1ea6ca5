import React from "react";
import { Card, CardContent, Typography, Box, IconButton } from "@mui/material";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import {
  <PERSON><PERSON>ropDownOutlined,
  ArrowDropUpOutlined,
} from "@mui/icons-material";
import { useRouter } from "next/navigation";

interface CustomAttendanceCardsProps {
  title: string;
  current: number;
  total?: number;
  // change: number;
  // positive: boolean;
  color: string;
  icon: React.ReactNode;
  linkPath?: string;
  showViewAll?: boolean;
}

const CustomAttendanceCards: React.FC<CustomAttendanceCardsProps> = ({
  title,
  current,
  total,
 // change,
 // positive,
  color,
  icon,
  linkPath,
  showViewAll = true, // Default to true for backward compatibility
}) => {
  const router = useRouter();

  const handleViewAll = () => {
    if (linkPath) {
      router.push(linkPath);
    }
  };

  return (
    <Box
    className="custom-attendance-card"
      sx={{
        display: "flex",
        flex: "0 0 auto",
        width: "25%",
        padding: "0px 12px",
      }}
    >
      <Card
        sx={{
          marginBottom: "1.5rem",
          backgroundColor: "#FFF",
          transition: "all 0.5sease-in-out",
          position: "relative",
          borderRadius: "5px",
          // border: "1px solid #E5E7EB",
          boxShadow: "None",
          color: "inherit",
          flex: "1 1 auto !important",

          // Temp Style
          display: "flex",
          justifyContent: "start",
          alignItems: "start",
          flexDirection: "column",
        }}
      >
        <CardContent
          sx={{
            padding: "1.25rem !important",
            flex: "1 1 auto",
            display: "flex",
            justifyContent: "center",
            alignItems: "start",
            flexDirection: "column",
          }}
        >
          <Box
            gap={1}
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "start",
              flexDirection: "column",
            }}
          >
            {icon}
            <Typography
              sx={{ fontSize: "0.8125rem" }}
              variant="h6"
              color="textSecondary"
            >
              {title}
            </Typography>
          </Box>
          <Box sx={{ display: "flex", marginBottom: "1rem" }}>
            <Typography
              sx={{
                fontSize: "20px",
                fontWeight: "600",
                color: "#202C4B",
              }}
              variant="h4"
              fontWeight="bold"
            >
              {current}/{total}
            </Typography>
            {/* <Box display="flex" alignItems="center">
              {positive ? (
                <ArrowDropUpOutlined
                  sx={{ color: "#03C95A !important", opacity: "1" }}
                />
              ) : (
                <ArrowDropDownOutlined sx={{ color: "#E70D0D" }} />
              )}
              <Typography
                sx={{ fontSize: "0.75rem" }}
                variant="body2"
                color={positive ? "#03C95A " : "#E70D0D"}
              >
                {change}%
              </Typography>
            </Box> */}
          </Box>
          {showViewAll &&
            linkPath && ( // Only show if both conditions are met
              <Typography
                variant="body2"
                onClick={handleViewAll}
                color="primary"
                sx={{
                  mt: 1,
                  cursor: "pointer",
                  color: "#6B7280",
                  "&:hover": {
                    textDecoration: "underline",
                  },
                }}
              >
                View All
              </Typography>
            )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default CustomAttendanceCards;

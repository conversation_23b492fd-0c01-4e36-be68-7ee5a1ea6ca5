import React, { useState } from "react";
import {
  Box,
  Typography,
  IconButton,
  Paper,
  Checkbox,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Button,
} from "@mui/material";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";

const todos = [
  "Add Holidays",
  "Add Meeting to Client",
  "Chat with <PERSON>",
  "Management Call",
  "Add Payroll",
  "Add Policy for Increment",
];

const TodoComponent = () => {
  const [checked, setChecked] = useState<number[]>([]);

  const handleToggle = (index: number) => {
    setChecked((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  return (
    <Paper elevation={3} sx={{ borderRadius: 2, boxShadow: 3, height: "100%" }}>
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Todo
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            sx={{
              color: "#111827",
              backgroundColor: "#FFF",
              borderColor: "#E5E7EB",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              "&:hover": {
                backgroundColor: "#FF7F0E",
                color: "#FFF",
              },
            }}
            size="small"
            variant="outlined"
          >
            View All
          </Button>

          <IconButton
            size="small"
            sx={{
              color: "#F26522",
            }}
          >
            <AddCircleIcon />
          </IconButton>
        </Box>
      </Box>
      <List>
        {todos.map((todo, index) => (
          <ListItem
            key={index}
            disablePadding
            sx={{
              backgroundColor: checked.includes(index) ? "#E0F7FA" : "inherit", // Change background color
              transition: "background-color 0.3s",
            }}
          >
            <ListItemButton onClick={() => handleToggle(index)}>
              <ListItemIcon>
                <DragIndicatorIcon />
              </ListItemIcon>
              <Checkbox
                edge="start"
                tabIndex={-1}
                disableRipple
                checked={checked.includes(index)}
              />
              <ListItemText
                primary={todo}
                sx={{
                  ml: 1,
                  textDecoration: checked.includes(index)
                    ? "line-through"
                    : "none", // Add underline
                  color: checked.includes(index) ? "#9E9E9E" : "inherit", // Change text color
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Paper>
  );
};

export default TodoComponent;

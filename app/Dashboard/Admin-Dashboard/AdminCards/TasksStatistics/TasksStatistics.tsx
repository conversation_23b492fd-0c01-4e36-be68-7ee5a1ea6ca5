import React from "react";
// import "./AttendanceOverview.scss";
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Avatar,
  AvatarGroup,
  Divider,
} from "@mui/material";
import { Pie<PERSON>hart } from "@mui/x-charts";
import Link from "next/link";

const TasksStatistics = () => {
  return (
    <Card sx={{ borderRadius: 2, boxShadow: 3, height: "100%" }}>
      <Box
        sx={{
          borderColor: "#E5E7EB",
          position: "relative",
          background: "transparent",
          padding: "1rem 1.25rem 1rem",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography
          sx={{
            fontSize: "16px",
            fontWeight: "600",
            color: "#202C4B",
          }}
          variant="h6"
        >
          Tasks Statistics
        </Typography>
        <Button
          sx={{
            color: "#111827",
            backgroundColor: "#FFF",
            borderColor: "#E5E7EB",
            padding: "0.25rem 0.5rem",
            fontSize: "0.75rem",
            borderRadius: "5px",
            transition: "all 0.3s",
            textTransform: "none",
            "&:hover": {
              backgroundColor: "#FF7F0E",
              color: "#FFF",
            },
          }}
          size="small"
          variant="outlined"
        >
          This Week
        </Button>
      </Box>
      <CardContent
        sx={{
          padding: "1.5rem",
          paddingTop: "0",
          borderTop: "1px solid #E5E7EB",
        }}
      >
        <Box
          sx={{
            mt: 2,
            width: "100%",
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <PieChart
            series={[
              {
                data: [
                  { id: "Ongoing", value: 59, color: "#03C95A" },
                  { id: "OnHold", value: 21, color: "#3B7080" },
                  { id: "Overdue", value: 2, color: "#FFC107" },
                  { id: "Absent", value: 15, color: "#E70D0D" },
                ],
                innerRadius: 90,
                outerRadius: 150,
                paddingAngle: -5,
                cornerRadius: 30,
                startAngle: -110,
                endAngle: 110,
                cx: 210,
                cy: 150,
              },
            ]}
            height={200}
            width={430}
          />
          <Box
            sx={{
              textAlign: "center",
            }}
          >
            <Typography
              sx={{ fontSize: "0.8125rem", marginBottom: ".25rem" }}
              variant="h6"
            >
              Total Tasks
            </Typography>
            <Typography
              sx={{ fontSize: "20px", fontWeight: "600", color: "#202C4B" }}
              variant="h6"
            >
              124/165
            </Typography>
          </Box>
        </Box>
        <Box sx={{ mt: 2, display: "flex" }}>
          <Box
            display="flex"
            sx={{
              mt: 1,
              paddingRight: ".5rem",
              marginBottom: "1rem",
              marginRight: ".5rem",
              borderInlineEnd: "1px solid #E5E7EB",
            }}
          >
            <Box
              display="flex"
              alignItems="center"
              sx={{ flexDirection: "column" }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box
                  sx={{
                    bgcolor: "#FFC107",
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    mr: 1,
                  }}
                ></Box>
                <Typography>Ongoing</Typography>
              </Box>
              <Typography
                sx={{ fontSize: "16px", fontWeight: "600", color: "#202C4B" }}
              >
                59%
              </Typography>
            </Box>
          </Box>
          <Box
            display="flex"
            sx={{
              mt: 1,
              paddingRight: ".5rem",
              marginBottom: "1rem",
              marginRight: ".5rem",
              borderInlineEnd: "1px solid #E5E7EB",
            }}
          >
            <Box
              display="flex"
              alignItems="center"
              sx={{ flexDirection: "column" }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box
                  sx={{
                    bgcolor: "#1B84FF",
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    mr: 1,
                  }}
                ></Box>
                <Typography>On Hold</Typography>
              </Box>
              <Typography
                sx={{ fontSize: "16px", fontWeight: "600", color: "#202C4B" }}
              >
                10%
              </Typography>
            </Box>
          </Box>
          <Box
            display="flex"
            sx={{
              mt: 1,
              paddingRight: ".5rem",
              marginBottom: "1rem",
              marginRight: ".5rem",
              borderInlineEnd: "1px solid #E5E7EB",
            }}
          >
            <Box
              display="flex"
              alignItems="center"
              sx={{ flexDirection: "column" }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box
                  sx={{
                    bgcolor: "#E70D0D",
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    mr: 1,
                  }}
                ></Box>
                <Typography>Overdue</Typography>
              </Box>
              <Typography
                sx={{ fontSize: "16px", fontWeight: "600", color: "#202C4B" }}
              >
                16%
              </Typography>
            </Box>
          </Box>
          <Box display="flex" sx={{ mt: 1 }}>
            <Box
              display="flex"
              alignItems="center"
              sx={{ flexDirection: "column" }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box
                  sx={{
                    bgcolor: "#03C95A",
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    mr: 1,
                  }}
                ></Box>
                <Typography>Ongoing</Typography>
              </Box>
              <Typography
                sx={{ fontSize: "16px", fontWeight: "600", color: "#202C4B" }}
              >
                15%
              </Typography>
            </Box>
          </Box>
        </Box>

        <Box
          sx={{
            borderRadius: "0.3125rem",
            backgroundColor: "#212529 !important",
            border: "1px solid #212529 !important",
            color: "#FFF",
            padding: "1rem",
            paddingBottom: "0",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Box sx={{ marginBottom: ".5rem", color: "#FFF" }}>
            <Typography
              sx={{
                color: "#03C95A !important",
                opacity: "1",
                fontSize: "18px",
                fontWeight: "600",
                marginBottom: "0",
              }}
              variant="h4"
            >
              389/689 hrs
            </Typography>
            <Typography sx={{ fontSize: "0.8125rem", marginBottom: "0" }}>
              Spent on Overall Tasks This Week
            </Typography>
          </Box>
          <Link
            style={{
              backgroundColor: "#F8F9FA",
              border: "1px solid #F8F9FA",
              color: "#111827",
              padding: "0.25rem 0.5rem",
              fontSize: "0.75rem",
              borderRadius: "5px",
              transition: "all 0.3s",
              textTransform: "none",
              marginBottom: ".5rem",
            }}
            href="/Attendance/leaves/leaves-employee"
          >
            View All
          </Link>
        </Box>
      </CardContent>
    </Card>
  );
};

export default TasksStatistics;

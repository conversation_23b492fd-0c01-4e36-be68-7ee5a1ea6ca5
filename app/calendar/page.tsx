"use client";
import { Box, Typography, useMediaQuery } from "@mui/material";
import React, { useState, useEffect } from "react";
import "./Calendar.scss";
import { HomeOutlined } from "@mui/icons-material";
import BreadcrumbsComponent from "@/components/Breadcrumbs/Breadcrumbs";
import Loader from "@/components/Loader/Loader";
import "react-toastify/dist/ReactToastify.css";
import useRoleAuth from "../hooks/useRoleAuth";
import { ROLE_GROUPS } from "../constants/roles";
import BigHolidayCalendar from "@/components/Calendar/BigHolidayCalendar";

// Define the type for a Holiday

// First, modify how you use useRoleAuth
function CalendarPage() {
  // Add this line instead
  const { hasAccess, roles } = useRoleAuth({
    allowedRoles: ROLE_GROUPS.USER_ROLES,
    redirectTo: "/unauthorized",
  });

  const [loading, setLoading] = useState(false);

  const breadcrumbItems = [
    {
      label: "",
      href: "/",
      icon: <HomeOutlined sx={{ mr: 0.5 }} fontSize="inherit" />,
    },
    { label: "Employee", href: "" },
    { label: "Holiday" },
  ];

  const isMobile = useMediaQuery("(max-width:768px)");

  return (
    <Box className="calendar-container">
      {loading && <Loader loading={loading} />}

      <Box className="content">
        <Box className="calendar-header">
          <Box className="breadcrumbs-box">
            <Typography variant="h2">Calendar</Typography>
            <BreadcrumbsComponent items={breadcrumbItems} />
          </Box>
        </Box>

        <Box>
          <BigHolidayCalendar />
        </Box>
      </Box>
    </Box>
  );
}

export default CalendarPage;

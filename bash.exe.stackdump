Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB24130000 ntdll.dll
7FFB23DF0000 KERNEL32.DLL
7FFB21560000 KERNELBASE.dll
7FFB22680000 USER32.dll
7FFB21D80000 win32u.dll
7FFB22C50000 GDI32.dll
7FFB21440000 gdi32full.dll
7FFB21AB0000 msvcp_win.dll
7FFB21920000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB23560000 advapi32.dll
7FFB24040000 msvcrt.dll
7FFB22A00000 sechost.dll
7FFB21DB0000 bcrypt.dll
7FFB22B30000 RPCRT4.dll
7FFB20BE0000 CRYPTBASE.DLL
7FFB21DE0000 bcryptPrimitives.dll
7FFB23520000 IMM32.DLL

// Import Google Fonts
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");

// Define global variables
:root {
  --font-family: "Roboto", sans-serif;

  // Primary colors
  --primary-color: #f26522;
  --primary-light: #ff7b3d;
  --primary-dark: #5c260c;

  // Secondary colors
  --secondary-color: #3b7080;

  // Text colors
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-light: #fffaf8;
  --text-dark: #202c4b;

  // Background colors
  --bg-light: #fffaf8;
  --bg-gray: #e5e7eb;
  --bg-lighter: #f8f9fa;

  // Border colors
  --border-color: #e5e7eb;

  // Spacing
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  // Border radius
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;

  // Shadows
  --shadow-sm: 0px 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0px 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0px 10px 15px rgba(0, 0, 0, 0.1);

  // Avatar sizes
  --avatar-sm: 24px;
  --avatar-md: 40px;
  --avatar-lg: 56px;
}

// Global styles
body {
  font-family: var(--font-family);
  margin: 0;
  padding: 0;
  color: var(--text-primary);
  background-color: #fff;
}

// Typography
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-family);
  font-weight: 500;
  margin-top: 0;
  color: var(--text-dark);
}

h1 {
  font-size: 24px;
  font-weight: 600;
}

h2 {
  font-size: 20px;
  font-weight: 600;
}

h3 {
  font-size: 18px;
  font-weight: 600;
}

h4 {
  font-size: 16px;
  font-weight: 600;
}

h5 {
  font-size: 14px;
  font-weight: 500;
}

h6 {
  font-size: 14px;
  font-weight: 500;
}

p,
span,
div,
button,
input,
select,
textarea {
  font-family: var(--font-family);
}

// Typography classes
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-sm {
  font-size: 12px;
}

.text-md {
  font-size: 14px;
}

.text-lg {
  font-size: 16px;
}

// Spacing classes
.margin-bottom-xs {
  margin-bottom: var(--spacing-xs) !important;
}

.margin-bottom-sm {
  margin-bottom: var(--spacing-sm) !important;
}

.margin-bottom-md {
  margin-bottom: var(--spacing-md) !important;
}

.margin-bottom-lg {
  margin-bottom: var(--spacing-lg) !important;
}

.margin-top-xs {
  margin-top: var(--spacing-xs) !important;
}

.margin-top-sm {
  margin-top: var(--spacing-sm) !important;
}

.margin-top-md {
  margin-top: var(--spacing-md) !important;
}

.margin-top-lg {
  margin-top: var(--spacing-lg) !important;
}

// Button styles
button {
  font-family: var(--font-family);
}

.required {
  color: red;
}

// Global MUI Dialog styling
.MuiDialog-root {
  .MuiBackdrop-root {
    pointer-events: none; // This prevents the backdrop from being clickable
  }

  .MuiDialog-paper {
    pointer-events: auto; // This ensures the dialog itself is still interactive
  }

  .MuiDialogTitle-root {
    border-color: var(--border-color);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) !important;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .MuiTypography-root {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-dark);
    }
  }

  .MuiDialogContent-root {
    padding: var(--spacing-md) !important;
  }

  .MuiDialogActions-root {
    padding: var(--spacing-md);
    border-color: var(--border-color);
    border-top: 1px solid var(--border-color);
    margin-top: var(--spacing-md);
  }

  //Global label styling for all dialogs
  label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
    margin-top: var(--spacing-md);
    padding: 0px;
    display: block;
  }
}

// Global form styling
label,
.field-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: var(--spacing-sm);
  display: block;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.form-field {
  margin-bottom: var(--spacing-md);
}

// Input field styling
.MuiInputBase-root,
.MuiOutlinedInput-root {
  min-height: 38px;
  max-height: 38px;

  .MuiInputBase-input,
  .MuiOutlinedInput-input {
    padding: 8px 12px !important;
    font-size: 14px;
  }

  // Add focus state styling
  &.Mui-focused {
    .MuiOutlinedInput-notchedOutline {
      // border: none;
      border-color: var(--border-color) !important; // Material UI's default blue color
      border-width: 2px;
    }
  }

  // Add hover state styling
  &:hover {
    .MuiOutlinedInput-notchedOutline {
      border-color: var(--border-color) !important;
    }
  }
}

// Button styling
.MuiButton-root {
  text-transform: none;
  font-weight: 500;
  border-radius: var(--border-radius-sm);

  &.MuiButton-contained {
    &.MuiButton-containedPrimary {
      background-color: var(--primary-color);
      color: white;
      padding: 8px 16px;
      font-size: 14px;

      &:hover {
        background-color: var(--primary-dark);
        box-shadow: var(--shadow-md);
      }
    }

    &.MuiButton-containedSecondary {
      background-color: white;
      color: var(--text-primary);
      border: 1px solid var(--border-color);
      padding: 8px 16px;
      font-size: 14px;
    }
  }

  &.MuiButton-outlined {
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    font-size: 14px;
  }

  &.MuiButton-text {
    font-size: 14px;
    padding: 8px 16px;
  }
}

// Card styling
.MuiCard-root {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);

  .MuiCardContent-root {
    padding: var(--spacing-md) !important;
  }

  .MuiCardHeader-root {
    padding: var(--spacing-md);
  }
}

// Avatar styling
.MuiAvatar-root {
  &.avatar-sm {
    width: var(--avatar-sm);
    height: var(--avatar-sm);
    font-size: 12px;
  }

  &.avatar-md {
    width: var(--avatar-md);
    height: var(--avatar-md);
    font-size: 16px;
  }

  &.avatar-lg {
    width: var(--avatar-lg);
    height: var(--avatar-lg);
    font-size: 24px;
  }
}
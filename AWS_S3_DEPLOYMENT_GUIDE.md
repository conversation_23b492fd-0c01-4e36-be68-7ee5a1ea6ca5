# AWS S3 Deployment Guide for Next.js Static Export

This guide explains how to properly configure AWS S3 to host the Next.js static export of the HRMS application.

## S3 Bucket Configuration

1. **Create or use an existing S3 bucket**
   - Make sure the bucket is configured for static website hosting

2. **Enable Static Website Hosting**
   - Go to the bucket properties
   - Enable "Static website hosting"
   - Set "Index document" to `index.html`
   - Set "Error document" to `error.html`

3. **Configure Bucket Policy for Public Access**
   - If the site needs to be publicly accessible, configure the bucket policy:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::YOUR-BUCKET-NAME/*"
        }
    ]
}
```

## CloudFront Configuration (Recommended)

For better performance and additional features, it's recommended to use CloudFront:

1. **Create a CloudFront Distribution**
   - Origin: Your S3 bucket website endpoint
   - Viewer Protocol Policy: Redirect HTTP to HTTPS
   - Allowed HTTP Methods: GET, HEAD, OPTIONS
   - Cache Policy: CachingOptimized

2. **Configure Error Pages**
   - Create a custom error response for 404 errors
   - HTTP Error Code: 404
   - Response Page Path: `/error.html`
   - HTTP Response Code: 200

## Deployment Process

1. **Build the Next.js application**
   ```bash
   npm run build
   ```

2. **Upload the contents of the `build_dir` folder to your S3 bucket**
   ```bash
   aws s3 sync build_dir/ s3://YOUR-BUCKET-NAME --delete
   ```

3. **Invalidate CloudFront cache (if using CloudFront)**
   ```bash
   aws cloudfront create-invalidation --distribution-id YOUR-DISTRIBUTION-ID --paths "/*"
   ```

## Troubleshooting

If you encounter routing issues:

1. **Check that all files were uploaded correctly**
   - Ensure all HTML files are uploaded with `Content-Type: text/html`

2. **Verify error page configuration**
   - Make sure the error.html page is properly configured

3. **Test deep linking**
   - Try accessing a deep link directly and check if the routing works

4. **Check CloudFront configuration**
   - Ensure the origin is correctly set to the S3 website endpoint (not the bucket endpoint)
   - Verify that error pages are properly configured

## Additional Resources

- [AWS S3 Static Website Hosting Documentation](https://docs.aws.amazon.com/AmazonS3/latest/userguide/WebsiteHosting.html)
- [CloudFront Documentation](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/Introduction.html)
- [Next.js Static Export Documentation](https://nextjs.org/docs/pages/building-your-application/deploying/static-exports)

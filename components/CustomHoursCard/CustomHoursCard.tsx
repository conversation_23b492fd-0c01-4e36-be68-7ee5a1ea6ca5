import React from "react";
import { Box, Divider, Typography, SxProps, Theme } from "@mui/material";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";

interface CustomHoursCardProps {
    icon: React.ReactNode;
    value: string | number;
    total: string | number;
    title: string;
    subtitle: string;
    color: string;
    sx?: SxProps<Theme>;
}

const CustomHoursCard: React.FC<CustomHoursCardProps> = ({
    icon,
    value,
    total,
    title,
    subtitle,
    color,
    sx,
}) => {
    return (
        <Box
            sx={{
                flex: 1,
                backgroundColor: "#fff",
                boxShadow: "0px 4px 8px rgba(0,0,0,0.1)",
                borderRadius: "8px",
                padding: "16px",
                ...sx,
            }}
        >
            {/* Icon with background */}
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: color,
                    width: "28px",
                    height: "28px",
                    borderRadius: "5px",
                    margin: "0 0 8px",
                }}
            >
                {icon}
            </Box>

            {/* Hours Display */}
            <Typography sx={{ fontSize: "20px", fontWeight: "bold" }}>
                {value} / {total}
            </Typography>

            {/* Title */}
            <Typography sx={{ fontSize: "14px", color: "#6B7280", marginBottom: ".5rem" }}>
                {title}
            </Typography>

            {/* <Divider /> */}

            {/* Percentage Change */}
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    marginTop: "8px",
                    // color: percentage >= 0 ? "green" : "red",
                    fontWeight: "600",
                }}
            >
                {/* {percentage >= 0 ? <ArrowUpwardIcon fontSize="small" /> : <ArrowDownwardIcon fontSize="small" />}
                <Typography sx={{ fontSize: "12px", marginLeft: "4px" }}>{Math.abs(percentage)}% {subtitle}</Typography> */}
            </Box>
        </Box>
    );
};

export default CustomHoursCard;

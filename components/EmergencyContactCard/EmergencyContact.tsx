"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  Box,
  Typography,
  Card<PERSON>ontent,
  IconButton,
  Avatar,
} from "@mui/material";
import { EditNote, CallOutlined } from "@mui/icons-material";
import "./EmergencyContact.scss";
import EmergencyContactDialog from "../EmergencyContactDialog/EmergencyContactDialog";
import { getUserById } from "@/app/services/users.service";

// interface EmergencyContactData {
//   _id?: string;
//   userId: string;
//   name: string;
//   relationship: string;
//   phone1: string;
//   phone2: string;
// }

interface ContactData {
  primaryName: string;
  primaryRelationship: string;
  primaryPhone1: string;
  primaryPhone2: string;
  primaryId?: string;
}

interface EmergencyContactProps {
  employeeId: string | null;
}

const EmergencyContact: React.FC<EmergencyContactProps> = ({ employeeId }) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [contactData, setContactData] = useState<ContactData>({
    primaryName: "",
    primaryRelationship: "",
    primaryPhone1: "",
    primaryPhone2: "",
    primaryId: undefined,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchEmergencyContacts = async () => {
      if (!employeeId) {
        setContactData({
          primaryName: "",
          primaryRelationship: "",
          primaryPhone1: "",
          primaryPhone2: "",
        });
        setLoading(false);
        return;
      }

      try {
        const userData = await getUserById(employeeId);
        console.log("Emergency contact data:", userData.user.emergencyContact);

        // Check if emergencyContact exists and is an array
        const emergencyContacts = Array.isArray(userData.user.emergencyContact)
          ? userData.user.emergencyContact
          : [];

        // Get the first contact or use empty object
        const primary =
          emergencyContacts.length > 0 ? emergencyContacts[0] : {};

        setContactData({
          primaryName: primary.name || "",
          primaryRelationship: primary.relationship || "",
          primaryPhone1: primary.phone1 || "",
          primaryPhone2: primary.phone2 || "",
          primaryId: primary._id,
        });
      } catch (error) {
        console.error("Error fetching emergency contacts:", error);
        setContactData({
          primaryName: "",
          primaryRelationship: "",
          primaryPhone1: "",
          primaryPhone2: "",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchEmergencyContacts();
  }, [employeeId]);

  const handleEditClick = () => {
    setOpenDialog(true);
  };

  const handleSave = (data: ContactData) => {
    setContactData(data);
    setOpenDialog(false);
  };

  if (loading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card className="emergency-contact">
      <Box className="header">
        <Typography variant="h6">Emergency Contact Number</Typography>
        <IconButton onClick={handleEditClick} sx={{ padding: "4px" }}>
          <EditNote sx={{ fontSize: "16px" }} />
        </IconButton>
      </Box>

      <CardContent>
        <Box className="primary">
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Avatar
              className="avatar-md"
              sx={{
                bgcolor: "var(--primary-color)",
                mr: 2,
              }}
            >
              {contactData.primaryName?.charAt(0)}
            </Avatar>
            <Box>
              <Typography component="h6">
                {contactData.primaryName}
                <span>({contactData.primaryRelationship})</span>
              </Typography>

              <Box sx={{ display: "flex", gap: 2, mt: 0.5 }}>
                <Typography className="phone-number">
                  <CallOutlined className="icon" />
                  {contactData.primaryPhone1}
                </Typography>

                {contactData.primaryPhone2 &&
                  contactData.primaryPhone2 !== "" && (
                    <Typography className="phone-number">
                      <CallOutlined className="icon" />
                      {contactData.primaryPhone2}
                    </Typography>
                  )}
              </Box>
            </Box>
          </Box>
        </Box>
      </CardContent>

      <EmergencyContactDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        initialData={{
          ...contactData,
          secondaryName: "",
          secondaryRelationship: "",
          secondaryPhone1: "",
          secondaryPhone2: "",
        }}
        onSave={handleSave}
        employeeId={employeeId}
      />
    </Card>
  );
};

export default EmergencyContact;

"use client";

import type React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
} from "@mui/material";

interface DeleteConfirmationDialogProps {
  open: boolean;
  title?: string;
  message?: string;
  onClose: () => void;
  onConfirm: () => void;
}

const StatusChangeConfirmation: React.FC<DeleteConfirmationDialogProps> = ({
  open,
  title = "",
  message = "",
  onClose,
  onConfirm,
}) => {
  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: "8px",
          width: "100%",
          maxWidth: "400px",
          p: 2,
        },
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          textAlign: "center",
        }}
      >
        {/* <Box
          sx={{
            backgroundColor: "#FFDDDD",
            borderRadius: "8px",
            p: 2,
            mb: 2,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            width: "60px",
            height: "60px",
          }}
        >
         <DeleteIcon sx={{ color: "#e70d0d ", fontSize: "28px" }} />
        </Box> */}

        <DialogTitle sx={{ p: 0, padding: "0px" }}>
          <Typography
            component="div"
            sx={{ fontWeight: "bold", color: "#202c4b", fontSize: "1.125rem" }}
          >
            {title}
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ p: 0, mb: 3 }}>
          <Typography
            variant="body1"
            sx={{
              color: "#848484",
              fontSize: "14px",
              fontFamily: "Roboto, sans-serif",
              lineHeight: 1.5,
            }}
          >
            {message}
          </Typography>
        </DialogContent>

        <DialogActions
          sx={{
            p: 0,
            padding: "1rem 0px 0px 0px  !important",
            width: "100%",
            justifyContent: "space-between",
            margin: "0px !important",
          }}
        >
          <Button
            onClick={onClose}
            sx={{
              bgcolor: "#F1F1F1",
              color: "#333333",
              px: 3,
              py: 1,
              borderRadius: "4px",
              textTransform: "none",
              "&:hover": {
                bgcolor: "#E0E0E0",
              },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            sx={{
              bgcolor: "#E30000",
              color: "white",
              px: 3,
              py: 1,
              borderRadius: "4px",
              textTransform: "none",
              margin: "0px !important",
              "&:hover": {
                bgcolor: "#C00000",
              },
            }}
          >
            Yes, Change
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};

export default StatusChangeConfirmation;

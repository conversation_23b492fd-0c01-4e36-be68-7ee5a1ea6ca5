import * as React from "react";
import Link from "next/link";
import "./FamilyInfoAccordion.scss";
import {
  Box,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Dialog,
  DialogActions,
  TextField,
  DialogContent,
  DialogTitle,
  MenuItem,
  IconButton,
} from "@mui/material";
import Typography from "@mui/material/Typography";
import { Close, EditNote, ExpandMore } from "@mui/icons-material";
import { addFamily, updateFamily } from "@/app/services/users.service";
import { toast } from "react-toastify";
import * as Yup from "yup";
import { RelationshipType, MaritalStatus } from "@/app/types/common";

interface FamilyInfoAccordionProps {
  userData: {
    user: {
      _id: string;
      maritalStatus: MaritalStatus;
      family: {
        _id?: string;
        name: string;
        relationship: RelationshipType;
        phone: string;
        dob: string;
      }[];
    };
  } | null;
}

interface familyEmpId {
  employeeId: string | null;
}

// Calculate date 18 years ago
const eighteenYearsAgo = new Date();
eighteenYearsAgo.setFullYear(eighteenYearsAgo.getFullYear() - 18);

const familyValidationSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  relationship: Yup.string()
    .required("Relationship is required")
    .test(
      "valid-relationship",
      "Wife cannot be selected for single status",
      function (value) {
        const isUserSingle =
          this.parent.userMaritalStatus === MaritalStatus.Single;
        return !(isUserSingle && value === RelationshipType.Wife);
      }
    ),
  phone: Yup.string()
    .required("Phone number is required")
    .matches(/^\d+$/, "Phone number should only contain digits")
    .length(10, "Phone number must be exactly 10 digits"),
  dob: Yup.date()
    .required("Date of birth is required")
    .max(eighteenYearsAgo, "Family member must be at least 18 years old")
    .typeError("Please enter a valid date"),
});

export default function FamilyInfoAccordion({
  userData,
  employeeId,
}: FamilyInfoAccordionProps & familyEmpId) {
  const [open, setOpen] = React.useState(false);
  const [familyData, setFamilyData] = React.useState({
    familyId: employeeId,
    name: "",
    relationship: "",
    phone: "",
    dob: "",
  });
  const [errors, setErrors] = React.useState<{ [key: string]: string }>({});
  const [touched, setTouched] = React.useState<{ [key: string]: boolean }>({
    name: false,
    relationship: false,
    phone: false,
    dob: false,
  });

  //const familyId = userData?.user?.family[0]?._id;
  const userId = userData?.user?._id;

  // Get user's marital status
  const userMaritalStatus =
    userData?.user?.maritalStatus || MaritalStatus.Single;

  // Function to check if relationship option should be disabled
  const isRelationshipDisabled = (relation: RelationshipType) => {
    return (
      relation === RelationshipType.Wife &&
      userMaritalStatus === MaritalStatus.Single
    );
  };

  React.useEffect(() => {
    if (
      userData?.user?.family &&
      userData.user.family.length > 0 &&
      userData.user.family[0]
    ) {
      const family = userData.user.family[0];
      setFamilyData({
        ...familyData,
        name: family?.name || "",
        relationship: family?.relationship || "",
        phone: family?.phone || "",
        dob: family?.dob ? family.dob.split("T")[0] : "", // Format to YYYY-MM-DD
      });
    } else {
      // Reset to empty values if no family data exists
      setFamilyData({
        ...familyData,
        name: "",
        relationship: "",
        phone: "",
        dob: "",
      });
    }
  }, [userData]);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const validateField = async (name: string, value: string) => {
    try {
      // Mark field as touched
      setTouched((prev) => ({ ...prev, [name]: true }));

      await familyValidationSchema.validateAt(name, {
        [name]: value,
        userMaritalStatus, // Add this for relationship validation
      });
      setErrors((prev) => ({ ...prev, [name]: "" }));
      return true;
    } catch (error: any) {
      setErrors((prev) => ({ ...prev, [name]: error.message }));
      return false;
    }
  };

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFamilyData((prev) => ({ ...prev, [name]: value }));
    await validateField(name, value);
  };

  // Add blur handler to mark fields as touched
  const handleBlur = async (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setTouched((prev) => ({ ...prev, [name]: true }));
    await validateField(name, value);
  };

  const handleSave = async () => {
    try {
      // Validate all fields
      await familyValidationSchema.validate(familyData, { abortEarly: false });

      const familyPayload = {
        userId: employeeId || userId || "",
        name: familyData.name,
        relationship: familyData.relationship,
        phone: familyData.phone,
        dob: familyData.dob,
      };

      try {
        // Always use addFamily (POST API)
        const newFamily = await addFamily(familyPayload);
        toast.success("Family details saved successfully");
        handleClose();
      } catch (error) {
        console.error("Failed to save family details:", error);
        // toast.error("Failed to save family details. Please try again.");
      }
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        // Handle validation errors
        const validationErrors: { [key: string]: string } = {};
        error.inner.forEach((err) => {
          if (err.path) {
            validationErrors[err.path] = err.message;
          }
        });
        setErrors(validationErrors);
        // toast.error("Please check all fields and try again");
      } else {
        console.error(error);
        // toast.error("Failed to save family details");
      }
    }
  };

  // Modify the display in the UI to show "N/A" when value is empty
  const displayValue = (value: string) => value || "";

  return (
    <>
      <Accordion className="info-accordion">
        <AccordionSummary
          className="accordion-summary"
          expandIcon={<ExpandMore />}
          aria-controls="panel1-content"
          id="panel1-header"
          sx={{ padding: "20px" }}
        >
          <Typography
            className="span"
            component="span"
            sx={{
              margin: "0px",
              fontSize: "16px",
              fontWeight: 600,
              display: "flex",
              justifyContent: "space-between",
              minWidth: "100%",
            }}
          >
            Family Information
            <Link
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleOpen();
              }}
            >
              <EditNote sx={{ fontSize: "16px", cursor: "pointer" }} />
            </Link>
          </Typography>
        </AccordionSummary>
        <AccordionDetails
          className="accordion-details"
          sx={{ padding: "20px", display: "flex" }}
        >
          <Box className="name">
            <Typography
              className="span"
              component="span"
              sx={{ margin: "0px" }}
            >
              Name
            </Typography>
            <Typography variant="h6">
              {displayValue(familyData.name)}
            </Typography>
          </Box>
          <Box className="relationship">
            <Typography
              className="span"
              component="span"
              sx={{ margin: "0px" }}
            >
              Relationship
            </Typography>
            <Typography variant="h6">
              {displayValue(familyData.relationship)}
            </Typography>
          </Box>
          <Box className="DOB">
            <Typography
              className="span"
              component="span"
              sx={{ margin: "0px" }}
            >
              Date of Birth
            </Typography>
            <Typography variant="h6">{displayValue(familyData.dob)}</Typography>
          </Box>
          <Box className="phone">
            <Typography
              className="span"
              component="span"
              sx={{ margin: "0px" }}
            >
              Phone
            </Typography>
            <Typography variant="h6">
              {displayValue(familyData.phone)}
            </Typography>
          </Box>
        </AccordionDetails>
      </Accordion>

      <Dialog
        className="edit-dialog"
        open={open}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleClose();
          }
        }}
        disableEscapeKeyDown
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography
            variant="h6"
            sx={{ fontSize: "20px", fontWeight: "600", color: "#202C4B" }}
          >
            Family Information
          </Typography>
          <IconButton
            onClick={handleClose}
            aria-label="close"
            sx={{
              backgroundColor: "#6b7280",
              backgroundImage: "none",
              borderRadius: "50%",
              color: "#fff",
              height: "20px",
              width: "20px",
              margin: 0,
              padding: 0,
              "&:hover": {
                backgroundColor: "#d55a1d",
              },
              "& .MuiSvgIcon-root": {
                fontSize: "14px",
              },
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{
            display: "flex",
            flexDirection: "column",
            padding: "1rem",
          }}
        >
          <Box sx={{ width: "100%" }}>
            <label>
              Name <span className="required">*</span>
            </label>
            <TextField
              name="name"
              value={familyData.name}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.name && Boolean(errors.name)}
              helperText={touched.name && errors.name}
              fullWidth
              InputLabelProps={{ shrink: true }}
            />
          </Box>
          <Box sx={{ width: "100%" }}>
            <label>
              Relationship <span className="required">*</span>
            </label>
            <TextField
              select
              fullWidth
              name="relationship"
              value={familyData.relationship}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.relationship && Boolean(errors.relationship)}
              helperText={touched.relationship && errors.relationship}
              margin="normal"
            >
              {Object.values(RelationshipType).map((relation) => (
                <MenuItem
                  key={relation}
                  value={relation}
                  disabled={isRelationshipDisabled(relation)}
                >
                  {relation}
                  {isRelationshipDisabled(relation) &&
                    " (Not available for single status)"}
                </MenuItem>
              ))}
            </TextField>
          </Box>
          <Box sx={{ width: "100%" }}>
            <label>
              Phone <span className="required">*</span>
            </label>
            <TextField
              name="phone"
              value={familyData.phone}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.phone && Boolean(errors.phone)}
              helperText={touched.phone && errors.phone}
              fullWidth
              InputLabelProps={{ shrink: true }}
              inputProps={{
                maxLength: 10,
                pattern: "[0-9]*",
                onKeyPress: (e) => {
                  if (!/[0-9]/.test(e.key)) {
                    e.preventDefault();
                  }
                },
              }}
            />
          </Box>
          <Box sx={{ width: "100%" }}>
            <label>
              Date of Birth <span className="required">*</span>
            </label>
            <TextField
              type="date"
              name="dob"
              value={familyData.dob}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.dob && Boolean(errors.dob)}
              helperText={touched.dob && errors.dob}
              fullWidth
              InputLabelProps={{ shrink: true }}
              inputProps={{
                max: eighteenYearsAgo.toISOString().split("T")[0], // Prevents selecting dates less than 18 years ago
              }}
            />
          </Box>

          <DialogActions
            sx={{ padding: "0px !important", paddingTop: "1rem !important" }}
          >
            <Button
              sx={{
                fontWeight: "400",
                fontSize: "14px",
                color: "#111827",
                border: "1px solid #E5E7EB",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={handleClose}
              color="secondary"
            >
              Cancel
            </Button>
            <Button
              sx={{
                backgroundColor: "#F26522",
                border: "1px solid #F26522",
                color: "#FFF",
                borderRadius: "5px",
                padding: "0.5rem 0.85rem",
                fontSize: "14px",
                transition: "all 0.5s",
                fontWeight: 500,
                textTransform: "none",
              }}
              onClick={handleSave}
              color="primary"
            >
              Save
            </Button>
          </DialogActions>
        </DialogContent>
      </Dialog>
    </>
  );
}

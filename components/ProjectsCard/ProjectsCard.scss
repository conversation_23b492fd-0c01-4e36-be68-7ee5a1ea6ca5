.MuiCard-root {
    // margin-bottom: 1.5rem !important;
    // background-color: #FFF !important;
    // transition: all 0.5s ease-in-out !important;
    // position: relative !important;
    // border-radius: 5px !important;
    // border: 1px solid #E5E7EB !important;
    // box-shadow: 0px 1px 1px 1px rgba(198, 198, 198, 0.2) !important;

    .card-content {
        padding: 1.25rem !important;

        .card-title {
            font-size: 14px;
            font-weight: 600;
        }

        .deadline-text-box {
            flex: 0 0 auto;
            width: 33.33333333%;
            padding: 0px 12px !important;
            height: 62px;
        }

        .deadline-text-label {
            margin-bottom: 0.5rem !important;
            color: #6B7280 !important;
        }

        .deadline-text-value {
            color: #212529 !important;
            font-size: 14px !important;
        }

        .footer-progress {
            padding: 0.5rem !important;
            color: #6B7280 !important;
            background-color: #F8F9FA !important;
            border: 1px solid #F8F9FA !important;
        }
    }
}
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON>po<PERSON>,
  Avatar,
  LinearProgress,
  Stack,
  Divider,
  <PERSON><PERSON><PERSON>,
} from "@mui/material";
import "./ProjectsCard.scss";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { Circle } from "@mui/icons-material";

interface Project {
  _id: string;
  title: string;
  tasks: number;
  completedTasks: number;
  deadline: string;
  value: string;
  lead: {
    name: string;
    avatar: string;
  };
  totalHours: number;
  completion: number;
  color?: string;
  bgImage: string;
}

interface ProjectsCardProps {
  project: Project;
  onProjectClick: () => void;
  isSelected?: boolean;
}

const ProjectsCard: React.FC<ProjectsCardProps> = ({ project, onProjectClick }) => {
  return (
    <Card variant="outlined" sx={{ mb: 2, borderRadius: 2 }}>
      <CardContent className="card-content">
        {/* Header */}
        <Stack direction="row" alignItems="center" spacing={1}>
          <Box
            sx={{
              width: 37,
              height: 37,
              backgroundImage: `url(${project.bgImage})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              borderRadius: "50%",
            }}
          />
          <Box>
            <Typography
              component="a"
              href="#"
              className="card-title"
              onClick={(e) => {
                e.preventDefault(); // Prevent default link behavior
                onProjectClick();
              }}
            >
              {project.title}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ alignItems: "center" }}
            >
              {project.tasks} tasks{" "}
              <Circle
                sx={{
                  fontSize: "7px",
                  color: "#F26522",
                  marginRight: ".25rem !important",
                  marginLeft: ".25rem !important",
                }}
              />{" "}
              {project.completedTasks} Completed
            </Typography>
          </Box>
        </Stack>

        <Divider sx={{ my: 2 }} />

        {/* Info */}
        <Stack direction="row">
          <Box className="deadline-text-box">
            <Typography className="deadline-text-label" variant="body2">
              Deadline
            </Typography>
            <Typography className="deadline-text-value">
              {project.deadline}
            </Typography>
          </Box>

          <Box className="deadline-text-box">
            <Typography className="deadline-text-label" variant="body2">
              Value
            </Typography>
            <Typography className="deadline-text-value">
              {project.value}
            </Typography>
          </Box>

          <Box className="deadline-text-box">
            <Typography className="deadline-text-label" variant="body2">
              Project Lead
            </Typography>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Avatar
                src={project.lead.avatar}
                alt={project.lead.name}
                sx={{ width: 20, height: 20 }}
              />
              <Typography variant="body2">{project.lead.name}</Typography>
            </Stack>
          </Box>
        </Stack>

        {/* Footer: Total Hours and Progress */}
        <Box className="footer-progress" mt={2}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Stack direction="row" spacing={1} alignItems="center">
              <AccessTimeIcon
                sx={{ fontSize: 14, color: "#F26522 !important" }}
              />
              <Typography variant="body2" color="text.secondary">
                Total {project.totalHours} Hrs
              </Typography>
            </Stack>

            <Box sx={{ flex: "0 0 auto", width: "50%" }}>
              <Typography
                sx={{
                  color: "#212529 !important",
                  marginBottom: ".25rem!important",
                  fontSize: "0.875em !important",
                }}
              >
                {project.completion}% Completed
              </Typography>
              <LinearProgress
                variant="determinate"
                value={project.completion}
                sx={{
                  height: 5,
                  borderRadius: 3,
                  [`& .MuiLinearProgress-bar`]: {
                    backgroundColor: project.color,
                  },
                }}
              />
            </Box>
          </Stack>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ProjectsCard;
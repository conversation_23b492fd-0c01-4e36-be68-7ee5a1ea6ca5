"use client";

import { useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { ToggleButton, ToggleButtonGroup } from "@mui/material";
import ViewListIcon from "@mui/icons-material/ViewList";
import GridViewIcon from "@mui/icons-material/GridView";

const ViewToggleHolidays = () => {
    const router = useRouter();
    const pathname = usePathname();

    // Determine the active view based on the current route
    const [view, setView] = useState<"list" | "grid">(
        pathname.includes("holidaysgrid") ? "grid" : "list"
    );

    const handleChange = (_: React.MouseEvent<HTMLElement>, newView: "list" | "grid" | null) => {
        if (newView !== null) {
            setView(newView);
            router.push(newView === "list" ? "/holidays" : "/holidaysgrid");
        }
    };

    return (
        <ToggleButtonGroup value={view} exclusive onChange={handleChange} sx={{ maxHeight: "38px", borderRadius: 2, overflow: "hidden" }}>
            <ToggleButton
                value="list"
                sx={{ bgcolor: view === "list" ? "#F26522 !important" : "transparent", borderRadius: 2 }}
            >
                <ViewListIcon sx={{ color: view === "list" ? "white" : "gray", width: "13px", height: "13px" }} />
            </ToggleButton>
            <ToggleButton
                value="grid"
                sx={{ bgcolor: view === "grid" ? "#F26522 !important" : "transparent", borderRadius: 2 }}
            >
                <GridViewIcon sx={{ color: view === "grid" ? "white" : "gray", width: "13px", height: "13px" }} />
            </ToggleButton>
        </ToggleButtonGroup>
    );
};

export default ViewToggleHolidays;

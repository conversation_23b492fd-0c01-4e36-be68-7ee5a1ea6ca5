"use client";
import { useState } from "react";
import {
  TextField,
  Button,
  Typography,
  Box,
  InputAdornment,
} from "@mui/material";
import { Email } from "@mui/icons-material";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import "./ForgetPasswordForm.scss";
import { toast, ToastContainer } from "react-toastify";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { addForgetPasswordSection } from "@/app/services/auth/auth.service";
import Loader from "../Loader/Loader";

// Validation schema using Yup
const ForgetPasswordFormSchema = Yup.object().shape({
  email: Yup.string()
    .email("Invalid email address")
    .required("Email is required"),
});

const ForgetPasswordForm: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const router = useRouter();

  const initialValues = {
    email: "",
  };

  const handleLoginAccount = () => {
    router.push("/login");
  };

  const handleSubmit = async (values: { email: string }) => {
    setIsLoading(true);
    try {
      const response = await addForgetPasswordSection({ email: values.email });
      setIsLoading(false);
      toast.success("Password reset email sent successfully!");
      setTimeout(() => router.push("/login"), 2000);
      console.log("API Response:", response);
    } catch (error) {
      setIsLoading(false);
      console.error("API Error:", error);
      const errorResponse = error as { response: { data: { message: string } } };
      const errorMessage = errorResponse?.response?.data?.message || "An error occurred";
      toast.error<string>(errorMessage);
  };
}

  return (
    <div className="login-form-container">
      <ToastContainer />
      <div className="logo-container">
        <div className="logo">
          <Typography variant="h6" component="span" className="logo-text">
            <Image
              src="/assets/Aadvik-noBG.png"
              alt="Logo"
              width={300}
              height={100}
              className="logo-image"
            />
          </Typography>
        </div>
      </div>

      <Box className="form-wrapper">
        <Typography variant="h4" component="h1" className="form-title">
          Forget Password
        </Typography>
        <Typography
          variant="body2"
          color="textSecondary"
          className="form-subtitle"
        >
          If you forgot your password, we'll email you instructions
          to reset your password.
        </Typography>

        <Formik
          initialValues={initialValues}
          validationSchema={ForgetPasswordFormSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isSubmitting }) => (
            <Form className="login-form">
              <div className="form-field">
                <Typography variant="body2" className="field-label">
                  Email Address
                </Typography>
                <Field
                  as={TextField}
                  name="email"
                  type="email"
                  fullWidth
                  placeholder="Enter your email"
                  variant="outlined"
                  disabled={isLoading || isSubmitting}
                  error={touched.email && !!errors.email}
                  helperText={<ErrorMessage name="email" />}
                  className="input-field"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Email className="field-icon" />
                      </InputAdornment>
                    ),
                  }}
                />
              </div>

              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                disabled={isLoading || isSubmitting}
                className="submit-button"
                startIcon={isLoading ? <Loader loading={isLoading} /> : null}
              >
                {isLoading ? "Submitting..." : "Submit"}
              </Button>
            </Form>
          )}
        </Formik>

        <div className="account-options">
          <Typography variant="body2" className="no-account">
            Return to{" "}
            <Button
              variant="text"
              color="primary"
              className="create-account"
              disabled={isLoading}
              onClick={handleLoginAccount}
            >
              Sign In
            </Button>
          </Typography>
        </div>

        <Typography variant="body2" className="copyright">
          Copyright © 2024 - 2025 AAdvik Labs
        </Typography>
      </Box>
    </div>
  );
};

export default ForgetPasswordForm;
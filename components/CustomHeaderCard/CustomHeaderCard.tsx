import React from 'react';
import { Box, Typography } from '@mui/material';
import { SvgIconComponent } from '@mui/icons-material';

type CustomHeaderCardProps = {
    icon: SvgIconComponent;
    title: string;
    value: string | number;
    percentage: string;
    iconBgColor?: string;
    badgeBgColor?: string;
    badgeTextColor?: string;
    showIcon?: boolean; 
};

const CustomHeaderCard: React.FC<CustomHeaderCardProps> = ({
    icon: Icon,
    title,
    value,
    percentage,
    iconBgColor = "black",
    badgeBgColor = "#F0DEF3",
    badgeTextColor = "#AB47BC",
    showIcon = false, // Default value is false
}) => {
    // Parse the percentage string to determine if it's positive or negative
    const isPositive = percentage.startsWith('+');
    const percentageColor = isPositive ? '#03C95A' : '#E70D0D';

    return (
        <Box
            className="card"
            sx={{
                // boxShadow: "0px 0px 0px 2px rgba(198, 198, 198, 0.2)",
                backgroundColor: "#FFF",
                width: "100%",
                border: "1px solid #e5e7eb",
            }}
        >
            <Box
                className="cardBodyContent"
                sx={{
                    display: "flex",
                    padding: "16px",
                    alignItems: "center",
                    justifyContent: "space-between"
                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        gap: "8px",
                        alignItems: "center",
                    }}
                >
                    {showIcon && ( // Conditionally render the icon
                        <Typography
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                backgroundColor: iconBgColor,
                                borderRadius: "50%",
                                width: "45px",
                                height: "45px"
                            }}
                        >
                            {Icon && <Icon sx={{ color: "white" }} />}
                        </Typography>
                    )}
                    <Box>
                        <Typography sx={{ fontSize: "14px",color:"#6B7280",fontWeight: 500 }}>
                            {title}
                        </Typography>
                        <Typography
                            variant="h5"
                            sx={{ fontSize: "16px", fontWeight: 600,color:"#202C4B" }}
                        >
                            {value}
                        </Typography>
                    </Box>
                </Box>
               {/*  <Box className="badge">
                    <Typography
                        sx={{
                            fontSize: "11px",
                            padding: "5px 8px",
                            lineHeight: "11px",
                            background: badgeBgColor,
                            color: '#FFFFFF', // Use dynamic color based on percentage
                        }}
                    >
                        {percentage}
                    </Typography>
                </Box> */}
            </Box>
        </Box>
    );
};

export default CustomHeaderCard;

// components/TicketCard.tsx
import {
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  Chip,
} from "@mui/material";
import "./TicketCounterCard.scss";
import Image from "next/image";
import { ReactNode } from "react";

type TicketCardProps = {
  graphSvg: string;
  iconPath: ReactNode;
  color: string;
  label: string;
  count: number;
  // percentage: string;
  bgColor: string;
};

const TicketCounterCard = ({
  graphSvg,
  iconPath,
  color,
  label,
  count,
  // percentage,
  bgColor,
}: TicketCardProps) => {
  return (
    <Card className="ticket-card">
      <CardContent className="ticket-card-content">
        <Box sx={{ flex: "0 0 auto", width: "50%" }}>
          <Avatar
            sx={{
              bgcolor: bgColor.replace(" !important", ""),
              border: `2px dotted ${color}`,
              color: color,
              width: "2.813rem",
              height: "2.813rem",
              marginBottom: "1rem",
              padding: ".25rem !important",
              "& > *": {
                // Style for the icon inside Avatar
                width: "20px",
                height: "20px",
                fontSize: "20px",
              },
            }}
          >
            {iconPath}
          </Avatar>

          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: "#6b7280",
                fontSize: "0.75rem !important",
                fontWeight: "500",
                marginBottom: ".25rem",
              }}
            >
              {label}
            </Typography>
            <Typography
              variant="h6"
              sx={{ fontSize: "18px", fontWeight: 600, color: "#202C4B" }}
            >
              {count}
            </Typography>
          </Box>
        </Box>

        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            alignItems: "flex-end",
          }}
        >
          {/* <Chip
            label={`+${percentage}`}
            size="small"
            sx={{
              bgcolor: `${color}20`,
              color,
              fontSize: 12,
              padding: "0.25rem 0.45rem",
              fontWeight: 600,
              letterSpacing: "0.5px",
              borderRadius: "4px",
            }}
          /> */}

          {/* SVG Graph */}
          <Box sx={{ height: "70px", width: "100%" }}>
            <Image
              src={graphSvg}
              alt={label}
              width={24}
              height={24}
              style={{ color: color, width: "100%", height: "100%" }}
            />
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default TicketCounterCard;

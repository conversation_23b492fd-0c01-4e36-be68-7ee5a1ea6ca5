import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  Box,
  Avatar,
  Divider,
} from "@mui/material";
import { Cancel, Circle } from "@mui/icons-material";

interface AttendanceReportProps {
  open: boolean;
  onClose: () => void;
  data: {
    avatar?: string;
    employee?: string;
    department?: string;
    date?: string;
    checkIn?: string;
    checkOut?: string;
    status?: string;
    totalWorkingHours?: string;
    productiveHours?: string;
    breakHours?: string;
    overtime?: string;
  };
}

const AttendanceReport: React.FC<AttendanceReportProps> = ({
  open,
  onClose,
  data,
}) => {
  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth="md"
    >
      {/* Dialog Header */}
      <DialogTitle>
        Attendance
        <IconButton
          onClick={onClose}
          sx={{ position: "absolute", right: 16, top: 16 }}
        >
          <Cancel />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignContent: "center",
            gap: 2,
            backgroundColor: "#F8F9FA",
            p: 2,
            mb: 2,
            borderRadius: 2,
          }}
        >
          {/* Employee Info */}
          <Box sx={{ display: "flex", alignItems: "center", gap: "1px" }}>
            <Avatar
              src={data?.avatar}
              sx={{
                width: "1.5rem",
                height: "1.5rem",
                lineHeight: "1.5rem",
                fontSize: "0.65rem",
                marginRight: "0.5rem",
              }}
            />
            <Box>
              <Typography
                sx={{ fontWeight: "500", fontSize: "14px", color: "#202C4B" }}
                variant="h6"
              >
                {data?.employee}
              </Typography>
              <Typography color="textSecondary">{data?.department}</Typography>
            </Box>
          </Box>

          {/* Attendance Details */}
          <Box sx={{ display: "flex", alignItems: "center", gap: 7 }}>
            <Box>
              <Typography sx={{ display: "flex", flexDirection: "column" }}>
                <strong>Date:</strong> {data?.date}
              </Typography>
            </Box>
            <Box>
              <Typography sx={{ display: "flex", flexDirection: "column" }}>
                <strong>Status:</strong> {data?.status}
              </Typography>
            </Box>
            <Box>
              <Typography sx={{ display: "flex", flexDirection: "column" }}>
                <strong>Punch in at:</strong> {data?.checkIn}
              </Typography>
            </Box>
            <Box>
              <Typography sx={{ display: "flex", flexDirection: "column" }}>
                <strong>Punch out at:</strong> {data?.checkOut}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Working Hours Summary */}
        <Box sx={{ p: 2, bgcolor: "#F8F9FA", borderRadius: 2 }}>
          <Box sx={{ display: "flex", gap: 9 }}>
            <Box>
              <Typography color="textSecondary">
                <Circle
                  sx={{
                    color: "#E8E9EA",
                    width: "7px",
                    height: "7px",
                    marginRight: ".25rem",
                  }}
                />
                Total Working Hours
              </Typography>
              <Typography variant="h6">12h 36m</Typography>
            </Box>
            <Box>
              <Typography color="textSecondary">
                {" "}
                <Circle
                  sx={{
                    color: "#03C95A",
                    width: "7px",
                    height: "7px",
                    marginRight: ".25rem",
                  }}
                />
                Productive Hours
              </Typography>
              <Typography variant="h6">08h 36m</Typography>
            </Box>
            <Box>
              <Typography color="textSecondary">
                <Circle
                  sx={{
                    color: "#FFC107",
                    width: "7px",
                    height: "7px",
                    marginRight: ".25rem",
                  }}
                />
                Break Hours
              </Typography>
              <Typography variant="h6">22m 15s</Typography>
            </Box>
            <Box>
              <Typography color="textSecondary">
                <Circle
                  sx={{
                    color: "#1B84FF",
                    width: "7px",
                    height: "7px",
                    marginRight: ".25rem",
                  }}
                />
                Overtime
              </Typography>
              <Typography variant="h6">02h 15m</Typography>
            </Box>
          </Box>

          {/* Time Distribution Visualization */}
          <Box
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: "column",
              width: "100%",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Box
              sx={{
                width: "66.66666667%",
                display: "flex",
                justifyContent: "center",
                gap: 1,
                mt: 1,
              }}
            >
              <Box
                sx={{
                  width: "18%",
                  height: 20,
                  bgcolor: "#03C95A",
                  borderRadius: 1,
                }}
              />
              <Box
                sx={{
                  width: "5%",
                  height: 20,
                  bgcolor: "#FFC107",
                  borderRadius: 1,
                }}
              />
              <Box
                sx={{
                  width: "28%",
                  height: 20,
                  bgcolor: "#03C95A",
                  borderRadius: 1,
                }}
              />
              <Box
                sx={{
                  width: "17%",
                  height: 20,
                  bgcolor: "#FFC107",
                  borderRadius: 1,
                }}
              />
              <Box
                sx={{
                  width: "22%",
                  height: 20,
                  bgcolor: "#03C95A",
                  borderRadius: 1,
                }}
              />
              <Box
                sx={{
                  width: "5%",
                  height: 20,
                  bgcolor: "#FFC107",
                  borderRadius: 1,
                }}
              />
              <Box
                sx={{
                  width: "3%",
                  height: 20,
                  bgcolor: "#1B84FF",
                  borderRadius: 1,
                }}
              />
              <Box
                sx={{
                  width: "2%",
                  height: 20,
                  bgcolor: "#1B84FF",
                  borderRadius: 1,
                }}
              />
            </Box>

            <Box
              sx={{
                display: "flex",
                width: "100%",
                justifyContent: "space-between",
                gap: 1,
                mt: 1,
              }}
            >
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                06:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                07:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                08:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                09:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                10:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                11:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                12:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                01:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                02:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                03:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                04:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                05:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                06:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                07:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                08:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                09:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                10:00
              </Typography>
              <Typography sx={{ fontSize: "0.75rem", color: "#6B7280" }}>
                11:00
              </Typography>
            </Box>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default AttendanceReport;

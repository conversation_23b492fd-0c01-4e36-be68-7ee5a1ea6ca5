"use client";

import React, { useEffect, useState, useLayoutEffect } from "react";
import "./AddEmployeeForm.scss";
import {
  Checkbox,
  FormGroup,
  Switch,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  FormControl,
  Typography,
  Box,
  Tab,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Avatar,
  Autocomplete,
  FormHelperText,
  InputAdornment,
} from "@mui/material";
import { Cancel, Close, Visibility, VisibilityOff } from "@mui/icons-material";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import { useFormik } from "formik";
import * as Yup from "yup";

import {
  getDepartment,
  getDepartments,
} from "@/app/services/department.service";

import {
  getAllDesignations,
  getDesignations,
} from "@/app/services/designation.service";
import { addUser, editUser } from "@/app/services/users.service";
import useUploadMedia from "@/app/hooks/useUploadMedia";
import Loader from "@/components/Loader/Loader";
import { toast } from "react-toastify";
import { getCompany } from "@/app/services/setting.service";
import {
  EmploymentType,
  WorkMode,
  ProbationStatus,
  Gender,
} from "@/app/types/EmployeeForm";
import useAuthStore from "@/store/authStore";
import { ROLES } from "@/app/constants/roles";

// Define interfaces
interface AddEmployeeFormProps {
  open: boolean;
  onClose: () => void;
  refreshList?: () => void;
  isEdit?: boolean;
  employeeData?: {
    _id: string;
    roles?: string[];
    firstName?: string;
    lastName?: string;
    avatar?: string;
    email?: string;
    phone?: string;
    pan?: string;
    company?: string;
    about?: string;
    joiningDate?: string;
    departmentId?:
      | string
      | {
          _id: string;
          departmentName: string;
          isActive?: boolean;
          isDeleted?: boolean;
        };
    designationId?:
      | string
      | {
          _id: string;
          designationName: string;
          isActive?: boolean;
          isDeleted?: boolean;
        };
    birthDate?: string;
    maritalStatus?: string;
    weddingAnniversaryDate?: string;
    onProbation?: boolean;
    employmentType?: EmploymentType;
    workMode?: WorkMode;
    gender?: string;
    address?: string;
  };
}

interface Department {
  _id: string;
  departmentName: string;
}

interface Designation {
  _id: string;
  designationName: string;
}

interface CompanyData {
  companyName: string;
  allowedDomains?: string[]; // Add domains field
}

interface Permission {
  module: string;
  enabled: boolean;
  permissions: { [key: string]: boolean };
}

// Define the payload type that matches what we're sending
interface UserPayload {
  roles: string;
  firstName: string;
  lastName: string;
  avatar: string;
  email: string;
  password: string;
  phone: string;
  pan: string;
  company: string;
  about: string;
  joiningDate: string;
  departmentId: string;
  designationId: string;
  birthDate: string;
  maritalStatus: string;
  weddingAnniversaryDate?: string;
  onProbation: boolean;
  employmentType: EmploymentType;
  workMode: WorkMode;
  gender: string;
  address: string;
}

const initialPermissions: Permission[] = [
  {
    module: "Holidays",
    enabled: true,
    permissions: {
      Read: true,
      Write: false,
      Create: false,
      Delete: true,
      Import: false,
      Export: false,
    },
  },
  {
    module: "Leaves",
    enabled: false,
    permissions: {
      Read: false,
      Write: false,
      Create: false,
      Delete: false,
      Import: false,
      Export: false,
    },
  },
  {
    module: "Clients",
    enabled: false,
    permissions: {
      Read: false,
      Write: false,
      Create: false,
      Delete: false,
      Import: false,
      Export: false,
    },
  },
  {
    module: "Projects",
    enabled: false,
    permissions: {
      Read: false,
      Write: false,
      Create: false,
      Delete: false,
      Import: false,
      Export: false,
    },
  },
  {
    module: "Tasks",
    enabled: false,
    permissions: {
      Read: false,
      Write: false,
      Create: false,
      Delete: false,
      Import: false,
      Export: false,
    },
  },
  {
    module: "Chats",
    enabled: false,
    permissions: {
      Read: false,
      Write: false,
      Create: false,
      Delete: false,
      Import: false,
      Export: false,
    },
  },
  {
    module: "Assets",
    enabled: true,
    permissions: {
      Read: true,
      Write: false,
      Create: true,
      Delete: false,
      Import: true,
      Export: false,
    },
  },
  {
    module: "Timing Sheets",
    enabled: false,
    permissions: {
      Read: false,
      Write: false,
      Create: false,
      Delete: false,
      Import: false,
      Export: false,
    },
  },
];

const roleOptions = ["Admin", "Employee", "Manager", "HR", "SuperAdmin"];

const AddEmployeeForm: React.FC<AddEmployeeFormProps> = ({
  open,
  onClose,
  refreshList,
  isEdit = false,
  employeeData,
}) => {
  const [tabValue, setTabValue] = useState("1");
  const [departments, setDepartments] = useState<Department[]>([]);
  const [designations, setDesignations] = useState<Designation[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | undefined>(
    employeeData?.avatar || ""
  );
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [company, setCompany] = useState<CompanyData>();
  // Comment out permissions-related state
  /*
  const [permissions, setPermissions] = useState<Permission[]>(initialPermissions);
  const [selectAll, setSelectAll] = useState(false);
  */
  const { uploadMedia } = useUploadMedia();

  const { roles } = useAuthStore();

  const isSuperAdmin = roles.includes(ROLES.SuperAdmin);

  const availableRoleOptions = isSuperAdmin ? roleOptions : ["Employee"];

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };


  // Get today's date and 18 years ago date
  const today = new Date();
  const eighteenYearsAgo = new Date(
    today.getFullYear() - 18,
    today.getMonth(),
    today.getDate()
  );

  const formik = useFormik({
    initialValues: {
      roles: isEdit && employeeData?.roles ? employeeData.roles[0] : "Employee",
      firstName: employeeData?.firstName || "",
      lastName: employeeData?.lastName || "",
      avatar: employeeData?.avatar || "",
      email: employeeData?.email || "",
      password: "",
      phone: employeeData?.phone || "",
      pan: employeeData?.pan || "",
      company: company?.companyName, // Set default value
      about: employeeData?.about || "",
      joiningDate: employeeData?.joiningDate?.split("T")[0] || "",
      departmentId: employeeData?.departmentId || "",
      designationId: employeeData?.designationId || "",
      birthDate: employeeData?.birthDate?.split("T")[0] || "",
      maritalStatus: employeeData?.maritalStatus || "Single",
      weddingAnniversaryDate:
        employeeData?.weddingAnniversaryDate?.split("T")[0] || "",
      onProbation: employeeData?.onProbation
        ? ProbationStatus.Yes
        : ProbationStatus.No,
      employmentType: employeeData?.employmentType || EmploymentType.FullTime,
      workMode: employeeData?.workMode || WorkMode.Office,
      gender: employeeData?.gender || "",
      address: employeeData?.address || "", // Make sure this is correctly set
    },
    validationSchema: Yup.object({
      roles: Yup.string().required("Role is required"),
      firstName: Yup.string().required("First Name is required"),
      lastName: Yup.string().required("Last Name is required"),
      email: Yup.string()
        .email("Invalid email")
        .required("Email is required")
        .test(
          "domain-validation",
          "Email domain not allowed for this company",
          function (value) {
            if (
              !value ||
              !company?.allowedDomains ||
              company.allowedDomains.length === 0
            )
              return true;

            // Extract domain from email
            const emailDomain = value.split("@")[1];

            // Check if domain is in allowed list
            return company.allowedDomains.includes(emailDomain);
          }
        ),
      company: Yup.string().required("Company Name is required"),
      password: isEdit
        ? Yup.string()
        : Yup.string()
            .min(8, "Password must be at least 8 characters")
            .max(64, "Max 64 chars allowed")
            .matches(
              /^(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>]).*$/,
              "Need 1 lowercase, 1 uppercase, 1 special char"
            )
            .required("Password is required"),
      phone: Yup.string()
        .required("Phone Number is required")
        .matches(/^\d+$/, "Phone number should only contain digits")
        .max(10, "Phone number should not exceed 10 digits")
        .min(10, "Phone number should be 10 digits"),
      //company: Yup.string().required("Company is required"),
      pan: Yup.string()
        .matches(/^[A-Z]{5}\d{4}[A-Z]$/, "Invalid PAN format")
        .required("PAN is required"),
      joiningDate: Yup.string()
        .required("Joining Date is required")
        .test("not-future", "Joining date cannot be in the future", (value) => {
          if (!value) return false;
          const date = new Date(value);
          return date <= today;
        }),
      departmentId: Yup.string().required("Department is required"),
      designationId: Yup.string().required("Designation is required"),
      birthDate: Yup.string()
        .required("Birth Date is required")
        .test(
          "minimum-age",
          "Employee must be at least 18 years old",
          (value) => {
            if (!value) return false;
            const date = new Date(value);
            return date <= eighteenYearsAgo;
          }
        ),
      maritalStatus: Yup.string().required("Marital Status is required"),
      onProbation: Yup.string()
        .oneOf(Object.values(ProbationStatus))
        .required("Probation Status is required"),
      employmentType: Yup.string()
        .oneOf(Object.values(EmploymentType))
        .required("Employment Type is required"),
      workMode: Yup.string()
        .oneOf(Object.values(WorkMode))
        .required("Work Mode is required"),
      gender: Yup.string()
        .required("Gender is required")
        .oneOf(Object.values(Gender), "Invalid gender selection"),
      address: Yup.string()
        .required("Address is required")
        .min(5, "Address must be at least 5 characters")
        .max(200, "Address must not exceed 200 characters"),
    }),
    onSubmit: async (values) => {
      setIsLoading(true);
      try {
        const payload: UserPayload = {
          roles: values.roles,
          firstName: values.firstName,
          lastName: values.lastName,
          avatar: values.avatar,
          email: values.email,
          password: values.password,
          phone: values.phone,
          pan: values.pan,
          company: company?.companyName || "", // Use company from state instead of values
          about: values.about,
          joiningDate: values.joiningDate,
          departmentId:
            typeof values.departmentId === "object"
              ? values.departmentId._id
              : values.departmentId,
          designationId:
            typeof values.designationId === "object"
              ? values.designationId._id
              : values.designationId,
          birthDate: values.birthDate,
          maritalStatus: values.maritalStatus,
          weddingAnniversaryDate:
            values.maritalStatus === "Married" && values.weddingAnniversaryDate
              ? values.weddingAnniversaryDate
              : undefined,
          onProbation: values.onProbation === ProbationStatus.Yes,
          employmentType: values.employmentType as EmploymentType,
          workMode: values.workMode as WorkMode,
          gender: values.gender,
          address: values.address,
        };

        let response;
        if (isEdit && employeeData?._id) {
          response = await editUser(employeeData._id, payload);
          if (response.success) {
            // toast.success("Employee updated successfully", {
            //   position: "top-right",
            //   autoClose: 3000,
            //   hideProgressBar: false,
            //   closeOnClick: true,
            //   pauseOnHover: true,
            //   draggable: true,
            // });
            if (refreshList) {
              refreshList();
            }
            onClose();
          }
        } else {
          response = await addUser(payload);
          if (response.success) {
            // toast.success("Employee added successfully", {
            //   position: "top-right",
            //   autoClose: 3000,
            //   hideProgressBar: false,
            //   closeOnClick: true,
            //   pauseOnHover: true,
            //   draggable: true,
            // });
            if (refreshList) {
              refreshList();
            }
            onClose();
          }
        }
      } catch (error: any) {
        console.error("Error submitting form:", error);

        let errorMessage = "An unexpected error occurred";

        // Handle the specific error response structure
        if (error.response?.data?.error?.errors) {
          errorMessage = error.response.data.error.errors;
        } else if (error.response?.data?.error?.message) {
          errorMessage = error.response.data.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        toast.error(errorMessage, {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      } finally {
        setIsLoading(false);
      }
    },
  });

   useEffect(() => {
    let isMounted = true;
    const fetchDepartments = async () => {
      setIsLoading(true);
      try {
        const response = await getDepartment(
          undefined,
          undefined,
          "",
          "asc",
          undefined,
          undefined,
          "true"
        );
        if (isMounted && response?.departments.results) {
          // Sort departments alphabetically by departmentName
          const sortedDepartments = [...response.departments.results].sort(
            (a, b) => a.departmentName.localeCompare(b.departmentName)
          );
          setDepartments(sortedDepartments);
        }
      } catch (error) {
        console.error("Failed to fetch departments:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (open) {
      fetchDepartments();
    }

    return () => {
      isMounted = false;
    };
  }, [open]);

  useEffect(() => {
  let isMounted = true;
  const fetchDesignations = async () => {
    setIsLoading(true);
    try {
      // Fetch designations based on the selected departmentId
      const response = await getDesignations(
        undefined, 
        undefined,
        "",
        "asc",
        undefined,
        undefined,
        "true",
        undefined,
        typeof formik.values.departmentId === "object"
          ? formik.values.departmentId._id
          : formik.values.departmentId
      );
      if (isMounted && response?.designations.results) {
        // Sort designations alphabetically by designationName
        const sortedDesignations = [...response.designations.results].sort(
          (a, b) => a.designationName.localeCompare(b.designationName)
        );
        setDesignations(sortedDesignations);
      }
    } catch (error) {
      console.error("Failed to fetch designations:", error);
      toast.error("Failed to fetch designations", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (open && formik.values.departmentId) {
    fetchDesignations();
  } else if (open && !formik.values.departmentId) {
    // Clear designations if no department is selected
    setDesignations([]);
  }

  return () => {
    isMounted = false;
  };
}, [open, formik.values.departmentId]);

  useEffect(() => {
    const fetchCompany = async () => {
      try {
        const response = await getCompany();
        if (response) {
          setCompany({
            companyName: response.company.companyName,
            allowedDomains: response.company.allowedDomains || [], // Store domains from API
          });
          // Update formik value for company
          formik.setFieldValue("company", response.company.companyName);
        }
      } catch (error) {
        console.error("Failed to fetch company:", error);
      }
    };
    if (open) {
      fetchCompany();
    }
  }, [open]); // Empty dependency array to run only once

  useEffect(() => {
    if (!isEdit) {
      formik.resetForm();
      setSelectedImage("");
    }
  }, [open, isEdit]);

  useEffect(() => {
    if (isEdit && employeeData) {
      console.log("Setting form values in edit mode with data:", employeeData);
      console.log("Address from employee data:", employeeData.address);

      // Extract departmentId and designationId from objects if they exist
      const departmentId =
        employeeData.departmentId &&
        typeof employeeData.departmentId === "object"
          ? employeeData.departmentId._id
          : employeeData.departmentId || "";

      const designationId =
        employeeData.designationId &&
        typeof employeeData.designationId === "object"
          ? employeeData.designationId._id
          : employeeData.designationId || "";

      const roleValue = isSuperAdmin
        ? employeeData.roles?.[0] || "Employee"
        : "Employee";

      // Set form values directly from database
      formik.setValues({
        roles: roleValue,
        firstName: employeeData.firstName || "",
        lastName: employeeData.lastName || "",
        avatar: employeeData.avatar || "",
        email: employeeData.email || "",
        password: "",
        phone: employeeData.phone || "",
        pan: employeeData.pan || "",
        company: company?.companyName,
        about: employeeData.about || "",
        joiningDate: employeeData.joiningDate?.split("T")[0] || "",
        departmentId: departmentId,
        designationId: designationId,
        birthDate: employeeData.birthDate?.split("T")[0] || "",
        maritalStatus: employeeData?.maritalStatus || "Single",
        weddingAnniversaryDate:
          employeeData.weddingAnniversaryDate?.split("T")[0] || "",
        onProbation:
          employeeData.onProbation === true
            ? ProbationStatus.Yes
            : ProbationStatus.No,
        // Use the exact values from the database for enums
        employmentType:
          (employeeData.employmentType as EmploymentType) ||
          EmploymentType.FullTime,
        workMode: (employeeData.workMode as WorkMode) || WorkMode.Office,
        gender: (employeeData.gender as Gender) || Gender.Male,
        address: employeeData.address || "",
      });

      console.log("Form values after setting in edit mode:", formik.values);
      setSelectedImage(employeeData.avatar || "");
    }
  }, [isEdit, employeeData, open, company]);

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setIsLoading(true);
      try {
        const { preview } = await uploadMedia(file, "avatars", 1, setIsLoading);
        setSelectedImage(preview);
        formik.setFieldValue("avatar", preview);
      } catch (error) {
        console.error("Image upload failed:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const generateRandomPassword = () => {
    const lowercase = "abcdefghijklmnopqrstuvwxyz";
    const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const numbers = "0123456789";
    const special = '!@#$%^&*(),.?":{}|<>';

    // Ensure at least one of each required character type
    let password = "";
    password += lowercase.charAt(Math.floor(Math.random() * lowercase.length));
    password += uppercase.charAt(Math.floor(Math.random() * uppercase.length));
    password += numbers.charAt(Math.floor(Math.random() * numbers.length));
    password += special.charAt(Math.floor(Math.random() * special.length));

    // Add more random characters to reach minimum length
    const allChars = lowercase + uppercase + numbers + special;
    for (let i = password.length; i < 10; i++) {
      password += allChars.charAt(Math.floor(Math.random() * allChars.length));
    }

    // Shuffle the password characters
    password = password
      .split("")
      .sort(() => 0.5 - Math.random())
      .join("");

    formik.setFieldValue("password", password);
  };

  // Comment out permission-related functions
  /*
  const toggleModule = (index: number) => {
    setPermissions((prev) =>
      prev.map((item, i) =>
        i === index ? { ...item, enabled: !item.enabled } : item
      )
    );
  };

  const togglePermission = (index: number, key: string) => {
    setPermissions((prev) =>
      prev.map((item, i) =>
        i === index
          ? {
              ...item,
              permissions: {
                ...item.permissions,
                [key]: !item.permissions[key],
              },
            }
          : item
      )
    );
  };

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    setPermissions((prev) =>
      prev.map((item) => ({
        ...item,
        enabled: true,
        permissions: Object.fromEntries(
          Object.keys(item.permissions).map((key) => [key, !selectAll])
        ),
      }))
    );
  };
  */

  // Add useLayoutEffect to ensure layout calculations happen before render
  useLayoutEffect(() => {
    if (open) {
      // Force a layout recalculation when the modal opens
      const timer = setTimeout(() => {
        window.dispatchEvent(new Event("resize"));
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [open]);

  return (
    <>
      <Dialog
        className="AddEdit-Employee-dialog"
        open={open}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            onClose();
          }
        }}
        disableEscapeKeyDown
        fullWidth
        maxWidth="md"
      >
        {isLoading && <Loader loading={isLoading} />}
        <DialogTitle className="dialog-title">
          <Typography>
            {isEdit ? "Edit Employee" : "Add New Employee"}
          </Typography>
          <IconButton
            onClick={onClose}
            sx={{
              backgroundColor: "#6b7280",
              backgroundImage: "none",
              borderRadius: "50%",
              color: "#fff",
              height: "20px",
              width: "20px",
              margin: 0,
              padding: 0,
              "&:hover": {
                backgroundColor: "#d55a1d",
              },
              "& .MuiSvgIcon-root": {
                fontSize: "14px",
              },
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>

        <DialogContent className="dialog-content" dividers>
          <Box sx={{ width: "100%", typography: "body1" }}>
            <TabContext value={tabValue}>
              <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                <TabList onChange={(_, newValue) => setTabValue(newValue)}>
                  <Tab label="Basic Information" value="1" />

                  {/* 
                  <Tab label="Permissions" value="2" /> 
                  */}
                </TabList>
              </Box>

              <TabPanel value="1">
                <form onSubmit={formik.handleSubmit}>
                  <Box
                    className="upload-avatar"
                    sx={{
                      display: "flex",
                      padding: "16px",
                      backgroundColor: "#F8F9FA",
                      gap: "10px",
                      marginBottom: "1.5rem !important",
                      marginTop: "36px !important",
                    }}
                  >
                    <Box
                      className="avatar-logo"
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        borderRadius: "50%",
                        width: "80px",
                        height: "80px",
                        border: "1px solid #E5E7EB",
                      }}
                    >
                      <Avatar
                        src={selectedImage}
                        alt="Employee Avatar"
                        sx={{ width: "100%", height: "100%" }}
                      />
                    </Box>
                    <Box className="upload-button">
                      <Typography sx={{ fontSize: "14px", fontWeight: 600 }}>
                        Upload Profile Image
                      </Typography>
                      <Typography
                        sx={{ fontSize: "0.75rem", marginBottom: ".5rem" }}
                      >
                        Image should be below 4 MB
                      </Typography>
                      <Button
                        variant="contained"
                        component="label"
                        className="upload-button"
                        disabled={isLoading}
                      >
                        {isLoading ? "Uploading..." : "Upload"}
                        <input
                          type="file"
                          hidden
                          accept="image/*"
                          onChange={handleImageUpload}
                        />
                      </Button>
                    </Box>
                  </Box>

                  <Box
                    className="input-fields"
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      flexWrap: "wrap",
                    }}
                  >
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>Role</label>
                      <Autocomplete
                        options={availableRoleOptions}
                        value={formik.values.roles}
                        onChange={(_, newValue) =>
                          formik.setFieldValue("roles", newValue || "")
                        }
                        disabled={isLoading || (!isSuperAdmin && isEdit)} // Disable for non-SuperAdmin in edit mode
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            error={
                              formik.touched.roles &&
                              Boolean(formik.errors.roles)
                            }
                            helperText={
                              formik.touched.roles && formik.errors.roles
                            }
                          />
                        )}
                      />
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        First Name <span className="required">*</span>
                      </label>
                      <TextField
                        {...formik.getFieldProps("firstName")}
                        error={
                          formik.touched.firstName &&
                          Boolean(formik.errors.firstName)
                        }
                        helperText={
                          formik.touched.firstName && formik.errors.firstName
                        }
                        disabled={isLoading}
                      />
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        Last Name <span className="required">*</span>
                      </label>
                      <TextField
                        {...formik.getFieldProps("lastName")}
                        error={
                          formik.touched.lastName &&
                          Boolean(formik.errors.lastName)
                        }
                        helperText={
                          formik.touched.lastName && formik.errors.lastName
                        }
                        disabled={isLoading}
                      />
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        Email <span className="required">*</span>
                      </label>
                      <TextField
                        {...formik.getFieldProps("email")}
                        error={
                          formik.touched.email && Boolean(formik.errors.email)
                        }
                        helperText={formik.touched.email && formik.errors.email}
                        disabled={isLoading}
                      />
                    </Box>
                    {!isEdit && (
                      <Box
                        className="input-field"
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          minWidth: "49%",
                          marginBottom: "1rem",
                        }}
                      >
                        <label>
                          Password <span className="required">*</span>
                        </label>
                        <Box sx={{ display: "flex", gap: 1 }}>
                          <TextField
                            type={showPassword ? "text" : "password"}
                            {...formik.getFieldProps("password")}
                            error={
                              formik.touched.password &&
                              Boolean(formik.errors.password)
                            }
                            helperText={
                              formik.touched.password && formik.errors.password
                            }
                            disabled={isLoading}
                            fullWidth
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  <IconButton
                                    aria-label="toggle password visibility"
                                    onClick={togglePasswordVisibility}
                                    edge="end"
                                    disabled={isLoading}
                                  >
                                    {showPassword ? (
                                      <VisibilityOff />
                                    ) : (
                                      <Visibility />
                                    )}
                                  </IconButton>
                                </InputAdornment>
                              ),
                            }}
                          />
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={generateRandomPassword}
                            disabled={isLoading}
                            sx={{
                              top: "0px",
                              whiteSpace: "nowrap",
                              minWidth: "auto",
                              fontSize: "12px",
                              height: "36px",
                              borderColor: "rgb(186, 192, 205)",
                              color: "#374151",
                              "&:hover": {
                                borderColor: " #6B7280",
                                backgroundColor: "transparent",
                              },
                            }}
                          >
                            Generate
                          </Button>
                        </Box>
                      </Box>
                    )}
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        Phone <span className="required">*</span>
                      </label>
                      <TextField
                        {...formik.getFieldProps("phone")}
                        error={
                          formik.touched.phone && Boolean(formik.errors.phone)
                        }
                        helperText={formik.touched.phone && formik.errors.phone}
                        disabled={isLoading}
                        inputProps={{
                          maxLength: 10,
                          pattern: "[0-9]*",
                          onKeyPress: (e) => {
                            if (!/[0-9]/.test(e.key)) {
                              e.preventDefault();
                            }
                          },
                        }}
                      />
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        Pan No. <span className="required">*</span>
                      </label>
                      <TextField
                        {...formik.getFieldProps("pan")}
                        error={formik.touched.pan && Boolean(formik.errors.pan)}
                        helperText={formik.touched.pan && formik.errors.pan}
                        disabled={isLoading}
                      />
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        Gender <span className="required">*</span>
                      </label>
                      <FormControl fullWidth>
                        <Select
                          {...formik.getFieldProps("gender")}
                          error={
                            formik.touched.gender &&
                            Boolean(formik.errors.gender)
                          }
                          disabled={isLoading} // Disable in edit mode
                        >
                          <MenuItem value={Gender.Male}>Male</MenuItem>
                          <MenuItem value={Gender.Female}>Female</MenuItem>
                          <MenuItem value={Gender.Other}>Other</MenuItem>
                        </Select>
                        {formik.touched.gender && formik.errors.gender && (
                          <FormHelperText error>
                            {formik.errors.gender}
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        Company <span className="required">*</span>
                      </label>
                      <TextField
                        {...formik.getFieldProps("company")}
                        value={company?.companyName || ""}
                        disabled={true} // Make it non-editable
                        error={
                          formik.touched.company &&
                          Boolean(formik.errors.company)
                        }
                        helperText={
                          formik.touched.company && formik.errors.company
                        }
                      />
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        Joining Date <span className="required">*</span>
                      </label>
                      <TextField
                        type="date"
                        {...formik.getFieldProps("joiningDate")}
                        error={
                          formik.touched.joiningDate &&
                          Boolean(formik.errors.joiningDate)
                        }
                        helperText={
                          formik.touched.joiningDate &&
                          formik.errors.joiningDate
                        }
                        disabled={isLoading}
                        inputProps={{
                          max: new Date().toISOString().split("T")[0], // Prevents selecting future dates
                        }}
                      />
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        Department <span className="required">*</span>
                      </label>
                      <FormControl fullWidth>
                        <Select
                          {...formik.getFieldProps("departmentId")}
                          error={
                            formik.touched.departmentId &&
                            Boolean(formik.errors.departmentId)
                          }
                          disabled={isLoading}
                          MenuProps={{
                            PaperProps: {
                              sx: {
                                maxHeight: "200px",
                                "& .MuiMenuItem-root": {
                                  height: "38px",
                                  fontSize: "14px",
                                  padding: "8px 14px",
                                },
                              },
                            },
                          }}
                        >
                          <MenuItem value="">
                            <em>None</em>
                          </MenuItem>
                          {departments.map((department) => (
                            <MenuItem
                              key={department._id}
                              value={department._id}
                            >
                              {department.departmentName}
                            </MenuItem>
                          ))}
                        </Select>
                        {formik.touched.departmentId &&
                          formik.errors.departmentId && (
                            <FormHelperText error>
                              {formik.errors.departmentId}
                            </FormHelperText>
                          )}
                      </FormControl>
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        Designation <span className="required">*</span>
                      </label>
                      <FormControl fullWidth>
                        <Select
                          {...formik.getFieldProps("designationId")}
                          error={
                            formik.touched.designationId &&
                            Boolean(formik.errors.designationId)
                          }
                          disabled={isLoading}
                          MenuProps={{
                            PaperProps: {
                              sx: {
                                maxHeight: "200px",
                                "& .MuiMenuItem-root": {
                                  height: "38px",
                                  fontSize: "14px",
                                  padding: "8px 14px",
                                },
                              },
                            },
                          }}
                        >
                          <MenuItem value="">
                            <em>None</em>
                          </MenuItem>
                          {designations.map((designation) => (
                            <MenuItem
                              key={designation._id}
                              value={designation._id}
                            >
                              {designation.designationName}
                            </MenuItem>
                          ))}
                        </Select>
                        {formik.touched.designationId &&
                          formik.errors.designationId && (
                            <FormHelperText error>
                              {formik.errors.designationId}
                            </FormHelperText>
                          )}
                      </FormControl>
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        Birth Date <span className="required">*</span>
                      </label>
                      <TextField
                        type="date"
                        {...formik.getFieldProps("birthDate")}
                        error={
                          formik.touched.birthDate &&
                          Boolean(formik.errors.birthDate)
                        }
                        helperText={
                          formik.touched.birthDate && formik.errors.birthDate
                        }
                        disabled={isLoading}
                        inputProps={{
                          max: eighteenYearsAgo.toISOString().split("T")[0], // Prevents selecting dates less than 18 years ago
                        }}
                      />
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>Marital Status</label>
                      <FormControl fullWidth>
                        <Select
                          {...formik.getFieldProps("maritalStatus")}
                          error={
                            formik.touched.maritalStatus &&
                            Boolean(formik.errors.maritalStatus)
                          }
                          disabled={isLoading}
                        >
                          <MenuItem value="Single">Single</MenuItem>
                          <MenuItem value="Married">Married</MenuItem>
                        </Select>
                      </FormControl>
                    </Box>
                    {formik.values.maritalStatus === "Married" && (
                      <Box
                        className="input-field"
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          minWidth: "49%",
                          marginBottom: "1rem",
                        }}
                      >
                        <label>Wedding Anniversary Date</label>
                        <TextField
                          type="date"
                          {...formik.getFieldProps("weddingAnniversaryDate")}
                          error={
                            formik.touched.weddingAnniversaryDate &&
                            Boolean(formik.errors.weddingAnniversaryDate)
                          }
                          helperText={
                            formik.touched.weddingAnniversaryDate &&
                            formik.errors.weddingAnniversaryDate
                          }
                          disabled={isLoading}
                          inputProps={{
                            max: new Date().toISOString().split("T")[0], // Prevents selecting future dates
                          }}
                        />
                      </Box>
                    )}
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>Work Mode</label>
                      <FormControl fullWidth>
                        <Select
                          {...formik.getFieldProps("workMode")}
                          error={
                            formik.touched.workMode &&
                            Boolean(formik.errors.workMode)
                          }
                          disabled={isLoading} // Add isEdit condition
                        >
                          <MenuItem value={WorkMode.Office}>Office</MenuItem>
                          <MenuItem value={WorkMode.WFH}>WFH</MenuItem>
                          <MenuItem value={WorkMode.Hybrid}>Hybrid</MenuItem>
                        </Select>
                      </FormControl>
                    </Box>
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        minWidth: "49%",
                        marginBottom: "1rem",
                      }}
                    >
                      <label>Employment Type</label>
                      <FormControl fullWidth>
                        <Select
                          {...formik.getFieldProps("employmentType")}
                          error={
                            formik.touched.employmentType &&
                            Boolean(formik.errors.employmentType)
                          }
                          disabled={isLoading} // Add isEdit condition
                        >
                          <MenuItem value={EmploymentType.FullTime}>
                            Full Time
                          </MenuItem>
                          <MenuItem value={EmploymentType.Contract}>
                            Contract
                          </MenuItem>
                        </Select>
                      </FormControl>
                    </Box>

                    {/* Only show On Probation field if Employment Type is FullTime */}
                    {formik.values.employmentType ===
                      EmploymentType.FullTime && (
                      <Box
                        className="input-field"
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          minWidth: "49%",
                          marginBottom: "1rem",
                        }}
                      >
                        <label>On Probation</label>
                        <FormControl fullWidth>
                          <Select
                            {...formik.getFieldProps("onProbation")}
                            error={
                              formik.touched.onProbation &&
                              Boolean(formik.errors.onProbation)
                            }
                            disabled={isLoading} // Add isEdit condition
                          >
                            <MenuItem value={ProbationStatus.Yes}>Yes</MenuItem>
                            <MenuItem value={ProbationStatus.No}>No</MenuItem>
                          </Select>
                          {formik.touched.onProbation &&
                            formik.errors.onProbation && (
                              <FormHelperText error>
                                {formik.errors.onProbation}
                              </FormHelperText>
                            )}
                        </FormControl>
                      </Box>
                    )}
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        // minWidth: "49%",
                        width: "100%", // Make it full width
                        marginBottom: "1rem",
                      }}
                    >
                      <label>
                        Address <span className="required">*</span>
                      </label>
                      <FormControl fullWidth>
                        <TextField
                          {...formik.getFieldProps("address")}
                          error={
                            formik.touched.address &&
                            Boolean(formik.errors.address)
                          }
                          helperText={
                            formik.touched.address && formik.errors.address
                          }
                          disabled={isLoading}
                          //placeholder="Enter your address"
                        />
                      </FormControl>
                    </Box>
                    <Box
                      className="input-field"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        width: "100%", // Make it full width
                        // marginTop: "16px", // Add some spacing
                      }}
                    >
                      <label>About</label>
                      <FormControl fullWidth>
                        <TextField
                          {...formik.getFieldProps("about")}
                          error={
                            formik.touched.about && Boolean(formik.errors.about)
                          }
                          helperText={
                            formik.touched.about && formik.errors.about
                          }
                          disabled={isLoading}
                        />
                      </FormControl>
                    </Box>
                  </Box>
                </form>
              </TabPanel>

              {/* 
              <TabPanel value="2">

                <Box className="permissionTab" sx={{ mt: 2 }}>
                  <Grid
                    container
                    alignItems="center"
                    justifyContent="space-between"
                    sx={{ mb: 2, backgroundColor: "#F4F6FA", padding: "20px" }}
                  >
                    <Typography variant="h6">Enable Options</Typography>
                    <Box>
                      <FormControlLabel
                        control={<Switch />}
                        label="Enable All Module"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={selectAll}
                            onChange={handleSelectAll}
                          />
                        }
                        label="Select All"
                      />
                    </Box>
                  </Grid>
                  {permissions.map((module, index) => (
                    <Grid
                      key={module.module}
                      container
                      spacing={2}
                      alignItems="center"
                      sx={{ py: 1, borderBottom: "1px solid #eee" }}
                    >
                      <Grid item xs={2}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={module.enabled}
                              onChange={() => toggleModule(index)}
                              disabled={isLoading}
                            />
                          }
                          label={module.module}
                        />
                      </Grid>
                      <Grid item xs={10}>
                        <FormGroup row>
                          {Object.keys(module.permissions).map((perm) => (
                            <FormControlLabel
                              key={perm}
                              control={
                                <Checkbox
                                  checked={module.permissions[perm]}
                                  onChange={() => togglePermission(index, perm)}
                                  disabled={!module.enabled || isLoading}
                                />
                              }
                              label={perm}
                            />
                          ))}
                        </FormGroup>
                      </Grid>
                    </Grid>
                  ))}
                </Box>


              </TabPanel> */}
            </TabContext>
          </Box>
          <DialogActions sx={{ padding: "16px 0 0px 0 !important" }}>
            <Button
              onClick={onClose}
              sx={{
                fontWeight: "400",
                fontSize: "14px",
                color: "#111827",
                border: "1px solid #E5E7EB",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={formik.submitForm}
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              disabled={isLoading}
            >
              {isEdit ? "Update" : "Save"}
            </Button>
          </DialogActions>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AddEmployeeForm;

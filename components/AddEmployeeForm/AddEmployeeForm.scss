.AddEdit-Employee-dialog {

  .input-fields {
    label {
      font-size: 14px;
      font-weight: 500;
      color: #202C4B;
      margin-bottom: 0.5rem;
    }
  }

  .upload-button {
    text-transform: none;

    label {
      background-color: rgb(226.95, 82.1538461538, 13.35) !important;
      color: #fff !important;
      border: 1px solid rgb(226.95, 82.1538461538, 13.35) !important;
      padding: 0.25rem 0.5rem !important;
      font-size: 0.75rem !important;
      border-radius: 5px !important;
      font-weight: 600 !important;
      text-transform: none;
      max-width: fit-content !important;
    }
  }

  // Tab panel styling
  .MuiTabPanel-root {
    padding: 0px;
  }

  .MuiDialog-paper {
    overflow: hidden;
  }

  // Tab styling
  .Mui-selected {
    color: var(--primary-color) !important;
  }

  .MuiTabs-flexContainer {
    button {
      text-transform: none;
      padding: 0.5rem 0px 0.5rem;
      font-weight: 500;
      max-height: 39px !important;
    }
  }

  .MuiTabs-indicator {
    background-color: var(--primary-color);
  }

  // Dialog content
  .dialog-content {

    .MuiInputBase-input{
          padding: 0.5rem 0.625rem;
      // font-size: 14px;
      // font-weight: 500;
      // color: var(--text-dark);
    }

    // Autocomplete styling
    .MuiAutocomplete-root {
      margin-bottom: 0px;
      margin-top: 0px;
    }

    .MuiAutocomplete-inputRoot {
      padding: 0px;
    }

    // Permission tab styling
    .permissionTab {
      h6 {
        font-size: 14px;
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 0;
      }

      .MuiFormControlLabel-root {
        .MuiTypography-root {
          font-size: 14px;
          color: var(--text-secondary);
        }

        .MuiSwitch-track {
          background-color: var(--primary-color);
          border-color: var(--primary-color);
        }

        .MuiSwitch-thumb {
          background-color: white;
          width: 11px;
          height: 11px;
        }

        .MuiButtonBase-root {
          padding: 13px;
        }

        .Mui-checked {
          color: var(--primary-color);
          opacity: 1;
        }
      }
    }
  }

  // Form layout
  .form-row {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);

    @media (max-width: 767px) {
      flex-direction: column;
    }
  }
}

@media (max-width: 768px) {
  .upload-avatar {
    flex-direction: column;
    align-items: center;
  }

  .upload-button {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .input-field {
    width: 100%;
  }
}
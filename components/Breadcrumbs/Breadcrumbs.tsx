"use client";

import { Breadcrumbs, <PERSON>, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import React from "react";
import "./Breadcrumbs.scss"

type BreadcrumbItem = {
  label: string;
  href?: string;
  icon?: React.ReactNode;
};

type BreadcrumbsProps = {
  items: BreadcrumbItem[];
};

export default function BreadcrumbsComponent({ items }: BreadcrumbsProps) {
  const router = useRouter();

  const handleClick = (event: React.MouseEvent, href?: string) => {
    event.preventDefault(); // Prevents default link behavior
    if (href) router.push(href);
  };

  return (
    <nav aria-label="breadcrumb">
      <Breadcrumbs>
        {items.map((item, index) =>
          item.href ? (
            <Link
              key={index}
              underline="hover"
              sx={{ display: "flex", alignItems: "center", cursor: "pointer" }}
              color="red"
              onClick={(event) => handleClick(event, item.href)}
            >
              {item.icon}
              {item.label}
            </Link>
          ) : (
            <Typography
              key={index}
              sx={{ display: "flex", alignItems: "center", color: "text.primary" }}
            >
              {item.icon}
              {item.label}
            </Typography>
          )
        )}
      </Breadcrumbs>
    </nav>
  );
}

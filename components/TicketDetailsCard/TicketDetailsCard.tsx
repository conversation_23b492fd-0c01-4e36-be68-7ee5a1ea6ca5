"use client";
import React, { useState, useEffect } from "react";
import "./TicketDetailsCard.scss";
import {
  Box,
  Typography,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Avatar,
} from "@mui/material";
import { toast } from "react-toastify";
import { getUserswithdepartmenetId } from "@/app/services/users.service";
import { updateTicket } from "@/app/services/tickets/tickets.service";
import useRoleAuth from "@/app/hooks/useRoleAuth";
import { ROLE_GROUPS } from "@/app/constants/roles";

interface Employee {
  _id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  email: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Comment {
  _id: string;
  message: string;
  commentBy: Employee;
  cc: Employee[];
  commentDate: string;
  media: string[];
  createdAt: string;
  updatedAt: string;
}

interface TicketCategory {
  _id: string;
  category: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Department {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Ticket {
  _id: string;
  empId: Employee;
  category: TicketCategory | null;
  departmentId?: Department | null;
  subject: string;
  description: string;
  priority: "High" | "Low" | "Medium";
  status: "Open" | "OnHold" | "Reopened" | "Closed";
  comments: Comment[];
  isActive: boolean;
  isDeleted: boolean;
  ticketId: string;
  createdAt: string;
  updatedAt: string;
  cc?: Employee[];
  assignTo?: Employee;
}

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface TicketDetailsCardProps {
  ticket: Ticket;
  onUpdate: () => void;
}

const TicketDetailsCard: React.FC<TicketDetailsCardProps> = ({ ticket, onUpdate }) => {
  const [priority, setPriority] = useState(ticket.priority);
  const [status, setStatus] = useState(ticket.status);
  const [assignToId, setAssignToId] = useState(ticket.assignTo?._id || "");
  const [users, setUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);


  const { roles } = useRoleAuth({
        allowedRoles: ROLE_GROUPS.USER_ROLES,
        redirectTo: "/unauthorized",
      });
  
  const isEmployee = roles.includes("Employee");

  useEffect(() => {
    const fetchUsers = async () => {
      if (!ticket.departmentId?._id) {
        setUsers([]);
        return;
      }

      setLoadingUsers(true);
      try {
        const response = await getUserswithdepartmenetId(
          ticket.departmentId._id
        );
        const fetchedUsers: User[] = response.users.results
          .filter((user: any) => user.isActive && !user.isDeleted)
          .map((user: any) => ({
            _id: user._id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            isActive: user.isActive,
            isDeleted: user.isDeleted,
          }));
        setUsers(fetchedUsers);
      } catch (error) {
        console.error("Error fetching users:", error);
        // toast.error("Failed to load users for assignment");
        setUsers([]);
      } finally {
        setLoadingUsers(false);
      }
    };

    fetchUsers();
  }, [ticket.departmentId?._id]);

  // Handle priority change
  const handlePriorityChange = async (event: any) => {
    const newPriority = event.target.value as "High" | "Low" | "Medium";
    try {
      await updateTicket(ticket._id, { priority: newPriority });
      setPriority(newPriority);
      toast.success("Priority updated successfully");
      onUpdate();
    } catch (error) {
      console.error("Error updating priority:", error);
      // toast.error("Failed to update priority");
    }
  };

  // Handle status change
  const handleStatusChange = async (event: any) => {
    const newStatus = event.target.value as
      | "Open"
      | "OnHold"
      | "Reopened"
      | "Closed";
    try {
      await updateTicket(ticket._id, { status: newStatus });
      setStatus(newStatus);
      toast.success("Status updated successfully");
      onUpdate();
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update status");
    }
  };

  // Handle assignTo change
  const handleAssignToChange = async (event: any) => {
    const newAssignToId = event.target.value as string;
    try {
      await updateTicket(ticket._id, { assignTo: newAssignToId || null });
      setAssignToId(newAssignToId);
      toast.success("Assignment updated successfully");
      onUpdate();
    } catch (error) {
      console.error("Error updating assignment:", error);
      toast.error("Failed to update assignment");
    }
  };

  return (
    <Box className="ticket-details-sidecard">
      <Box className="details-header">
        <Typography variant="h4" className="ticket-details-title">
          Ticket Details
        </Typography>
      </Box>

      <Box className="details-content">
        {/* Change Priority */}
        <InputLabel>Change Priority</InputLabel>
        <FormControl fullWidth sx={{ mb: 2 }}>
          <Select
            value={priority}
            onChange={handlePriorityChange}
            label="Change Priority"
            disabled={isEmployee}
          >
            <MenuItem value="High">High</MenuItem>
            <MenuItem value="Medium">Medium</MenuItem>
            <MenuItem value="Low">Low</MenuItem>
          </Select>
        </FormControl>

        {/* Assign To */}
        <InputLabel>Assign To</InputLabel>
        <FormControl fullWidth sx={{ mb: 2 }}>
          <Select
            value={assignToId}
            onChange={handleAssignToChange}
            label="Assign To"
            disabled={isEmployee || loadingUsers}
          >
            <MenuItem value="">None</MenuItem>
            {users.map((user) => (
              <MenuItem key={user._id} value={user._id}>
                {`${user.firstName} ${user.lastName}`}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Ticket Status */}
        <InputLabel>Ticket Status</InputLabel>
        <FormControl fullWidth sx={{ mb: 2 }}>
          <Select
            value={status}
            onChange={handleStatusChange}
            label="Ticket Status"
            disabled={isEmployee}
          >
            <MenuItem value="Open">Open</MenuItem>
            <MenuItem value="OnHold">OnHold</MenuItem>
            <MenuItem value="Reopened">Reopened</MenuItem>
            <MenuItem value="Closed">Closed</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* User Info */}
      <Box className="user-info">
        <Avatar
          className="user-avatar"
          sx={{ width: 40, height: 40, mr: 2 }}
          src={ticket.empId.avatar}
        />
        <Box>
          <Typography fontSize={12} color="text.secondary">
            User
          </Typography>
          <Typography fontWeight="medium">
            {ticket.empId.firstName} {ticket.empId.lastName}
          </Typography>
        </Box>
      </Box>

      {/* Support Agent */}
      {/* <Box className="support-agent">
        <Avatar
          sx={{ width: 40, height: 40, mr: 2 }}
          src={ticket.assignTo?.avatar || "/avatars/default.png"}
        />
        <Box>
          <Typography fontSize={12} color="text.secondary">
            Support Agent
          </Typography>
          <Typography fontWeight="medium">
            {ticket.assignTo
              ? `${ticket.assignTo.firstName} ${ticket.assignTo.lastName}`
              : "None"}
          </Typography>
        </Box>
      </Box> */}

      {/* Category */}
      <Box className="category">
        <Box>
          <Typography fontSize={12} color="text.secondary">
            Category
          </Typography>
          <Typography>{ticket.category?.category || "N/A"}</Typography>
        </Box>
      </Box>

      {/* Email */}
      <Box className="email">
        <Box>
          <Typography fontSize={12} color="text.secondary">
            Email
          </Typography>
          <Typography>{ticket.empId.email}</Typography>
        </Box>
      </Box>

      {/* Last Updated */}
      <Box className="last-updated">
        <Box>
          <Typography fontSize={12} color="text.secondary">
            Last Updated / Closed On
          </Typography>
          <Typography>
            {new Date(ticket.updatedAt).toLocaleDateString("en-US", {
              day: "2-digit",
              month: "short",
              year: "numeric",
            })}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default TicketDetailsCard;

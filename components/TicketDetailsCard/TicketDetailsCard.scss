.ticket-details-sidecard {
    margin-bottom: 1.5rem;
    background-color: #FFF;
    transition: all 0.5s ease-in-out;
    position: relative;
    border-radius: 5px;
    border: 1px solid #E5E7EB;
    box-shadow: 0px 1px 1px 1px rgba(198, 198, 198, 0.2);
    color: inherit;

    .details-header {
        border-color: #E5E7EB;
        position: relative;
        background: transparent;
        padding: 1rem;

        .ticket-details-title {
            font-size: 18px;
            font-weight: 600;
            color: #202C4B;
        }

    }


    .details-content {
        border-bottom: 1px solid #E5E7EB !important;
        padding: 1rem;
        display: flex;
        flex-direction: column;
        gap: "0px !important";

        label {
            font-size: 14px;
            font-weight: 500;
            color: #202C4B;
            margin-bottom: .5rem;
        }

        .MuiOutlinedInput-notchedOutline {
            legend {
                display: none;
            }
        }
    }

    .user-info,
    .support-agent,
    .category,
    .email,
    .last-updated {
        border-bottom: 1px solid #E5E7EB !important;
        padding: 1rem !important;
        align-items: center !important;
        display: flex;

        .user-avatar {
            width: 2rem;
            height: 2rem;
            line-height: 2rem;
            font-size: 0.8rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            // border-radius: 4px;
            color: #FFF;
            font-weight: 500;
            margin-right: .5rem
        }


    }
}
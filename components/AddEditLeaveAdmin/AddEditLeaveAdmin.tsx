import React from "react";
import "./AddEditLeaveAdmin.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  MenuItem,
  IconButton,
  Box,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import dayjs from "dayjs";

interface LeaveDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: LeaveFormValues) => void;
  initialValues?: LeaveFormValues;
}

interface LeaveFormValues {
  leaveType: string;
  fromDate: string | null;
  toDate: string | null;
  noOfDays: number;
  remainingDays: number;
  reason: string;
}

const leaveTypes = ["Sick Leave", "Casual Leave", "Earned Leave"];

const validationSchema = Yup.object({
  leaveType: Yup.string().required("Leave Type is required"),
  fromDate: Yup.date().nullable().required("Start Date is required"),
  toDate: Yup.date()
    .nullable()
    .required("End Date is required")
    .test("is-after", "End date must be after start date", function (value) {
      const { fromDate } = this.parent;
      return !fromDate || !value || dayjs(value).isAfter(dayjs(fromDate));
    }),
  reason: Yup.string().required("Reason is required"),
});

const AddEditLeaveAdmin: React.FC<LeaveDialogProps> = ({
  open,
  onClose,
  onSubmit,
  initialValues,
}) => {
  const isEditing = Boolean(initialValues);

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        {isEditing ? "Edit Leave" : "Add Leave"}
        <IconButton onClick={onClose} sx={{ position: "absolute", right: 8 }}>
          <Close />
        </IconButton>
      </DialogTitle>

      <Formik
        initialValues={
          initialValues || {
            leaveType: "",
            fromDate: null,
            toDate: null,
            noOfDays: 0,
            remainingDays: 0, // Default remaining days
            reason: "",
          }
        }
        validationSchema={validationSchema}
        onSubmit={onSubmit}
      >
        {({ values, setFieldValue, errors, touched }) => {
          // useEffect(() => {
          //     if (values.fromDate && values.toDate) {
          //         const start = dayjs(values.fromDate);
          //         const end = dayjs(values.toDate);
          //         setFieldValue("noOfDays", end.diff(start, "day") + 1);
          //     }
          // }, [values.fromDate, values.toDate]);

          return (
            <Form>
              <DialogContent className="dialog-content ">
                <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                  {/* Leave Type */}
                  <Box>
                    <label>Leave Type</label>
                    <Field
                      as={TextField}
                      select
                      fullWidth
                      name="leaveType"
                      error={touched.leaveType && Boolean(errors.leaveType)}
                      helperText={touched.leaveType && errors.leaveType}
                    >
                      {leaveTypes.map((type) => (
                        <MenuItem key={type} value={type}>
                          {type}
                        </MenuItem>
                      ))}
                    </Field>
                  </Box>

                  {/* From & To Dates */}
                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Box>
                      <label>From</label>
                      <Field
                        as={TextField}
                        type="date"
                        name="fromDate"
                        value={values.fromDate}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          setFieldValue("fromDate", e.target.value)
                        }
                        fullWidth
                        error={touched.fromDate && Boolean(errors.fromDate)}
                        helperText={touched.fromDate && errors.fromDate}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        inputProps={{
                          placeholder: "DD/MM/YYYY",
                        }}
                      />
                    </Box>
                    <Box>
                      <label>To</label>
                      <Field
                        as={TextField}
                        type="date"
                        name="toDate"
                        value={values.toDate}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          setFieldValue("toDate", e.target.value)
                        }
                        fullWidth
                        error={touched.toDate && Boolean(errors.toDate)}
                        helperText={touched.toDate && errors.toDate}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        inputProps={{
                          placeholder: "DD/MM/YYYY",
                        }}
                      />
                    </Box>
                  </Box>

                  <Box sx={{ display: "flex", gap: 2 }}>
                    <Box>
                      <label>No of Days</label>
                      <Field
                        as={TextField}
                        fullWidth
                        name="noOfDays"
                        value={values.noOfDays}
                        InputProps={{ readOnly: true }}
                      />
                    </Box>
                    <Box>
                      <label>Remaining Days</label>
                      <Field
                        as={TextField}
                        fullWidth
                        name="remainingDays"
                        value={values.remainingDays}
                        InputProps={{ readOnly: true }}
                      />
                    </Box>
                  </Box>

                  {/* Reason */}
                  <Box>
                    <label>Reason</label>
                    <Field
                      as={TextField}
                      fullWidth
                      name="reason"
                      multiline
                      rows={3}
                      error={touched.reason && Boolean(errors.reason)}
                      helperText={touched.reason && errors.reason}
                    />
                  </Box>
                </Box>
              </DialogContent>

              {/* Actions */}
              <DialogActions>
                <Button
                  sx={{
                    fontWeight: "400",
                    fontSize: "14px",
                    color: "#111827",
                    border: "1px solid #E5E7EB",
                    borderRadius: "5px",
                    textTransform: "none",
                    padding: "8px 13.6px",
                  }}
                  onClick={onClose}
                  variant="outlined"
                >
                  Cancel
                </Button>
                <Button
                  sx={{
                    display: "flex",
                    gap: "8px",
                    backgroundColor: "#F26522",
                    borderColor: "#F26522",
                    color: "#FFF",
                    fontWeight: 400,
                    fontSize: "14px",
                    borderRadius: "5px",
                    textTransform: "none",
                    padding: "8px 13.6px",
                  }}
                  type="submit"
                  variant="contained"
                >
                  {isEditing ? "Update Leave" : "Add Leave"}
                </Button>
              </DialogActions>
            </Form>
          );
        }}
      </Formik>
    </Dialog>
  );
};

export default AddEditLeaveAdmin;

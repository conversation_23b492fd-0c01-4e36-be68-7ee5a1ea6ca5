.resignation-dialog {
  

  // Make sure the editor fits properly in the dialog
  .dialog-content {
    padding: 1rem !important;
    .jodit-container {
      width: 100%;
      box-sizing: border-box;
    }
  }

  .dialog-content {
    padding: 1rem !important;

    label {
      font-size: 14px;
      font-weight: 500;
      color: #202C4B;
      margin-bottom: 0.5rem;
      display: block;
    }

    .MuiInputBase-root {
      padding: 5.5px 14px !important;
      height: 36px !important;
      box-sizing: border-box;

      &.MuiInputBase-multiline {
        height: auto !important;
        padding: 5.5px 14px !important;
      }

      .MuiInputBase-input {
        padding: 0 !important;
        height: 100% !important;
        box-sizing: border-box;
      }
    }

    .MuiAutocomplete-inputRoot {
      padding: 5.5px 14px !important;
      height: 36px !important;
      box-sizing: border-box;

      .MuiAutocomplete-input {
        padding: 0 !important;
        height: 100% !important;
      }
    }

    // .MuiTextField-root,
    // .MuiAutocomplete-root {
    //   margin-top: 0px !important;
    //   margin-bottom: 8px !important;
    // }

    .MuiAutocomplete-root{
      margin-top: 0px !important;
      margin-bottom: 0px !important;
    }

    .MuiFormControl-root{
      margin-top: 0px !important;
      margin-bottom: 8px !important;  
    }
  }

}

// Add these styles for the Jodit editor
.jodit-container {
  border-radius: 5px;
  margin-top: 8px;
  margin-bottom: 8px;

  .jodit-workplace {
    border-radius: 0 0 5px 5px;
  }

  .jodit-toolbar__box {
    border-radius: 5px 5px 0 0;
    background-color: #f9fafb;
    border-bottom: 1px solid #E5E7EB;
  }

  .jodit-toolbar-button {
    &:hover:not([disabled]) {
      background-color: rgba(242, 101, 34, 0.1);
    }
  }

  .jodit-ui-group {
    border-right: 1px solid #E5E7EB;
  }

  .jodit-status-bar {
    border-top: 1px solid #E5E7EB;
    background-color: #f9fafb;
    color: #6B7280;
    font-size: 12px;
    border-radius: 0 0 5px 5px;
  }
}
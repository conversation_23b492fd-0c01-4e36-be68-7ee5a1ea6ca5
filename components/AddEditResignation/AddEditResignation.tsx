import React, { useState, useEffect, useRef } from "react";
import "./AddEditResignation.scss";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  Box,
  Typography,
  Tooltip,
} from "@mui/material";
import { Cancel, InfoOutlined } from "@mui/icons-material";
import useAuthStore from "@/store/authStore";
import { useFormik } from "formik";
import * as Yup from "yup";
import JoditEditor from "jodit-react";
import { getNoticePeriod } from "@/app/services/setting.service";

interface ResignationDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (formData: {
    empId: string;
    reason: string;
    noticeDate: string;
    resignationDate: string;
  }) => void;
  editData?: {
    empId?: string;
    employeeName?: string;
    reason?: string;
    noticeDate?: string;
    resignationDate?: string;
  };
}

interface Employee {
  _id: string;
  firstName: string;
  lastName: string;
}

// Yup validation schema
const validationSchema = Yup.object({
  empId: Yup.string().required("Employee is required"),
  reason: Yup.string().required("Reason is required"),
  resignationDate: Yup.string().required("Resignation Date is required"),
  noticeDate: Yup.string().required("Notice Date is required"),
});

const ResignationDialog: React.FC<ResignationDialogProps> = ({
  open,
  onClose,
  onSave,
  editData,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { roles, employeeId } = useAuthStore(); // Get user roles and employeeId
  const isEmployee =
    roles.includes("Employee") &&
    !roles.some((role) =>
      ["Admin", "SuperAdmin", "Manager", "HR"].includes(role)
    );
  const isAdminRole = roles.some((role) =>
    ["Admin", "SuperAdmin", "Manager", "HR"].includes(role)
  );
  const [noticePeriodDays, setNoticePeriodDays] = useState(0);
  const editorRef = useRef(null);
  const [formReset, setFormReset] = useState(false);

  // Reset form when dialog opens/closes
  useEffect(() => {
    const fetchNoticePeriod = async () => {
      try {
        const response = await getNoticePeriod();
        if (response && response.noticeSettings) {
          setNoticePeriodDays(response.noticeSettings.noticePeriodDays || 0);
        }
      } catch (error) {
        console.error("Error fetching notice period:", error);
      }
    };

    if (open) {
      fetchNoticePeriod();
    }
  }, [open]);

  // Initialize formik with proper values
  const formik = useFormik({
    initialValues: {
      empId: editData?.empId || (isEmployee ? employeeId : ""),
      reason: editData?.reason || "",
      noticeDate: editData?.noticeDate || "",
      resignationDate:
        editData?.resignationDate || new Date().toISOString().split("T")[0],
    },
    validationSchema: validationSchema,
    enableReinitialize: true, // This ensures form values update when editData changes
    onSubmit: (values) => {
      const payload = {
        empId: isEmployee ? employeeId : values.empId,
        reason: values.reason,
        noticeDate: values.noticeDate,
        resignationDate: values.resignationDate,
      };

      console.log("Submitting payload:", payload);
      onSave({
        ...payload,
        empId: payload.empId || "",
      });

      // Reset only the reason field after submission if not in edit mode
      if (!editData) {
        formik.resetForm();
        if (editorRef.current) {
          // Reset the Jodit editor content
          (editorRef.current as any).value = "";
        }
      }
    },
  });

  // Log initial values when component mounts or editData changes
  useEffect(() => {
    if (open) {
      console.log("Component opened with editData:", editData);

      // If in edit mode, explicitly set form values from editData
      if (editData) {
        console.log("Setting form values from editData:", {
          empId: editData.empId || "",
          reason: editData.reason || "",
          noticeDate: editData.noticeDate || "",
          resignationDate: editData.resignationDate || "",
        });

        // Force update the form values
        formik.setValues({
          empId: editData.empId || "",
          reason: editData.reason || "",
          noticeDate: editData.noticeDate || "",
          resignationDate: editData.resignationDate || "",
        });
      }
    }
  }, [open, editData]);

  // Only calculate notice date in add mode
  useEffect(() => {
    if (noticePeriodDays > 0 && !editData) {
      // Use today's date as resignation date
      const today = new Date();
      const formattedToday = today.toISOString().split("T")[0];

      // Set resignation date to today
      formik.setFieldValue("resignationDate", formattedToday);

      // Calculate notice date by adding notice period days to today
      const noticeDate = new Date(today);
      noticeDate.setDate(today.getDate() + noticePeriodDays);

      // Format the date as YYYY-MM-DD for the input field
      const formattedNoticeDate = noticeDate.toISOString().split("T")[0];
      formik.setFieldValue("noticeDate", formattedNoticeDate);
    }
  }, [noticePeriodDays, editData, formik.setFieldValue]);

  // Add this function to handle date changes with additional validation
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const selectedDate = new Date(value);
    selectedDate.setHours(0, 0, 0, 0);

    // Allow today's date and future dates
    if (selectedDate < today) {
      formik.setFieldError(name, "Date cannot be before today");
      return;
    }

    formik.handleChange(e);
  };

  return (
    <Dialog
      className="resignation-dialog"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      fullWidth
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          padding: "1rem !important",
        }}
      >
        {editData ? "Edit Resignation" : "Add Resignation"}
        <Cancel
          onClick={onClose}
          sx={{ ":hover": { color: "#F26522 !important" } }}
        />
      </DialogTitle>
      <DialogContent className="dialog-content">
        <form onSubmit={formik.handleSubmit}>
          {/* Resignation Date */}
          {/* <Box>
            <label>Resignation Date</label>
            <TextField
              fullWidth
              margin="dense"
              name="resignationDate"
              type="date"
              value={formik.values.resignationDate}
              onChange={handleDateChange}
              onBlur={formik.handleBlur}
              error={
                formik.touched.resignationDate &&
                Boolean(formik.errors.resignationDate)
              }
              helperText={
                formik.touched.resignationDate && formik.errors.resignationDate
              }
              InputLabelProps={{ shrink: true }}
              InputProps={{
                inputProps: {
                  min: new Date().toISOString().split("T")[0],
                },
                sx: {
                  padding: "5.5px 14px",
                  height: "36px",
                },
              }}
            />
          </Box> */}

          {/* Hidden Resignation Date field - not shown to user */}
          <input
            type="hidden"
            name="resignationDate"
            value={formik.values.resignationDate}
          />

          {/* Notice Date - Disabled for employees, editable for admins */}
          <Box>
            <label
              style={{ display: "flex", alignItems: "center", gap: "4px" }}
            >
              Notice Date
              <Tooltip
                arrow
                placement="top"
                title={
                  noticePeriodDays > 0 ? (
                    <Typography variant="caption">
                      {isEmployee
                        ? `Automatically calculated as ${editData ? "resignation date" : "today"} + ${noticePeriodDays} days notice period`
                        : `Default is ${editData ? "resignation date" : "today"} + ${noticePeriodDays} days notice period (editable)`}
                    </Typography>
                  ) : (
                    ""
                  )
                }
              >
                <InfoOutlined sx={{ fontSize: "14px" }} />
              </Tooltip>
            </label>
            <TextField
              fullWidth
              margin="dense"
              name="noticeDate"
              type="date"
              value={formik.values.noticeDate}
              onChange={isEmployee ? undefined : handleDateChange}
              onBlur={isEmployee ? undefined : formik.handleBlur}
              disabled={isEmployee} // Disabled only for employees
              error={
                formik.touched.noticeDate && Boolean(formik.errors.noticeDate)
              }
              helperText={formik.touched.noticeDate && formik.errors.noticeDate}
              InputLabelProps={{ shrink: true }}
              InputProps={{
                inputProps: { min: new Date().toISOString().split("T")[0] },
                sx: {
                  padding: "5.5px 14px",
                  height: "36px",
                },
              }}
            />
          </Box>

          {/* Reason */}
          <Box>
            <label
              style={{ display: "flex", alignItems: "center", gap: "4px" }}
            >
              Reason
              {isAdminRole && (
                <Tooltip
                  arrow
                  placement="top"
                  title={
                    <Typography variant="caption">
                      Reason field is read-only for administrative roles
                    </Typography>
                  }
                >
                  <InfoOutlined sx={{ fontSize: "14px" }} />
                </Tooltip>
              )}
            </label>
            <JoditEditor
              ref={editorRef}
              value={formik.values.reason}
              config={{
                readonly: isAdminRole, // Disable for admin roles
                placeholder: "Enter resignation reason...",
                height: 200,
                buttons: [
                  "bold",
                  "italic",
                  "underline",
                  "|",
                  "ul",
                  "ol",
                  "|",
                  "link",
                  "|",
                  "source",
                ],
                removeButtons: ["image", "file", "video"],
                showCharsCounter: false,
                showWordsCounter: false,
                showXPathInStatusbar: false,
                statusbar: false,
                license: "#14JAHTYD1", // Add this line to remove "Powered by Jodit"
                toolbarAdaptive: false,
                enableDragAndDropFileToEditor: false,
                enter: "p",
                useSearch: false,
                spellcheck: false,
                defaultMode: 1,
                askBeforePasteHTML: false,
                askBeforePasteFromWord: false,
                disablePlugins: ["mobile"],
                language: "en",
                buttonsXS: [
                  "bold",
                  "italic",
                  "underline",
                  "|",
                  "ul",
                  "ol",
                  "|",
                  "link",
                  "|",
                  "source",
                ],
                style: {
                  background: isAdminRole ? "#f5f5f5" : "#ffffff",
                  border: "1px solid #E5E7EB",
                  borderRadius: "5px",
                },
              }}
              onBlur={(newContent) => {
                if (!isAdminRole) {
                  // Only update if not admin role
                  formik.setFieldValue("reason", newContent);
                  formik.setFieldTouched("reason", true);
                }
              }}
            />
            {formik.touched.reason && formik.errors.reason && (
              <div
                style={{
                  color: "#d32f2f",
                  fontSize: "0.75rem",
                  marginTop: "3px",
                  marginLeft: "14px",
                }}
              >
                {formik.errors.reason}
              </div>
            )}
          </Box>

          <DialogActions sx={{ padding: "0px !important" }}>
            <Button
              onClick={onClose}
              sx={{
                fontWeight: "400",
                fontSize: "14px",
                color: "#111827",
                border: "1px solid #E5E7EB",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              sx={{
                display: "flex",
                gap: "8px",
                backgroundColor: "#F26522",
                borderColor: "#F26522",
                color: "#FFF",
                fontWeight: 400,
                fontSize: "14px",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
            >
              Save
            </Button>
          </DialogActions>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ResignationDialog;

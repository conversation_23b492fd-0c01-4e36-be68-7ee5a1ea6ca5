"use client";

import { useState, useRef, useEffect } from "react";
import {
  AppBar,
  Toolbar,
  IconButton,
  InputBase,
  Box,
  Avatar,
  Card,
  Typography,
  Divider,
} from "@mui/material";
import {
  Search,
  West,
  East,
  AccountCircleOutlined,
  SettingsOutlined,
  ArrowCircleUpOutlined,
  LoginOutlined,
} from "@mui/icons-material";
import "./Navbar.scss";
import Link from "next/link";
import { useRouter } from "next/navigation";
import useAuthStore from "@/store/authStore";
import { getUserById } from "@/app/services/users.service";
import { useIsMobile } from "@/utils/useIsMobile";
import useMediaQuery from "@mui/material/useMediaQuery";
import { MenuOpen } from "@mui/icons-material";
import Image from "next/image";

interface UserData {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string;
  designationId: string;
  designationName?: string;
}

interface NavbarProps {
  onToggleSidebar: (isCollapsed: boolean) => void;
  onMobileMenuToggle: () => void; // New prop
}

const Navbar = ({ onToggleSidebar, onMobileMenuToggle }: NavbarProps) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { employeeId: authEmployeeId } = useAuthStore();
  const [userData, setUserData] = useState<UserData | null>(null);
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const router = useRouter();
  const isTablet = useMediaQuery("screen and (max-width:1199px)");
  const isMobile = useIsMobile();

  useEffect(() => {
    const fetchUserData = async () => {
      if (authEmployeeId) {
        try {
          const response = await getUserById(authEmployeeId);
          setUserData(response.user);
        } catch (error) {
          console.error("Failed to fetch user data:", error);
        }
      }
    };

    fetchUserData();
  }, [authEmployeeId]);

  const handleToggle = () => {
    setIsCollapsed(!isCollapsed);
    onToggleSidebar(!isCollapsed);
  };

  const handleAvatarClick = () => {
    setIsDropdownOpen((prev) => !prev);
  };

  const handleLogout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("userEmail");
    sessionStorage.removeItem("auth-storage");
    router.push("/login");
    window.location.reload();
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDropdownOpen]);

  // Close dropdown on route change
  useEffect(() => {
    const handleRouteChange = () => setIsDropdownOpen(false);
    router.push = (
      (originalPush) =>
      (...args) => {
        handleRouteChange();
        return originalPush(...args);
      }
    )(router.push);

    return () => {
      router.push = (
        (originalPush) =>
        (...args) =>
          originalPush(...args)
      )(router.push);
    };
  }, [router]);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good Morning";
    if (hour < 17) return "Good Afternoon";
    return "Good Evening";
  };

  return (
    <AppBar position="static" color="transparent" elevation={0}>
      <Toolbar className="navbar">
        {isTablet ? (
          <Box
            className="navbarMobile"
            sx={{
              width: "100%",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Box>
              <IconButton onClick={onMobileMenuToggle}>
                <MenuOpen sx={{ color: "#F26522" }} />
              </IconButton>
            </Box>

            {/* Middle Navbar Logo  */}
            <Box className="middle-logo">
              <Image
                src="/assets/Aadvik-noBG.png"
                alt="Logo"
                width={100}
                height={50}
                className="logo"
              />
            </Box>
            <Box className="rightSection">
              <Avatar
                alt="User Profile"
                src={userData?.avatar}
                className="profileAvatar"
                onClick={handleAvatarClick}
              />
              {isDropdownOpen && (
                <Box
                  ref={dropdownRef}
                  className="dropdown-menu"
                  sx={{
                    position: "absolute",
                    transform: "translate3d(0px, 26px, 0px)",
                    top: "25px",
                    right: "24px",
                    backgroundColor: "white",
                    boxShadow: "0 0 10px rgba(0, 0, 0, 0.1)",
                    borderRadius: "5px",
                    zIndex: "5",
                  }}
                >
                  <Card className="card">
                    <Box
                      className="card-header"
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        position: "relative",
                        borderColor: "rgb(229, 231, 235)",
                        background: "transparent",
                        padding: "1rem 1.25rem",
                      }}
                    >
                      <Box
                        sx={{
                          width: "2.813rem",
                          height: "2.813rem",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          borderRadius: "50%",
                        }}
                      >
                        <Avatar
                          sx={{ marginRight: ".5rem" }}
                          alt="User Profile"
                          src={userData?.avatar}
                        />
                      </Box>

                      <Box>
                        <Typography
                          variant="h5"
                          sx={{
                            fontSize: "16px",
                            fontWeight: "600",
                            color: "rgb(31, 41, 55)",
                          }}
                        >
                          {userData
                            ? `${userData.firstName} ${userData.lastName}`
                            : ""}
                        </Typography>
                        <Typography
                          sx={{ fontSize: "0.75rem", fontWeight: 500 }}
                        >
                          {userData?.email || ""}
                        </Typography>
                      </Box>
                    </Box>

                    <Divider />

                    <Box
                      className="card-body"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        padding: "1rem 1.25rem",
                      }}
                    >
                      <Link
                        className="dropdown-item"
                        href="/employees/employeesDetails"
                      >
                        <AccountCircleOutlined
                          sx={{
                            width: "14px",
                            height: "14px",
                            marginRight: ".25rem",
                          }}
                        />
                        My Profile
                      </Link>
                      <Link className="dropdown-item" href="/settings">
                        <SettingsOutlined
                          sx={{
                            width: "14px",
                            height: "14px",
                            marginRight: ".25rem",
                          }}
                        />
                        Settings
                      </Link>
                      <Link className="dropdown-item" href="">
                        <ArrowCircleUpOutlined
                          sx={{
                            width: "14px",
                            height: "14px",
                            marginRight: ".25rem",
                          }}
                        />
                        My Account
                      </Link>
                    </Box>

                    <Box
                      className="card-footer"
                      sx={{
                        borderTop: "1px solid",
                        borderColor: "rgb(229, 231, 235)",
                        background: "transparent",
                        padding: "1rem 1.25rem",
                      }}
                    >
                      <Link
                        className="dropdown-item"
                        onClick={handleLogout}
                        style={{ cursor: "pointer" }}
                        href={""}
                      >
                        <LoginOutlined
                          sx={{
                            width: "14px",
                            height: "14px",
                            marginRight: ".25rem",
                          }}
                        />
                        Log out
                      </Link>
                    </Box>
                  </Card>
                </Box>
              )}
            </Box>
          </Box>
        ) : (
          <>
            <Box className="leftSection">
              <IconButton onClick={handleToggle}>
                {isCollapsed ? <East /> : <West />}
              </IconButton>
              <Box className="searchWrapper">
                <InputBase
                  placeholder="Search in HRMS"
                  className="searchInput"
                  startAdornment={
                    <IconButton size="small">
                      <Search />
                    </IconButton>
                  }
                />
              </Box>
            </Box>

            <Box className="rightSection">
              <Box className="UserName">
                <Typography
                  variant="h5"
                  sx={{
                    fontSize: "16px",
                    fontWeight: "600",
                    color: "rgb(31, 41, 55)",
                  }}
                >
                  {userData
                    ? `${getGreeting()}, ${userData.firstName} ${userData.lastName}`
                    : ""}
                </Typography>
              </Box>
              <Avatar
                alt="User Profile"
                src={userData?.avatar}
                className="profileAvatar"
                onClick={handleAvatarClick}
              />
              {isDropdownOpen && (
                <Box
                  ref={dropdownRef}
                  className="dropdown-menu"
                  sx={{
                    position: "absolute",
                    transform: "translate3d(0px, 26px, 0px)",
                    top: "25px",
                    right: "24px",
                    backgroundColor: "white",
                    boxShadow: "0 0 10px rgba(0, 0, 0, 0.1)",
                    borderRadius: "5px",
                    zIndex: "5",
                  }}
                >
                  <Card className="card">
                    <Box
                      className="card-header"
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        position: "relative",
                        borderColor: "rgb(229, 231, 235)",
                        background: "transparent",
                        padding: "1rem 1.25rem",
                      }}
                    >
                      <Box
                        sx={{
                          width: "2.813rem",
                          height: "2.813rem",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          borderRadius: "50%",
                        }}
                      >
                        <Avatar
                          sx={{ marginRight: ".5rem" }}
                          alt="User Profile"
                          src={userData?.avatar}
                        />
                      </Box>

                      <Box>
                        <Typography
                          variant="h5"
                          sx={{
                            fontSize: "16px",
                            fontWeight: "600",
                            color: "rgb(31, 41, 55)",
                          }}
                        >
                          {userData
                            ? `${userData.firstName} ${userData.lastName}`
                            : ""}
                        </Typography>
                        <Typography
                          sx={{ fontSize: "0.75rem", fontWeight: 500 }}
                        >
                          {userData?.email || ""}
                        </Typography>
                      </Box>
                    </Box>

                    <Divider />

                    <Box
                      className="card-body"
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        padding: "1rem 1.25rem",
                      }}
                    >
                      <Link className="dropdown-item" href="/employees/employeesDetails">
                        <AccountCircleOutlined
                          sx={{
                            width: "14px",
                            height: "14px",
                            marginRight: ".25rem",
                          }}
                        />
                        My Profile
                      </Link>
                      <Link className="dropdown-item" href="/settings">
                        <SettingsOutlined
                          sx={{
                            width: "14px",
                            height: "14px",
                            marginRight: ".25rem",
                          }}
                        />
                        Settings
                      </Link>
                      <Link className="dropdown-item" href="">
                        <ArrowCircleUpOutlined
                          sx={{
                            width: "14px",
                            height: "14px",
                            marginRight: ".25rem",
                          }}
                        />
                        My Account
                      </Link>
                    </Box>

                    <Box
                      className="card-footer"
                      sx={{
                        borderTop: "1px solid",
                        borderColor: "rgb(229, 231, 235)",
                        background: "transparent",
                        padding: "1rem 1.25rem",
                      }}
                    >
                      <Link
                        className="dropdown-item"
                        onClick={handleLogout}
                        style={{ cursor: "pointer" }}
                        href={""}
                      >
                        <LoginOutlined
                          sx={{
                            width: "14px",
                            height: "14px",
                            marginRight: ".25rem",
                          }}
                        />
                        Log out
                      </Link>
                    </Box>
                  </Card>
                </Box>
              )}
            </Box>
          </>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
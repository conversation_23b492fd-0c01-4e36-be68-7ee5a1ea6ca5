.navbar {
  display: flex;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  // margin-left: 251px;
  min-width: none;
  min-height: 50px !important;
  max-height: 50px !important;

  .leftSection {
    display: flex;
    align-items: center;

    .backArrow {
      font-size: 1.5rem;
      color: #555;
      cursor: pointer;
    }

    .searchWrapper {
      background-color: #f0f0f0;
      padding: 4px 12px;
      border-left: 1px solid #E5E7EB !important;
      border-radius: 5px;
      display: flex;
      align-items: center;
      margin-left: 8px;
      height: 30px;
      font-size: 12px;

      .MuiSvgIcon-root{
        font-size: 14px;
      }

      .searchInput {
        margin-left: 8px;
        font-size: 12px;
      }
    }
  }

  .rightSection {
    display: flex;
    align-items: center;
    gap: 16px;

    .profileAvatar {
      width: 32px;
      height: 32px;
      cursor: pointer;
    }

    .dropdown-menu {
      .dropdown-item {
        color: rgb(17, 24, 39);
        font-size: 14px;
        padding-top: .5rem;
        padding-bottom: .5rem;
        transition: color 0.5s ease;

        &:hover {
          color: #F26522;

        }
      }
    }
  }
}
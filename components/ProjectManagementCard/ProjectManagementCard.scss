.project-card {
  max-width: 290px;
  width: 290px;
  max-height: 260px;
  height: 260px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  display: flex;
  flex-direction: column;

  .card-content {
    padding: 16px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;

    // Header Section
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 8px;
      min-height: 28px;

      .project-title {
        font-size: 14px;
        font-weight: 600;
        color: #262A2A;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .menu-button {
        padding: 4px;
      }
    }

    // Description Section
    .project-description {
      margin-bottom: 12px;
      min-height: 50px;
      font-size: 14px;
      color: #6B7280;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
    }

    .divider {
      margin-bottom: 12px;
      background-color: #e2e8f0;
    }

    // Leader and Deadline Section
    .leader-deadline-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      min-height: 40px;

      .leader-info {
        display: flex;
        align-items: center;

        .leader-avatar {
          width: 32px;
          height: 32px;
          margin-right: 12px;
        }

        .leader-details {
          .leader-name {
            font-size: 12px;
            // font-weight: 500;
            color: #111827;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100px;
          }

          .leader-title {
            font-size: 12px;
            color: #6B7280;
          }
        }
      }

      .deadline-info {
        text-align: right;

        .deadline-label {
          font-size: 12px;
          color: #6B7280;
        }

        .deadline-date {
          font-size: 12px;
          font-weight: 500;
          color: #6B7280;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    // Tasks and Team Members Section
    .tasks-team-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      min-height: 28px;

      .tasks-info {
        display: flex;
        align-items: center;

        .task-icon-container {
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 8px;
          width: 28px;
          height: 28px;
          margin-right: 8px;

          &.active {
            background-color: rgba(34, 197, 94, 0.1);

            .task-icon {
              color: #22c55e;
              font-size: 16px;
            }
          }

          &.inactive {
            background-color: rgba(255, 0, 0, 0.1);

            .task-icon {
              color: #ff0000;
              font-size: 16px;
            }
          }
        }

        .tasks-text {
          font-size: 12.5px;
          color: #6B7280;
          font-weight: 500;

          .tasks-count {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }

      .team-members {
        .avatar-group {
          .MuiAvatarGroup-avatar {
            width: 24px;
            height: 24px;
            font-size: 12px;
            border: 2px solid #fff;
            background-color: #F26522;
            transition: transform 0.3s ease-in-out;

            &:hover {
              transform: translateY(-5px);
              z-index: 2;
            }
          }

          .avatar {
            width: 1.5rem;
            height: 1.5rem;
            transition: transform 0.3s ease-in-out;

            &:hover {
              transform: translateY(-5px);
            }
          }

          .more-members {
            background-color: #f97316;
          }
        }
      }
    }

    // Menu styling
    .card-menu {
      min-width: 120px;

      .menu-item {
        font-size: 14px;
      }
    }
  }

  // Add global styles for avatar groups
  .avatarGroup {
    .MuiAvatarGroup-avatar {
      width: 24px;
      height: 24px;
      font-size: 12px;
      background-color: #F26522;
      transition: transform 0.3s ease-in-out;

      &:hover {
        transform: translateY(-5px);
        z-index: 2;
      }
    }

    .avatar {
      width: 1.5rem;
      height: 1.5rem;
      transition: transform 0.3s ease-in-out;

      &:hover {
        transform: translateY(-5px);
      }
    }
  }
}
"use client";

import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Avatar,
  AvatarGroup,
  IconButton,
  Divider,
  Menu,
  MenuItem,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import AssignmentIcon from "@mui/icons-material/Assignment";
import { useState } from "react";
import "./ProjectManagementCard.scss";

interface ProjectManagementCardProps {
  id: string;
  name: string;
  leader: string;
  deadline: string;
  status: string;
  avatar?: string;
  description?: string;
  teamMembers: Array<{
    firstName: string;
    lastName: string;
    avatar: string;
  }>;
  teamLeaderAvatar?: string;
  tasksCompleted?: number;
  tasksTotal?: number;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}

export default function ProjectManagementCard({
  id,
  name,
  leader,
  deadline,
  status,
  avatar,
  description = "No description available",
  teamMembers,
  teamLeaderAvatar,
  tasksCompleted = 6,
  tasksTotal = 10,
  onEdit,
  onDelete,
}: ProjectManagementCardProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    onEdit(id);
    handleMenuClose();
  };

  const handleDelete = () => {
    onDelete(id);
    handleMenuClose();
  };

  const isActive = status.toLowerCase() === "active";

  return (
    <Card className="project-card">
      <CardContent className="card-content">
        {/* Header Section */}
        <Box className="card-header">
          <Typography
            className="project-title"
            variant="subtitle1"
            component="div"
          >
            {name}
          </Typography>
          <IconButton
            className="menu-button"
            aria-label="settings"
            size="small"
            onClick={handleMenuOpen}
          >
            <MoreVertIcon sx={{ fontSize: "14px" }} />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleMenuClose}
            PaperProps={{
              className: "card-menu",
            }}
          >
            <MenuItem className="menu-item" onClick={handleEdit}>
              Edit
            </MenuItem>
            <MenuItem className="menu-item" onClick={handleDelete}>
              Delete
            </MenuItem>
          </Menu>
        </Box>

        {/* Description Section */}
        <Typography className="project-description" variant="body2">
          {description}
        </Typography>

        <Divider className="divider" />

        {/* Leader and Deadline Section */}
        <Box className="leader-deadline-section">
          <Box className="leader-info">
            <Avatar
              className="leader-avatar"
              src={teamLeaderAvatar || "/placeholder.svg?height=20&width=20"}
              alt={leader}
            />
            <Box className="leader-details">
              <Typography className="leader-name" variant="subtitle2">
                {leader}
              </Typography>
              <Typography className="leader-title" variant="caption">
                Project Leader
              </Typography>
            </Box>
          </Box>

          <Box className="deadline-info">
            <Typography className="deadline-label" variant="caption">
              Deadline
            </Typography>
            <Typography className="deadline-date" variant="subtitle2">
              {deadline}
            </Typography>
          </Box>
        </Box>

        <Divider className="divider" />

        {/* Tasks and Team Members Section */}
        <Box className="tasks-team-section">
          <Box className="tasks-info">
            <Box
              className={`task-icon-container ${isActive ? "active" : "inactive"}`}
            >
              <AssignmentIcon className="task-icon" />
            </Box>
            <Typography className="tasks-text" variant="body2">
              Tasks:{" "}
              <span className="tasks-count">{`${tasksCompleted}/${tasksTotal}`}</span>
            </Typography>
          </Box>

          <Box className="team-members">
            <AvatarGroup className="avatarGroup avatar-group" max={3}>
              {teamMembers.length > 0 ? (
                teamMembers.map((member, index) => (
                  <Avatar
                    className="avatar member-avatar"
                    key={index}
                    alt={`${member.firstName} ${member.lastName}`}
                    src={member.avatar || "/placeholder.svg?height=24&width=24"}
                  />
                ))
              ) : (
                <Avatar
                  className="avatar member-avatar"
                  alt="No members"
                  src="/placeholder.svg?height=24&width=24"
                />
              )}
              {teamMembers.length > 2 && (
                <Avatar className="avatar member-avatar more-members">
                  +{teamMembers.length - 2}
                </Avatar>
              )}
            </AvatarGroup>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}

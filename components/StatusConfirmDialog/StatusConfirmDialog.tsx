import React from "react";
import "./StatusConfirmDialog.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
} from "@mui/material";
import { CheckCircle, Cancel } from "@mui/icons-material";

interface StatusConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isActive: boolean;
  title?: string;
}

const StatusConfirmDialog = ({
  open,
  onClose,
  onConfirm,
  isActive,
  title = "Confirm Status Change",
}: StatusConfirmDialogProps) => {
  const newStatus = !isActive;

  return (
    <Dialog
      className="status-confirm-dialog"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="xs"
      fullWidth
    >
      <DialogTitle className="status-confirm-dialog-title">{title}</DialogTitle>
      <DialogContent>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 2 }}>
          <Typography className="status-confirm-dialog-text">
            Are you sure you want to change the status from{" "}
            <span
              style={{
                color: isActive ? "#03C95A" : "#E70D0D",
                fontWeight: "bold",
              }}
            >
              {isActive ? "Active" : "Inactive"}
            </span>{" "}
            to{" "}
            <span
              style={{
                color: newStatus ? "#03C95A" : "#E70D0D",
                fontWeight: "bold",
              }}
            >
              {newStatus ? "Active" : "Inactive"}
            </span>
            ?
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions
        className="status-confirm-dialog-actions"
        sx={{ padding: "16px 24px" }}
      >
        <Button
          onClick={onClose}
          variant="outlined"
          color="inherit"
          startIcon={<Cancel />}
        >
          Cancel
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          sx={{
            bgcolor: newStatus ? "#03C95A" : "#E70D0D",
            "&:hover": {
              bgcolor: newStatus ? "#03B050" : "#D50000",
            },
          }}
          startIcon={<CheckCircle />}
        >
          Confirm
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default StatusConfirmDialog;

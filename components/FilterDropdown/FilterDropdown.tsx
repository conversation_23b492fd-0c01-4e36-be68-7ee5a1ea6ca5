"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON>, Button as <PERSON><PERSON><PERSON><PERSON>on, <PERSON>u, MenuItem, Popover } from "@mui/material";
import { KeyboardArrowDown } from "@mui/icons-material";
import { DateRange, DateRangePicker } from "@mui/x-date-pickers-pro";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers";
import dayjs, { Dayjs } from "dayjs";
import axios from "axios"; // Import axios for API requests

const FilterDropdown = () => {
    const router = useRouter();
    const searchParams = useSearchParams();

    // State for Date Dropdown
    const [anchorElDate, setAnchorElDate] = useState<null | HTMLElement>(null);
    const [dateRange, setDateRange] = useState<DateRange<Dayjs>>([
        dayjs().subtract(7, "day"),
        dayjs()
    ]);
    const [isCustomRange, setIsCustomRange] = useState(false);
    const [anchorElPopover, setAnchorElPopover] = useState<null | HTMLElement>(null);

    // Handlers for Date Filter
    const handleDateFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorElDate(event.currentTarget);
    };

    const handleDateFilterClose = () => {
        setAnchorElDate(null);
    };

    const handleStatusFilterChange = (option: string, event?: React.MouseEvent<HTMLElement>) => {
        let newRange: DateRange<Dayjs> = dateRange;

        switch (option) {
            case "Today":
                newRange = [dayjs(), dayjs()];
                break;
            case "Yesterday":
                newRange = [dayjs().subtract(1, "day"), dayjs().subtract(1, "day")];
                break;
            case "Last 7 Days":
                newRange = [dayjs().subtract(7, "day"), dayjs()];
                break;
            case "Last 30 Days":
                newRange = [dayjs().subtract(30, "day"), dayjs()];
                break;
            case "This Year":
                newRange = [dayjs().startOf("year"), dayjs()];
                break;
            case "Next Year":
                newRange = [
                    dayjs().add(1, "year").startOf("year"),
                    dayjs().add(1, "year").endOf("year"),
                ];
                break;
            case "Custom Range":
                if (event) {
                    setIsCustomRange(true);
                    setAnchorElPopover(event.currentTarget);
                }
                return;
            default:
                break;
        }

        setDateRange(newRange);
        updateURL(newRange); // Update the URL
        handleDateFilterClose();
    };

    const handleApply = () => {
        updateURL(dateRange);
        setIsCustomRange(false);
        setAnchorElPopover(null);
    };

    // Function to update URL with selected date range
    const updateURL = (newRange: DateRange<Dayjs>) => {
        const formattedStart = newRange[0]?.format("YYYY-MM-DD");
        const formattedEnd = newRange[1]?.format("YYYY-MM-DD");

        const newQueryParams = new URLSearchParams(searchParams);
        if (formattedStart) {
            newQueryParams.set("startDate", formattedStart);
        }
        if (formattedEnd) {
            newQueryParams.set("endDate", formattedEnd);
        }

        router.push(`?${newQueryParams.toString()}`, { scroll: false });
    };

    // API call when date range changes
    useEffect(() => {
        const fetchData = async () => {
            if (dateRange[0] && dateRange[1]) {
                const formattedStart = dateRange[0].format("YYYY-MM-DD");
                const formattedEnd = dateRange[1].format("YYYY-MM-DD");

                try {
                    const response = await axios.get(`/api/data`, {
                        params: {
                            startDate: formattedStart,
                            endDate: formattedEnd
                        }
                    });
                    console.log("API Response:", response.data);
                } catch (error) {
                    console.error("Error fetching data:", error);
                }
            }
        };

        fetchData();
    }, [dateRange]);

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Box className="filter-dropdown">
                {/* Date Filter Dropdown */}
                <MuiButton onClick={handleDateFilterClick} sx={{ textTransform: "none" }}>
                    {`${dateRange[0]?.format("MM/DD/YYYY")} - ${dateRange[1]?.format("MM/DD/YYYY")}`}
                    <KeyboardArrowDown />
                </MuiButton>
                <Menu anchorEl={anchorElDate} open={Boolean(anchorElDate)} onClose={handleDateFilterClose}>
                    <MenuItem onClick={(event) => handleStatusFilterChange("Today", event)}>Today</MenuItem>
                    <MenuItem onClick={(event) => handleStatusFilterChange("Yesterday", event)}>Yesterday</MenuItem>
                    <MenuItem onClick={(event) => handleStatusFilterChange("Last 7 Days", event)}>Last 7 Days</MenuItem>
                    <MenuItem onClick={(event) => handleStatusFilterChange("Last 30 Days", event)}>Last 30 Days</MenuItem>
                    <MenuItem onClick={(event) => handleStatusFilterChange("This Year", event)}>This Year</MenuItem>
                    <MenuItem onClick={(event) => handleStatusFilterChange("Next Year", event)}>Next Year</MenuItem>
                    <MenuItem onClick={(event) => handleStatusFilterChange("Custom Range", event)}>Custom Range</MenuItem>
                </Menu>

                {/* Popover for Custom Date Range Selection */}
                <Popover
                    open={isCustomRange}
                    anchorEl={anchorElPopover}
                    onClose={() => setIsCustomRange(false)}
                    anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
                >
                    <Box p={2}>
                        <DateRangePicker
                            value={dateRange}
                            onChange={(newValue: DateRange<Dayjs>) => setDateRange(newValue)}
                        />
                        <Box display="flex" justifyContent="space-between" mt={2}>
                            <MuiButton onClick={() => setIsCustomRange(false)} sx={{ color: "#212529" }}>
                                Cancel
                            </MuiButton>
                            <MuiButton
                                sx={{ backgroundColor: "#f26522", borderColor: "#f26522", color: "#fff" }}
                                onClick={handleApply}
                                variant="contained"
                            >
                                Apply
                            </MuiButton>
                        </Box>
                    </Box>
                </Popover>
            </Box>
        </LocalizationProvider>
    );
};

export default FilterDropdown;

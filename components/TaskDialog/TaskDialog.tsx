"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  IconButton,
  Typography,
  Box,
  Chip,
  Avatar,
  AvatarGroup,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import StarIcon from "@mui/icons-material/Star";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import CircleIcon from "@mui/icons-material/Circle";
import { getTaskById } from "@/app/services/projects/project.service";
import { toast } from "react-toastify";
import Loader from "../Loader/Loader";

import "./TaskDialog.scss";

interface TaskDialogProps {
  open: boolean;
  onClose: () => void;
  taskId: string;
}

interface TaskData {
  _id: string;
  title: string;
  dueDate: string;
  status: { _id: string; title: string };
  description: string;
  teamMembers: Array<{
    _id: string;
    firstName: string;
    lastName: string;
    avatar: string;
  }>;
  priority: string;
  createdAt: string;
}

export default function TaskDialog({ open, onClose, taskId }: TaskDialogProps) {
  const [task, setTask] = useState<TaskData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchTask = async () => {
      if (open && taskId) {
        try {
          setLoading(true);
          const response = await getTaskById(taskId);
          setTask(response.task);
        } catch (error) {
          console.error("Failed to fetch task:", error);
          toast.error("Failed to load task details");
        } finally {
          setLoading(false);
        }
      }
    };
    fetchTask();
  }, [open, taskId]);

  if (loading || !task) {
    return (
      <Dialog
        open={open}
        onClose={onClose}
        disableEscapeKeyDown
        maxWidth={false}
        PaperProps={{ sx: { width: 500, height: 460, borderRadius: 1, overflow: "hidden" } }}
      >
        <Loader loading={loading} />
      </Dialog>
    );
  }

  // Map status title to TaskDialog expected values
  const mapStatus = (title: string): "Completed" | "In Progress" | "Pending" => {
    const normalized = title.toLowerCase();
    if (normalized.includes("completed")) return "Completed";
    if (normalized.includes("progress") || normalized.includes("to do")) return "In Progress";
    return "Pending";
  };

  const taskProps = {
    title: task.title,
    isUrgent: task.priority === "High",
    createdOn: new Date(task.createdAt).toLocaleDateString("en-US", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    }),
    dueDate: new Date(task.dueDate).toLocaleDateString("en-US", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    }),
    status: mapStatus(task.status.title),
    description: task.description || "No description provided.",
    assignees: task.teamMembers.map(member => ({
      avatar: member.avatar,
      name: `${member.firstName} ${member.lastName}`,
    })),
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth={false}
      PaperProps={{
        sx: {
          width: 500,
          height: 460,
          borderRadius: 1,
          boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
          overflow: "hidden",
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: "#212529",
          color: "white",
          p: "16px !important",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography
          sx={{
            fontSize: "20px",
            fontWeight: "600",
            color: "#FFFFFF !important",
          }}
        >
          {taskProps.title}
        </Typography>
        <Box sx={{ display: "flex", gap: 0 }}>
          {taskProps.isUrgent && (
            <Chip
              label="Urgent"
              size="small"
              sx={{
                bgcolor: "#f44336",
                color: "white",
                fontWeight: "bold",
                height: 24,
              }}
            />
          )}
          <IconButton size="small" sx={{ color: "#ffc107" }}>
            <StarIcon fontSize="small" />
          </IconButton>
          <IconButton size="small" sx={{ color: "white" }}>
            <DeleteOutlineIcon fontSize="small" />
          </IconButton>
          <IconButton size="small" sx={{ color: "white" }} onClick={onClose}>
            <CloseIcon fontSize="small" />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ padding: "16px" }}>
        <Box>
          <Typography sx={{ margin: "0 0 8px 0", color: "#202C4B", fontSize: "16px" }}>
            Task Details
          </Typography>

          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "repeat(3, 1fr)",
              border: "1px solid #e0e0e0",
              borderRadius: 1,
              margin: "0 0 16px 0",
            }}
          >
            <Box sx={{ p: "8px", textAlign: "center" }}>
              <Typography sx={{ fontSize: "14px", color: "#212529" }}>Created On</Typography>
              <Typography sx={{ fontSize: "14px", color: "#212529" }}>
                {taskProps.createdOn}
              </Typography>
            </Box>
            <Box sx={{ p: "8px", textAlign: "center" }}>
              <Typography sx={{ fontSize: "14px", color: "#212529" }}>Due Date</Typography>
              <Typography sx={{ fontSize: "14px", color: "#212529" }}>
                {taskProps.dueDate}
              </Typography>
            </Box>
            <Box sx={{ p: "8px", textAlign: "center" }}>
              <Typography sx={{ fontSize: "14px", color: "#212529" }}>Status</Typography>
              <Box sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
                <CircleIcon
                  sx={{
                    fontSize: "10px",
                    mr: 0.5,
                    color:
                      taskProps.status === "Completed" ? "#4caf50" :
                      taskProps.status === "In Progress" ? "#ff9800" : "#9e9e9e",
                  }}
                />
                <Typography
                  variant="body1"
                  sx={{
                    fontSize: "14px",
                    color:
                      taskProps.status === "Completed" ? "#4caf50" :
                      taskProps.status === "In Progress" ? "#ff9800" : "text.primary",
                  }}
                >
                  {taskProps.status}
                </Typography>
              </Box>
            </Box>
          </Box>

          <Typography sx={{ color: "#202C4B", fontSize: "16px", margin: "0 0 8px 0" }}>
            Description
          </Typography>
          <Typography variant="body1" sx={{ mb: "16px", color: "#212529" }}>
            {taskProps.description}
          </Typography>

          <Typography sx={{ color: "#202C4B", fontSize: "16px", margin: "0 0 8px 0" }}>
            Assignee
          </Typography>
          <AvatarGroup
            max={4}
            className="task-avatars"
            sx={{ display: "flex", flexDirection: "row", justifyContent: "flex-start" }}
          >
            {taskProps.assignees.map((assignee, index) => (
              <Avatar
                key={index}
                alt={assignee.name}
                src={assignee.avatar}
                className="task-avatar"
              />
            ))}
          </AvatarGroup>
        </Box>
      </DialogContent>
    </Dialog>
  );
}
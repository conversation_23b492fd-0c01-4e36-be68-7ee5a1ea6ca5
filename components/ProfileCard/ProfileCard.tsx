import { useState } from "react";
import {
  Card,
  IconButton,
  Menu,
  MenuItem,
  Checkbox,
  Avatar,
  LinearProgress,
  Typography,
  Box,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import AddEmployeeForm from "@/components/AddEmployeeForm/AddEmployeeForm";
import { getUserById } from "@/app/services/users.service";
import { EditNote } from "@mui/icons-material";

interface ProfileCardProps {
  id: string;
  name: string;
  role: string;
  projects: number;
  done: number;
  progress: number;
  productivity: number;
  avatar: string;
  roleColor: string;
  progressColor: string;
  onDelete?: (id: string) => void;
  refreshList?: () => void;
}

interface EmployeeFormData {
  roles: string[];
  _id: string;
  firstName: string;
  lastName: string;
  employeeId?: string;
  joiningDate: string;
  email: string;
  password: string;
  confirmPassword?: string;
  phone: string;
  company: string;
  departmentId: string;
  designationId: string;
  about: string;
  avatar: string | undefined;
  birthDate?: string;
}

const ProfileCard: React.FC<ProfileCardProps> = ({
  id,
  name,
  role,
  projects,
  done,
  progress,
  productivity,
  avatar,
  roleColor,
  progressColor,
  onDelete,
  refreshList,
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const open = Boolean(anchorEl);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState<EmployeeFormData | null>(null);

  const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleEditClick = async () => {
    try {
      const response = await getUserById(id);
      if (response.success) {
        const userData = response.user;
        const mappedEmployee: EmployeeFormData = {
          roles: userData.roles || ["Employee"],
          _id: userData._id,
          firstName: userData.firstName || "",
          lastName: userData.lastName || "",
          employeeId: userData.employeeId || "",
          email: userData.email || "",
          phone: userData.phone || "",
          designationId: userData.designationId || "",
          joiningDate: userData.joiningDate
            ? new Date(userData.joiningDate).toISOString().split("T")[0]
            : "",
          password: "",
          confirmPassword: "",
          company: userData.company || "",
          departmentId: userData.departmentId || "",
          birthDate: userData.birthDate
            ? new Date(userData.birthDate).toISOString().split("T")[0]
            : "",
          about: userData.about || "",
          avatar: userData.avatar || "",
        };
        setEditingEmployee(mappedEmployee);
        setEditModalOpen(true);
      } else {
        throw new Error("Failed to fetch employee data");
      }
    } catch (error) {
      console.error("Error fetching employee data:", error);
    }
    handleClose();
  };

  const handleDeleteClick = () => {
    if (onDelete) {
      onDelete(id);
    }
    handleClose();
  };

  return (
    <>
      <Card sx={{ borderRadius: 3, padding: 2, textAlign: "center", minWidth: "205px", width: "379px" }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          {/* <Checkbox sx={{ width: "1.15rem", height: "1.15rem" }} /> */}
          <Box sx={{ width: "1.15rem", height: "1.15rem" }}></Box>
          <Avatar
            src={avatar}
            sx={{ width: 47.38, height: 47.38, border: "3px solid white" }}
          />
          <IconButton sx={{ width: "27.9px", height: "27.9px" }} onClick={handleMenuClick}>
            <MoreVertIcon sx={{ width: "12.8px", height: "12.22px" }} />
          </IconButton>
        </Box>

        {/* Dropdown Menu */}
        <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem onClick={handleEditClick}>
            <EditNote sx={{ mr: 1 }} /> Edit
          </MenuItem>
          <MenuItem onClick={handleDeleteClick}>
            <DeleteIcon sx={{ mr: 1 }} /> Delete
          </MenuItem>
        </Menu>

        <Typography
          variant="h6"
          sx={{
            marginTop: 1,
            color: "rgb(17, 24, 39)",
            cursor: "pointer",
            textDecoration: "none",
            transition: "0.5s",
            fontWeight: 600,
            fontSize: "14px",
          }}
        >
          {name}
        </Typography>
        <Typography
          variant="body2"
          sx={{
            background: roleColor,
            color: "white",
            borderRadius: 2,
            display: "inline-block",
            padding: "2px 8px",
            fontSize: "10.5px",
            fontWeight: 500,
          }}
        >
          {role}
        </Typography>

        <Box sx={{ display: "flex", justifyContent: "space-between", marginTop: 2, fontSize: "12px" }}>
          <Typography variant="body2" sx={{ fontSize: "0.75rem" }}>
            Projects<br />
            <b>{projects}</b>
          </Typography>
          <Typography variant="body2" sx={{ fontSize: "0.75rem" }}>
            Done<br />
            <b>{done}</b>
          </Typography>
          <Typography variant="body2" sx={{ fontSize: "0.75rem" }}>
            Progress<br />
            <b>{progress}</b>
          </Typography>
        </Box>

        <Typography variant="body2" sx={{ marginTop: 1, marginBottom: "8px", fontSize: "14px" }}>
          Productivity: {productivity}%
        </Typography>
        <LinearProgress
          variant="determinate"
          value={productivity}
          sx={{
            height: "0.3125rem",
            borderRadius: 5,
            backgroundColor: "#eee",
            "& .MuiLinearProgress-bar": { backgroundColor: progressColor },
          }}
        />
      </Card>

      {/* AddEmployeeForm for Editing */}
      {editingEmployee && (
        <AddEmployeeForm
          open={editModalOpen}
          onClose={() => setEditModalOpen(false)}
          refreshList={refreshList}
          isEdit
          employeeData={editingEmployee}
        />
      )}
    </>
  );
};

export default ProfileCard;
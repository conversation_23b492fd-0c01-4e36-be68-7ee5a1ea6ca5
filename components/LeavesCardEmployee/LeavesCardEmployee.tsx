import { Card, CardContent, Typography, Box } from "@mui/material";
// import "./LeavesCardEmployee.scss";

interface LeavesCardEmployeeProps {
    title: string;
    value: string | number;
    remainingLeaveValue: string;
    bgImage: string;
    icon?: React.ReactNode;
    textcolor: string;
    bgcolor: string;
}

const LeavesCardEmployee: React.FC<LeavesCardEmployeeProps> = ({ title, value, bgImage, remainingLeaveValue, icon, textcolor, bgcolor }) => {
    return (
        <Card 
            sx={{
                display: "flex",
                alignItems: "center",
                width: "50%",
                borderRadius: 2,
                overflow: "hidden",
                boxShadow: 3,
                // border:"2px solid red"
            }}
        >
            {/* Left Icon Section */}
            <CardContent sx={{ flexGrow: 1, display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "start", padding: "1.25rem !important" }}>
                <Typography variant="body2" color="textSecondary" sx={{ fontSize: "14px" }}>
                    {title}
                </Typography>

                <Typography variant="h6" fontWeight="bold" sx={{ fontSize: "18px" }}>
                    {value}
                </Typography>

                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        backgroundColor: `${bgcolor}`,
                        color: `${textcolor}`,
                        borderRadius: "4px",
                    }}
                >
                    <Typography variant="body2" sx={{ fontSize: "10.5px", fontWeight: 600, letterSpacing: "0.5px", padding: "0.25rem 0.45rem", borderRadius: "4px" }}>
                        Remaining Leave : {remainingLeaveValue}
                    </Typography>
                </Box>

            </CardContent>


            {/* Right Content */}
            <Box
                sx={{
                    backgroundImage: `url(${bgImage})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    width: "110px",
                    height: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    color: "#fff",
                }}
            >
                <Box sx={{ ml: 4, marginBottom: 2 }}>
                    {/* Icon */}
                    {icon}
                </Box>
            </Box>

        </Card >
    );
};

export default LeavesCardEmployee;

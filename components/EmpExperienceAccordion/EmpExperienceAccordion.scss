.experience-accordion {
    width: 100%;
    margin-top: 0px;
    margin-bottom: 24px;

    .MuiCollapse-entered {
        margin-bottom: 24px !important;
    }

    .accordion-summary {
        .MuiAccordionSummary-content {
            margin: 0px;
        }
    }

    .accordion-details {
        display: flex;
        flex-direction: column;
        border-top: 1px solid #E5E7EB !important;


        .span {
            font-size: 13px;
            color: #677788;
        }

        h6 {
            font-size: 14px;
            font-weight: 500;
            margin-top: 4px;
            color: #202C4B;
        }

        p {
            font-size: 13px;
            color: #212529;
        }

    }

}

.edit-dialog-container {
    .MuiPaper-root {
        min-width: 800px;


    }

    .MuiDialogContent-root {
        padding-top: 20px !important;
    }
}
import * as React from "react";
import Link from "next/link";
import "./EmpExperienceAccordion.scss";
import {
  Box,
  Checkbox,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Dialog,
  DialogContent,
  DialogActions,
  TextField,
  DialogTitle,
  IconButton,
} from "@mui/material";
import Typography from "@mui/material/Typography";
import { Close, EditNote, ExpandMore } from "@mui/icons-material";
import { addExperience, updateExperience } from "@/app/services/users.service";
import { toast } from "react-toastify";

interface EmpExperienceAccordionProps {
  userData: {
    user: {
      _id: string;
      experience: {
        _id?: string;
        previousCompany: string;
        designation: string;
        startDate: string;
        endDate: string;
      }[];
    };
  } | null;
  refreshUserData?: () => void; // Add this prop to allow parent to refresh data
}

interface ExperienceEmpIdProps {
  employeeId: string | null;
}

export default function EmpExperienceAccordion({
  userData,
  employeeId,
  refreshUserData,
}: EmpExperienceAccordionProps & ExperienceEmpIdProps) {
  const [open, setOpen] = React.useState(false);
  const [experienceData, setExperienceData] = React.useState({
    experienceId: employeeId || "",
    previousCompany: "",
    designation: "",
    startDate: "",
    endDate: "",
  });
  const [isCurrent, setIsCurrent] = React.useState(false); // For "working present" checkbox
  const [dateErrors, setDateErrors] = React.useState({
    startDate: "",
    endDate: "",
  });

  const experienceId =
    userData?.user?.experience && userData.user.experience.length > 0
      ? userData.user.experience[0]?._id
      : undefined;
  const userId = userData?.user?._id;

  // Sync experienceData with userData when userData changes
  React.useEffect(() => {
    if (userData?.user?.experience && userData.user.experience.length > 0) {
      const exp = userData.user.experience[0];
      setExperienceData({
        ...experienceData,
        previousCompany: exp.previousCompany || "",
        designation: exp.designation || "",
        startDate: exp.startDate ? exp.startDate.split("T")[0] : "", // Format to YYYY-MM-DD
        endDate: exp.endDate ? exp.endDate.split("T")[0] : "", // Format to YYYY-MM-DD
      });
      setIsCurrent(!exp.endDate); // If no endDate, assume current job
    }
  }, [userData]);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const validateDates = (start: string, end: string): boolean => {
    // Reset errors
    setDateErrors({ startDate: "", endDate: "" });

    const startDate = new Date(start);
    const endDate = new Date(end);
    const today = new Date();

    // Validate start date
    if (startDate > today) {
      setDateErrors((prev) => ({
        ...prev,
        startDate: "Start date cannot be in the future",
      }));
      return false;
    }

    // Validate end date if it's not current job
    if (!isCurrent && end) {
      if (endDate > today) {
        setDateErrors((prev) => ({
          ...prev,
          endDate: "End date cannot be in the future",
        }));
        return false;
      }

      if (startDate > endDate) {
        setDateErrors((prev) => ({
          ...prev,
          endDate: "End date must be after start date",
        }));
        return false;
      }
    }

    return true;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setExperienceData((prev) => ({ ...prev, [name]: value }));

    // Validate dates when either date field changes
    if (name === "startDate" || name === "endDate") {
      const start = name === "startDate" ? value : experienceData.startDate;
      const end = name === "endDate" ? value : experienceData.endDate;
      validateDates(start, end);
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsCurrent(e.target.checked);
    if (e.target.checked) {
      setExperienceData({ ...experienceData, endDate: "" }); // Clear endDate if current
    }
  };

  const handleSave = async () => {
    // Validate dates before saving
    if (!validateDates(experienceData.startDate, experienceData.endDate)) {
      return; // Don't proceed if validation fails
    }

    const experiencePayload = {
      previousCompany: experienceData.previousCompany,
      designation: experienceData.designation,
      startDate: experienceData.startDate,
      endDate: isCurrent ? "" : experienceData.endDate,
      userId: employeeId || userId,
    };

    try {
      // Always use addExperience (POST API)
      await addExperience(experiencePayload);
      console.log("Experience saved successfully");
      // toast.success("Experience saved successfully");
      handleClose();

      // Refresh user data after successful save
      if (refreshUserData) {
        refreshUserData();
      }
    } catch (error) {
      console.error("Failed to save experience details:", error);
      // toast.error("Failed to save experience details");
    }
  };

  return (
    <>
      <Accordion
        className="experience-accordion"
        sx={{ marginTop: "0 !important" }}
      >
        <AccordionSummary
          className="accordion-summary"
          expandIcon={<ExpandMore />}
          aria-controls="panel1-content"
          id="panel1-header"
          sx={{ padding: "20px" }}
        >
          <Typography
            className="span"
            component="span"
            sx={{
              margin: "0px",
              fontSize: "16px",
              fontWeight: 600,
              display: "flex",
              justifyContent: "space-between",
              minWidth: "100%",
            }}
          >
            Experience
            <Link
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleOpen();
              }}
            >
              <EditNote sx={{ fontSize: "16px" }} />
            </Link>
          </Typography>
        </AccordionSummary>
        <AccordionDetails
          className="accordion-details"
          sx={{
            padding: "20px",
            display: "flex",
            flexDirection: "column",
          }}
        >
          {userData?.user?.experience && userData.user.experience.length > 0 ? (
            userData.user.experience.map((exp, index) => (
              <Box
                key={index}
                className="experience"
                sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}
              >
                <Box>
                  <Typography
                    className="span"
                    component="span"
                    sx={{ margin: "0px" }}
                  >
                    {exp.previousCompany}
                  </Typography>
                  <Typography variant="h6">{exp.designation}</Typography>
                </Box>
                <Box className="year">
                  <Typography>
                    {new Date(exp.startDate).getFullYear()} -{" "}
                    {exp.endDate
                      ? new Date(exp.endDate).getFullYear()
                      : "Present"}
                  </Typography>
                </Box>
              </Box>
            ))
          ) : (
            <Typography>No experience data available</Typography>
          )}
        </AccordionDetails>
      </Accordion>

      <Dialog
        className="edit-dialog-container"
        open={open}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleClose();
          }
        }}
        disableEscapeKeyDown
        fullWidth
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography
            variant="h6"
            sx={{ fontWeight: 600, fontSize: "20px", color: "#202C4B" }}
          >
            Experience Information
          </Typography>
          <IconButton
            onClick={handleClose}
            aria-label="close"
            sx={{
              backgroundColor: "#6b7280",
              backgroundImage: "none",
              borderRadius: "50%",
              color: "#fff",
              height: "20px",
              width: "20px",
              margin: 0,
              padding: 0,
              "&:hover": {
                backgroundColor: "#d55a1d",
              },
              "& .MuiSvgIcon-root": {
                fontSize: "14px",
              },
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ padding: "1rem !important" }}>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Box sx={{ width: "100%" }}>
              <label>
                Previous Company Name <span className="required">*</span>
              </label>
              <TextField
                fullWidth
                name="previousCompany"
                value={experienceData.previousCompany}
                onChange={handleChange}
                required
                sx={{ marginBottom: 2 }}
              />
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>
                Designation <span className="required">*</span>
              </label>
              <TextField
                fullWidth
                name="designation"
                value={experienceData.designation}
                onChange={handleChange}
                required
                sx={{ marginBottom: 2 }}
              />
            </Box>
          </Box>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Box sx={{ width: "100%" }}>
              <label>
                Start Date <span className="required">*</span>
              </label>
              <TextField
                fullWidth
                type="date"
                name="startDate"
                value={experienceData.startDate}
                onChange={handleChange}
                InputLabelProps={{ shrink: true }}
                required
                error={!!dateErrors.startDate}
                helperText={dateErrors.startDate}
                sx={{ marginBottom: 2 }}
              />
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>
                End Date <span className="required">*</span>
              </label>
              <TextField
                fullWidth
                type="date"
                name="endDate"
                value={experienceData.endDate}
                onChange={handleChange}
                InputLabelProps={{ shrink: true }}
                disabled={isCurrent}
                error={!!dateErrors.endDate}
                helperText={dateErrors.endDate}
                sx={{ marginBottom: 2 }}
              />
            </Box>
          </Box>
          <Box>
            <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
              <Checkbox
                className="checkbox"
                checked={isCurrent}
                onChange={handleCheckboxChange}
                sx={{ padding: "0px" }}
              />
              <Typography variant="body2" sx={{ color: "#212529" }}>
                Check if you are working present
              </Typography>
            </Box>
          </Box>

          <DialogActions
            sx={{ padding: "0px !important", paddingTop: "1rem !important" }}
          >
            <Button
              sx={{
                fontWeight: "400",
                fontSize: "14px",
                color: "#111827",
                border: "1px solid #E5E7EB",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={handleClose}
              color="secondary"
            >
              Cancel
            </Button>
            <Button
              sx={{
                backgroundColor: "#F26522",
                border: "1px solid #F26522",
                color: "#FFF",
                borderRadius: "5px",
                padding: "0.5rem 0.85rem",
                fontSize: "14px",
                transition: "all 0.5s",
                fontWeight: 500,
                textTransform: "none",
              }}
              onClick={handleSave}
              variant="contained"
              color="primary"
            >
              Save
            </Button>
          </DialogActions>
        </DialogContent>
      </Dialog>
    </>
  );
}

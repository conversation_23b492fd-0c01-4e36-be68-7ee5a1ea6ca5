"use client";

import React, { useState, useEffect } from "react";
import { Box, Button, Typography, Avatar, CircularProgress } from "@mui/material";
import FingerprintIcon from '@mui/icons-material/Fingerprint';
import { punchIn, punchOut } from "@/app/services/attendance.service";
import useAuthStore from "@/store/authStore";
import { toast } from "react-toastify";
import useFetchAttendanceByEmpId from "@/app/hooks/attendance/useFetchAttendanceByEmpId";

interface User {
  _id: string;
  firstName: string;
  avatar: string;
}

interface AttendanceRecord {
  _id: string;
  empId: {
    _id: string;
    firstName: string;
    avatar: string;
    email: string;
    departmentName: string;
  };
  date?: string;
  avatar?: string;
  status?: string;
  employeeName?: string;
  departmentName?: string;
  isActive?: boolean;
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
  break?: number;
  checkIn?: string;
  checkOut?: string;
  checkIns?: string[];
  checkOuts?: string[];
  late?: number;
  productionHours?: number;
  totalWorkingHours?: number;
}

interface AttendancePunchCardProps {
  onPunch?: () => void;
}

const AttendancePunchCard: React.FC<AttendancePunchCardProps> = ({ onPunch }) => {
  const { employeeId } = useAuthStore();
  const [isPunchedIn, setIsPunchedIn] = useState(false);
  const [isPunchedOut, setIsPunchedOut] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [user, setUser] = useState<User | null>(null);
  const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord | null>(null);
  const [refreshKey, setRefreshKey] = useState(0); // State to trigger refetch

  // Fetch attendance data
  const [attendanceData] = useFetchAttendanceByEmpId({
    empId: employeeId || '',
    setIsLoading: setLoading,
    refresh: refreshKey, // Pass refreshKey to trigger refetch
  });

  // Get today's date in YYYY-MM-DD format
  const getTodayDate = () => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  };

  // Fetch user and determine punch status on mount or when attendance data changes
  useEffect(() => {
    if (!employeeId) {
      setError("Please log in");
      return;
    }

    if (attendanceData && Array.isArray(attendanceData.results) && attendanceData.results.length > 0) {
      const userData = attendanceData.results[0].empId;
      setUser({
        _id: userData._id,
        firstName: userData.firstName,
        avatar: userData?.avatar,
      });

      // Determine today's attendance record
      const today = getTodayDate();
      const todayRecord = attendanceData.results.find(
        (record: AttendanceRecord) => record.date?.split("T")[0] === today
      );
      setTodayAttendance(todayRecord || null);

      // Set punch status based on today's record
      if (todayRecord) {
        const checkIns = todayRecord.checkIns || [];
        const checkOuts = todayRecord.checkOuts || [];
        if (checkIns.length > checkOuts.length) {
          setIsPunchedIn(true);
          setIsPunchedOut(false);
        } else if (checkIns.length === checkOuts.length && checkIns.length > 0) {
          setIsPunchedIn(false);
          setIsPunchedOut(true);
        } else {
          setIsPunchedIn(false);
          setIsPunchedOut(false);
        }
      } else {
        setIsPunchedIn(false);
        setIsPunchedOut(false);
      }
    } else {
      setError("No attendance data found");
    }
  }, [employeeId, attendanceData]);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const handlePunch = async () => {
    if (!employeeId) {
      setError("Please log in to punch in/out");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (!isPunchedIn && !isPunchedOut) {
        await punchIn(employeeId);
        toast.success("Punch in successful");
        setIsPunchedIn(true);
        setIsPunchedOut(false);
        const newCheckIn = new Date().toISOString();
        setTodayAttendance({
          _id: "",
          empId: {
            _id: employeeId,
            firstName: user?.firstName || "",
            avatar: user?.avatar || "",
            email: "",
            departmentName: "",
          },
          date: getTodayDate(),
          checkIn: newCheckIn,
          checkIns: [newCheckIn],
          status: "Present",
        });
        setRefreshKey(prev => prev + 1); // Trigger refetch
        onPunch?.();
      } else if (isPunchedIn && !isPunchedOut) {
        await punchOut(employeeId);
        toast.success("Punch out successful");
        setIsPunchedIn(false);
        setIsPunchedOut(true);
        const newCheckOut = new Date().toISOString();
        setTodayAttendance({
          ...todayAttendance!,
          checkOut: newCheckOut,
          checkOuts: [...(todayAttendance?.checkOuts || []), newCheckOut],
        });
        setRefreshKey(prev => prev + 1); // Trigger refetch
        onPunch?.();
      } else if (!isPunchedIn && isPunchedOut) {
        await punchIn(employeeId);
        toast.success("Punch in successful");
        setIsPunchedIn(true);
        setIsPunchedOut(false);
        const newCheckIn = new Date().toISOString();
        setTodayAttendance({
          ...todayAttendance!,
          checkIn: newCheckIn,
          checkIns: [...(todayAttendance?.checkIns || []), newCheckIn],
        });
        setRefreshKey(prev => prev + 1); // Trigger refetch
        onPunch?.();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
      // toast.error("Punch operation failed");
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (isoDate?: string) => {
    if (!isoDate) return "N/A";
    const date = new Date(isoDate);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
  };

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return "Good Morning";
    if (hour < 17) return "Good Afternoon";
    return "Good Evening";
  };

  return (
    <Box
      className="attendance-punch-card"
      sx={{
        backgroundColor: "#FFF",
        boxShadow: "0px 4px 8px rgba(0,0,0,0.1)",
        borderRadius: "8px",
        padding: "20px",
        textAlign: "center",
        minWidth: "258px",
        minHeight: "352px",
      }}
    >
      <Typography sx={{ fontSize: "14px", color: "#6b7280", fontWeight: "500" }}>
        {loading ? "Loading..." : `${getGreeting()}, ${user?.firstName || "User"}`}
      </Typography>

      <Typography sx={{ fontSize: "1.125rem", fontWeight: "600", color: "#202C4B" }}>
        {formatTime(currentTime.toISOString())}, {formatDate(currentTime)}
      </Typography>

      <Box sx={{ display: "flex", justifyContent: "center", marginTop: "12px" }}>
        <Avatar
          src={user?.avatar}
          alt={user?.firstName || "User"}
          sx={{
            width: "110px",
            height: "110px",
          }}
        />
      </Box>

      <Box
        sx={{
          backgroundColor: "#f26522",
          color: "#fff",
          padding: "5px 12px",
          borderRadius: "4px",
          fontSize: "12px",
          lineHeight: "1.5",
          fontWeight: "600",
          margin: "10px auto",
          display: "inline-block",
        }}
      >
        Production: {todayAttendance?.productionHours || 0} hrs
      </Box>

      <Box sx={{ display: "flex", alignItems: "center", justifyContent: "center", gap: "6px" }}>
        <FingerprintIcon sx={{ color: "#f26522", fontSize: "16px" }} />
        <Typography sx={{ fontSize: "14px", color: "#202C4B", fontWeight: "600" }}>
          {isPunchedIn
            ? `Punched In at ${formatTime(todayAttendance?.checkIns?.[todayAttendance.checkIns.length - 1])}`
            : `Not Punched In`}
        </Typography>
      </Box>

      <Button
        onClick={handlePunch}
        disabled={loading || !employeeId}
        sx={{
          width: "100%",
          backgroundColor: isPunchedIn ? "#d32f2f" : "#111827",
          color: "#fff",
          marginTop: "14px",
          borderRadius: "5px",
          textTransform: "none",
          fontSize: "14px",
          fontWeight: "500",
          padding: "0.5rem 0.85rem",
        }}
      >
        {loading ? (
          <CircularProgress size={24} color="inherit" />
        ) : isPunchedIn ? (
          "Punch Out"
        ) : (
          "Punch In"
        )}
      </Button>
    </Box>
  );
};

export default AttendancePunchCard;

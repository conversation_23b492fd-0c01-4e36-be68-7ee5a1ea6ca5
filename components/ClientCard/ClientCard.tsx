"use client";

import type React from "react";
import { useState } from "react";
import {
  Card,
  Avatar,
  Typography,
  Box,
  IconButton,
  LinearProgress,
  Menu,
  MenuItem,
  AvatarGroup,
  Divider,
} from "@mui/material";
import {
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Message as MessageIcon,
  Phone as PhoneIcon,
} from "@mui/icons-material";
// import "./profile-card.module.scss";

interface ProfileCardProps {
  id: string;
  name: string;
  role: string;
  project: string;
  progress: number;
  company: string;
  avatar: string;
  teamMembers?: string[];
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  loading?: boolean; // New prop for loading state
}

const ClientCard: React.FC<ProfileCardProps> = ({
  id,
  name,
  role,
  project,
  progress,
  company,
  avatar,
  teamMembers,
  onEdit,
  onDelete,
  loading = false, // Default to false
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(id);
    }
    handleClose();
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(id);
    }
    handleClose();
  };

  return (
    <Card
      sx={{
        borderRadius: 3,
        padding: 2,
        textAlign: "center",
        minWidth: "205px",
        width: "379px",
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.05)",
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          width: "100%",
          mb: 1,
        }}
      >
        <Box sx={{ width: "28px", height: "28px" }} />
        <Box sx={{ display: "flex", justifyContent: "center", flex: 1 }}>
          <Avatar
            src={avatar || "/placeholder.svg?height=48&width=48"}
            sx={{
              width: 57,
              height: 57,
              border: "1px solid #f26522",
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
            }}
          />
        </Box>

        <IconButton
          sx={{ width: "28px", height: "28px" }}
          onClick={handleMenuClick}
          disabled={loading}
        >
          <MoreVertIcon sx={{ width: "16px", height: "16px" }} />
        </IconButton>
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={handleEdit} disabled={loading}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} /> Edit
        </MenuItem>
        <MenuItem onClick={handleDelete} disabled={loading}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} /> Delete
        </MenuItem>
      </Menu>

      <Typography
        sx={{
          marginTop: 1,
          color: "#262A2A",
          fontWeight: 600,
          fontSize: "14px",
        }}
      >
        {name}
      </Typography>

      <Typography
        sx={{
          background: "#FFEDF6",
          color: "#fd3995",
          borderRadius: 2,
          display: "inline-block",
          padding: "4px 8px",
          fontSize: "10.5px",
          fontWeight: 500,
          marginBottom: 2,
        }}
      >
        {role}
      </Typography>

      <Box sx={{ width: "100%", textAlign: "left", mb: 1.5 }}>
        <Typography
          sx={{
            fontSize: "14px",
            color: "#6b7280",
            mb: 1,
          }}
        >
          Project :{" "}
          <Box component="span" sx={{ color: "#6b7280" }}>
            {project || "N/A"}
          </Box>
        </Typography>
      </Box>

      <Box sx={{ width: "100%", mb: 2 }}>
        <LinearProgress
          variant="determinate"
          value={progress}
          sx={{
            height: 5,
            borderRadius: 5,
            backgroundColor: "#f3f4f6",
            mb: 0.5,
            "& .MuiLinearProgress-bar": {
              background: "linear-gradient(to right, #9333ea, #ec4899)",
            },
          }}
        />
        <Box display={"flex"} justifyContent={"space-between"}>
          <AvatarGroup
            max={3}
            sx={{
              "& .MuiAvatar-root": {
                width: 24,
                height: 24,
                fontSize: "0.75rem",
                border: "2px solid white",
              },
            }}
          >
            {teamMembers?.length ? (
              teamMembers.map((member, index) => (
                <Avatar
                  key={index}
                  src={member || `/placeholder.svg?height=24&width=24`}
                  alt={`Team member ${index + 1}`}
                />
              ))
            ) : (
              <Typography variant="caption" sx={{ color: "#6b7280" }}>
                No team members
              </Typography>
            )}
          </AvatarGroup>
          <Typography
            variant="caption"
            sx={{
              display: "block",
              textAlign: "right",
              fontSize: "12px",
              color: "#6b7280",
            }}
          >
            {progress}%
          </Typography>
        </Box>
      </Box>
      <Divider />

      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          width: "100%",
          pt: 2,
        }}
      >
        <Box sx={{ textAlign: "left" }}>
          <Typography
            variant="caption"
            sx={{
              display: "block",
              color: "#6b7280",
              fontSize: "12px",
              mb: 0.5,
            }}
          >
            Company
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 600,
              color: "#111827",
              fontSize: "14px",
            }}
          >
            {company || "N/A"}
          </Typography>
        </Box>
        <Box sx={{ display: "flex", gap: 1 }}>
          <IconButton
            sx={{
              backgroundColor: "#f3f4f6",
              "&:hover": { backgroundColor: "#e5e7eb" },
            }}
            disabled={loading}
          >
            <MessageIcon sx={{ fontSize: "16px" }} />
          </IconButton>
          <IconButton
            size="small"
            sx={{
              backgroundColor: "#f3f4f6",
              "&:hover": { backgroundColor: "#e5e7eb" },
            }}
            disabled={loading}
          >
            <PhoneIcon sx={{ fontSize: "16px" }} />
          </IconButton>
        </Box>
      </Box>
    </Card>
  );
};

export default ClientCard;
.card {
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    min-width: 205px;
    width: 379px;
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .checkbox {
    width: 18px;
    height: 18px;
    
    input {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
  
  .avatarContainer {
    position: relative;
    
    .avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      border: 3px solid white;
      object-fit: cover;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
  
  .menuContainer {
    position: relative;
    
    .menuButton {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      cursor: pointer;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
    
    .menu {
      position: absolute;
      top: 100%;
      right: 0;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      z-index: 10;
      overflow: hidden;
      
      button {
        display: block;
        width: 100%;
        padding: 8px 16px;
        text-align: left;
        background: none;
        border: none;
        cursor: pointer;
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
    }
  }
  
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .name {
    margin: 8px 0 4px;
    color: rgb(17, 24, 39);
    font-weight: 600;
    font-size: 14px;
  }
  
  .role {
    background-color: #ff6b9d;
    color: white;
    border-radius: 8px;
    display: inline-block;
    padding: 2px 8px;
    font-size: 10.5px;
    font-weight: 500;
    margin-bottom: 16px;
  }
  
  .projectInfo {
    width: 100%;
    margin-bottom: 12px;
    
    .projectLabel {
      font-size: 14px;
      color: #6b7280;
      margin-bottom: 8px;
      text-align: left;
      
      span {
        color: #4b5563;
        font-weight: 500;
      }
    }
  }
  
  .teamMembers {
    display: flex;
    margin-bottom: 8px;
    
    .memberAvatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid white;
      margin-left: -8px;
      
      &:first-child {
        margin-left: 0;
      }
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  
  .progressContainer {
    width: 100%;
    margin-bottom: 16px;
    
    .progressBar {
      height: 5px;
      background-color: #f3f4f6;
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 4px;
      
      .progressFill {
        height: 100%;
        background: linear-gradient(to right, #9333ea, #ec4899);
        border-radius: 10px;
      }
    }
    
    .progressText {
      font-size: 12px;
      color: #6b7280;
      text-align: right;
      display: block;
    }
  }
  
  .companyInfo {
    width: 100%;
    text-align: left;
    margin-bottom: 16px;
    
    .companyLabel {
      font-size: 12px;
      color: #6b7280;
      margin-bottom: 4px;
    }
    
    .companyName {
      font-size: 14px;
      font-weight: 600;
      color: #111827;
    }
  }
  
  .actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    
    .actionButton {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f3f4f6;
      border: none;
      cursor: pointer;
      
      &:hover {
        background-color: #e5e7eb;
      }
    }
  }
  
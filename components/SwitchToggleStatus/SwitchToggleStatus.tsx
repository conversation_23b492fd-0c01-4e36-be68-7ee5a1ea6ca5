import React, { useState } from "react";
import { styled } from "@mui/material/styles";
import Switch from "@mui/material/Switch";
import FormControlLabel from "@mui/material/FormControlLabel";
import StatusConfirmDialog from "../StatusConfirmDialog/StatusConfirmDialog";
import { transform } from "next/dist/build/swc/generated-native";

interface GenderToggleProps {
  isActive: boolean;
  onChange: (newStatus: boolean) => void;
  title?: string;
}

const GenderSwitch = styled(Switch)(({ theme }) => ({
  width: 60,
  height: 20,
  padding: 0,
  display: "flex",
  "& .MuiSwitch-switchBase": {
    padding: 2,
    transition: "transform 0.2s ease-in-out",
    "&.Mui-checked": {
      transform: "translate(40px, 1px)",
      color: "#fff",
      "& + .MuiSwitch-track": {
        backgroundColor: "#03C95A",
        opacity: 1,
        "&:before": {
          opacity: 0,
        },
        "&:after": {
          opacity: 1,
        },
      },
    },
    "&:not(.Mui-checked)": {
      transform: "translate(0px, 1px)",
      "& + .MuiSwitch-track": {
        backgroundColor: "#E70D0D",
        opacity: 1,
        "&:before": {
          opacity: 1,
        },
        "&:after": {
          opacity: 0,
        },
      },
    },
    "&:focus-within": {
      boxShadow: `0 0 0 3px ${theme.palette.primary.light}`,
    },
  },
  "& .MuiSwitch-thumb": {
    width: 14,
    height: 14,
    backgroundColor: "#fff",
    boxShadow: "0 1px 2px rgba(0, 0, 0, 0.3)",
    zIndex: 3,
  },
  "& .MuiSwitch-track": {
    borderRadius: 10,
    backgroundColor: "#E70D0D",
    opacity: 1,
    position: "relative",
    "&:before, &:after": {
      content: '""',
      color: "#000",
      position: "absolute",
      top: "50%",
      transform: "translateY(-49%)",
      fontSize: "9px",
      fontWeight: 600,
      fontFamily: '"Roboto", sans-serif',
      transition: "opacity 0.2s ease-in-out",
    },
    "&:after": {
      content: '"Active"',
      right: 25,
    },
    "&:before": {
      content: '"Inactive"',
      left: 20,
    },
  },
  // Responsive design
  [theme.breakpoints.down("sm")]: {
    width: 50,
    height: 18,
    "& .MuiSwitch-thumb": {
      width: 12,
      height: 12,
    },
    "& .MuiSwitch-switchBase.Mui-checked": {
      transform: "translate(32px, 1px)", // Active state transform for small screens
    },
    "& .MuiSwitch-switchBase:not(.Mui-checked)": {
      transform: "translate(0px, 1px)", // Inactive state transform for small screens
    },
    "& .MuiSwitch-track": {
      borderRadius: 9,
      "&:after": {
        right: 4,
        fontSize: 7,
      },
      "&:before": {
        left: 4,
        fontSize: 7,
      },
    },
  },
}));

export default function StatusToggle({
  isActive,
  onChange,
  title,
}: GenderToggleProps) {
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<boolean | null>(null);

  const handleSwitchClick = (newStatus: boolean) => {
    setPendingStatus(newStatus);
    setConfirmDialogOpen(true);
  };

  const handleConfirm = () => {
    if (pendingStatus !== null) {
      onChange(pendingStatus);
    }
    setConfirmDialogOpen(false);
    setPendingStatus(null);
  };

  const handleCancel = () => {
    setConfirmDialogOpen(false);
    setPendingStatus(null);
  };

  return (
    <>
      <FormControlLabel
        control={
          <GenderSwitch
            checked={isActive}
            onChange={(e) => handleSwitchClick(e.target.checked)}
          />
        }
        label=""
      />
      <StatusConfirmDialog
        open={confirmDialogOpen}
        onClose={handleCancel}
        onConfirm={handleConfirm}
        isActive={isActive}
        title={title}
      />
    </>
  );
}
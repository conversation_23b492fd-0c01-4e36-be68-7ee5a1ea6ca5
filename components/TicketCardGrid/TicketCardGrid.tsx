"use client";

import "./TicketCardGrid.scss";
import {
  <PERSON><PERSON>,
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import ChatBubbleOutlineIcon from "@mui/icons-material/ChatBubbleOutline";
import PhoneOutlinedIcon from "@mui/icons-material/PhoneOutlined";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import { Circle } from "@mui/icons-material";
import { useState } from "react";

export interface TicketCardProps {
  _id: string;
  avatarUrl: string;
  name: string;
  ticketId: string;
  title: string;
  category: string;
  categoryType: string;
  status: "Open" | "On Hold" | "Reopened" | "Closed";
  priority: "High" | "Low" | "Medium";
  assignee: string;
  assigneeAvatar: string;
  departmentName: string;
  onEdit: (_id: string) => void;
  onDelete: (_id: string) => void;
}

const getStatusColor = (status: string) => {
  if (status === "Open")
    return { color: "#FD3995", backgroundColor: "#FFEDF6" };
  if (status === "On Hold")
    return { color: "#F59E0B", backgroundColor: "#FFF4E6" };
  if (status === "Reopened")
    return { color: "#E91E63", backgroundColor: "#FCE7F3" };
  return { color: "#FD3995", backgroundColor: "#FFEDF6" };
};

const getPriorityColor = (priority: string) => {
  if (priority === "High") return "red";
  if (priority === "Medium") return "orange";
  return "teal";
};

const TicketCard: React.FC<TicketCardProps> = ({
  _id,
  avatarUrl,
  name,
  ticketId,
  title,
  category,
  categoryType,
  status,
  priority,
  assignee,
  assigneeAvatar,
  onEdit,
  onDelete,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(_id);
    }
    handleClose();
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(_id);
    }
    handleClose();
  };

  return (
    <Card className="grid-ticket-card">
      <CardContent sx={{ position: "relative", padding: 2 }}>
        <Box display="flex" justifyContent="space-between" alignItems="">
          {/* <CheckBoxOutlineBlankIcon fontSize="small" /> */}
          <Box
            sx={{
              border: "2px solid orange",
              borderRadius: "50%",
              width: 47,
              height: 47,
              mx: "auto",
              overflow: "hidden",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Avatar src={avatarUrl} alt={name} sx={{ width: 64, height: 64 }} />
          </Box>

          <IconButton
            onClick={handleClick}
            sx={{
              display: "flex",
              alignItems: "flex-start",
              maxHeight: "fit-content",
            }}
          >
            <MoreVertIcon sx={{ fontSize: "13px" }} />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
            sx={{
              padding: " 1rem !important",
            }}
          >
            <MenuItem onClick={handleEdit} sx={{ gap: 1, fontSize: "14px" }}>
              <ListItemIcon>
                <EditOutlinedIcon sx={{ fontSize: "18px", color: "#6B7280" }} />
              </ListItemIcon>
              Edit
            </MenuItem>
            <MenuItem
              onClick={handleDelete}
              sx={{ gap: 1, fontSize: "14px", color: "#DC2626" }}
            >
              <ListItemIcon>
                <DeleteOutlineOutlinedIcon
                  sx={{ fontSize: "18px", color: "#DC2626" }}
                />
              </ListItemIcon>
              Delete
            </MenuItem>
          </Menu>
        </Box>

        <Box textAlign="center" mt={1}>
          <Typography
            sx={{
              fontSize: "14px",
              fontWeight: 600,
              color: "#111827",
              cursor: "pointer",
            }}
            mt={1}
          >
            {title}
          </Typography>
          <Chip
            label={ticketId}
            size="small"
            sx={{
              backgroundColor: "#D6E9FF !important",
              color: "#1B84FF !important",
              mt: 0.5,
              fontSize: "0.75em !important;",
              fontWeight: 500,
              padding: "0.25rem 0.45rem",
              letterSpacing: 0.5,
              borderRadius: 4,
            }}
          />
        </Box>

        <Box mt={2}>
          <Box display="flex" justifyContent="space-between">
            <Typography sx={{ fontSize: "14px", color: "#6B7280" }}>
              Category
            </Typography>
            <Typography variant="body2" fontWeight={500}>
              {categoryType}
            </Typography>
          </Box>

          <Box display="flex" justifyContent="space-between" mt={1}>
            <Typography sx={{ color: "#6B7280" }}>Status</Typography>
            <Chip
              label={status}
              size="small"
              icon={<Circle sx={{ fontSize: "6px !important" }} />}
              sx={{
                backgroundColor: getStatusColor(status).backgroundColor,
                color: getStatusColor(status).color,
                height: 20,
                fontSize: "0.75em",
                letterSpacing: "0.5px",
                borderRadius: "4px",
                "& .MuiChip-icon": {
                  color: getStatusColor(status).color,
                },
              }}
            />
          </Box>

          <Box display="flex" justifyContent="space-between" mt={1}>
            <Typography variant="body2" color="text.secondary">
              Priority
            </Typography>
            <Chip
              label={priority}
              size="small"
              sx={{
                borderColor: getPriorityColor(priority),
                color: getPriorityColor(priority),
                height: 22,
                border: "1px solid",
                backgroundColor: "transparent",
                fontSize: "0.75em",
                padding: "0.25rem 0.45rem",
                letterSpacing: "0.5px",
                fontWeight: 500,
                borderRadius: "4px",
              }}
              variant="outlined"
            />
          </Box>

          <Box display="flex" alignItems="center" mt={2}>
            <Box>
              <Typography
                sx={{
                  fontSize: "0.75rem",
                  marginBottom: "0.25rem",
                  color: "#6B7280",
                }}
              >
                Assigned To
              </Typography>
              <Box sx={{ display: "flex" }}>
                <Avatar
                  src={assigneeAvatar}
                  sx={{ width: 20, height: 20, mr: 1 }}
                />
                <Typography variant="body2" fontWeight={500}>
                  {assignee}
                </Typography>
              </Box>
            </Box>
            {/* <Box ml="auto" display="flex" gap={1}>
              <Tooltip title="Chat">
                <IconButton size="small">
                  <ChatBubbleOutlineIcon
                    sx={{ fontSize: "16px", color: "#F26522" }}
                  />
                </IconButton>
              </Tooltip>
              <Tooltip title="Call">
                <IconButton size="small">
                  <PhoneOutlinedIcon sx={{ fontSize: "16px" }} />
                </IconButton>
              </Tooltip>
            </Box> */}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default TicketCard;

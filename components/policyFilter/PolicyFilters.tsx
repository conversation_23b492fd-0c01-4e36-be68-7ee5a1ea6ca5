import React, { useState, useEffect, useRef } from "react";
import { Box, IconButton, Menu, MenuItem, But<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import { Close, KeyboardArrowDown } from "@mui/icons-material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs, { Dayjs } from "dayjs";
import { getUsers } from "@/app/services/users.service";

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface PolicyFiltersProps {
  departments: string[];
  designations: string[];
  selectedDepartment: string;
  setSelectedDepartment: (department: string) => void;
  selectedDesignation: string;
  setSelectedDesignation: (designation: string) => void;
  selectedSortBy: string;
  setSelectedSortBy: (sortBy: string) => void;
  selectedDateRange: string;
  setSelectedDateRange: (range: string) => void;
  setStartDate: (date: string) => void;
  setEndDate: (date: string) => void;
  selectedStatus: string;
  setSelectedStatus: (status: string) => void;
  selectedLeaveType: string;
  setSelectedLeaveType: (leaveType: string) => void;
  selectedPriority: string;
  setSelectedPriority: (priority: string) => void;
  selectedDueDate?: string; // Made optional
  setSelectedDueDate?: (dueDate: string) => void; // Made optional
  setPage: (page: number) => void;
  currentDate: Date;
  showDateRangeFilter?: boolean;
  showDepartmentFilter?: boolean;
  showDesignationFilter?: boolean;
  showStatusFilter?: boolean;
  showSortByFilter?: boolean;
  showLeaveTypeFilter?: boolean;
  showPriorityFilter?: boolean;
  showEmployeeFilter?: boolean;
  showDueDateFilter?: boolean;
  optionalStatusFilter?: boolean;
  selectedEmployee?: string;
  setSelectedEmployee?: (employee: string) => void;
}

export const getDateRange = (range: string, currentDate: Date) => {
  const today = new Date(currentDate);
  today.setHours(0, 0, 0, 0);

  const formatDate = (date: Date) => {
    const offset = date.getTimezoneOffset();
    const adjustedDate = new Date(date.getTime() - offset * 60 * 1000);
    return adjustedDate.toISOString().split("T")[0];
  };

  switch (range) {
    case "Today":
      const todayStr = formatDate(today);
      return {
        startDate: todayStr,
        endDate: todayStr,
        rangeStr: `${todayStr} - ${todayStr}`,
      };
    case "Yesterday":
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = formatDate(yesterday);
      return {
        startDate: yesterdayStr,
        endDate: yesterdayStr,
        rangeStr: `${yesterdayStr} - ${yesterdayStr}`,
      };
    case "Last 7 Days":
      const last7Days = new Date(today);
      last7Days.setDate(last7Days.getDate() - 6);
      const last7DaysStr = formatDate(last7Days);
      const todayFor7Days = formatDate(today);
      return {
        startDate: last7DaysStr,
        endDate: todayFor7Days,
        rangeStr: `${last7DaysStr} - ${todayFor7Days}`,
      };
    case "Last 30 Days":
      const last30Days = new Date(today);
      last30Days.setDate(last30Days.getDate() - 29);
      const last30DaysStr = formatDate(last30Days);
      const todayFor30Days = formatDate(today);
      return {
        startDate: last30DaysStr,
        endDate: todayFor30Days,
        rangeStr: `${last30DaysStr} - ${todayFor30Days}`,
      };
    case "This Month":
      const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
      const thisMonthStartStr = formatDate(thisMonthStart);
      const todayForMonth = formatDate(today);
      return {
        startDate: thisMonthStartStr,
        endDate: todayForMonth,
        rangeStr: `${thisMonthStartStr} - ${todayForMonth}`,
      };
    case "Last Month":
      const lastMonthStart = new Date(
        today.getFullYear(),
        today.getMonth() - 1,
        1
      );
      const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
      const lastMonthStartStr = formatDate(lastMonthStart);
      const lastMonthEndStr = formatDate(lastMonthEnd);
      return {
        startDate: lastMonthStartStr,
        endDate: lastMonthEndStr,
        rangeStr: `${lastMonthStartStr} - ${lastMonthEndStr}`,
      };
    case "Date Range":
    default:
      return { startDate: "", endDate: "", rangeStr: "Date Range" };
  }
};

export default function PolicyFilters({
  departments,
  designations,
  selectedDepartment,
  setSelectedDepartment,
  selectedDesignation,
  setSelectedDesignation,
  selectedSortBy,
  setSelectedSortBy,
  selectedDateRange,
  setSelectedDateRange,
  setStartDate,
  setEndDate,
  selectedStatus,
  setSelectedStatus,
  selectedLeaveType,
  setSelectedLeaveType,
  selectedPriority,
  setSelectedPriority,
  selectedDueDate,
  setSelectedDueDate,
  setPage,
  currentDate,
  showDateRangeFilter = true,
  showDepartmentFilter = true,
  showDesignationFilter = false,
  showStatusFilter = true,
  showSortByFilter = true,
  showLeaveTypeFilter = false,
  showPriorityFilter = false,
  showEmployeeFilter = false,
  showDueDateFilter = false,
  optionalStatusFilter = false,
  selectedEmployee,
  setSelectedEmployee,
}: PolicyFiltersProps) {
  const [departmentAnchorEl, setDepartmentAnchorEl] =
    useState<null | HTMLElement>(null);
  const [designationAnchorEl, setDesignationAnchorEl] =
    useState<null | HTMLElement>(null);
  const [sortByAnchorEl, setSortByAnchorEl] = useState<null | HTMLElement>(
    null
  );
  const [dateRangeAnchorEl, setDateRangeAnchorEl] =
    useState<null | HTMLElement>(null);
  const [statusAnchorEl, setStatusAnchorEl] = useState<null | HTMLElement>(
    null
  );
  const [leaveTypeAnchorEl, setLeaveTypeAnchorEl] =
    useState<null | HTMLElement>(null);
  const [priorityAnchorEl, setPriorityAnchorEl] = useState<null | HTMLElement>(
    null
  );
  const [employeeAnchorEl, setEmployeeAnchorEl] = useState<null | HTMLElement>(
    null
  );
  const [dueDateOpen, setDueDateOpen] = useState(false);
  const dueDateButtonRef = useRef<HTMLButtonElement>(null); // Ref for the Due Date button
  const [employees, setEmployees] = useState<User[]>([]);

  // Fetch employees on mount
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        const response = await getUsers();
        const activeEmployees = response.users.results.filter(
          (user: User) => user.isActive && !user.isDeleted
        );
        setEmployees(activeEmployees);
      } catch (error) {
        console.error("Failed to fetch employees:", error);
      }
    };
    fetchEmployees();
  }, []);

  // Department filter handlers
  const handleDepartmentFilterClick = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    setDepartmentAnchorEl(event.currentTarget);
  };

  const handleDepartmentFilterClose = () => {
    setDepartmentAnchorEl(null);
  };

  const handleDepartmentFilterChange = (department: string) => {
    setSelectedDepartment(department);
    setPage(1);
    handleDepartmentFilterClose();
  };

  // Designation filter handlers
  const handleDesignationFilterClick = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    setDesignationAnchorEl(event.currentTarget);
  };

  const handleDesignationFilterClose = () => {
    setDesignationAnchorEl(null);
  };

  const handleDesignationFilterChange = (designation: string) => {
    setSelectedDesignation(designation);
    setPage(1);
    handleDesignationFilterClose();
  };

  // Sort By filter handlers
  const handleSortByFilterClick = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    setSortByAnchorEl(event.currentTarget);
  };

  const handleSortByFilterClose = () => {
    setSortByAnchorEl(null);
  };

  const handleSortByFilterChange = (sortBy: string) => {
    setSelectedSortBy(sortBy);
    setPage(1);
    handleSortByFilterClose();
  };

  // Date Range filter handlers
  const handleDateRangeFilterClick = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    setDateRangeAnchorEl(event.currentTarget);
  };

  const handleDateRangeFilterClose = () => {
    setDateRangeAnchorEl(null);
  };

  const handleDateRangeFilterChange = (range: string) => {
    const { startDate: newStartDate, endDate: newEndDate, rangeStr } =
      getDateRange(range, currentDate);

    const formattedStartDate = newStartDate
      ? dayjs(newStartDate).format("YYYY-MM-DD")
      : "";
    const formattedEndDate = newEndDate
      ? dayjs(newEndDate).format("YYYY-MM-DD")
      : "";

    setSelectedDateRange(rangeStr);
    setStartDate(formattedStartDate);
    setEndDate(formattedEndDate);
    setPage(1);
    handleDateRangeFilterClose();
  };

  // Status filter handlers
  const handleStatusFilterClick = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    setStatusAnchorEl(event.currentTarget);
  };

  const handleStatusFilterClose = () => {
    setStatusAnchorEl(null);
  };

  const handleStatusFilterChange = (status: string) => {
    setSelectedStatus(status);
    setPage(1);
    handleStatusFilterClose();
  };

  // Leave Type filter handlers
  const handleLeaveTypeFilterClick = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    setLeaveTypeAnchorEl(event.currentTarget);
  };

  const handleLeaveTypeFilterClose = () => {
    setLeaveTypeAnchorEl(null);
  };

  const handleLeaveTypeFilterChange = (leaveType: string) => {
    setSelectedLeaveType(leaveType);
    setPage(1);
    handleLeaveTypeFilterClose();
  };

  // Priority filter handlers
  const handlePriorityFilterClick = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    setPriorityAnchorEl(event.currentTarget);
  };

  const handlePriorityFilterClose = () => {
    setPriorityAnchorEl(null);
  };

  const handlePriorityFilterChange = (priority: string) => {
    setSelectedPriority(priority);
    setPage(1);
    handlePriorityFilterClose();
  };

  // Employee filter handlers
  const handleEmployeeFilterClick = (
    event: React.MouseEvent<HTMLButtonElement>
  ) => {
    setEmployeeAnchorEl(event.currentTarget);
  };

  const handleEmployeeFilterClose = () => {
    setEmployeeAnchorEl(null);
  };

  const handleEmployeeFilterChange = (
    employeeId: string,
    employeeName: string
  ) => {
    if (setSelectedEmployee) {
      setSelectedEmployee(
        employeeId ? `${employeeName}|${employeeId}` : "All Employees"
      );
    }
    setPage(1);
    handleEmployeeFilterClose();
  };

  // Due Date filter handlers
const handleDueDateFilterClick = () => {
    setDueDateOpen(true);
  };

  const handleDueDateFilterClose = () => {
    setDueDateOpen(false);
  };

  const handleDueDateChange = (newValue: dayjs.Dayjs | null) => {
    if (setSelectedDueDate) {
      setSelectedDueDate(newValue ? newValue.format("YYYY-MM-DD") : "");
      setPage(1);
    }
    setDueDateOpen(false);
  };

  const handleClearDueDate = () => {
    if (setSelectedDueDate) {
      setSelectedDueDate("");
      setPage(1);
    }
  };

  const getDisplayEmployeeName = () => {
    if (selectedEmployee === "All Employees" || !selectedEmployee)
      return "All Employees";
    const [name] = selectedEmployee.split("|");
    return name;
  };

  return (
    <Box className="filters" sx={{ display: "flex", gap: 1 }}>
      {/* Date Range Filter Dropdown */}
      {showDateRangeFilter && (
        <>
          <MuiButton
            onClick={handleDateRangeFilterClick}
            sx={{
              textTransform: "none",
              backgroundColor: "#fff",
              border: "1px solid #e5e7eb",
              color: "#111827",
              fontSize: "14px",
              fontWeight: 400,
              borderRadius: "5px",
              padding: "8px 13.6px",
              "&:hover": {
                backgroundColor: "#F26522",
                color: "#FFF",
              },
            }}
          >
            {selectedDateRange} <KeyboardArrowDown />
          </MuiButton>
          <Menu
            anchorEl={dateRangeAnchorEl}
            open={Boolean(dateRangeAnchorEl)}
            onClose={handleDateRangeFilterClose}
          >
            <MenuItem onClick={() => handleDateRangeFilterChange("Date Range")}>
              All Dates
            </MenuItem>
            <MenuItem onClick={() => handleDateRangeFilterChange("Today")}>
              Today
            </MenuItem>
            <MenuItem onClick={() => handleDateRangeFilterChange("Yesterday")}>
              Yesterday
            </MenuItem>
            <MenuItem
              onClick={() => handleDateRangeFilterChange("Last 7 Days")}
            >
              Last 7 Days
            </MenuItem>
            <MenuItem
              onClick={() => handleDateRangeFilterChange("Last 30 Days")}
            >
              Last 30 Days
            </MenuItem>
            <MenuItem onClick={() => handleDateRangeFilterChange("This Month")}>
              This Month
            </MenuItem>
            <MenuItem onClick={() => handleDateRangeFilterChange("Last Month")}>
              Last Month
            </MenuItem>
          </Menu>
        </>
      )}

      {/* Department Filter Dropdown */}
      {showDepartmentFilter && (
        <>
          <MuiButton
            onClick={handleDepartmentFilterClick}
            sx={{
              textTransform: "none",
              backgroundColor: "#fff",
              border: "1px solid #e5e7eb",
              color: "#111827",
              fontSize: "14px",
              fontWeight: 400,
              borderRadius: "5px",
              padding: "8px 13.6px",
              "&:hover": {
                backgroundColor: "#F26522",
                color: "#FFF",
              },
            }}
          >
            {selectedDepartment} <KeyboardArrowDown />
          </MuiButton>
          <Menu
            anchorEl={departmentAnchorEl}
            open={Boolean(departmentAnchorEl)}
            onClose={handleDepartmentFilterClose}
            PaperProps={{
              style: {
                maxHeight: "250px",
                overflowY: "auto",
              },
            }}
          >
            <MenuItem
              onClick={() => handleDepartmentFilterChange("Department")}
            >
              All Departments
            </MenuItem>
            {departments.map((dept, index) => (
              <MenuItem
                key={index}
                onClick={() => handleDepartmentFilterChange(dept)}
              >
                {dept}
              </MenuItem>
            ))}
          </Menu>
        </>
      )}

      {/* Designation Filter Dropdown */}
      {showDesignationFilter && (
        <>
          <MuiButton
            onClick={handleDesignationFilterClick}
            sx={{
              textTransform: "none",
              backgroundColor: "#fff",
              border: "1px solid #e5e7eb",
              color: "#111827",
              fontSize: "14px",
              fontWeight: 400,
              borderRadius: "5px",
              padding: "8px 13.6px",
              "&:hover": {
                backgroundColor: "#F26522",
                color: "#FFF",
              },
            }}
          >
            {selectedDesignation} <KeyboardArrowDown />
          </MuiButton>
          <Menu
            anchorEl={designationAnchorEl}
            open={Boolean(designationAnchorEl)}
            onClose={handleDesignationFilterClose}
            PaperProps={{
              style: {
                maxHeight: "250px",
                overflowY: "auto",
              },
            }}
          >
            <MenuItem
              onClick={() => handleDesignationFilterChange("Designation")}
            >
              All Designations
            </MenuItem>
            {designations?.map((designation, index) => (
              <MenuItem
                key={index}
                onClick={() => handleDesignationFilterChange(designation)}
              >
                {designation}
              </MenuItem>
            ))}
          </Menu>
        </>
      )}

      {/* Status Filter Dropdown */}
      {showStatusFilter && (
        <>
          <MuiButton
            onClick={handleStatusFilterClick}
            sx={{
              textTransform: "none",
              backgroundColor: "#fff",
              border: "1px solid #e5e7eb",
              color: "#111827",
              fontSize: "14px",
              fontWeight: 400,
              borderRadius: "5px",
              padding: "8px 13.6px",
              "&:hover": {
                backgroundColor: "#F26522",
                color: "#FFF",
              },
            }}
          >
            {selectedStatus} <KeyboardArrowDown />
          </MuiButton>
          <Menu
            anchorEl={statusAnchorEl}
            open={Boolean(statusAnchorEl)}
            onClose={handleStatusFilterClose}
          >
            <MenuItem onClick={() => handleStatusFilterChange("Select Status")}>
              All Statuses
            </MenuItem>
            {optionalStatusFilter
              ? [
                  <MenuItem
                    key="Open"
                    onClick={() => handleStatusFilterChange("Open")}
                  >
                    Open
                  </MenuItem>,
                  <MenuItem
                    key="Closed"
                    onClick={() => handleStatusFilterChange("Closed")}
                  >
                    Closed
                  </MenuItem>,
                  <MenuItem
                    key="Reopened"
                    onClick={() => handleStatusFilterChange("Reopened")}
                  >
                    Reopened
                  </MenuItem>,
                  <MenuItem
                    key="OnHold"
                    onClick={() => handleStatusFilterChange("OnHold")}
                  >
                    OnHold
                  </MenuItem>,
                ]
              : [
                  <MenuItem
                    key="Active"
                    onClick={() => handleStatusFilterChange("Active")}
                  >
                    Active
                  </MenuItem>,
                  <MenuItem
                    key="Inactive"
                    onClick={() => handleStatusFilterChange("Inactive")}
                  >
                    Inactive
                  </MenuItem>,
                ]}
          </Menu>
        </>
      )}

      {/* Priority Filter Dropdown */}
      {showPriorityFilter && (
        <>
          <MuiButton
            onClick={handlePriorityFilterClick}
            sx={{
              textTransform: "none",
              backgroundColor: "#fff",
              border: "1px solid #e5e7eb",
              color: "#111827",
              fontSize: "14px",
              fontWeight: 400,
              borderRadius: "5px",
              padding: "8px 13.6px",
              "&:hover": {
                backgroundColor: "#F26522",
                color: "#FFF",
              },
            }}
          >
            {selectedPriority} <KeyboardArrowDown />
          </MuiButton>
          <Menu
            anchorEl={priorityAnchorEl}
            open={Boolean(priorityAnchorEl)}
            onClose={handlePriorityFilterClose}
          >
            <MenuItem onClick={() => handlePriorityFilterChange("Priority")}>
              All Priorities
            </MenuItem>
            <MenuItem onClick={() => handlePriorityFilterChange("High")}>
              High
            </MenuItem>
            <MenuItem onClick={() => handlePriorityFilterChange("Medium")}>
              Medium
            </MenuItem>
            <MenuItem onClick={() => handlePriorityFilterChange("Low")}>
              Low
            </MenuItem>
          </Menu>
        </>
      )}

      {/* Leave Type Filter Dropdown */}
      {showLeaveTypeFilter && (
        <>
          <MuiButton
            onClick={handleLeaveTypeFilterClick}
            sx={{
              textTransform: "none",
              backgroundColor: "#fff",
              border: "1px solid #e5e7eb",
              color: "#111827",
              fontSize: "14px",
              fontWeight: 400,
              borderRadius: "5px",
              padding: "8px 13.6px",
              "&:hover": {
                backgroundColor: "#F26522",
                color: "#FFF",
              },
            }}
          >
            {selectedLeaveType} <KeyboardArrowDown />
          </MuiButton>
          <Menu
            anchorEl={leaveTypeAnchorEl}
            open={Boolean(leaveTypeAnchorEl)}
            onClose={handleLeaveTypeFilterClose}
          >
            <MenuItem onClick={() => handleLeaveTypeFilterChange("Leave Type")}>
              All Leave Types
            </MenuItem>
            <MenuItem onClick={() => handleLeaveTypeFilterChange("Medical")}>
              Medical
            </MenuItem>
            <MenuItem onClick={() => handleLeaveTypeFilterChange("Casual")}>
              Casual
            </MenuItem>
            <MenuItem onClick={() => handleLeaveTypeFilterChange("Annual")}>
              Annual
            </MenuItem>
            <MenuItem onClick={() => handleLeaveTypeFilterChange("Others")}>
              Others
            </MenuItem>
          </Menu>
        </>
      )}

      {/* Employee Filter Dropdown */}
      {showEmployeeFilter && (
        <>
          <MuiButton
            onClick={handleEmployeeFilterClick}
            sx={{
              textTransform: "none",
              backgroundColor: "#fff",
              border: "1px solid #e5e7eb",
              color: "#111827",
              fontSize: "14px",
              fontWeight: 400,
              borderRadius: "5px",
              padding: "8px 13.6px",
              "&:hover": {
                backgroundColor: "#F26522",
                color: "#FFF",
              },
            }}
          >
            {getDisplayEmployeeName()} <KeyboardArrowDown />
          </MuiButton>
          <Menu
            anchorEl={employeeAnchorEl}
            open={Boolean(employeeAnchorEl)}
            onClose={handleEmployeeFilterClose}
            PaperProps={{
              style: {
                maxHeight: "250px",
                overflowY: "auto",
              },
            }}
          >
            <MenuItem
              onClick={() => handleEmployeeFilterChange("", "All Employees")}
            >
              All Employees
            </MenuItem>
            {employees.map((employee) => (
              <MenuItem
                key={employee._id}
                onClick={() =>
                  handleEmployeeFilterChange(
                    employee._id,
                    `${employee.firstName} ${employee.lastName}`
                  )
                }
              >
                {employee.firstName} {employee.lastName}
              </MenuItem>
            ))}
          </Menu>
        </>
      )}

      {/* Due Date Filter */}
      {showDueDateFilter && (
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <MuiButton
            ref={dueDateButtonRef} // Attach ref to the button
            onClick={handleDueDateFilterClick}
            sx={{
              textTransform: "none",
              backgroundColor: "#fff",
              border: "1px solid #e5e7eb",
              color: "#111827",
              fontSize: "14px",
              fontWeight: 400,
              borderRadius: "5px",
              padding: "8px 13.6px",
              "&:hover": {
                backgroundColor: "#F26522",
                color: "#FFF",
              },
            }}
          >
            {selectedDueDate || "Due Date"} <KeyboardArrowDown />
          </MuiButton>
          {selectedDueDate && (
            <IconButton size="small" onClick={handleClearDueDate} sx={{ ml: 1 }}>
              <Close fontSize="small" />
            </IconButton>
          )}
          <DatePicker
            open={dueDateOpen}
            onClose={handleDueDateFilterClose}
            onOpen={() => setDueDateOpen(true)}
            value={selectedDueDate ? dayjs(selectedDueDate) : null}
            onChange={handleDueDateChange}
            slotProps={{
              popper: {
                anchorEl: dueDateButtonRef.current, // Anchor to the button
                placement: "bottom-start", // Open below the button, aligned to the start
              },
              textField: {
                sx: { display: "none" }, // Hide the text field
              },
            }}
          />
        </Box>
      )}

      {/* Sort By Filter Dropdown */}
      {showSortByFilter && (
        <>
          <MuiButton
            onClick={handleSortByFilterClick}
            sx={{
              textTransform: "none",
              backgroundColor: "#fff",
              border: "1px solid #e5e7eb",
              color: "#111827",
              fontSize: "14px",
              fontWeight: 400,
              borderRadius: "5px",
              padding: "8px 13.6px",
              "&:hover": {
                backgroundColor: "#F26522",
                color: "#FFF",
              },
            }}
          >
            {selectedSortBy} <KeyboardArrowDown />
          </MuiButton>
          <Menu
            anchorEl={sortByAnchorEl}
            open={Boolean(sortByAnchorEl)}
            onClose={handleSortByFilterClose}
          >
            <MenuItem onClick={() => handleSortByFilterChange("Sort By")}>
              Default
            </MenuItem>
            <MenuItem onClick={() => handleSortByFilterChange("Ascending")}>
              Ascending
            </MenuItem>
            <MenuItem onClick={() => handleSortByFilterChange("Descending")}>
              Descending
            </MenuItem>
          </Menu>
        </>
      )}
    </Box>
  );
}

import { useRef, useMemo } from "react";
import JoditEditor from "jodit-react";

interface TextEditorProps {
  value: string;
  onChange: (newValue: string) => void;
}
const TextEditor = ({ value, onChange }: TextEditorProps) => {
  const editor = useRef(null);

  const config = useMemo(
    () => ({
      readonly: false,
      height: 180,
      toolbar: true,
      buttons: [
        "bold",
        "italic",
        "underline",
        "|",
        "ul",
        "ol",
        "|",
        "font",
        "fontsize",
        "brush",
        "link",
        "image",
        "symbols",
        "|",
        "align",
        "undo",
        "redo",
        "eraser",
        "source",
      ],
      ********************: false,
      showCharsCounter: false,
      showWordsCounter: false,
      uploader: {
        insertImageAsBase64URI: true,
      },
      spellcheck: true,
      toolbarButtonSize: "small" as "small" | "large" | "tiny" | "xsmall" | "middle",
    }),
    []
  );

  return (
    <JoditEditor
      ref={editor}
      value={value}
      config={config}
      tabIndex={1}
      onBlur={(newContent) => onChange(newContent)}
      // style={{ marginBottom: "20px" }}
    />
  );
};
export default TextEditor;
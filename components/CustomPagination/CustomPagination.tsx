import React from "react";
import {
  Box,
  Typography,
  Select,
  MenuItem,
  FormControl,
  IconButton,
  SelectChangeEvent,
} from "@mui/material";
import {
  useGridApiContext,
  useGridSelector,
  gridPageCountSelector,
  gridPageSelector,
  gridPageSizeSelector,
  GridPagination,
} from "@mui/x-data-grid";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import "./CustomPagination.scss";

export function CustomPagination() {
  const apiRef = useGridApiContext();
  const page = useGridSelector(apiRef, gridPageSelector);
  const pageCount = useGridSelector(apiRef, gridPageCountSelector);
  const pageSize = useGridSelector(apiRef, gridPageSizeSelector);

  const handlePageSizeChange = (event: SelectChangeEvent<number>) => {
    const newPageSize = event.target.value as number;
    apiRef.current.setPageSize(newPageSize);
  };

  const handlePageChange = (newPage: number) => {
    apiRef.current.setPage(newPage);
  };

  // Calculate the range of entries being displayed
  const startEntry = page * pageSize + 1;
  const endEntry = Math.min(
    (page + 1) * pageSize,
    apiRef.current.getRowsCount()
  );
  const totalEntries = apiRef.current.getRowsCount();

  return (
    <Box className="custom-pagination-container">
      {/* Keep the original pagination for functionality but hide it visually */}
      <Box sx={{ display: "none" }}>
        <GridPagination />
      </Box>

      {/* Custom pagination UI */}
      <Box className="custom-pagination">
        <Box className="row-per-page">
          <Typography>Row Per Page</Typography>
          <FormControl size="small">
            <Select
              value={pageSize}
              onChange={handlePageSizeChange}
              displayEmpty
              className="page-size-select"
            >
              {[10, 25, 50, 100].map((size) => (
                <MenuItem key={size} value={size}>
                  {size}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Typography className="entries-label">Entries</Typography>
        </Box>

        <Box className="pagination-controls">
          <Typography className="showing-entries">
            Showing {startEntry} - {endEntry} of {totalEntries} entries
          </Typography>

          <Box className="pagination-buttons">
            <IconButton
              onClick={() => handlePageChange(page - 1)}
              disabled={page === 0}
              className="pagination-button"
            >
              <KeyboardArrowLeftIcon />
            </IconButton>

            <Box className="page-number">
              <Typography>{page + 1}</Typography>
            </Box>

            <IconButton
              onClick={() => handlePageChange(page + 1)}
              disabled={page >= pageCount - 1}
              className="pagination-button"
            >
              <KeyboardArrowRightIcon />
            </IconButton>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

export default CustomPagination;

.custom-pagination-container {
  width: 100%;

  .custom-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    width: 100%;

    .row-per-page {
      display: flex;
      align-items: center;
      gap: 8px;

      Typography {
        font-size: 14px;
        color: #6b7280;
      }

      .page-size-select {
        min-width: 70px;
        height: 32px;

        .MuiSelect-select {
          padding: 6px 12px;
          font-size: 14px;
        }
      }

      .entries-label {
        font-size: 14px;
        color: #6b7280;
      }
    }

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: 16px;

      .showing-entries {
        font-size: 14px;
        color: #6b7280;
      }

      .pagination-buttons {
        display: flex;
        align-items: center;

        .pagination-button {
          color: #6b7280;
          padding: 4px;

          &.Mui-disabled {
            color: #d1d5db;
          }
        }

        .page-number {
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 32px;
          height: 32px;
          background-color: #f26522;
          color: white;
          border-radius: 4px;
          margin: 0 4px;

          Typography {
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// Override MUI DataGrid footer styles
.MuiDataGrid-root {
  .MuiDataGrid-footerContainer {
    min-height: 56px;
    border-top: 1px solid #e5e7eb;
    padding: 0;

    // Hide the default pagination controls but keep the container
    .MuiTablePagination-root {
      .MuiTablePagination-toolbar {
        padding: 0;
        min-height: 0;

        .MuiTablePagination-spacer,
        .MuiTablePagination-selectLabel,
        .MuiTablePagination-select,
        .MuiTablePagination-selectIcon,
        .MuiTablePagination-displayedRows,
        .MuiTablePagination-actions {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .custom-pagination-container {
    .custom-pagination {
      flex-direction: column;
      gap: 16px;

      .row-per-page {
        width: 100%;
        justify-content: flex-start;
      }

      .pagination-controls {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}

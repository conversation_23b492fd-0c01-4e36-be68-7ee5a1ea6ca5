"use client";

import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Divider,
} from "@mui/material";
import html2pdf from "html2pdf.js";
import "./Payslip.scss";
import Image from "next/image";
import { getCompany } from "@/app/services/setting.service";
import { getUserById } from "@/app/services/users.service";
import { getPayrollItemForEmployee } from "@/app/services/payroll.service";
import Loader from "@/components/Loader/Loader";

interface PayrollItem {
  type: string;
  name: string;
  amount: number | null;
  payrollItemId?: string;
}

const Payslip = ({
  onDownloadRef,
  userId,
}: {
  onDownloadRef?: (downloadFn: () => void) => void;
  userId: string;
}): React.ReactElement => {
  // Fetch company NAME AND ADDRESS from the server
  const [companyName, setCompanyName] = useState<string>("");
  const [companyAddress, setCompanyAddress] = useState<string>("");
  const [companyEmail, setCompanyEmail] = useState<string>("");
  const [companyPhone, setCompanyPhone] = useState<string>("");

  // Fetch user data from the server
  const [userName, setUserName] = useState<string>("");
  const [userEmail, setUserEmail] = useState<string>("");
  const [userPhone, setUserPhone] = useState<string>("");
  const [userDesignation, setUserDesignation] = useState<string>("");
  const [employeeId, setEmployeeId] = useState<string>("");
  const [payrollItems, setPayrollItems] = useState<PayrollItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPayrollItems = async () => {
      if (!userId) {
        setError("No employee ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await getPayrollItemForEmployee(userId);
        if (response.success) {
          const { payroll } = response;
          // Convert the payroll object structure into array of items
          const items: PayrollItem[] = [
            ...Object.entries(payroll.earnings || {}).map(([name, amount]) => ({
              type: "Earnings",
              name,
              amount: amount as number,
            })),
            ...Object.entries(payroll.additions || {}).map(
              ([name, amount]) => ({
                type: "Additions",
                name,
                amount: amount as number,
              })
            ),
            ...Object.entries(payroll.deductions || {}).map(
              ([name, amount]) => ({
                type: "Deductions",
                name,
                amount: amount as number,
              })
            ),
            ...Object.entries(payroll.overtime || {}).map(([name, amount]) => ({
              type: "Overtime",
              name,
              amount: amount as number,
            })),
          ];
          setPayrollItems(items);
        } else {
          setError("Failed to fetch payroll data");
        }
      } catch (error) {
        setError("Failed to fetch payroll data");
        console.error("Error fetching payroll items:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchPayrollItems();
  }, [userId]);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!userId) {
        setError("No employee ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await getUserById(userId);
        if (response.success) {
          const { user } = response;
          setUserName(`${user.firstName} ${user.lastName}`);
          setUserEmail(user.email);
          setUserPhone(user.phone);
          setUserDesignation(user.designationId?.designationName || "");
          setEmployeeId(user.employeeId);
        } else {
          setError("Failed to fetch user data");
        }
      } catch (error) {
        setError("Failed to fetch user data");
        console.error("Error fetching user data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [userId]);

  useEffect(() => {
    const fetchCompanyData = async () => {
      try {
        setLoading(true);
        const response = await getCompany();
        if (response.success) {
          const { company } = response;
          setCompanyName(company.companyName);
          setCompanyAddress(company.companyAddress);
          setCompanyEmail(company.companyEmail);
          setCompanyPhone(company.companyPhone);
        } else {
          setError("Failed to fetch company data");
        }
      } catch (error) {
        setError("Failed to fetch company data");
        console.error("Error fetching company data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCompanyData();
  }, []);

  const payslipRef = useRef<HTMLDivElement>(null);
  const [companyLogo, setCompanyLogo] = useState<string>(
    "/assets/Aadvik-noBG.png"
  );

  const handleDownloadPDF = () => {
    if (payslipRef.current) {
      const opt = {
        margin: 1,
        filename: "Payslip.pdf",
        image: { type: "jpeg", quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: "in", format: "letter", orientation: "portrait" },
      };

      html2pdf().set(opt).from(payslipRef.current).save();
    }
  };

  // Pass the download function to parent
  React.useEffect(() => {
    if (onDownloadRef) {
      onDownloadRef(handleDownloadPDF);
    }
  }, [onDownloadRef]);

  const calculateTotalEarnings = () => {
    return payrollItems
      .filter((item) => item.type === "Earnings" || item.type === "Additions")
      .reduce((total, item) => total + (item.amount || 0), 0);
  };

  const calculateTotalDeductions = () => {
    return payrollItems
      .filter((item) => item.type === "Deductions")
      .reduce((total, item) => total + (item.amount || 0), 0);
  };

  const getEarnings = () => {
    return payrollItems.filter(
      (item) => item.type === "Earnings" || item.type === "Additions"
    );
  };

  const getDeductions = () => {
    return payrollItems.filter((item) => item.type === "Deductions");
  };

  const getOvertime = () => {
    return payrollItems.filter((item) => item.type === "Overtime");
  };

  const getAdditions = () => {
    return payrollItems.filter((item) => item.type === "Additions");
  };

  const calculateTotalOvertime = () => {
    return payrollItems
      .filter((item) => item.type === "Overtime")
      .reduce((total, item) => total + (item.amount || 0), 0);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
    }).format(amount);
  };

  const getCurrentMonth = () => {
    return new Date().toLocaleString("default", {
      month: "long",
      year: "numeric",
    });
  };

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
        <Loader loading={true} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ mt: 4, textAlign: "center" }}>
        <Typography variant="h6" color="error">
          {error}
        </Typography>
      </Box>
    );
  }

  // Helper function to create a table section
  const renderTable = (title: string, items: PayrollItem[]) => {
    if (items.length === 0) return null;

    const total = items.reduce((sum, item) => sum + (item.amount || 0), 0);

    return (
      <Grid item xs={12} md={6}>
        <Typography fontWeight="bold" className="table-title">
          {title}
        </Typography>
        <Table size="small" sx={{ border: "1px solid #e0e0e0" }}>
          <TableBody>
            {items.map((item, index) => (
              <TableRow key={item.payrollItemId || index}>
                <TableCell sx={{ borderBottom: "1px solid #e0e0e0" }}>
                  {item.name}
                </TableCell>
                <TableCell
                  sx={{ borderBottom: "1px solid #e0e0e0" }}
                  align="right"
                >
                  {formatCurrency(item.amount || 0)}
                </TableCell>
              </TableRow>
            ))}
            <TableRow>
              <TableCell>
                <b>Total {title}</b>
              </TableCell>
              <TableCell align="right">
                <b>{formatCurrency(total)}</b>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </Grid>
    );
  };

  return (
    <Paper elevation={3} className="payslip-paper" ref={payslipRef}>
      <Grid container justifyContent="space-between" alignItems="center">
        <Box>
          <Typography
            variant="h5"
            fontWeight="bold"
            color="error"
            sx={{ mb: 0.5 }}
          >
            <Image
              src={companyLogo}
              alt="Company Logo"
              width={100}
              height={100}
            />
          </Typography>
          <Typography className="company-address">{companyAddress}</Typography>
        </Box>
        <Box textAlign="right">
          <Typography variant="subtitle2">
            Employee ID: <b style={{ color: "#F26522" }}>{employeeId}</b>
          </Typography>
          <Typography
            variant="subtitle2"
            sx={{ fontSize: "14px", color: "#6B7280" }}
          >
            Salary Month:{" "}
            <b style={{ color: "#212529" }}>{getCurrentMonth()}</b>
          </Typography>
        </Box>
      </Grid>

      <Divider sx={{ my: 3 }} />

      <Grid container mb={2} className="from-to">
        <Box className="left-from">
          <Typography
            fontWeight="bold"
            sx={{ fontSize: "14px", marginBottom: ".5rem" }}
          >
            From
          </Typography>
          <Typography
            fontWeight="bold"
            sx={{ fontSize: "18px", marginBottom: ".5rem" }}
          >
            {companyName}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontSize: "14px",
              color: "#6B7280",
              marginBottom: "0.5rem",
            }}
          >
            {companyAddress}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontSize: "14px",
              color: "#6B7280",
              marginBottom: "0.5rem",
            }}
          >
            Email: <b style={{ color: "#212529" }}>{companyEmail}</b>
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontSize: "14px", color: "#6B7280" }}
          >
            Phone: <b style={{ color: "#212529" }}>{companyPhone}</b>
          </Typography>
        </Box>

        <Box className="right-to">
          <Typography
            fontWeight="bold"
            sx={{ fontSize: "14px", marginBottom: "0.5rem" }}
          >
            To
          </Typography>
          <Typography
            fontWeight="bold"
            sx={{ fontSize: "18px", marginBottom: "0.5rem" }}
          >
            {userName}
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "#6B7280", marginBottom: "0.5rem" }}
          >
            {userDesignation}
          </Typography>
          <Typography
            variant="body2"
            sx={{ fontSize: "14px", color: "#6B7280", marginBottom: "0.5rem" }}
          >
            Email: <b style={{ color: "#212529" }}>{userEmail}</b>
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "#6B7280", marginBottom: "0.5rem" }}
          >
            Phone: <b>{userPhone}</b>
          </Typography>
        </Box>
      </Grid>

      <Typography align="center" className="payslip-title">
        Payslip for the month of {getCurrentMonth()}
      </Typography>

      <Grid container spacing={4}>
        {renderTable("Earnings", getEarnings())}
        {renderTable("Deductions", getDeductions())}
        {renderTable("Overtime", getOvertime())}
        {renderTable("Additions", getAdditions())}
      </Grid>

      <Typography
        variant="subtitle1"
        mt={4}
        sx={{ color: "#6B7280", fontSize: "14px" }}
      >
        Net Salary:{" "}
        <b style={{ color: "#111827", fontSize: "14px", fontWeight: "500" }}>
          {formatCurrency(
            calculateTotalEarnings() +
              calculateTotalOvertime() -
              calculateTotalDeductions()
          )}
          {" (" +
            new Intl.NumberFormat("en-IN", { style: "decimal" })
              .format(
                calculateTotalEarnings() +
                  calculateTotalOvertime() -
                  calculateTotalDeductions()
              )
              .toString()
              .split(".")[0] +
            " Rupees Only)"}
        </b>
      </Typography>
    </Paper>
  );
};

export default Payslip;

import { Box, TextField, InputAdornment } from "@mui/material";
import { GridToolbarExport } from "@mui/x-data-grid";
import SearchIcon from "@mui/icons-material/Search";
import React, { useEffect, useRef } from "react";

export const QuickSearchToolbar = React.memo(
  ({
    onSearchChange,
    searchQuery,
  }: {
    onSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
    searchQuery: string;
  }) => {
    const inputRef = useRef<HTMLInputElement>(null);

    // Restore focus after re-renders
    useEffect(() => {
      if (inputRef.current && document.activeElement !== inputRef.current) {
        inputRef.current.focus();
      }
    }, [searchQuery]);

    return (
      <Box
        sx={{
          p: 0.5,
          pb: 0,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "10px 20px",
        }}
      >
        <TextField
          inputRef={inputRef}
          variant="outlined"
          placeholder="Search"
          size="small"
          value={searchQuery}
          onChange={onSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
        <Box
          className="grid-export"
          sx={{ display: "flex", alignItems: "center" }}
        >
          <GridToolbarExport printOptions={{ disableToolbarButton: true }} />
        </Box>
      </Box>
    );
  }
);
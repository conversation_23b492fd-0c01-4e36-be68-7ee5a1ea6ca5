"use client";

import React, { useState, useEffect } from "react";
import {
  Box,
  Card,
  Typography,
  Avatar,
  Chip,
  Button,
  CircularProgress,
} from "@mui/material";
import ReplyIcon from "@mui/icons-material/Reply";
import CommentIcon from "@mui/icons-material/Comment";
import { Circle, Event, Download } from "@mui/icons-material";
import CommentAdd from "@/app/(tickets)/ticket-details/CommentAdd";
import { toast } from "react-toastify";
import { saveAs } from "file-saver";
import { getTicketById } from "@/app/services/tickets/tickets.service";
import "./ticket-details-content.scss";

interface Employee {
  _id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  email: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Comment {
  _id: string;
  message: string;
  commentBy: Employee;
  cc: Employee[];
  commentDate: string;
  media: string[];
  createdAt: string;
  updatedAt: string;
}

interface TicketCategory {
  _id: string;
  category: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Department {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Ticket {
  _id: string;
  empId: Employee;
  category: TicketCategory | null;
  departmentId: Department | null;
  subject: string;
  description: string;
  priority: "High" | "Low" | "Medium";
  status: "Open" | "OnHold" | "Reopened" | "Closed";
  comments: Comment[];
  isActive: boolean;
  isDeleted: boolean;
  ticketId: string;
  createdAt: string;
  updatedAt: string;
  cc: Employee[];
  assignTo?: Employee;
}

interface TicketDetailsContentProps {
  ticket: Ticket;
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case "High":
      return "#E70D0D";
    case "Medium":
      return "#ffca28";
    case "Low":
      return "#00e676";
    default:
      return "#ccc";
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "Open":
      return "#FD3995";
    case "OnHold":
      return "#FFC107";
    case "Reopened":
      return "#AB47BC";
    case "Closed":
      return "#03C95A";
    default:
      return "#ccc";
  }
};

const getFileNameFromUrl = (url: string): string => {
  try {
    const urlParts = url.split("/");
    return urlParts[urlParts.length - 1] || "Unknown File";
  } catch (error) {
    console.error("Error parsing file name from URL:", error);
    return "Unknown File";
  }
};

const getRelativeTime = (dateString: string): string => {
  const now = new Date();
  const updatedDate = new Date(dateString);
  const diffInMs = now.getTime() - updatedDate.getTime();

  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  const diffInWeeks = Math.floor(diffInMs / (1000 * 60 * 60 * 24 * 7));
  const diffInMonths = Math.floor(diffInMs / (1000 * 60 * 60 * 24 * 30));
  const diffInYears = Math.floor(diffInMs / (1000 * 60 * 60 * 24 * 365));

  if (diffInMinutes < 1) {
    return "Updated just now";
  } else if (diffInMinutes < 60) {
    return `Updated ${diffInMinutes} minute${diffInMinutes === 1 ? "" : "s"} ago`;
  } else if (diffInHours < 24) {
    return `Updated ${diffInHours} hour${diffInHours === 1 ? "" : "s"} ago`;
  } else if (diffInDays < 7) {
    return `Updated ${diffInDays} day${diffInDays === 1 ? "" : "s"} ago`;
  } else if (diffInWeeks < 4) {
    return `Updated ${diffInWeeks} week${diffInWeeks === 1 ? "" : "s"} ago`;
  } else if (diffInMonths < 12) {
    return `Updated ${diffInMonths} month${diffInMonths === 1 ? "" : "s"} ago`;
  } else {
    return `Updated ${diffInYears} year${diffInYears === 1 ? "" : "s"} ago`;
  }
};

const handleDownload = async (url: string, fileName: string) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch file: ${response.statusText}`);
    }
    const blob = await response.blob();
    saveAs(blob, fileName);
  } catch (error) {
    console.error("Error downloading file:", error);
    toast.error("Failed to download file");
  }
};

const TicketDetailsContent: React.FC<TicketDetailsContentProps> = ({
  ticket: initialTicket,
}) => {
  const [ticket, setTicket] = useState<Ticket>(initialTicket);
  const [openCommentDialog, setOpenCommentDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // New loading state

  // Fetch updated ticket data
  const fetchTicket = async () => {
    setIsLoading(true); // Start loading
    try {
      const response = await getTicketById(ticket._id);
      setTicket(response.ticket);
    } catch (error) {
      console.error("Error fetching ticket:", error);
    } finally {
      setIsLoading(false); // Stop loading
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchTicket();
  }, []);

  const handleOpenCommentDialog = () => {
    setOpenCommentDialog(true);
  };

  const handleCloseCommentDialog = () => {
    setOpenCommentDialog(false);
  };

  const handleAddComment = async (values: {
    message: string;
    commentBy: string;
    cc: string[];
    commentDate: string;
    media: string[];
  }) => {
    setIsLoading(true); // Start loading
    try {
      await fetchTicket();
    } catch (error) {
      console.error("Error refreshing ticket after comment:", error);
      toast.error("Failed to refresh comments");
    } finally {
      setIsLoading(false); // Stop loading
    }
  };

  return (
    <>
      <Card className="ticket-details-card-box">
        <Box
          className="ticket-details-header-box"
          sx={{
            padding: "1rem 1.25rem 1rem",
            borderColor: "divider",
            bgcolor: "background.paper",
          }}
        >
          <Typography variant="h6" color="primary" fontWeight="bold">
            {ticket.category?.category || "-"}
          </Typography>

          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Chip
              icon={<Circle sx={{ fontSize: "6px !important",color: "#FFF !important" }} />}
              label={ticket.priority}
              size="small"
              sx={{
                fontWeight: "600",
                letterSpacing: "0.5px",
                borderRadius: "4px",
                backgroundColor: `${getPriorityColor(ticket.priority)} !important`,
                border: `1px solid ${getPriorityColor(ticket.priority)} !important`,
                color: "#FFF",
                marginRight: "1rem",
                "& .MuiChip-label": {
                  padding: "0.25rem 0.45rem",
                },
              }}
            />
          </Box>
        </Box>
        <Box className="ticket-details-content-box">
          <Chip
            label={ticket.ticketId}
            sx={{
              background: "#1B84FF",
              color: "#FFF",
              fontWeight: "600",
              letterSpacing: "0.5px",
              fontSize: "0.75em",
              "& .MuiChip-label": {
                padding: "0.25rem 0.45rem",
              },
            }}
            size="small"
          />
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Box sx={{ display: "flex" }}>
              <Typography
                variant="subtitle1"
                fontWeight="bold"
                sx={{
                  marginRight: ".5rem",
                  fontWeight: "600",
                  fontSize: "16px",
                  color: "#202C4B",
                }}
              >
                {ticket.subject}
              </Typography>
              <Chip
                icon={<Circle sx={{ fontSize: "6px !important" }} />}
                label={ticket.status}
                size="small"
                sx={{
                  fontWeight: "600",
                  letterSpacing: "0.5px",
                  borderRadius: "4px",
                  backgroundColor: "transparent",
                  border: `1px solid ${getStatusColor(ticket.status)}`,
                  color: getStatusColor(ticket.status),
                }}
              />
            </Box>

            <Button
              className="reply-btn"
              onClick={handleOpenCommentDialog}
              disabled={isLoading}
            >
              Post a Reply
            </Button>
          </Box>
          <Typography
            variant="body2"
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              color: "#6B7280",
              marginBottom: "1rem",
              paddingBottom: "1rem",
              borderBottom: "1px solid #E5E7EB",
            }}
          >
            <Avatar
              className="avatar"
              src={ticket.assignTo?.avatar || "/avatar-placeholder.png"}
            />
            Assigned to:{" "}
            <span style={{ color: "black" }}>
              {ticket.assignTo
                ? `${ticket.assignTo.firstName} ${ticket.assignTo.lastName}`
                : "N/A"}
            </span>{" "}
            <Event sx={{ fontSize: "14px", color: "#6B7280" }} />
            {getRelativeTime(ticket.updatedAt)}{" "}
            <CommentIcon sx={{ fontSize: "14px", color: "#6B7280" }} />
            {ticket.comments.length} Comments
          </Typography>

          <Typography
            variant="body1"
            sx={{ marginBottom: "1rem", color: "#6B7280", fontSize: "14px" }}
            dangerouslySetInnerHTML={{ __html: ticket.description }}
          />

          {ticket.comments.map((comment, index) => (
            <Box
              key={comment._id}
              mt={3}
              p={2}
              borderBottom={1}
              borderColor="grey.300"
              borderRadius={2}
            >
              <Box display="flex" alignItems="center" gap={2}>
                <Avatar
                  src={comment?.commentBy?.avatar}
                  sx={{ width: 45, height: 45 }}
                />
                <Box>
                  <Typography
                    sx={{
                      fontSize: "14px",
                      fontWeight: "500",
                      marginBottom: ".25rem",
                      color: "#202C4B",
                    }}
                  >
                    {`${comment?.commentBy?.firstName} ${comment?.commentBy?.lastName}`}
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{ fontSize: "14px", color: "#6B7280" }}
                  >
                    <Event sx={{ fontSize: "14px", verticalAlign: "middle" }} />
                    {new Date(comment?.commentDate).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
              <Typography
                mt={1}
                sx={{
                  fontSize: "14px",
                  color: "#6B7280",
                  marginBottom: "1rem",
                }}
              >
                {comment.message}
              </Typography>
              {comment.media && comment.media.length > 0 && (
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {comment.media.map((file, index) => {
                    const fileName = getFileNameFromUrl(file);
                    return (
                      <Chip
                        key={index}
                        label={fileName}
                        deleteIcon={<Download />}
                        onDelete={() => handleDownload(file, fileName)}
                        size="small"
                        sx={{
                          fontWeight: 400,
                          fontSize: "0.75em",
                          letterSpacing: 0.5,
                          borderRadius: 4,
                          color: "#6B7280",
                          backgroundColor: "#F8F9FA !important",
                          border: "1px solid #F8F9FA !important",
                          "& .MuiChip-deleteIcon": {
                            color: "#6B7280",
                          },
                        }}
                      />
                    );
                  })}
                </Box>
              )}
              {index === ticket.comments.length - 1 && ( // Only show Reply for the last comment
                <Box mt={1} display="flex" alignItems="center" gap={1}>
                  <Typography
                    sx={{
                      color: "#F26522 !important",
                      fontWeight: "500",
                      marginRight: "1rem",
                      fontSize: "14px",
                      cursor: "pointer",
                    }}
                    onClick={handleOpenCommentDialog}
                  >
                    <ReplyIcon fontSize="small" /> Reply
                  </Typography>
                </Box>
              )}
            </Box>
          ))}
        </Box>
      </Card>

      <CommentAdd
        open={openCommentDialog}
        onClose={handleCloseCommentDialog}
        onSubmit={handleAddComment}
        ticketId={ticket._id}
      />
    </>
  );
};

export default TicketDetailsContent;

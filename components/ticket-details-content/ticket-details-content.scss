.ticket-details-card-box {
    margin-bottom: 1.5rem;
    background-color: #FFF;
    transition: all 0.5s ease-in-out;
    position: relative;
    border-radius: 5px;
    border: 1px solid #E5E7EB;
    box-shadow: 0px 1px 1px 1px rgba(198, 198, 198, 0.2);
    color: inherit;
    font-size: 14px;

    .ticket-details-header-box {
        border-color: #E5E7EB;
        position: relative;
        background: transparent;
        padding: 1rem 1.25rem 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #E5E7EB;
    }

    .ticket-details-content-box {
        padding: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .ticket-status-chip {
        padding: 0.25rem 0.45rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        border-radius: 4px;
        border: 1px solid #FD3995 !important;
        color: #FD3995 !important;
        background-color: transparent !important;
        margin-left: .25rem;
    }

    .reply-btn {
        background-color: #F26522;
        border: 1px solid #F26522;
        color: #FFF;
        border-radius: 5px;
        padding: 0.5rem 0.85rem;
        font-size: 14px;
        transition: all 0.5s;
        font-weight: 500;
        text-transform: none;
        max-height: fit-content !important;
    }

    .avatar {
        width: 1.25rem;
        height: 1.25rem;
        line-height: 1.25rem;
        font-size: 0.65rem;
    }

    ul {
        border-bottom: 1px solid #E5E7EB !important;
        padding-bottom: 1rem !important;
        margin-bottom: 1rem;

        li {
            list-style-type: disc !important;
            margin-left: 1.5rem;
            margin-bottom: 1rem !important;
            display: list-item;
            text-align: -webkit-match-parent;
            unicode-bidi: isolate;
            padding: 0 !important;
            color: #6B7280;
        }
    }
}

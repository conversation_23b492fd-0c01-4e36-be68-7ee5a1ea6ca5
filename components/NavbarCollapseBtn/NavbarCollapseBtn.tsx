import React from 'react';
import Button from '@mui/material/Button';
import KeyboardDoubleArrowUp from '@mui/icons-material/KeyboardDoubleArrowUp';

const CollapseButton: React.FC = () => {
    return (
        <Button
            variant="contained"
            sx={{
                minHeight: "39px",
                maxHeight: "39px",
                maxWidth: "39px",
                minWidth: "39px",
                backgroundColor: "transparent",
                color: "#111827",
                border: "1px solid #E5E7EB",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
            }}
        >
            <KeyboardDoubleArrowUp sx={{ width: "14px", height: "14px" }} />
        </Button>
    );
};

export default CollapseButton;
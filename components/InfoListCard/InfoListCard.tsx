// components/InfoListCard.tsx
import {
  Avatar,
  Box,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
} from "@mui/material";
import "./InfoListCard.scss";

type InfoItem = {
  label: string;
  count: number;
  avatarUrl?: string;
};

type InfoListCardProps = {
  title: string;
  items: InfoItem[];
  showAvatar?: boolean;
};

const InfoListCard = ({
  title,
  items,
  showAvatar = false,
}: InfoListCardProps) => {
  const isAgentList = title === "Support Agents";

  return (
    <Card className="card">
      <Box className="card-header">
        <Typography>{title}</Typography>
      </Box>
      <CardContent className="card-content">
        <List disablePadding>
          {items.map((item, index) => (
            <Box key={index}>
              <ListItem>
                {showAvatar && (
                  <ListItemAvatar sx={{ minWidth: 30 }}>
                    <Avatar
                      className="list-item-avatar"
                      src={item.avatarUrl}
                      alt={item.label}
                    />
                  </ListItemAvatar>
                )}
                <ListItemText
                  primary={
                    <Typography
                      className="list-item-text"
                      sx={
                        isAgentList
                          ? { color: "#6B7280 !important" }
                          : undefined
                      }
                    >
                      {item.label}
                    </Typography>
                  }
                />
                <ListItemSecondaryAction>
                  <Box className="list-item-count">{item.count}</Box>
                </ListItemSecondaryAction>
              </ListItem>
              {index < items.length - 1 && <Divider />}
            </Box>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

export default InfoListCard;

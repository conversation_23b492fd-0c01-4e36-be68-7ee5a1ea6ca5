.card {
    margin-bottom: 24px;

    .card-header {
        border-color: #E5E7EB;
        position: relative;
        background: transparent;
        padding: 1rem 1.25rem 1rem;
        border-bottom: 1px solid #E5E7EB;

        p {
            font-size: 18px;
            font-weight: 600;
            color: #202C4B;

        }
    }

    .card-content {
        padding: 0px !important;

        li {
            .MuiListItem-root {
                padding: 1rem;
            }

            .list-item-text {
                color: #111827;
                cursor: pointer;
                font-size: 14px;
            }

            .list-item-avatar {
                width: 1.25rem;
                height: 1.25rem;
                line-height: 1.25rem;
                font-size: 0.65rem;
            }

            .list-item-count {
                background-color: #212529 !important;
                border: 1px solid #212529 !important;
                color: #FFF;
                font-size: 10px;
                font-weight: 500;
                padding: 0px 5px;
                line-height: 18px;
                border-radius: 50%
            }
        }

    }
}
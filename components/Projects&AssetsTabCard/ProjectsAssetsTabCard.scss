.project-assets-container {
  // Tab styling
  .Mui-selected {
    color: var(--primary-color) !important;
  }

  .MuiTabs-indicator {
    background-color: var(--primary-color);
  }

  button {
    text-transform: none;
  }

  .tab-panel {
    .tab-content {
      .card {
        .card-header {
          padding-bottom: 16px;

          a {
            margin-right: 0.5rem !important;

            img {
              border-radius: 50%;
            }
          }

          .heading {
            h6 {
              margin-bottom: 0.25rem !important;
              font-size: 14px;
              font-weight: 600;

              a {
                color: #111827;
                cursor: pointer;
                text-decoration: none;
                -webkit-transition: all 0.5sease;
                -ms-transition: all 0.5s ease;
                transition: all 0.5sease;
              }
            }

            .sub-heading {
              p {
                font-size: 0.8125rem;
              }
            }
          }
        }

        hr {
          margin-bottom: 16px;
        }

        .card-content {
          .left-box {
            width: 100%;
            padding: 0px 12px;

            span {
              margin-bottom: 0.25rem !important;
              display: block !important;
              color: #6b7280;
            }

            p {
              margin-bottom: 0;
              color: #212529 !important;
            }
          }

          .right-box {
            width: 100%;
            padding: 0px 12px;

            span {
              margin-bottom: 0.25rem !important;
              display: block !important;
              color: #6b7280;
            }

            a {
              display: flex;

              img {
                line-height: 1.5rem;
                font-size: 0.65rem;
                border-radius: 50%;
              }
            }
          }
        }
      }

      .assets-card {
        margin-bottom: var(--spacing-lg);
        background-color: #fff;
        transition: all 0.3s ease-in-out;
        position: relative;
        border-radius: var(--border-radius-md);
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        color: inherit;
        align-items: center;

        .card-header {
          a {
            margin-right: var(--spacing-sm) !important;

            img {
              border-radius: 50%;
            }
          }

          .heading {
            h6 {
              margin-bottom: var(--spacing-xs) !important;
              font-size: 14px;
              font-weight: 600;

              a {
                color: var(--text-primary);
                cursor: pointer;
                text-decoration: none;
                transition: all 0.3s ease;

                &:hover {
                  color: var(--primary-color);
                }
              }
            }

            .sub-heading {
              span {
                color: var(--primary-color);
                font-size: 14px;
              }

              p {
                font-size: 14px;
              }
            }
          }
        }

        .left-box {
          width: 25%;
          padding: 0px 12px;

          span {
            margin-bottom: 0.25rem !important;
            display: block !important;
            color: #6b7280;
          }

          a {
            display: flex;

            img {
              line-height: 1.5rem;
              font-size: 0.65rem;
              border-radius: 50%;
              margin-right: 0.5rem;
            }
          }
        }

        .right-box {
          width: 8.33333333%;
          padding: 0px 12px;

          span {
            margin-bottom: 0.25rem !important;
            display: block !important;
            color: #6b7280;
          }

          a {
            display: flex;

            img {
              line-height: 1.5rem;
              font-size: 0.65rem;
              border-radius: 50%;
            }
          }
        }
      }
    }
  }
}

.assets-info-dialog {
  .MuiPaper-root {
    max-width: 800px;
  }

  .assets-card {
    margin-bottom: var(--spacing-md);
    background-color: #fff;
    transition: all 0.3s ease-in-out;
    position: relative;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    color: inherit;
    align-items: center;

    .card-header {
      a {
        margin-right: var(--spacing-sm) !important;

        img {
          border-radius: 50%;
        }
      }

      .heading {
        h6 {
          margin-bottom: var(--spacing-xs) !important;
          font-size: 14px;
          font-weight: 600;

          a {
            color: var(--text-primary);
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;

            &:hover {
              color: var(--primary-color);
            }
          }
        }

        .sub-heading {
          span {
            color: var(--primary-color);
            font-size: 14px;
          }

          p {
            font-size: 14px;
          }
        }
      }
    }
  }

  .specifications {
    span {
      font-size: 14px;
      line-height: 1.5;
      color: var(--text-secondary);
    }

    p {
      font-size: 14px;
      line-height: 1.5;
      color: var(--text-primary);
    }
  }
}

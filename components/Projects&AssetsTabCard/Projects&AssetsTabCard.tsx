"use client";
import * as React from "react";
import { useState, useEffect } from "react";
import "./ProjectsAssetsTabCard.scss";
import {
  Box,
  Dialog,
  DialogTitle,
  DialogActions,
  Button,
  TextField,
  DialogContent,
  IconButton,
  Card,
  Typography,
  Menu,
  MenuItem,
  CircularProgress,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import Tab from "@mui/material/Tab";
import TabContext from "@mui/lab/TabContext";
import TabList from "@mui/lab/TabList";
import TabPanel from "@mui/lab/TabPanel";
import Link from "next/link";
// import Image from "next/image";
import {
  addAssetsIssue,
  // updateAssetsIssue,
} from "@/app/services/users.service";
import { toast } from "react-toastify";
import Image from "next/image";
import AddAssetForm from "@/components/Projects&AssetsTabCard/AddAssetForm";
import { getUsers, getUserById } from "@/app/services/users.service";
import { postAssets, getAssets } from "@/app/services/assets.service";
import useUploadMedia from "@/app/hooks/useUploadMedia";
import Loader from "../Loader/Loader";
import { Close } from "@mui/icons-material";
import useRoleAuth from "@/app/hooks/useRoleAuth";

// Update the Asset interface
interface Asset {
  _id: string;
  userId: string;
  assetImage?: string[]; // Changed from string to string[]
  brand: string;
  type: string;
  serialNumber: string;
  category: string;
  assignedDate: string;
  assignedBy: string;
  cost: string;
  vendor: string;
  warrentyTo: string;
  location: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

// Define UserData type
interface UserData {
  _id: string;
  firstName?: string;
  lastName?: string;
  designationId: string;
}
interface AssetsIssue {
  assetId: string;
  issueDescription: string;
  // Add other fields as required by your API
}

// Define UserDataResponse type
interface UserDataResponse {
  user: {
    assets: Asset[];
  };
}

// Props interface
interface ProjectsAssetsTabCardProps {
  userData: {
    user: {
      assets: Asset[];
      _id: string;
      firstName?: string;
      lastName?: string;
    };
  };
  refreshUserData: () => Promise<void>;
}

export default function ProjectsAssetsTabCard({
  userData,
  refreshUserData,
}: ProjectsAssetsTabCardProps) {
  // Add this near other state declarations
  const { roles } = useRoleAuth({
    allowedRoles: ["Admin", "HR", "Manager", "Employee", "SuperAdmin"],
    redirectTo: "/unauthorized",
  });
  const isEmployee = roles.includes("Employee");

  const [value, setValue] = React.useState("1");
  const [viewInfoDialogOpen, setViewInfoDialogOpen] = React.useState(false);
  const [raiseIssueDialogOpen, setRaiseIssueDialogOpen] = React.useState(false);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [selectedAsset, setSelectedAsset] = React.useState<Asset | null>(null);
  const [isAddAssetOpen, setIsAddAssetOpen] = React.useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [allUsers, setAllUsers] = useState<UserData[]>([]);
  // Remove the assetsRefreshKey state since we're using refreshUserData
  // const [assetsRefreshKey, setAssetsRefreshKey] = useState(0);

  // Remove the assets state and use userData directly
  const assets = userData?.user?.assets || [];

  // Add this with your other state declarations
  const { uploadMedia } = useUploadMedia();

  // Add useEffect to fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await getUsers();
        if (response && response.users?.results) {
          setAllUsers(response.users.results);
        }
      } catch (error) {
        console.error("Failed to fetch users:", error);
      }
    };

    fetchUsers();
  }, []);

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setValue(newValue);
  };

  const handleMenuOpen = (
    event: React.MouseEvent<HTMLElement>,
    asset: Asset
  ) => {
    setAnchorEl(event.currentTarget);
    setSelectedAsset(asset);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleViewInfoClick = () => {
    handleMenuClose();
    setViewInfoDialogOpen(true);
  };

  const handleRaiseIssueClick = () => {
    handleMenuClose();
    setRaiseIssueDialogOpen(true);
  };

  const handleViewInfoDialogClose = () => {
    setViewInfoDialogOpen(false);
    setSelectedAsset(null);
  };

  const handleRaiseIssueDialogClose = () => {
    setRaiseIssueDialogOpen(false);
    setSelectedAsset(null);
  };

  const handleRaiseIssueSubmit = async () => {
    if (!selectedAsset) {
      return;
    }

    const payload: AssetsIssue = {
      assetId: selectedAsset._id,
      issueDescription: "",
    };

    try {
      const response = await addAssetsIssue(payload);
      console.log("Issue added successfully:", response);
      handleRaiseIssueDialogClose();
      // toast.success("Issue raised successfully");
    } catch (err) {
      console.error("Error raising issue:", err);
      // toast.error("Failed to raise issue. Please try again.");
    }
  };

  const handleAddAssetClick = () => {
    if (isEmployee) return;
    setIsAddAssetOpen(true);
  };

  const handleAddAssetClose = () => {
    setIsAddAssetOpen(false);
  };

  const handleImageUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
    setFieldValue: (field: string, value: any) => void
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      setIsLoading(true);
      try {
        const { preview } = await uploadMedia(file, "assets", 1, setIsLoading);
        setFieldValue("assetImage", preview);
      } catch (error) {
        console.error("Image upload failed:", error);
        // toast.error("Failed to upload image");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleAddAssetSubmit = async (formData: FormData) => {
    if (isEmployee) return;
    try {
      setIsLoading(true);
      const response = await postAssets(formData);
      if (response) {
        toast.success("Asset added successfully");
        // Call refreshUserData to get the updated assets
        await refreshUserData();
        handleAddAssetClose();
      }
    } catch (error) {
      console.error("Error adding asset:", error);
      // toast.error("Failed to add asset");
    } finally {
      setIsLoading(false);
    }
  };

  // Add a useEffect to fetch assets when the refresh key changes
  useEffect(() => {
    const fetchAssets = async () => {
      if (userData?.user?._id) {
        try {
          setIsLoading(true);
          const response = await getUserById(userData.user._id);
          if (response?.user?.assets) {
            // Update the assets directly from the response
            // No need to modify the userData state since we're using assets directly
          }
        } catch (error) {
          console.error("Error fetching assets:", error);
          // toast.error("Failed to fetch assets");
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchAssets();
  }, [userData?.user?._id]);

  // Add a useEffect to log assets when they change
  useEffect(() => {
    console.log("Current assets:", assets);
  }, [assets]);

  return (
    <Box
      className="project-assets-container"
      sx={{ width: "100%", typography: "body1" }}
    >
      <TabContext value={value}>
        <Box
          sx={{
            borderBottom: 1,
            borderColor: "divider",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <TabList onChange={handleChange} aria-label="lab API tabs example">
            <Tab label="Assets" value="1" />
          </TabList>

          {/* Only show Add Assets button for non-employees */}
        </Box>

        <TabPanel className="tab-panel" value="1">
          <Box className="tab-content">
            {isLoading ? (
              <Loader loading={isLoading} />
            ) : assets.length > 0 ? (
              assets.map((asset) => (
                <Card
                  key={asset._id}
                  className="assets-card"
                  sx={{ flex: 1, padding: "20px", marginBottom: "16px" }}
                >
                  <Box
                    className="card-header"
                    sx={{ display: "flex", justifyContent: "space-between" }}
                  >
                    <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                      <Link href="">
                        <Image
                          src={
                            asset.assetImage?.[0] || "/assets/product-05.webp"
                          }
                          alt={`${asset.brand} ${asset.type}`}
                          width={36}
                          height={36}
                        />
                      </Link>
                      <Box className="heading">
                        <Typography variant="h6">
                          <Link href="">{`${asset.brand} ${asset.type} - #${asset.serialNumber}`}</Link>
                        </Typography>
                        <Box
                          className="sub-heading"
                          sx={{ display: "flex", gap: 2 }}
                        >
                          <Typography component="span">
                            {asset.category}
                          </Typography>
                          <Typography component="span">
                            Assigned:{" "}
                            {new Date(asset.assignedDate).toLocaleDateString()}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>

                    <IconButton
                      onClick={(event) => handleMenuOpen(event, asset)}
                      size="small"
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </Box>

                  <Box sx={{ mt: 2 }}>
                    {/* <Typography variant="body2" color="text.secondary">
                      Cost: {asset.cost}
                    </Typography> */}
                    <Typography variant="body2" color="text.secondary">
                      Warranty Until:{" "}
                      {new Date(asset.warrentyTo).toLocaleDateString()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Location: {asset.location}
                    </Typography>
                    {!asset.isActive && (
                      <Typography variant="body2" sx={{ color: "error.main" }}>
                        Inactive Asset
                      </Typography>
                    )}
                  </Box>
                </Card>
              ))
            ) : (
              <Box sx={{ textAlign: "center", p: 3 }}>
                <Typography variant="body1" color="text.secondary">
                  No assets found
                </Typography>
              </Box>
            )}
          </Box>
        </TabPanel>
      </TabContext>

      {/* Assets Info Dialog */}
      <Dialog
        className="assets-info-dialog"
        open={viewInfoDialogOpen}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleViewInfoDialogClose();
          }
        }}
        disableEscapeKeyDown
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              fontSize: "16px",
              color: "rgb(32, 44, 75);",
            }}
          >
            Assets Information
          </Typography>

          <IconButton
            edge="end"
            color="inherit"
            onClick={handleViewInfoDialogClose}
            aria-label="close"
            sx={{
              backgroundColor: "#6b7280",
              backgroundImage: "none",
              borderRadius: "50%",
              color: "#fff",
              height: "20px",
              width: "20px",
              margin: 0,
              padding: 0,
              "&:hover": {
                backgroundColor: "#d55a1d",
              },
              "& .MuiSvgIcon-root": {
                fontSize: "14px",
              },
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {selectedAsset ? (
            <>
              <Card
                className="assets-card"
                sx={{ flex: 1, padding: "20px", display: "flex" }}
              >
                <Box className="card-header" sx={{ display: "flex" }}>
                  <Link href="#">
                    <Image
                      src={
                        selectedAsset.assetImage?.[0] ||
                        "/assets/product-05.webp"
                      }
                      alt={`${selectedAsset.brand} ${selectedAsset.type}`}
                      width={36}
                      height={36}
                    />
                  </Link>
                  <Box className="heading">
                    <Typography variant="h6">
                      <Link href="">{`${selectedAsset.brand} ${selectedAsset.type} - #${selectedAsset.serialNumber}`}</Link>
                    </Typography>
                    <Box className="sub-heading" sx={{ display: "flex" }}>
                      <Typography component="span">
                        {selectedAsset.category}
                      </Typography>
                      <Typography>{`Assigned on ${new Date(selectedAsset.assignedDate).toLocaleString()}`}</Typography>
                    </Box>
                  </Box>
                </Box>
              </Card>
              <Box
                className="specifications"
                sx={{ display: "flex", flexWrap: "wrap" }}
              >
                <Box sx={{ width: "50%" }}>
                  <Typography component="span">Type</Typography>
                  <Typography>{selectedAsset.type}</Typography>
                </Box>
                <Box sx={{ width: "50%" }}>
                  <Typography component="span">Brand</Typography>
                  <Typography>{selectedAsset.brand}</Typography>
                </Box>
                <Box sx={{ width: "50%" }}>
                  <Typography component="span">Cost</Typography>
                  <Typography>{selectedAsset.cost}</Typography>
                </Box>
                <Box sx={{ width: "50%" }}>
                  <Typography component="span">Vendor</Typography>
                  <Typography>{selectedAsset.vendor}</Typography>
                </Box>
                <Box sx={{ width: "50%" }}>
                  <Typography component="span">Warranty Until</Typography>
                  <Typography>
                    {new Date(selectedAsset.warrentyTo).toLocaleDateString()}
                  </Typography>
                </Box>
                <Box sx={{ width: "50%" }}>
                  <Typography component="span">Location</Typography>
                  <Typography>{selectedAsset.location}</Typography>
                </Box>
              </Box>

              <Box>
                <Typography
                  sx={{ fontSize: "0.8125rem", marginBottom: ".5rem" }}
                >
                  Asset Images
                </Typography>

                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                  {selectedAsset.assetImage &&
                  Array.isArray(selectedAsset.assetImage) ? (
                    selectedAsset.assetImage.map((imageUrl, index) => (
                      <Image
                        key={index}
                        src={imageUrl || "/assets/default-image.webp"}
                        alt={`${selectedAsset.brand} ${selectedAsset.type} - Image ${index + 1}`}
                        width={100}
                        height={100}
                        style={{
                          borderRadius: "8px",
                          objectFit: "cover",
                        }}
                      />
                    ))
                  ) : (
                    <Image
                      src="/assets/default-image.webp"
                      alt="Default Asset Image"
                      width={100}
                      height={100}
                      style={{
                        borderRadius: "8px",
                        objectFit: "cover",
                      }}
                    />
                  )}
                </Box>
              </Box>
            </>
          ) : (
            <Typography>No asset selected.</Typography>
          )}
        </DialogContent>
      </Dialog>

      {/* Raise Issue Dialog */}
      <Dialog
        className="raise-issue-dialog"
        open={raiseIssueDialogOpen}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleRaiseIssueDialogClose();
          }
        }}
        disableEscapeKeyDown
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          Raise Issue for{" "}
          {selectedAsset
            ? `${selectedAsset.brand} ${selectedAsset.type}`
            : "Asset"}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Description"
            type="text"
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            value=""
            onChange={() => {}}
          />
        </DialogContent>
        <DialogActions>
          <Button
            sx={{
              fontWeight: "400",
              fontSize: "14px",
              color: "#111827",
              border: "1px solid #E5E7EB",
              borderRadius: "5px",
              textTransform: "none",
              padding: "8px 13.6px",
            }}
            onClick={handleRaiseIssueDialogClose}
            color="primary"
          >
            Cancel
          </Button>
          <Button
            sx={{
              backgroundColor: "#F26522",
              border: "1px solid #F26522",
              color: "#FFF",
              borderRadius: "5px",
              padding: "0.5rem 0.85rem",
              fontSize: "14px",
              transition: "all 0.5s",
              fontWeight: 500,
              textTransform: "none",
            }}
            onClick={handleRaiseIssueSubmit} // Call API on submit
            color="primary"
            variant="contained"
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>

      <AddAssetForm
        open={isAddAssetOpen}
        onClose={handleAddAssetClose}
        onSubmit={handleAddAssetSubmit}
        selectedEmployee={{
          _id: userData.user._id,
          firstName: userData.user.firstName || "",
          lastName: userData.user.lastName || "",
        }}
        userData={allUsers.map((user) => ({
          ...user,
          firstName: user.firstName || "",
          lastName: user.lastName || "",
        }))} // Keep this for compatibility, though we won't use it for selection
      />

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem onClick={handleViewInfoClick}>View Info</MenuItem>
        <MenuItem onClick={handleRaiseIssueClick}>Raise Issue</MenuItem>
      </Menu>
    </Box>
  );
}

import React, { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import "./AddAssetsForm.scss";
import { toast } from "react-toastify";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  MenuItem,
  Typography,
  CircularProgress,
  IconButton,
} from "@mui/material";
import { Cancel, Close, CloudUpload } from "@mui/icons-material";
import { postAssets } from "@/app/services/assets.service";
import useUploadMedia from "@/app/hooks/useUploadMedia";
import Image from "next/image";

interface AddAssetFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: FormData) => void;
  userData: {
    _id: string;
    firstName: string;
    lastName: string;
    designationId?: string;
  }[];
  selectedEmployee: {
    _id: string;
    firstName: string;
    lastName: string;
  };
}

interface AssetFormValues {
  userId: string;
  type: string;
  brand: string;
  category: string;
  serialNumber: string;
  cost: string;
  vendor: string;
  /* assignedBy: string; */
  assignedDate: string;
  warrentyTo: string;
  location: string;
  assetImage: string[]; // Only string array, remove File[] option
  assetImages?: FileList | null; // Add this for temporary file handling
}

// Update the validation schema
const validationSchema = Yup.object({
  userId: Yup.string().required("Employee is required"),
  type: Yup.string().required("Type is required"),
  brand: Yup.string().required("Brand is required"),
  category: Yup.string().required("Category is required"),
  serialNumber: Yup.string().required("Serial number is required"),
  cost: Yup.string().required("Cost is required"),
  vendor: Yup.string().required("Vendor is required"),
  assignedDate: Yup.date()
    .required("Assigned date is required")
    .test(
      "within-range",
      "Assigned date must be within 7 days from today",
      (value) => {
        if (!value) return false;
        const date = new Date(value);
        const today = new Date();
        const sevenDaysAgo = new Date(today.setDate(today.getDate() - 7));
        const sevenDaysFuture = new Date(today.setDate(today.getDate() + 14)); // +14 because we moved back 7 days in previous line
        return date >= sevenDaysAgo && date <= sevenDaysFuture;
      }
    )
    .typeError("Please enter a valid date"),
  warrentyTo: Yup.date()
    .required("Warranty date is required")
    .min(new Date(), "Warranty date must be in the future")
    .test(
      "after-assigned",
      "Warranty date must be after assigned date",
      function (value) {
        const { assignedDate } = this.parent;
        if (!value || !assignedDate) return false;
        return new Date(value) > new Date(assignedDate);
      }
    )
    .typeError("Please enter a valid date"),
  location: Yup.string().required("Location is required"),
  assetImage: Yup.array()
    .of(Yup.string())
    .min(1, "At least one image is required")
    .required("Asset image is required"),
});

const AddAssetForm: React.FC<AddAssetFormProps> = ({
  open,
  onClose,
  onSubmit,
  userData,
  selectedEmployee, // Add this prop
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { uploadMedia } = useUploadMedia();

  const initialValues: AssetFormValues = {
    userId: selectedEmployee._id, // Set initial value to selected employee's ID
    type: "",
    brand: "",
    category: "",
    serialNumber: "",
    cost: "",
    vendor: "",
    /* assignedBy: "Admin", */
    assignedDate: "",
    warrentyTo: "",
    location: "",
    assetImage: [], // Initialize as empty array
  };

  // Update the handleImageUpload function to ensure strings are not double-quoted
  const handleImageUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
    setFieldValue: (field: string, value: any) => void,
    values: AssetFormValues
  ) => {
    const files = e.target.files;
    if (files) {
      setIsLoading(true);
      try {
        const uploadPromises = Array.from(files).map(async (file) => {
          const { preview } = await uploadMedia(
            file,
            "assets",
            1,
            setIsLoading
          );
          // Remove any extra quotes from the preview URL
          return preview.replace(/^"|"$/g, "");
        });

        const uploadedPreviews = await Promise.all(uploadPromises);

        // Update form values with all uploaded images
        setFieldValue("assetImage", [
          ...values.assetImage,
          ...uploadedPreviews,
        ]);
        setFieldValue("assetImages", files);
      } catch (error) {
        console.error("Image upload failed:", error);
        toast.error("Failed to upload images");
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <Dialog
      className="dialog"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="md"
      fullWidth
    >
      <DialogTitle className="dialog-title">
        <Typography
          variant="h6"
          sx={{ fontWeight: 600, fontSize: "20px", color: "#111827" }}
        >
          Add New Asset
        </Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          aria-label="close"
          sx={{
            backgroundColor: "#6b7280",
            backgroundImage: "none",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": {
              backgroundColor: "#d55a1d",
            },
            "& .MuiSvgIcon-root": {
              fontSize: "14px",
            },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={async (values, { setSubmitting, resetForm }) => {
          try {
            const formData = new FormData();

            // Format dates to ISO string
            const formattedValues = {
              ...values,
              assignedDate: new Date(values.assignedDate).toISOString(),
              warrentyTo: new Date(values.warrentyTo).toISOString(),
            };

            // Append all text fields except file related fields
            Object.keys(formattedValues).forEach((key) => {
              if (key !== "assetImage" && key !== "assetImages") {
                const value =
                  formattedValues[key as keyof typeof formattedValues];
                if (value !== undefined && value !== null) {
                  formData.append(key, value.toString());
                }
              }
            });

            // Handle asset images
            values.assetImage.forEach((imageUrl, index) => {
              // Remove any extra quotes and append to formData
              const cleanUrl = imageUrl.replace(/^"|"$/g, "");
              formData.append(`assetImage[${index}]`, cleanUrl);
            });

            const response = await postAssets(formData);

            if (response) {
              resetForm();
              onClose();
              // toast.success("Asset added successfully");
            }
          } catch (error) {
            console.error("Error adding asset:", error);
            // toast.error("Failed to add asset");
          } finally {
            setSubmitting(false);
          }
        }}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          isSubmitting,
          setFieldValue,
        }) => (
          <Form>
            <DialogContent
              className="dialog-content"
              dividers
              sx={{
                overflow: "auto",
                maxHeight: "80vh", // Adjust height to ensure buttons are visible
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 2,
                }}
              >
                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label> Brand</label>
                    <TextField
                      fullWidth
                      name="brand"
                      value={values.brand}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.brand && Boolean(errors.brand)}
                      helperText={touched.brand && errors.brand}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label> Type</label>
                    <TextField
                      fullWidth
                      name="type"
                      value={values.type}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.type && Boolean(errors.type)}
                      helperText={touched.type && errors.type}
                    />
                  </Box>
                </Box>

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label> Serial Number</label>
                    <TextField
                      fullWidth
                      name="serialNumber"
                      value={values.serialNumber}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={
                        touched.serialNumber && Boolean(errors.serialNumber)
                      }
                      helperText={touched.serialNumber && errors.serialNumber}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label> Category</label>
                    <TextField
                      fullWidth
                      name="category"
                      value={values.category}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.category && Boolean(errors.category)}
                      helperText={touched.category && errors.category}
                    />
                  </Box>
                </Box>

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label>Cost</label>
                    <TextField
                      fullWidth
                      name="cost"
                      value={values.cost}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.cost && Boolean(errors.cost)}
                      helperText={touched.cost && errors.cost}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label>Vendor</label>
                    <TextField
                      fullWidth
                      name="vendor"
                      value={values.vendor}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.vendor && Boolean(errors.vendor)}
                      helperText={touched.vendor && errors.vendor}
                    />
                  </Box>
                </Box>

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box className="mobile-alignment" sx={{ flex: 1 }}>
                    <label>Assigned Date</label>
                    <TextField
                      fullWidth
                      type="date"
                      name="assignedDate"
                      value={values.assignedDate}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={
                        touched.assignedDate && Boolean(errors.assignedDate)
                      }
                      helperText={touched.assignedDate && errors.assignedDate}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Box>

                  <Box sx={{ flex: 1 }}>
                    <label>Warranty Until</label>
                    <TextField
                      fullWidth
                      type="date"
                      name="warrentyTo"
                      value={values.warrentyTo}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.warrentyTo && Boolean(errors.warrentyTo)}
                      helperText={touched.warrentyTo && errors.warrentyTo}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Box>
                </Box>

                <Box
                  className="mobile-alignment"
                  sx={{ display: "flex", gap: 2 }}
                >
                  <Box sx={{ flex: 1 }}>
                    <label>Employee</label>
                    <TextField
                      fullWidth
                      name="userId"
                      value={`${selectedEmployee.firstName} ${selectedEmployee.lastName}`}
                      InputProps={{
                        readOnly: true,
                      }}
                      sx={{
                        "& .MuiInputBase-input.Mui-readOnly": {
                          backgroundColor: "#f5f5f5",
                        },
                      }}
                    />
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <label>Location</label>
                    <TextField
                      fullWidth
                      name="location"
                      value={values.location}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.location && Boolean(errors.location)}
                      helperText={touched.location && errors.location}
                    />
                  </Box>
                </Box>

                <Box sx={{ display: "flex", gap: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      variant="body1"
                      sx={{ mb: 1, fontSize: "14px" }}
                    >
                      Asset Image
                    </Typography>
                    <Box
                      sx={{
                        border: "2px dashed #ccc",
                        borderRadius: "8px",
                        padding: "20px",
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        gap: "10px",
                        position: "relative",
                        backgroundColor: "#F8F9FA",
                      }}
                    >
                      {isLoading ? (
                        <Box sx={{ display: "flex", justifyContent: "center" }}>
                          <CircularProgress size={24} />
                        </Box>
                      ) : (
                        <>
                          <Box
                            sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}
                          >
                            {values.assetImage.length > 0 ? (
                              values.assetImage.map((image, index) => (
                                <Box
                                  key={index}
                                  sx={{
                                    width: "100px",
                                    height: "100px",
                                    position: "relative",
                                    overflow: "hidden",
                                    borderRadius: "8px",
                                    border: "1px solid #E5E7EB",
                                    mb: 2,
                                  }}
                                >
                                  <Image
                                    src={
                                      typeof image === "string"
                                        ? image
                                        : URL.createObjectURL(image)
                                    }
                                    alt={`Asset Preview ${index + 1}`}
                                    fill
                                    style={{
                                      objectFit: "cover",
                                    }}
                                  />
                                  <IconButton
                                    sx={{
                                      position: "absolute",
                                      top: 0,
                                      right: 0,
                                      backgroundColor: "rgba(0,0,0,0.5)",
                                      color: "white",
                                      padding: "4px",
                                      zIndex: 1,
                                      "&:hover": {
                                        backgroundColor: "rgba(0,0,0,0.7)",
                                      },
                                    }}
                                    onClick={() => {
                                      const newImages =
                                        values.assetImage.filter(
                                          (_, i) => i !== index
                                        );
                                      setFieldValue("assetImage", newImages);
                                    }}
                                  >
                                    <Cancel fontSize="small" />
                                  </IconButton>
                                </Box>
                              ))
                            ) : (
                              <Box
                                sx={{
                                  width: "50px",
                                  height: "50px",
                                  borderRadius: "50%",
                                  backgroundColor: "#e3f2fd",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  mb: 1,
                                }}
                              >
                                <CloudUpload sx={{ color: "#F26522" }} />
                              </Box>
                            )}
                          </Box>

                          <Typography
                            sx={{
                              fontSize: "0.75rem",
                              color: "text.secondary",
                            }}
                          >
                            Image should be below 4 MB
                          </Typography>
                          <Button
                            variant="contained"
                            component="label"
                            sx={{
                              backgroundColor: "#F26522",
                              color: "#FFF",
                              textTransform: "none",
                              display: "flex",
                              gap: "8px",
                              "&:hover": {
                                backgroundColor: "#d55a1d",
                              },
                            }}
                            disabled={isSubmitting}
                          >
                            <span>
                              {isSubmitting ? "Uploading..." : "Upload Images"}
                            </span>
                            <input
                              type="file"
                              hidden
                              accept="image/*"
                              onChange={(e) =>
                                handleImageUpload(e, setFieldValue, values)
                              }
                            />
                          </Button>
                        </>
                      )}
                    </Box>
                    {touched.assetImage && errors.assetImage && (
                      <Typography color="error" variant="caption">
                        {errors.assetImage}
                      </Typography>
                    )}
                  </Box>
                </Box>

                {/* {values.assetImages && (
                  <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                    {Array.from(values.assetImages).map((file, index) => (
                      <Box
                        key={index}
                        sx={{
                          width: 100,
                          height: 100,
                          position: "relative",
                          overflow: "hidden",
                          borderRadius: 1,
                          border: "1px solid #E5E7EB",
                        }}
                      >
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Preview ${index + 1}`}
                          style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                          }}
                        />
                      </Box>
                    ))}
                  </Box>
                )} */}
              </Box>

              <DialogActions
                sx={{
                  padding: "0px",
                  marginTop: "20px",
                }}
              >
                <Button
                  onClick={onClose}
                  sx={{
                    fontWeight: "400",
                    fontSize: "14px",
                    color: "#111827",
                    border: "1px solid #E5E7EB",
                    borderRadius: "5px",
                    textTransform: "none",
                    padding: "8px 13.6px",
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  sx={{
                    backgroundColor: "#F26522",
                    border: "1px solid #F26522",
                    color: "#FFF",
                    borderRadius: "5px",
                    padding: "0.5rem 0.85rem",
                    fontSize: "14px",
                    transition: "all 0.5s",
                    fontWeight: 500,
                    textTransform: "none",
                    "&:hover": { backgroundColor: "#E55A1B" },
                  }}
                >
                  Add Asset
                </Button>
              </DialogActions>
            </DialogContent>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default AddAssetForm;

"use client";
import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  DialogT<PERSON>le,
  DialogContent,
  IconButton,
  TextField,
  Button,
  Box,
  Typography,
  Select,
  MenuItem,
  FormControl,
  FormHelperText,
  DialogActions,
  Chip,
} from "@mui/material";
import "./AddTaskDialog.scss";
import { Close, AttachFile as AttachFileIcon } from "@mui/icons-material";
import { toast } from "react-toastify";
import { useFormik } from "formik";
import * as Yup from "yup";
import Autocomplete from "@mui/material/Autocomplete";
import useUploadMedia from "@/app/hooks/useUploadMedia";
import TextEditor from "@/components/TextEditor/TextEditor";
import {
  getAllProjects,
  getProjectById,
  addTask,
  getTaskById,
  updateTask,
} from "@/app/services/projects/project.service";
import { getStatusBoard } from "@/app/services/projects/project.service";
import Loader from "../Loader/Loader";

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  avatar: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface Project {
  _id: string;
  projectName: string;
  teamMembers: User[];
}

interface Status {
  _id: string;
  title: string;
  hexaColour: string;
  isActive: boolean;
  isDeleted: boolean;
}

interface TaskDialogProps {
  open: boolean;
  onClose: () => void;
  refreshList?: () => void;
  editTaskId?: string | null;
  projectId?: string | null;
  readOnly?: boolean;
}

const stripHtmlTags = (html: string) => {
  const div = document.createElement("div");
  div.innerHTML = html;
  return div.textContent || div.innerText || "";
};

const TaskDialog: React.FC<TaskDialogProps> = ({
  open,
  onClose,
  refreshList,
  editTaskId,
  projectId,
  readOnly = false, // Added default value
}) => {
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [attachmentFiles, setAttachmentFiles] = useState<File[]>([]);
  const [attachmentPreviews, setAttachmentPreviews] = useState<string[]>([]);
  const [attachmentsUrl, setAttachmentsUrl] = useState<string[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [projectsLoading, setProjectsLoading] = useState<boolean>(false);
  const [teamMembers, setTeamMembers] = useState<User[]>([]);
  const [taskLoading, setTaskLoading] = useState<boolean>(false);
  const [statuses, setStatuses] = useState<Status[]>([]);
  const [statusesLoading, setStatusesLoading] = useState<boolean>(false);

  const { uploadMedia } = useUploadMedia();

  const validationSchema = Yup.object({
    title: Yup.string().required("Task Title is required"),
    dueDate: Yup.date().required("Due Date is required").nullable(),
    project: Yup.string().required("Project is required"),
    teamMembers: Yup.array()
      .of(Yup.string())
      .min(1, "At least one team member is required")
      .required("Team Members are required"),
    status: Yup.string().required("Status is required"),
    priority: Yup.string().required("Priority is required"),
    hours: Yup.number(),
    description: Yup.string().required("Description is required"),
  });

  const formik = useFormik({
    initialValues: {
      title: "",
      dueDate: "",
      project: projectId || "",
      teamMembers: [] as string[],
      status: "",
      priority: "",
      hours: "",
      description: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      if (readOnly) {
        onClose(); // Exit if read-only
        return;
      }
      try {
        const payload = {
          title: values.title,
          dueDate: new Date(values.dueDate).toISOString(),
          projectId: values.project,
          teamMembers: values.teamMembers,
          status: values.status, // Sends status _id
          priority: values.priority,
          hours: Number(values.hours) || 0,
          description: stripHtmlTags(values.description),
          attachmentsUrl: attachmentsUrl.length > 0 ? attachmentsUrl : [],
        };

        if (editTaskId) {
          const response = await updateTask(editTaskId, payload);
          toast.success(response.message || "Task updated successfully");
        } else {
          const response = await addTask(payload);
          toast.success(response.message || "Task added successfully");
        }

        if (refreshList) {
          refreshList();
        }
        onClose();
      } catch (error: any) {
        console.error("Error saving task:", error);
        const errorMessage =
          error.response?.data?.error?.errors ||
          `Failed to ${editTaskId ? "update" : "add"} task`;
        toast.error(errorMessage);
      }
    },
  });

  // Fetch statuses from API
  useEffect(() => {
    const fetchStatuses = async () => {
      if (open) {
        setStatusesLoading(true);
        try {
          const response = await getStatusBoard();
          if (response.success && response.taskBoard?.results) {
            setStatuses(response.taskBoard.results);
          } else {
            toast.error("Failed to load statuses");
          }
        } catch (error) {
          console.error("Failed to fetch statuses:", error);
          toast.error("Failed to load statuses");
        } finally {
          setStatusesLoading(false);
        }
      }
    };

    fetchStatuses();
  }, [open]);

  // Fetch task data for editing
  useEffect(() => {
    const fetchTaskData = async () => {
      if (editTaskId && open) {
        setTaskLoading(true);
        try {
          const response = await getTaskById(editTaskId);
          const task = response.task;
          formik.setValues({
            title: task.title || "",
            dueDate: task.dueDate
              ? new Date(task.dueDate).toISOString().split("T")[0]
              : "",
            project: task.projectId?._id || "",
            teamMembers:
              task.teamMembers?.map((member: User) => member._id) || [],
            status: task.status?._id || "", // Set to status._id
            priority: task.priority || "",
            hours: task.hours?.toString() || "",
            description: task.description || "",
          });
          setAttachmentsUrl(task.attachmentsUrl || []);
          setAttachmentPreviews(task.attachmentsUrl || []);
          // Set project for edit mode
          if (task.projectId) {
            setProjects([
              {
                _id: task.projectId._id,
                projectName: task.projectId.projectName,
                teamMembers: task.teamMembers || [],
              },
            ]);
            setTeamMembers(task.teamMembers || []); // Set team members directly
          }
        } catch (error) {
          console.error("Failed to fetch task:", error);
          toast.error("Failed to load task data");
        } finally {
          setTaskLoading(false);
        }
      }
    };

    fetchTaskData();
  }, [editTaskId, open]);

  // Fetch all projects for add mode
  useEffect(() => {
    const fetchProjects = async () => {
      if (!editTaskId) {
        setProjectsLoading(true);
        try {
          const response = await getAllProjects();
          setProjects(response.projects.results);
        } catch (error) {
          console.error("Failed to fetch projects:", error);
          toast.error("Failed to load projects");
        } finally {
          setProjectsLoading(false);
        }
      }
    };

    if (open) {
      fetchProjects();
    }
  }, [open, editTaskId]);

  // Fetch team members when project changes
  useEffect(() => {
    const fetchProjectDetails = async () => {
      if (formik.values.project && !editTaskId) { // Only fetch for add mode
        try {
          const response = await getProjectById(formik.values.project);
          const projectTeamMembers = response.project.teamMembers || [];
          setTeamMembers(projectTeamMembers);
          formik.setFieldValue(
            "teamMembers",
            projectTeamMembers.map((member: User) => member._id)
          );
        } catch (error) {
          console.error("Failed to fetch project details:", error);
          toast.error("Failed to load project details");
          setTeamMembers([]);
          formik.setFieldValue("teamMembers", []);
        }
      }
    };

    fetchProjectDetails();
  }, [formik.values.project, editTaskId]);

  // Reset form and attachments when dialog closes
  useEffect(() => {
    if (!open) {
      formik.resetForm();
      setAttachmentFiles([]);
      setAttachmentPreviews([]);
      setAttachmentsUrl([]);
      setProjects([]);
      setTeamMembers([]);
      setStatuses([]);
    }
    return () => {
      attachmentPreviews.forEach((preview) => {
        if (preview?.startsWith("blob:")) {
          URL.revokeObjectURL(preview);
        }
      });
    };
  }, [open]);

  const handleAttachmentChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files).filter((file) => {
        if (file.size > 4 * 1024 * 1024) {
          toast.error(`${file.name} exceeds 4 mb limit`);
          return false;
        }
        return true;
      });
      if (newFiles.length === 0) return;

      setIsUploading(true);
      try {
        const newPreviews: string[] = [];
        const newUrls: string[] = [];
        for (const file of newFiles) {
          const result = await uploadMedia(
            file,
            "task_attachments",
            1,
            setIsUploading,
            file.name
          );
          newPreviews.push(URL.createObjectURL(file));
          newUrls.push(result.preview);
        }
        setAttachmentFiles((prev) => [...prev, ...newFiles]);
        setAttachmentPreviews((prev) => [...prev, ...newPreviews]);
        setAttachmentsUrl((prev) => [...prev, ...newUrls]);
      } catch (error) {
        console.error("Attachment upload error:", error);
        toast.error("Failed to upload attachments");
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleRemoveAttachment = (index: number) => {
    if (attachmentPreviews[index]?.startsWith("blob:")) {
      URL.revokeObjectURL(attachmentPreviews[index]);
    }
    setAttachmentFiles((prev) => prev.filter((_, i) => i !== index));
    setAttachmentPreviews((prev) => prev.filter((_, i) => i !== index));
    setAttachmentsUrl((prev) => prev.filter((_, i) => i !== index));
  };

  const getUserLabel = (user: User) => {
    return `${user.firstName} ${user.lastName}`;
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
      className="task-dialog"
    >
      <DialogTitle className="dialog-title">
        <Box className="title-container">
          <Typography variant="h6" className="dialog-title-text">
            {readOnly ? "View Task" : editTaskId ? "Edit Task" : "Add New Task"}
          </Typography>
        </Box>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            backgroundColor: "#6b7280",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": { backgroundColor: "#d55a1d" },
            "& .MuiSvgIcon-root": { fontSize: "14px" },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent className="dialog-content">
        {taskLoading || statusesLoading ? (
          <Loader loading={taskLoading || statusesLoading} />
        ) : (
          <form onSubmit={formik.handleSubmit}>
            <Box className="basic-info-tab">
              <Box className="form-field">
                <Typography variant="subtitle1">
                  Todo Title <span className="required">*</span>
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  name="title"
                  value={formik.values.title}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.title && Boolean(formik.errors.title)}
                  helperText={formik.touched.title && formik.errors.title}
                  disabled={readOnly}
                />
              </Box>

              <Box className="date-fields">
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Due Date <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    variant="outlined"
                    type="date"
                    name="dueDate"
                    value={formik.values.dueDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.dueDate && Boolean(formik.errors.dueDate)
                    }
                    helperText={formik.touched.dueDate && formik.errors.dueDate}
                    InputLabelProps={{ shrink: true }}
                    disabled={readOnly}
                  />
                </Box>
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Project <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="project"
                      value={formik.values.project}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.project && Boolean(formik.errors.project)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Select</em>;
                        }
                        const selectedProject = projects.find(
                          (project) => project._id === selected
                        );
                        return selectedProject ? (
                          selectedProject.projectName
                        ) : (
                          <em>Select</em>
                        );
                      }}
                      disabled={projectsLoading || !!editTaskId || readOnly}
                    >
                      <MenuItem disabled value="">
                        <em>Select</em>
                      </MenuItem>
                      {projects.map((project) => (
                        <MenuItem key={project._id} value={project._id}>
                          {project.projectName}
                        </MenuItem>
                      ))}
                    </Select>
                    {projectsLoading && (
                      <FormHelperText>Loading projects...</FormHelperText>
                    )}
                    {formik.touched.project && formik.errors.project && (
                      <FormHelperText error>
                        {formik.errors.project}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Team Members <span className="required">*</span>
                </Typography>
                <Autocomplete
                  multiple
                  options={teamMembers}
                  getOptionLabel={(option) => getUserLabel(option)}
                  value={teamMembers.filter((member) =>
                    formik.values.teamMembers.includes(member._id)
                  )}
                  onChange={(event, newValue) => {
                    const newIds = newValue.map((member) => member._id);
                    formik.setFieldValue("teamMembers", newIds);
                  }}
                  onBlur={() => formik.setFieldTouched("teamMembers", true)}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => {
                      const { key, ...otherProps } = getTagProps({ index });
                      return (
                        <Chip
                          key={option._id}
                          label={getUserLabel(option)}
                          deleteIcon={<Close />}
                          sx={{
                            margin: "2px",
                            borderRadius: "4px",
                            "& .MuiChip-label": {
                              fontSize: "12px",
                            },
                          }}
                          {...otherProps}
                        />
                      );
                    })
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      placeholder="Add team members"
                      error={
                        formik.touched.teamMembers &&
                        Boolean(formik.errors.teamMembers)
                      }
                      helperText={
                        formik.touched.teamMembers && formik.errors.teamMembers
                      }
                      className="custom-autocomplete"
                    />
                  )}
                  className="autocomplete-input"
                  disabled={!formik.values.project || readOnly}
                  noOptionsText={
                    !formik.values.project
                      ? "Select a project first"
                      : "No team members available"
                  }
                  sx={{
                    width: "100%",
                  }}
                />
              </Box>

              <Box className="date-fields">
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Status <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="status"
                      value={formik.values.status}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.status && Boolean(formik.errors.status)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Select</em>;
                        }
                        const selectedStatus = statuses.find(
                          (status) => status._id === selected
                        );
                        return selectedStatus ? (
                          selectedStatus.title
                        ) : (
                          <em>Select</em>
                        );
                      }}
                      disabled={statusesLoading || readOnly}
                    >
                      <MenuItem disabled value="">
                        <em>Select</em>
                      </MenuItem>
                      {statuses.map((status) => (
                        <MenuItem key={status._id} value={status._id}>
                          {status.title}
                        </MenuItem>
                      ))}
                    </Select>
                    {statusesLoading && (
                      <FormHelperText>Loading statuses...</FormHelperText>
                    )}
                    {formik.touched.status && formik.errors.status && (
                      <FormHelperText error>
                        {formik.errors.status}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
                <Box className="form-field half-width">
                  <Typography variant="subtitle1">
                    Priority <span className="required">*</span>
                  </Typography>
                  <FormControl fullWidth variant="outlined">
                    <Select
                      name="priority"
                      value={formik.values.priority}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={
                        formik.touched.priority &&
                        Boolean(formik.errors.priority)
                      }
                      displayEmpty
                      renderValue={(selected) => {
                        if (!selected) {
                          return <em>Select</em>;
                        }
                        return selected;
                      }}
                      disabled={readOnly}
                    >
                      <MenuItem disabled value="">
                        <em>Select</em>
                      </MenuItem>
                      <MenuItem value="High">High</MenuItem>
                      <MenuItem value="Medium">Medium</MenuItem>
                      <MenuItem value="Low">Low</MenuItem>
                    </Select>
                    {formik.touched.priority && formik.errors.priority && (
                      <FormHelperText error>
                        {formik.errors.priority}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">Hours</Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  type="number"
                  name="hours"
                  value={formik.values.hours}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.hours && Boolean(formik.errors.hours)}
                  helperText={formik.touched.hours && formik.errors.hours}
                  inputProps={{ min: 1, max: 24 }}
                  disabled={readOnly}
                />
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">
                  Description <span className="required">*</span>
                </Typography>
                <TextEditor
                  value={formik.values.description}
                  onChange={(newContent: string) =>
                    formik.setFieldValue("description", newContent)
                  }
                  // readOnly={readOnly}
                />
                {formik.touched.description && formik.errors.description && (
                  <FormHelperText error>
                    {formik.errors.description}
                  </FormHelperText>
                )}
              </Box>

              <Box className="form-field">
                <Typography variant="subtitle1">Upload Attachment</Typography>
                <Box className="upload-files-section">
                  <Button
                    variant="outlined"
                    component="label"
                    className="file-upload-btn"
                    disabled={isUploading || readOnly}
                    startIcon={<AttachFileIcon />}
                  >
                    {isUploading ? "Uploading..." : "Select File"}
                    <input
                      type="file"
                      multiple
                      hidden
                      onChange={handleAttachmentChange}
                    />
                  </Button>
                </Box>

                {attachmentPreviews.length > 0 && (
                  <Box className="uploaded-files-section">
                    <Box className="file-previews">
                      {attachmentPreviews.map((preview, index) => (
                        <Box key={index} className="file-preview-item">
                          <Box className="file-preview-placeholder">
                            <AttachFileIcon className="file-icon" />
                            <Button
                              variant="text"
                              className="view-file-btn"
                              href={attachmentsUrl[index]}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              View File
                            </Button>
                          </Box>
                          {!readOnly && (
                            <IconButton
                              size="small"
                              className="remove-file-btn"
                              onClick={() => handleRemoveAttachment(index)}
                            >
                              <Close sx={{ fontSize: "12px" }} />
                            </IconButton>
                          )}
                        </Box>
                      ))}
                    </Box>
                  </Box>
                )}
              </Box>
            </Box>

            {!readOnly && (
              <DialogActions className="dialog-actions">
                <Button
                  variant="outlined"
                  onClick={onClose}
                  className="cancel-button"
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  className="save-button"
                  disabled={isUploading || taskLoading || statusesLoading}
                >
                  {editTaskId ? "Update Task" : "Add New Task"}
                </Button>
              </DialogActions>
            )}
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default TaskDialog;
import { getVersion } from "@/app/services/setting.service";
import { Box, Typography } from "@mui/material";
import Link from "next/link";
import { useEffect, useState } from "react";

interface VersionData {
  versionNumber: string;
}

interface FooterProps {
  isCollapsed: boolean;
  isTablet?: boolean;
}

const Footer: React.FC<FooterProps> = ({ isCollapsed, isTablet }) => {
  const [versionNum, setVersionNum] = useState<VersionData | null>(null);

  useEffect(() => {
    const fetchVersionData = async () => {
      try {
        const response = await getVersion();
        console.log("Version Response", response);
        if (response && response.version) {
          setVersionNum({
            versionNumber: response.version.version,
          });
        }
      } catch (error) {
        console.log("failed to fetch Version Data:", error);
      }
    };
    fetchVersionData();
  }, []);

  return (
    <footer
      style={{
        display: "flex",
        // justifyContent: "space-between",
        gap: "10px",
        alignItems: "center",
        padding: "1rem",
        backgroundColor: "#FFF",
        borderTop: "1px solid #E5E7EB !important",
        marginLeft: isTablet ? 0 : isCollapsed ? "80px" : "251px",
        transition: "margin-left 0.3s ease-in-out",
      }}
    >
      <Box>
        <Typography
          sx={{ fontSize: "14px", color: "#6B7280", lineHeight: "1.5" }}
        >
          {versionNum?.versionNumber}
        </Typography>
      </Box>
      <Box sx={{ display: "flex" }}>
        <Typography
          sx={{
            fontSize: "14px",
            color: "#6B7280",
            lineHeight: "1.5",
            marginRight: "0.5rem",
          }}
        >
          ©
        </Typography>
        <Link
          href="#"
          style={{
            textDecoration: "none",
            color: "#F26522",
            fontSize: "14px",
            lineHeight: "1.5",
            marginRight: "0.5rem",
          }}
        >
          Aadvik Teklabs
        </Link>
      </Box>
    </footer>
  );
};

export default Footer;

"use client"
import { Box, Typography, LinearProgress, Chip, Stack } from "@mui/material"
import LocationOnIcon from "@mui/icons-material/LocationOn"
import AttachMoneyIcon from "@mui/icons-material/AttachMoney"
import WorkIcon from "@mui/icons-material/Work"
import Image from "next/image"

interface JobCardProps {
  title: string
  applicants: number
  location: string
  salaryMin: number
  salaryMax: number
  salaryPeriod: string
  experience: string
  tags: string[]
  filled: number
  total: number
  logoUrl?: string
}

export default function JobCard({
  title,
  applicants,
  location,
  salaryMin,
  salaryMax,
  salaryPeriod,
  experience,
  tags,
  filled,
  total,
  logoUrl,
}: JobCardProps) {
  return (
    <Box
      sx={{
        width: "100%",
        maxWidth: "351px",
        borderRadius: 2,
        boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
        bgcolor: "background.paper",
        overflow: "hidden",
        padding: "20px",
      }}
    >
      <Box
        sx={{
          p: 2,
          bgcolor: "#f9f9f9",
          display: "flex",
          alignItems: "center",
          gap: 2,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Image
            src={logoUrl || "/placeholder.svg"}
            alt={`${title} logo`}
            style={{ width: "100%", height: "100%", objectFit: "contain" }}
            width={40}
            height={40}
          />
        </Box>
        <Box>
          <Typography variant="h6" sx={{ fontSize: "14px", color: "#262A2A" }}>
            {title}
          </Typography>
          <Typography variant="body2" sx={{ color: "#6B7280", fontSize: "12px" }}>
            {applicants} Applicants
          </Typography>
        </Box>
      </Box>

      <Box sx={{ p: 2 }}>
        <Stack spacing={2}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <LocationOnIcon sx={{ color: "text.secondary", fontSize: 20 }} />
            <Typography variant="body2">{location}</Typography>
          </Box>

          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <AttachMoneyIcon sx={{ color: "text.secondary", fontSize: 20 }} />
            <Typography variant="body2">
              {salaryMin.toLocaleString()} - {salaryMax.toLocaleString()} / {salaryPeriod}
            </Typography>
          </Box>

          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <WorkIcon sx={{ color: "text.secondary", fontSize: 20 }} />
            <Typography variant="body2">{experience}</Typography>
          </Box>

          <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
            {tags.map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                size="small"
                sx={{
                  bgcolor: index === 0 ? "#ffebf5" : "#e6f4f1",
                  color: index === 0 ? "#ff3399" : "#4db6ac",
                  fontWeight: 500,
                  borderRadius: 1,
                }}
              />
            ))}
          </Box>

          <Box sx={{ mt: 1 }}>
            <LinearProgress
              variant="determinate"
              value={(filled / total) * 100}
              sx={{
                height: 8,
                borderRadius: 4,
                bgcolor: "#f0f0f0",
                "& .MuiLinearProgress-bar": {
                  bgcolor: "#ffb74d",
                },
              }}
            />
            <Typography variant="caption" sx={{ display: "block", mt: 0.5 }}>
              {filled} of {total} filled
            </Typography>
          </Box>
        </Stack>
      </Box>
    </Box>
  )
}

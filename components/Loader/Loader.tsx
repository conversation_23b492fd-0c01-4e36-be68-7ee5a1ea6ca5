"use client";
import React from "react";
import { ThreeCircles } from "react-loader-spinner";
import './Loader.scss';

interface LoaderProps {
  loading: boolean;
}

const Loader: React.FC<LoaderProps> = ({ loading }) => {
  return (
    <div className="full-page-loader">
      <ThreeCircles
        height="80"
        width="80"
        color="#F26522"
        wrapperStyle={{}}
        wrapperClass=""
        visible={loading}
        ariaLabel="three-circles-loading"
      />
    </div>
  );
};

export default Loader;
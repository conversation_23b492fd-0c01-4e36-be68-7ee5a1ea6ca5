"use client";
import { useState } from "react";
import {
  <PERSON><PERSON>ield,
  Button,
  Typography,
  CircularProgress,
  Box,
  InputAdornment,
  IconButton,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import { useRouter } from "next/navigation";
import { Visibility, VisibilityOff, Email } from "@mui/icons-material";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { addLoginSection } from "@/app/services/auth/auth.service";
import "./Login.scss";
import { toast, ToastContainer } from "react-toastify";
import useAuthStore from "@/store/authStore";
import Image from "next/image";

interface FormValues {
  email: string;
  password: string;
}

// Validation schema using Yup
const LoginSchema = Yup.object().shape({
  email: Yup.string()
    .email("Invalid email address")
    .required("Email is required"),
  password: Yup.string()
    .min(6, "Password must be at least 6 characters")
    .required("Password is required"),
});

const LoginForm: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [rememberMe, setRememberMe] = useState<boolean>(false);
  const router = useRouter();
  const setLoginData = useAuthStore((state) => state.setLoginData);

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  // Initial form values
  const initialValues = {
    email: "",
    password: "",
  };

  // Handle form submission
  const handleLogin = async (values: FormValues) => {
    setIsLoading(true);
    try {
      const response = await addLoginSection(values);
      
      if (response?.success) {
        // First set the login data in store
        setLoginData(response, values.email);
        
        // Show success message
        toast.success(response.message || "Successfully signed in");

        // Add a longer delay before redirect to ensure token is properly stored
        // and toast is visible
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Redirect based on role
        const roles = response.auth.roles || [];
        if (roles.includes("Admin")) {
          router.push("/Dashboard/Admin-Dashboard");
        } else if (roles.includes("Employee")) {
          router.push("/Dashboard/Employee-Dashboard");
        } else {
          router.push("/"); // Default fallback
        }
      } else {
        throw new Error(response?.message || "Login failed");
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "An error occurred";
      toast.error("Invalid email or password");
      console.error("Login error:", errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateAccount = () => {
    router.push("/signup");
  };

  return (
    <div className="login-form-container">
      <ToastContainer />
      <div className="logo-container">
        <div className="logo">
          <Typography variant="h6" component="span" className="logo-text">
            <Image
              src="/assets/Aadvik-noBG.png"
              alt="Logo"
              width={300}
              height={100}
              className="logo-image"
            />
          </Typography>
        </div>
      </div>

      <Box className="form-wrapper">
        <Typography variant="h4" component="h1" className="form-title">
          Sign In
        </Typography>
        <Typography
          variant="body2"
          color="textSecondary"
          className="form-subtitle"
        >
          Please enter your details to sign in
        </Typography>

        <Formik
          initialValues={initialValues}
          validationSchema={LoginSchema}
          onSubmit={handleLogin}
        >
          {({ errors, touched, isSubmitting }) => (
            <Form className="login-form">
              <div className="form-field">
                <Typography variant="body2" className="field-label">
                  Email Address
                </Typography>
                <Field
                  as={TextField}
                  name="email"
                  type="email"
                  fullWidth
                  placeholder="Enter your email"
                  variant="outlined"
                  disabled={isLoading || isSubmitting}
                  error={touched.email && !!errors.email}
                  helperText={<ErrorMessage name="email" />}
                  className="input-field"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Email className="field-icon" />
                      </InputAdornment>
                    ),
                  }}
                />
              </div>

              <div className="form-field">
                <Typography variant="body2" className="field-label">
                  Password
                </Typography>
                <Field
                  as={TextField}
                  name="password"
                  type={showPassword ? "text" : "password"}
                  fullWidth
                  placeholder="Enter your password"
                  variant="outlined"
                  disabled={isLoading || isSubmitting}
                  error={touched.password && !!errors.password}
                  helperText={<ErrorMessage name="password" />}
                  className="input-field"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={togglePasswordVisibility}
                          edge="end"
                          disabled={isLoading || isSubmitting}
                          className="visibility-toggle"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </div>

              <div className="form-options">
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={rememberMe}
                      onChange={(e) => setRememberMe(e.target.checked)}
                      color="primary"
                      disabled={isLoading || isSubmitting}
                      className="remember-checkbox"
                    />
                  }
                  label="Remember Me"
                  className="remember-me"
                />
                <Button
                  variant="text"
                  color="primary"
                  className="forgot-password"
                  disabled={isLoading || isSubmitting}
                  onClick={() => router.push("/forget-password")}
                >
                  Forgot Password?
                </Button>
              </div>

              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                disabled={isLoading || isSubmitting}
                className="submit-button"
                startIcon={
                  isLoading ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : null
                }
              >
                {isLoading ? "Signing in..." : "Sign In"}
              </Button>
            </Form>
          )}
        </Formik>

        {/* <div className="account-options">
          <Typography variant="body2" className="no-account">
            Don&rsquo;t have an account?{" "}
            <Button
              variant="text"
              color="primary"
              className="create-account"
              disabled={isLoading}
              onClick={handleCreateAccount}
            >
              Create Account
            </Button>
          </Typography>
        </div> */}

        <Typography variant="body2" className="copyright">
          Copyright © 2024 - 2025 AAdvik Labs
        </Typography>
      </Box>
    </div>
  );
};

export default LoginForm;

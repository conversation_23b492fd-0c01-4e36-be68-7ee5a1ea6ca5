// Use CSS variables from theme.scss instead of SCSS variables
:root {
  --login-primary-color: var(--primary-color);
  --login-secondary-color: var(--secondary-color);
  --login-gray-900: var(--text-primary);
  --login-gray-700: var(--text-dark);
  --login-gray-500: var(--text-secondary);
  --login-light-bg: var(--bg-lighter);
  --login-white: var(--bg-light);
}

.login-page {
  display: flex;
  min-height: 100vh;
  width: 100%;
  font-family: "Roboto", sans-serif;
  overflow: hidden;
  background-color: var(--login-white);

  @media (max-width: 991.98px) {
    flex-direction: column;
  }

  .login-left {
    flex: 0.67;
    background: linear-gradient(180deg, #ff7b3d 0%, #5c260c 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        180deg,
        rgba(255, 123, 61, 0.8) 0%,
        rgba(92, 38, 12, 0.8) 100%
      );
      backdrop-filter: blur(6px);
      z-index: 0;
    }

    .login-left-content {
      max-width: 570px;
      color: var(--login-white);
      z-index: 1;
      padding: 40px;
      background: rgba(255, 247, 243, 0.25);
      border-radius: 15px;
      backdrop-filter: blur(26px);
      text-align: center;
      border: 1px solid #e5e7eb !important;

      h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        line-height: 1.2;
        color: var(--login-white);
      }

      p {
        font-size: 1.25rem;
        font-weight: 500;
        margin-bottom: 2rem;
        opacity: 0.9;
        color: var(--login-white);
      }

      .login-image {
        margin: 2rem auto;
        max-width: 400px;

        img {
          width: 100%;
          height: auto;
          border-radius: 8px;
        }
      }
    }

    @media (max-width: 991.98px) {
      display: none;
    }
  }

  // Right side with login form
  .login-right {
    flex: 1;
    background-color: var(--login-white);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }
}

.login-form-container {
  width: 100%;
  max-width: 490px;
  // padding: 1rem;

  .logo-container {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;

    .logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .logo-text {
        font-weight: 700;
        font-size: 1.5rem;
        color: var(--login-gray-900);
        margin-top: 0px;
        margin-bottom: 100px;
      }
    }
  }

  .form-wrapper {
    width: 100%;
    // padding: 0 1rem;

    .form-title {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 0.5rem;
      color: var(--login-gray-900);
      text-align: center;
    }

    .form-subtitle {
      margin-bottom: 2rem;
      color: var(--login-gray-500);
      text-align: center;
      font-size: 0.875rem;
    }

    .error-message {
      margin-bottom: 1rem;
      padding: 0.5rem;
      background-color: rgba(231, 13, 13, 0.1);
      border-radius: 4px;
      color: #e70d0d;
      text-align: center;
    }

    .login-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);

      .form-field {
        .field-label {
          margin-bottom: var(--spacing-sm);
          display: block;
          font-weight: 500;
          color: var(--text-dark);
          font-size: 14px;
        }

        .input-field {
          .MuiOutlinedInput-root {
            border-radius: var(--border-radius-sm);
            background-color: #ffffff;
            font-size: 14px;

            &:hover .MuiOutlinedInput-notchedOutline {
              border-color: var(--primary-color);
            }

            &.Mui-focused .MuiOutlinedInput-notchedOutline {
              border-color: var(--primary-color);
              border-width: 2px;
            }

            .MuiOutlinedInput-input {
              padding: 8px 12px;
              color: var(--text-dark);
              min-height: 38px;
            }
          }

          .field-icon {
            color: var(--text-secondary);
          }

          .visibility-toggle {
            color: var(--text-secondary);

            &:hover {
              color: var(--primary-color);
            }
          }
        }
      }

      .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .remember-me {
          .MuiFormControlLabel-label {
            font-size: 0.875rem;
            color: var(--login-gray-700);
          }

          .remember-checkbox {
            // color: $primary-color;

            &.Mui-checked {
              color: var(--login-primary-color);
            }
          }
        }

        .forgot-password {
          font-size: 0.95rem;
          color: #e70d0d;
          text-transform: none;
          padding: 0;
          min-width: auto;

          &:hover {
            // color: darken(#e70d0d , 10%);
            background-color: transparent;
          }
        }
      }

      .submit-button {
        background-color: var(--primary-color);
        color: white;
        padding: 8px 16px;
        border-radius: var(--border-radius-sm);
        font-weight: 500;
        font-size: 14px;
        text-transform: none;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);

        &:hover {
          background-color: var(--primary-dark);
          box-shadow: var(--shadow-md);
        }

        &.Mui-disabled {
          opacity: 0.7;
        }
      }
    }

    .account-options {
      text-align: center;
      margin: 1.5rem 0;

      .no-account {
        color: var(--login-gray-500);
        font-size: 0.875rem;
      }

      .create-account {
        color: var(--login-primary-color);
        font-weight: 600;
        text-transform: none;
        padding: 0 4px;
        min-width: auto;

        &:hover {
          // color: darken($primary-color, 10%);
          background-color: transparent;
        }
      }
    }

    .copyright {
      text-align: center;
      color: var(--login-gray-500);
      margin-top: 2rem;
      font-size: 0.75rem;
    }
  }
}

// Login-specific MUI Overrides
.login-page {
  // Button overrides
  .MuiButton-root {
    &.MuiButton-contained {
      &.MuiButton-containedPrimary {
        background-color: var(--primary-color);

        &:hover {
          background-color: var(--primary-dark);
        }
      }
    }

    &.MuiButton-text {
      font-size: 14px;
    }
  }

  // Checkbox overrides
  .MuiCheckbox-root {
    &.MuiCheckbox-colorPrimary {
      &.Mui-checked {
        color: var(--primary-color);
      }
    }
  }
}

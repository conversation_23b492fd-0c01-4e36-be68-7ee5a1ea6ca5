.rbc-calendar {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 20px;

  .rbc-toolbar {
    margin-bottom: 24px;
    padding: 0 10px;

    .rbc-toolbar-label {
      font-size: 1.25rem;
      font-weight: 600;
      color: #202C4B;
    }

    .rbc-btn-group {
      button {
        color: #202C4B;
        border: 1px solid #E5E7EB;
        background: #ffffff;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.2s ease;
        text-transform: capitalize;

        &:hover {
          background-color: #F8FAFC;
        }

        &.rbc-active {
          background-color: #F26522;
          color: white;
          border-color: #F26522;
          box-shadow: 0 2px 4px rgba(242, 101, 34, 0.2);

          &:hover {
            background-color: darken(#F26522, 5%);
          }
        }
      }
    }
  }

  .rbc-month-view {
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    overflow: hidden;
  }

  .rbc-header {
    padding: 12px 8px;
    font-weight: 600;
    background-color: #F8FAFC;
    color: #202C4B;
    border-bottom: 1px solid #E5E7EB;
  }

  .rbc-date-cell {
    padding: 8px;
    font-size: 0.875rem;
    color: #4B5563;

    &.rbc-now {
      font-weight: bold;
      color: #F26522;
    }
  }

  .rbc-today {
    background-color: #FFF5F0;
  }

  .rbc-event {
    background-color: #F26522;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.875rem;
    box-shadow: 0 2px 4px rgba(242, 101, 34, 0.2);

    &:hover {
      background-color: darken(#F26522, 5%);
    }

    .rbc-event-content {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .rbc-off-range-bg {
    background-color: #F8FAFC;
  }

  .rbc-show-more {
    color: #F26522;
    font-weight: 500;
    font-size: 0.875rem;
    padding: 2px 8px;
    background-color: transparent;

    &:hover {
      text-decoration: underline;
      background-color: #FFF5F0;
    }
  }

  // Add hover effect to day cells
  .rbc-day-bg {
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #F8FAFC;
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .rbc-calendar {
    padding: 12px;

    .rbc-toolbar {
      flex-direction: column;
      gap: 12px;
      margin-bottom: 16px;

      .rbc-toolbar-label {
        text-align: center;
        margin: 8px 0;
      }

      .rbc-btn-group {
        display: flex;
        justify-content: center;
        width: 100%;

        button {
          flex: 1;
          padding: 6px 12px;
          font-size: 0.875rem;
        }
      }
    }

    .rbc-month-view {
      border-radius: 6px;
    }

    .rbc-header {
      padding: 8px 4px;
      font-size: 0.875rem;
    }

    .rbc-date-cell {
      padding: 4px;
      font-size: 0.75rem;
    }

    .rbc-event {
      padding: 2px 4px;
      font-size: 0.75rem;
    }
  }
}
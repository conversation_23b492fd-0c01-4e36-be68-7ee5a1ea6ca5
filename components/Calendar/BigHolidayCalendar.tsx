import React, { useState, useEffect, useCallback } from "react";
import { Calendar, dateFnsLocalizer, View } from "react-big-calendar";
import { format, parse, startOfWeek, getDay } from "date-fns";
import { enUS } from "date-fns/locale";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { Box, Paper, Typography } from "@mui/material";
import { getHolidays } from "@/app/services/holiday";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";
import "./HolidayCalendar.scss";

interface Holiday {
  title: string;
  date: string;
  description: string;
  isActive: boolean;
}

interface CalendarEvent {
  title: string;
  start: Date;
  end: Date;
  description: string;
  allDay: boolean;
}

const locales = {
  "en-US": enUS,
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

const BigHolidayCalendar: React.FC = () => {
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [date, setDate] = useState(new Date());
  const [view, setView] = useState<View>("month");

  useEffect(() => {
    fetchHolidays();
  }, []);

  const fetchHolidays = async () => {
    try {
      const response = await getHolidays();
      if (response?.holidays?.results) {
        const formattedEvents: CalendarEvent[] = response.holidays.results
          .filter((holiday: Holiday) => holiday.isActive)
          .map((holiday: Holiday) => {
            const eventDate = new Date(holiday.date);
            return {
              title: holiday.title,
              start: eventDate,
              end: eventDate,
              description: holiday.description,
              allDay: true,
            };
          });
        setEvents(formattedEvents);
      }
    } catch (error) {
      console.error("Failed to fetch holidays:", error);
      // toast.error("Failed to load holidays");
    } finally {
      setLoading(false);
    }
  };

  const handleEventSelect = (event: CalendarEvent) => {
    toast.info(`${event.title}: ${event.description}`);
  };

  const eventStyleGetter = () => {
    return {
      style: {
        backgroundColor: "#45858C",
        borderRadius: "4px",
        opacity: 0.8,
        color: "white",
        border: "0px",
        display: "block",
      },
    };
  };

  const handleNavigate = useCallback((newDate: Date) => {
    setDate(newDate);
  }, []);

  const handleViewChange = useCallback((newView: View) => {
    setView(newView);
  }, []);

  if (loading) {
    return <Loader loading={loading} />;
  }

  return (
    <Paper
      sx={{
        p: 2,
        height: "calc(100vh - 200px)",
        boxShadow: "0px 4px 8px rgba(0,0,0,0.1)",
        borderRadius: "12px",
        bgcolor: "#ffffff",
      }}
    >
      <Calendar
        localizer={localizer}
        events={events}
        startAccessor="start"
        endAccessor="end"
        style={{ height: "100%" }}
        onSelectEvent={handleEventSelect}
        eventPropGetter={eventStyleGetter}
        views={["month", "week", "day"]}
        defaultView="month"
        view={view}
        onView={handleViewChange}
        date={date}
        onNavigate={handleNavigate}
        popup
        selectable
        toolbar={true}
        formats={{
          monthHeaderFormat: (date: Date) => format(date, "MMMM yyyy"),
          dayHeaderFormat: (date: Date) => format(date, "EEEE dd"),
          dayRangeHeaderFormat: ({ start, end }: { start: Date; end: Date }) =>
            `${format(start, "dd MMM")} - ${format(end, "dd MMM yyyy")}`,
        }}
        messages={{
          today: "Today",
          previous: "Previous",
          next: "Next",
          month: "Month",
          week: "Week",
          day: "Day",
        }}
      />
    </Paper>
  );
};

export default BigHolidayCalendar;

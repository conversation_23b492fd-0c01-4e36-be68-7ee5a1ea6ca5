import React, { useState, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import { Box, Paper, Typography } from '@mui/material';
import { getHolidays } from '@/app/services/holiday';
import { toast } from 'react-toastify';
import Loader from '@/components/Loader/Loader';
import "./HolidayCalendar.scss"

interface Holiday {
  title: string;
  date: string;
  description: string;
  isActive: boolean;
}

interface CalendarEvent {
  title: string;
  date: string;
  description: string;
  backgroundColor: string;
  borderColor: string;
  textColor: string;
  display: 'block';
}

const HolidayCalendar: React.FC = () => {
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchHolidays();
  }, []);

  const fetchHolidays = async () => {
    try {
      const response = await getHolidays();
      if (response?.holidays?.results) {
        const formattedEvents: CalendarEvent[] = response.holidays.results
          .filter((holiday: Holiday) => holiday.isActive)
          .map((holiday: Holiday) => ({
            title: holiday.title,
            date: new Date(holiday.date).toISOString().split('T')[0],
            description: holiday.description,
            backgroundColor: '#45858C',
            borderColor: '#45858C',
            textColor: '#ffffff',
            display: 'block'
          }));
        setEvents(formattedEvents);
      }
    } catch (error) {
      console.error('Failed to fetch holidays:', error);
      // toast.error('Failed to load holidays');
    } finally {
      setLoading(false);
    }
  };

  const handleEventClick = (info: any) => {
    toast.info(`${info.event.title}: ${info.event.extendedProps.description}`);
  };

  const handleDateClick = (info: any) => {
    const eventsOnDay = events.filter(event => event.date === info.dateStr);
    if (eventsOnDay.length > 0) {
      const holidayList = eventsOnDay
        .map(event => `${event.title}: ${event.description}`)
        .join('\n');
      toast.info(holidayList);
    }
  };

  const renderEventContent = (eventInfo: any) => {
    return (
      <Box sx={{ 
        padding: '2px 4px',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      }}>
        <Typography variant="body2">
          {eventInfo.event.title}
        </Typography>
      </Box>
    );
  };

  if (loading) {
    return <Loader loading={loading} />;
  }

  return (
    <Paper sx={{ 
      p: 2,
      height: '100%',
      boxShadow: '0px 4px 8px rgba(0,0,0,0.1)',
      borderRadius: '8px'
    }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" sx={{ color: '#202C4B', fontWeight: 600 }}>
          Holiday Calendar
        </Typography>
      </Box>
      <FullCalendar
        plugins={[dayGridPlugin, interactionPlugin]}
        initialView="dayGridMonth"
        events={events}
        eventContent={renderEventContent}
        eventClick={handleEventClick}
        dateClick={handleDateClick}
        headerToolbar={{
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth'
        }}
        height="auto"
        dayMaxEvents={2}
        eventDisplay="block"
        displayEventTime={false}
        eventTimeFormat={{
          hour: '2-digit',
          minute: '2-digit',
          meridiem: false
        }}
        slotMinTime="00:00:00"
        slotMaxTime="24:00:00"
        firstDay={1}
        weekends={true}
        fixedWeekCount={false}
        showNonCurrentDates={true}
        contentHeight="auto"
        aspectRatio={1.5}
        handleWindowResize={true}
        stickyHeaderDates={true}
        dayHeaders={true}
        titleFormat={{ year: 'numeric', month: 'long' }}
        dayHeaderFormat={{ weekday: 'short' }}
        slotEventOverlap={false}
        eventOverlap={false}
        nowIndicator={true}
        themeSystem="standard"
      />
    </Paper>
  );
};

export default HolidayCalendar;
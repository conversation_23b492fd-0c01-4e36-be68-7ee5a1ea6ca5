@use "sass:color";

.Calendar-header {
  display: flex;
  justify-content: space-between;

  .breadcrumbs-box {
    h2 {
      font-size: 24px;
      font-weight: 700;
      color: #202C4B;
    }
  }

  .add-holiday {

    display: flex;
    gap: 8px;
    background-color: #F26522;
    border-color: #F26522;
    color: #FFF;
    font-weight: 400;
    font-size: 14px;
    border-radius: 5px;
    text-transform: none;
    padding: 8px 13.6px;

  }
}


.fc {

  // Calendar container
  .fc-view-harness {
    background-color: #ffffff;
  }

  // Header styling
  .fc-toolbar {
    .fc-button {
      background-color: #45858C;
      border-color: #45858C;

      &:hover {

        background-color: color.adjust(#45858C, $lightness: -10%);
        border-color: color.adjust(#45858C, $lightness: -10%);

      }

      &.fc-button-active {

        background-color: color.adjust(#45858C, $lightness: -15%);
        border-color: color.adjust(#45858C, $lightness: -15%);

      }
    }

    .fc-toolbar-title {
      font-size: 1.25rem;
      color: #202C4B;
    }
  }

  // Day cells
  .fc-daygrid-day {
    &:hover {
      background-color: rgba(69, 133, 140, 0.1);
    }
  }

  // Today's cell
  .fc-day-today {
    background-color: rgba(69, 133, 140, 0.05) !important;
  }

  // Events
  .fc-event {
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.02);
    }
  }

  // Weekend days
  .fc-day-sat,
  .fc-day-sun {
    background-color: #f8f9fa;
  }

  // Header cells
  .fc-col-header-cell {
    background-color: #45858C;
    color: #ffffff;
    padding: 8px;
  }

  // More events popup
  .fc-more-popover {
    .fc-popover-title {
      background-color: #45858C;
      color: #ffffff;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .fc {
    .fc-toolbar {
      flex-direction: column;
      gap: 1rem;
    }

    .fc-toolbar-title {
      font-size: 1rem;
    }
  }
}

"use client";
import {
  Button,
  Typography,
  Box,
} from "@mui/material";
import "./SuccessScreen.scss";
import { ToastContainer } from "react-toastify";
import Image from "next/image";
import { useRouter } from "next/navigation";

const SuccessScreen: React.FC = () => {
  const router = useRouter(); // Add router hook


  const handleLoginAccount = () => {
    router.push("/login"); // Navigate to login
  };

  return (
    <div className="login-form-container">
      <ToastContainer />
      <div className="logo-container">
        <div className="logo">
          <Typography variant="h6" component="span" className="logo-text">
            <Image
              src="/assets/Aadvik-noBG.png"
              alt="Logo"
              width={300}
              height={100}
              className="logo-image"
            />
          </Typography>
        </div>
      </div>

      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          width: "100%",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Image
            src="/assets/success-tick.svg"
            alt="success"
            width={40}
            height={40}
            style={{ marginBottom: "1rem" }}
          />
          <Typography variant="h2" component="h1" className="form-title">
            Success
          </Typography>
          <Typography
            variant="body2"
            color="textSecondary"
            className="form-subtitle"
          >
            Your new password has been successfully saved
          </Typography>
        </Box>
        <Box sx={{ width: "100%", marginTop: "2rem" }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleLoginAccount}
            sx={{
              backgroundColor: "#F37438",
              border: "1px solid #F37438",
              color: "#FFF",
              textTransform: "none",
              borderRadius: "5px",
              padding: "0.5rem 0.85rem",
              fontSize: "14px",
              transition: "all 0.5s",
              fontWeight: "500",
              width: "100%",
            }}
          >
            Back to Sign In
          </Button>
        </Box>
      </Box>
    </div>
  );
};

export default SuccessScreen;

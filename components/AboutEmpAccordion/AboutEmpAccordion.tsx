import * as React from 'react';
import "./AboutEmpAccordion.scss";
import { Accordion, AccordionSummary, AccordionDetails, } from '@mui/material';
import Typography from '@mui/material/Typography';
import { ExpandMore } from '@mui/icons-material';

interface AboutEmpAccordionProps {
    userData: {
        user: {
            about: string;
        };
    };
}

export default function AboutEmpAccordion({userData}: AboutEmpAccordionProps) {
    return (
        <Accordion
            className='about-accordion'
            defaultExpanded
        >
            <AccordionSummary
                className='accordion-summary'
                expandIcon={
                    <ExpandMore />
                }
                aria-controls="panel1-content"
                id="panel1-header"
                sx={{ padding: '20px' }}
            >
                <Typography className='span' component="span" sx={{ margin: '0px', fontSize: "16px", fontWeight: 600, display: "flex", justifyContent: "space-between", minWidth: "100%" }}>
                    About Employee
                </Typography>
            </AccordionSummary>
            <AccordionDetails className='accordion-details' sx={{ padding: '20px' }}>
                {userData?.user?.about}

            </AccordionDetails>
        </Accordion>

    );
}
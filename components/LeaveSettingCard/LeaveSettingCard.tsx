"use client";
import React, { useState } from "react";
import './LeaveSettingCard.scss';
import { Card, CardContent, Typography, Switch, Box } from "@mui/material";
// import SettingsIcon from "@mui/icons-material/Settings";
// import AnnuallLeaveSetting from "@/app/Attendance/leaves/leaves-settings/AnnualLeaveSetting";

interface LeaveSettingCardProps {
  _id: string;
  title: string;
  linkUrl: string;
  switchChecked?: boolean;
  onCustomPolicyClick: (id: string) => void;
  policyName?: string;
}

const LeaveSettingCard: React.FC<LeaveSettingCardProps> = ({ _id, title, switchChecked = true, onCustomPolicyClick }) => {
  // const [openSettings, setOpenSettings] = useState(false);

  return (
    <>
      <Card sx={{ borderRadius: 2, boxShadow: "none", border: "1px solid #E5E7EB", padding: "1.25rem", width: "100%", "& .MuiCardContent-root:": { paddingBottom: "0px" } }}>
        <CardContent sx={{ display: "flex", alignItems: "center", padding: "0px", justifyContent: "space-between" }}>
          {/* Left Section: Switch and Title */}
          <Box display="flex" alignItems="center" gap={1.5}>
            <Switch
              defaultChecked={switchChecked}
              sx={{
                "& .MuiSwitch-switchBase": {
                  color: "#9CA3AF",
                  "&.Mui-checked": {
                    color: "#F97316",
                  },
                  "&.Mui-checked + .MuiSwitch-track": {
                    backgroundColor: "#F26522",
                    opacity: 1,
                  },
                },
                "& .MuiSwitch-track": {
                  backgroundColor: "#9CA3AF",
                },
              }}
            />
            <Typography fontWeight={600} color="#111827">
              {title}
            </Typography>
          </Box>

          {/* Right Section: Link & Settings Icon */}
          <Box display="flex" alignItems="center" gap={2}>
            <Typography
              component="button"
              onClick={() => onCustomPolicyClick(_id)}
              sx={{
                color: "#111827",
                textDecoration: "underline",
                fontWeight: 500,
                "&:hover": { color: "#F97316" },
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: 0,
              }}
            >
              Custom Policy
            </Typography>
            {/* <IconButton size="small" onClick={() => setOpenSettings(true)}>
              <SettingsIcon sx={{ fontSize: 18, color: "#111827" }} />
            </IconButton> */}
          </Box>
        </CardContent>
      </Card>

      {/* <AnnuallLeaveSetting 
        open={openSettings}
        onClose={() => setOpenSettings(false)}
      /> */}
    </>
  );
};

export default LeaveSettingCard;
"use client";

import { useState } from "react";
import { DateRange, DateRangePicker } from "@mui/x-date-pickers-pro";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { Box, Button, Popover, TextField } from "@mui/material";
import dayjs, { Dayjs } from "dayjs";

const CustomDateRangePicker = () => {
    const [selectedDateRange, setSelectedDateRange] = useState<DateRange<Dayjs>>([
        dayjs().subtract(7, "day"),
        dayjs(),
    ]);
    const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);

    const handleOpen = (event: React.MouseEvent<HTMLDivElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleApply = () => {
        console.log("Selected Date Range:", selectedDateRange);
        handleClose();
    };

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Box>
                <TextField
                    label="Select Date Range"
                    value={`${selectedDateRange[0]?.format("MM/DD/YYYY")} - ${selectedDateRange[1]?.format("MM/DD/YYYY")}`}
                    onClick={handleOpen}
                    fullWidth
                    InputProps={{ readOnly: true }}
                />

                <Popover
                    open={Boolean(anchorEl)}
                    anchorEl={anchorEl}
                    onClose={handleClose}
                    anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
                >
                    <Box p={2}>
                        <DateRangePicker
                            value={selectedDateRange}
                            onChange={(newValue: DateRange<Dayjs>) => setSelectedDateRange(newValue)}
                        />
                        <Box display="flex" justifyContent="space-between" mt={2}>
                            <Button onClick={handleClose} color="error">
                                Cancel
                            </Button>
                            <Button onClick={handleApply} color="primary" variant="contained">
                                Apply
                            </Button>
                        </Box>
                    </Box>
                </Popover>
            </Box>
        </LocalizationProvider>
    );
};

export default CustomDateRangePicker;

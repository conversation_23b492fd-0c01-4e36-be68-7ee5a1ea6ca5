import * as React from "react";
import * as Yup from "yup";
import dayjs from "dayjs";
import Link from "next/link";
import "./EducationDetailAccordion.scss";
import {
  Box,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  IconButton,
} from "@mui/material";
import Typography from "@mui/material/Typography";
import { Close, EditNote, ExpandMore } from "@mui/icons-material";
import { addEducation, updateEducation } from "@/app/services/users.service";
import { toast } from "react-toastify";

interface EducationDetailAccordionProps {
  userData: {
    user: {
      _id: string;
      education: {
        _id?: string;
        instituteName: string;
        course: string;
        startDate: string;
        endDate: string;
      }[];
    };
  } | null;
  refreshUserData?: () => void; // Add this prop to allow parent to refresh data
}

interface EducationErrors {
  instituteName?: string;
  course?: string;
  startDate?: string;
  endDate?: string;
}

interface EducationEmpIdProps {
  employeeId: string | null;
}

export default function EducationDetailAccordion({
  userData,
  employeeId,
  refreshUserData,
}: EducationDetailAccordionProps & EducationEmpIdProps) {
  const [open, setOpen] = React.useState(false);
  const [errors, setErrors] = React.useState<EducationErrors>({});
  const [educationData, setEducationData] = React.useState({
    educationId: employeeId || "",
    instituteName: "",
    course: "",
    startDate: "",
    endDate: "",
  });

  const userId = userData?.user?._id;

  // Validation schema
  const educationValidationSchema = Yup.object().shape({
    instituteName: Yup.string().required("Institution Name is required"),
    course: Yup.string().required("Course is required"),
    startDate: Yup.date()
      .required("Start Date is required")
      .max(new Date(), "Start date cannot be in the future")
      .typeError("Please enter a valid date"),
    endDate: Yup.date()
      .required("End Date is required")
      .test(
        "end-date-after-start-date",
        "End date must be after start date",
        function (endDate) {
          const startDate = this.parent.startDate;
          if (!startDate || !endDate) return true; // Skip validation if either date is missing

          const start = dayjs(startDate);
          const end = dayjs(endDate);
          return end.isAfter(start) || end.isSame(start);
        }
      )
      .test(
        "minimum-duration",
        "Course duration must be at least 1 year",
        function (endDate) {
          const startDate = this.parent.startDate;
          if (!startDate || !endDate) return true; // Skip validation if either date is missing

          const start = dayjs(startDate);
          const end = dayjs(endDate);
          const yearDiff = end.diff(start, "year", true);
          return yearDiff >= 1;
        }
      )
      .typeError("Please enter a valid date"),
  });

  // Validate a single field
  const validateField = async (name: string, value: any) => {
    try {
      // Create a data object with the current field value and the rest of the form data
      const dataToValidate = {
        ...educationData,
        [name]: value,
      };

      await educationValidationSchema.validateAt(name, dataToValidate);
      setErrors((prev) => ({ ...prev, [name]: undefined }));
      return true;
    } catch (error: any) {
      setErrors((prev) => ({ ...prev, [name]: error.message }));
      return false;
    }
  };

  // Sync educationData with userData when userData changes
  React.useEffect(() => {
    if (userData?.user?.education && userData.user.education.length > 0) {
      const edu = userData.user.education[0];
      setEducationData({
        ...educationData,
        instituteName: edu.instituteName || "",
        course: edu.course || "",
        startDate: edu.startDate ? edu.startDate.split("T")[0] : "", // Format to YYYY-MM-DD
        endDate: edu.endDate ? edu.endDate.split("T")[0] : "", // Format to YYYY-MM-DD
      });
    }
  }, [userData]);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  // Handle input changes with validation
  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Update the state with the new value
    setEducationData((prev) => {
      const updatedData = { ...prev, [name]: value };

      // Validate after state update to ensure we're using the latest values
      setTimeout(async () => {
        if (name === "startDate") {
          // If start date changes, validate end date as well
          await validateField("startDate", value);
          if (updatedData.endDate) {
            await validateField("endDate", updatedData.endDate);
          }
        } else if (name === "endDate") {
          // If end date changes, validate it with the current start date
          await validateField("endDate", value);
        } else {
          // For other fields, just validate the changed field
          await validateField(name, value);
        }
      }, 0);

      return updatedData;
    });
  };

  // Handle form submission
  const handleSave = async () => {
    try {
      // Validate all fields
      await educationValidationSchema.validate(educationData, {
        abortEarly: false,
      });

      const educationPayload = {
        instituteName: educationData.instituteName,
        course: educationData.course,
        startDate: educationData.startDate,
        endDate: educationData.endDate,
        userId: employeeId || userId || "", // Use employeeId if available, otherwise fall back to userId
      };

      try {
        // Check if educationId exists in the educationData
        if (
          userData?.user?.education &&
          userData.user.education.length > 0 &&
          userData.user.education[0]._id
        ) {
          const educationId = userData.user.education[0]._id;
          await updateEducation(educationId, educationPayload);
          toast.success("Education updated successfully");
        } else {
          await addEducation(educationPayload);
          toast.success("Education added successfully");
        }
        handleClose();

        // Refresh user data after successful save
        if (refreshUserData) {
          refreshUserData();
        }
      } catch (error) {
        console.error("Failed to save education details:", error);
        // toast.error("Failed to save education details");
      }
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        // Handle validation errors
        const validationErrors: EducationErrors = {};
        error.inner.forEach((err) => {
          if (err.path) {
            validationErrors[err.path as keyof EducationErrors] = err.message;
          }
        });
        setErrors(validationErrors);
        // toast.error("Please check all fields and try again");
      }
    }
  };

  return (
    <>
      <Accordion className="edu-detail-accordion">
        <AccordionSummary
          className="accordion-summary"
          expandIcon={<ExpandMore />}
          aria-controls="panel1-content"
          id="panel1-header"
          sx={{ padding: "20px" }}
        >
          <Typography
            className="span"
            component="span"
            sx={{
              margin: "0px",
              fontSize: "16px",
              fontWeight: 600,
              display: "flex",
              justifyContent: "space-between",
              minWidth: "100%",
            }}
          >
            Education Details
            <Link
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleOpen();
              }}
            >
              <EditNote sx={{ fontSize: "16px" }} />
            </Link>
          </Typography>
        </AccordionSummary>
        <AccordionDetails
          className="accordion-details"
          sx={{ padding: "20px", display: "flex", flexDirection: "column" }}
        >
          {userData?.user?.education && userData.user.education.length > 0 ? (
            userData.user.education.map((edu, index) => (
              <Box
                key={index}
                className="education"
                sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}
              >
                <Box>
                  <Typography
                    className="span"
                    component="span"
                    sx={{ margin: "0px" }}
                  >
                    {edu.instituteName}
                  </Typography>
                  <Typography variant="h6">{edu.course}</Typography>
                </Box>
                <Box className="year">
                  <Typography>
                    {new Date(edu.startDate).getFullYear()} -{" "}
                    {new Date(edu.endDate).getFullYear()}
                  </Typography>
                </Box>
              </Box>
            ))
          ) : (
            <Typography>No education data available</Typography>
          )}
        </AccordionDetails>
      </Accordion>

      <Dialog
        className="edit-dialog-container"
        open={open}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleClose();
          }
        }}
        disableEscapeKeyDown
        fullWidth
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography
            variant="h6"
            sx={{ fontWeight: 600, fontSize: "20px", color: "#202C4B" }}
          >
            Education Information
          </Typography>
          <IconButton
            onClick={handleClose}
            aria-label="close"
            sx={{
              backgroundColor: "#6b7280",
              backgroundImage: "none",
              borderRadius: "50%",
              color: "#fff",
              height: "20px",
              width: "20px",
              margin: 0,
              padding: 0,
              "&:hover": {
                backgroundColor: "#d55a1d",
              },
              "& .MuiSvgIcon-root": {
                fontSize: "14px",
              },
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ padding: "1rem", paddingTop: "0px" }}>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Box sx={{ width: "100%" }}>
              <label>
                Institution Name <span className="required">*</span>
              </label>
              <TextField
                fullWidth
                name="instituteName"
                value={educationData.instituteName}
                onChange={handleChange}
                error={Boolean(errors.instituteName)}
                helperText={errors.instituteName}
                sx={{ marginBottom: 2 }}
              />
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>
                Course <span className="required">*</span>
              </label>
              <TextField
                fullWidth
                name="course"
                value={educationData.course}
                onChange={handleChange}
                error={Boolean(errors.course)}
                helperText={errors.course}
                sx={{ marginBottom: 2 }}
              />
            </Box>
          </Box>
          <Box sx={{ display: "flex", gap: 2 }}>
            <Box sx={{ width: "100%" }}>
              <label>
                Start Date <span className="required">*</span>
              </label>
              <TextField
                fullWidth
                type="date"
                name="startDate"
                value={educationData.startDate}
                onChange={handleChange}
                error={Boolean(errors.startDate)}
                helperText={errors.startDate}
                sx={{
                  marginBottom: 2,
                  "& input": {
                    // Apply attributes directly to the input element
                    "&::-webkit-calendar-picker-indicator": {
                      cursor: "pointer",
                    },
                  },
                }}
                // Simple approach without deprecated props
                label=" "
                variant="outlined"
                // We'll handle date validation in the validation logic instead
              />
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>
                End Date <span className="required">*</span>
              </label>
              <TextField
                fullWidth
                type="date"
                name="endDate"
                value={educationData.endDate}
                onChange={handleChange}
                error={Boolean(errors.endDate)}
                helperText={errors.endDate}
                sx={{
                  marginBottom: 2,
                  "& input": {
                    // Apply attributes directly to the input element
                    "&::-webkit-calendar-picker-indicator": {
                      cursor: "pointer",
                    },
                  },
                }}
                // Simple approach without deprecated props
                label=" "
                variant="outlined"
                // We'll handle date validation in the validation logic instead
              />
            </Box>
          </Box>

          <DialogActions
            sx={{ padding: "0px !important", paddingTop: "1rem !important" }}
          >
            <Button
              sx={{
                fontWeight: "400",
                fontSize: "14px",
                color: "#111827",
                border: "1px solid #E5E7EB",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={handleClose}
              color="secondary"
            >
              Cancel
            </Button>
            <Button
              sx={{
                backgroundColor: "#F26522",
                border: "1px solid #F26522",
                color: "#FFF",
                borderRadius: "5px",
                padding: "0.5rem 0.85rem",
                fontSize: "14px",
                transition: "all 0.5s",
                fontWeight: 500,
                textTransform: "none",
              }}
              onClick={handleSave}
              color="primary"
            >
              Save
            </Button>
          </DialogActions>
        </DialogContent>
      </Dialog>
    </>
  );
}

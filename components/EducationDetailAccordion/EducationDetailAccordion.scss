.edu-detail-accordion {
  width: 100%;
  margin-bottom: 24px;


  .<PERSON>i<PERSON>oll<PERSON><PERSON>-entered {
    margin-bottom: 24px !important;
  }

  .accordion-summary {
    .MuiAccordionSummary-content {
      margin: 0px;
    }
  }

  .accordion-details {
    border-top: 1px solid #E5E7EB !important;

    display: flex;
    flex-direction: column;

    .span {
      font-size: 13px;
      color: #677788;
    }

    h6 {
      font-size: 14px;
      font-weight: 500;
      margin-top: 4px;
      color: #202c4b;
    }

    p {
      font-size: 13px;
      color: #212529;
    }
  }
}

.edit-dialog-container {

  label{
    margin-top: 0px !important;
  }
  .MuiPaper-root {
    min-width: 800px;
  }

  .MuiDialogContent-root {
    padding-top: 20px !important;
  }

  .MuiInputBase-root {
    min-height: 38px; // Set the desired height
    max-height: 38px;
  }

  .MuiOutlinedInput-root {
    min-height: 38px; // Set the desired height
    max-height: 38px;
  }

  .MuiInputBase-input,
  .MuiOutlinedInput-input {
    padding: 8px 10px !important;
  }

  .MuiInputLabel-root {
    font-size: 14px !important; // Set the desired font size
    transform: translate(14px, 12px) scale(1); // Adjust the label position
    // Add transition to ensure smooth movement
    transition: all 0.2s ease-out;
  }

  .MuiInputLabel-shrink {
    transform: translate(14px, -6px) scale(0.75); // Adjust the label position when focused
  }

  .MuiFormLabel-root {
    // position: relative !important;
  }

  // Label styling moved to global theme.scss
}

@media screen and (max-width: 767px) {
  .edu-detail-accordion {
    margin-bottom: 0px;
  }
}
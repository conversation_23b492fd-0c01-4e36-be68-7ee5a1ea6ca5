.profile-card {
    margin-bottom: 1.5rem;
    background-color: #FFF;
    transition: all 0.5s ease-in-out;
    position: relative;
    border-radius: 5px;
    border: 1px solid #E5E7EB;
    box-shadow: 0px 1px 1px 1px rgba(198, 198, 198, 0.2);
    color: inherit;

    .profile-card-content {
        padding: 1.25rem;
        flex: 1 1 auto;

        .profile-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;

            .profile-card-avatar {
                border-radius: 4px;
                width: 2.813rem;
                height: 2.813rem;
                line-height: 2.813rem;
                font-size: 1rem;
                margin-right: .5rem;
            }

            .profile-card-info {
                display: flex;
                align-items: center;

                .candidate-name {
                    color: #111827;
                    cursor: pointer;
                    font-size: 1rem;
                    font-weight: 600;
                    margin-right: .25rem;
                }

                .candidate-id-chip {
                    padding: 0.25rem 0.45rem;
                    border-radius: 4px;
                    background-color: #FEF1EB !important;


                    span {
                        font-size: 0.75em !important;
                        font-weight: 600;
                        letter-spacing: 0.5px;
                        color: #F26522 !important;
                        text-transform: none;
                    }
                }
            }

            .candidate-email {
                color: #6B7280;
                font-size: 0.8125rem;
                font-weight: 400;

            }


        }

        .profile-card-details {
            color: #6B7280;
            background-color: #F8F9FA !important;
            border: 1px solid #F8F9FA !important;
            padding: .5rem;

            .profile-card-detail {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: .5rem;

                .profile-card-detail-label {
                    color: #6B7280;
                    font-size: 0.875rem;
                    font-weight: 400;
                }

                .profile-card-detail-value {
                    color: #212529;
                    font-size: 0.875rem;
                    font-weight: 500;
                }
            }
        }
    }
}
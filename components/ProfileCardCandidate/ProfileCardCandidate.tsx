// components/ProfileCard.tsx
import React from "react";
import "./ProfileCardCandidate.scss"; // Import the CSS file for styling
import {
  Card,
  CardContent,
  Typography,
  Avatar,
  Box,
  Chip,
} from "@mui/material";
import { Circle } from "@mui/icons-material";

interface ProfileCardCandidateProps {
  name: string;
  candidateId: string;
  email: string;
  role: string;
  date: string;
  status:
    | "New"
    | "Scheduled"
    | "Interviewed"
    | "Offered"
    | "Hired"
    | "Rejected";
  imageUrl: string;
}

const statusColorMap: Record<string, string> = {
  New: "#AB47BC !important",
  Scheduled: "#FD3995 !important",
  Interviewed: "#1B84FF !important",
  Offered: "#FFC107 !important",
  Hired: "#03C95A !important",
  Rejected: "#E70D0D !important",
};

const ProfileCardCandidate: React.FC<ProfileCardCandidateProps> = ({
  name,
  candidateId,
  email,
  role,
  date,
  status,
  imageUrl,
}) => {
  return (
    <Card className="profile-card">
      <CardContent className="profile-card-content">
        <Box className="profile-card-header">
          <Avatar src={imageUrl} className="profile-card-avatar" />
          <Box>
            <Box className="profile-card-info">
              <Typography className="candidate-name">{name}</Typography>
              <Chip
                className="candidate-id-chip"
                label={candidateId}
                size="small"
              />
            </Box>

            <Typography className="candidate-email">{email}</Typography>
          </Box>
        </Box>

        <Box className="profile-card-details">
          <Box className="profile-card-detail">
            <Typography className="profile-card-detail-label">
              Applied Role
            </Typography>
            <Typography className="profile-card-detail-value">
              {role}
            </Typography>
          </Box>

          <Box className="profile-card-detail">
            <Typography className="profile-card-detail-label">
              Applied Date
            </Typography>
            <Typography className="profile-card-detail-value">
              {date}
            </Typography>
          </Box>

          <Box className="profile-card-detail">
            <Typography className="profile-card-detail-label">
              Status
            </Typography>
            <Chip
              label={status}
              sx={{
                backgroundColor: statusColorMap[status],
                borderRadius: "4px",
                padding: "0.25rem 0.45rem",
                "& span": {
                  color: "#fff",
                  fontSize: "0.75em",
                  
                  fontWeight: 500,
                },
              }}
              size="small"
              icon={
                <Circle
                  sx={{
                    width: "6px",
                    height: "6px",
                    marginLeft: "8px",
                    color: "#FFF !important",
                  }}
                />
              }
            />
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ProfileCardCandidate;

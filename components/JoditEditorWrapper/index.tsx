import { useRef, useMemo } from "react";
import JoditEditor from "jodit-react";
interface JoditEditorWrapperProps {
  height?: number;
  value: string;
  onChange: (content: string) => void;
}
const JoditEditorWrapper = ({
  value: initialValue,
  onChange,
  height,
}: JoditEditorWrapperProps) => {

  //Comment
  const editor = useRef(null);
  const config = useMemo(
    () => ({
      readonly: false,
      height: height || 100,
      toolbar: true,
      buttons: [
        "bold",
        "italic",
        "underline",
        "|",
        "ul",
        "ol",
        "|",
        "font",
        "fontsize",
        "brush",
        "link",
        "image",
        "symbols",
        "|",
        "align",
        "undo",
        "redo",
        "eraser",
        "source",
      ],
      ********************: false,
      showCharsCounter: false,
      showWordsCounter: false,
      uploader: {
        insertImageAsBase64URI: true,
      },
      spellcheck: true,
      toolbarButtonSize: "middle" as const,
    }),
    []
  );
  return (
    <div style={{ marginBottom: "20px" }}>
      <JoditEditor
        ref={editor}
        value={initialValue}
        config={config}
        tabIndex={1}
        onBlur={(newContent) => onChange(newContent)}
      />
    </div>
  );
};
export default JoditEditorWrapper;

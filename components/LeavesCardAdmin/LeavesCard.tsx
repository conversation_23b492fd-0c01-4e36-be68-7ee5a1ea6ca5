import { Card, CardContent, Typography, Box } from "@mui/material";
import { AccountCircleOutlined } from "@mui/icons-material";
import "./LeavesCard.scss";

interface LeavesCardProps {
  title: string;
  value: string | number;
  bgImage: string;
}

const LeavesCard: React.FC<LeavesCardProps> = ({ title, value, bgImage }) => {
  return (
    <Card className="leaves-card">
      {/* Left Icon Section */}
      <Box
        className="Left-icon-section"
        sx={{
          backgroundImage: `url(${bgImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          width: "100px",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          color: "#fff",
        }}
      >
        <Box sx={{ mr: 3 }}>
          <AccountCircleOutlined />
        </Box>
      </Box>

      {/* Right Content */}
      <CardContent
        sx={{
          flexGrow: 1,
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "end",
          padding: "1.25rem !important",
        }}
      >
        <Typography variant="body2" color="textSecondary">
          {title}
        </Typography>
        <Typography variant="h6" fontWeight="bold">
          {value}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default LeavesCard;

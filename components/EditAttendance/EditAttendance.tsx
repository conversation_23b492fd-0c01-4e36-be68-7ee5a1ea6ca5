import React, { useEffect, useState } from "react";
import "./EditAttendance.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  MenuItem,
  Box,
  IconButton,
} from "@mui/material";
import { Cancel, Close } from "@mui/icons-material";
import { useFormik } from "formik";
import * as Yup from "yup";
import { getAttendanceById } from "@/app/services/attendance.service";
import { toast } from "react-toastify";
import Loader from "@/components/Loader/Loader";

interface AttendanceData {
  _id: string;
  empId?: string;
  employee?: string;
  status?: string;
  checkIn?: string;
  checkOut?: string;
  break?: string | number;
  late?: string | number;
  date?: string;
}

interface EditAttendanceModalProps {
  open: boolean;
  onClose: () => void;
  rowData: AttendanceData | null;
  onSave: (updatedData: AttendanceData) => void;
}

// Utility function to format ISO date to YYYY-MM-DDTHH:MM for datetime-local input
const formatDateTimeForInput = (isoDate?: string): string => {
  if (!isoDate || isoDate === "N/A") return "";
  const date = new Date(isoDate);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

// Utility function to format ISO date to YYYY-MM-DD for date input
const formatDateForInput = (isoDate?: string): string => {
  if (!isoDate || isoDate === "N/A") return "";
  return isoDate.split("T")[0]; // Extract YYYY-MM-DD from ISO string
};

// Utility function to parse YYYY-MM-DDTHH:MM back to ISO format
const parseDateTimeToISO = (dateTime: string): string => {
  if (!dateTime) return "";
  return new Date(dateTime).toISOString();
};

// Utility function to parse YYYY-MM-DD back to ISO format
const parseDateToISO = (date: string): string => {
  if (!date) return "";
  return new Date(date).toISOString();
};

const validationSchema = Yup.object().shape({
  date: Yup.string().required("Date is required"),
  checkIn: Yup.string()
    .nullable()
    .test("valid-check-in", "Check-in time is invalid", function (value) {
      if (!value) return true; // Allow empty value

      const date = this.parent.date;
      if (!date) return true; // Skip validation if no date selected

      const checkInDate = new Date(value);
      const selectedDate = new Date(date);

      // Check if check-in is on the same date
      return (
        checkInDate.getFullYear() === selectedDate.getFullYear() &&
        checkInDate.getMonth() === selectedDate.getMonth() &&
        checkInDate.getDate() === selectedDate.getDate()
      );
    }),
  checkOut: Yup.string()
    .nullable()
    .test("valid-check-out", "Check-out time is invalid", function (value) {
      if (!value) return true; // Allow empty value

      const date = this.parent.date;
      if (!date) return true; // Skip validation if no date selected

      const checkOutDate = new Date(value);
      const selectedDate = new Date(date);

      // Check if check-out is on the same date
      return (
        checkOutDate.getFullYear() === selectedDate.getFullYear() &&
        checkOutDate.getMonth() === selectedDate.getMonth() &&
        checkOutDate.getDate() === selectedDate.getDate()
      );
    })
    .test(
      "check-out-after-check-in",
      "Check-out must be after check-in",
      function (value) {
        const { checkIn } = this.parent;
        if (!value || !checkIn) return true; // Skip validation if either value is empty

        const checkInTime = new Date(checkIn).getTime();
        const checkOutTime = new Date(value).getTime();

        return checkOutTime > checkInTime;
      }
    ),
  break: Yup.number().min(0, "Break time cannot be negative").nullable(),
  late: Yup.number().min(0, "Late time cannot be negative").nullable(),
  status: Yup.string().required("Status is required"),
});

const EditAttendanceModal: React.FC<EditAttendanceModalProps> = ({
  open,
  onClose,
  rowData,
  onSave,
}) => {
  const [fetchedData, setFetchedData] = useState<AttendanceData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch attendance data when the dialog opens
  useEffect(() => {
    const fetchAttendanceData = async () => {
      if (rowData?._id) {
        setIsLoading(true);
        try {
          const response = await getAttendanceById(rowData._id);
          console.log("Raw API response:", response);

          // Extract the attendance object from the response
          const data = response.attendanceRecords;
          if (!data) {
            throw new Error("Attendance data not found in API response");
          }

          // Format the fetched data for the form
          const formattedData: AttendanceData = {
            _id: data._id,
            // empId: data.empId,
            employee: rowData.employee,
            status: data.status || (data.isActive ? "Present" : "Absent"),
            checkIn: formatDateTimeForInput(data.checkIn),
            checkOut: formatDateTimeForInput(data.checkOut),
            break: data.break?.toString() || "",
            late: data.late?.toString() || "",
            date: formatDateForInput(data.date),
          };
          console.log("Formatted data for form:", formattedData);
          setFetchedData(formattedData);
        } catch (error) {
          console.error("Failed to fetch attendance data:", error);
          // toast.error("Failed to load attendance data");
        } finally {
          setIsLoading(false);
        }
      }
    };

    if (open) {
      fetchAttendanceData();
    }
  }, [open, rowData]);

  const formik = useFormik({
    initialValues: fetchedData || {
      _id: rowData?._id || "",
      // empId: rowData?.empId || "",
      employee: rowData?.employee || "",
      status: rowData?.status || "Present",
      checkIn: rowData?.checkIn || "",
      checkOut: rowData?.checkOut || "",
      break: rowData?.break?.toString() || "",
      late: rowData?.late?.toString() || "",
      date: rowData?.date || new Date().toISOString().split("T")[0],
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: (values) => {
      const updatedData = {
        ...values,
        checkIn: parseDateTimeToISO(values.checkIn || ""),
        checkOut: parseDateTimeToISO(values.checkOut || ""),
        break: parseInt(values.break as string, 10),
        late: parseInt(values.late as string, 10),
        date: parseDateToISO(values.date!),
      };
      console.log("Submitting updatedData:", updatedData);
      onSave(updatedData);
      onClose();
    },
  });

  return (
    <>
      {isLoading && <Loader loading={isLoading} />}
      <Dialog
        open={open}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            onClose();
          }
        }}
        disableEscapeKeyDown
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle sx={{ display: "flex", justifyContent: "space-between" }}>
          Edit Attendance
          <IconButton
            onClick={onClose}
            sx={{
              backgroundColor: "#6b7280",
              backgroundImage: "none",
              borderRadius: "50%",
              color: "#fff",
              height: "20px",
              width: "20px",
              margin: 0,
              padding: 0,
              "&:hover": {
                backgroundColor: "#d55a1d",
              },
              "& .MuiSvgIcon-root": {
                fontSize: "14px",
              },
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent className="dialog-content">
          <form onSubmit={formik.handleSubmit}>
            <Box>
              <label>
                Employee <span className="required">*</span>
              </label>
              <TextField fullWidth value={formik.values.employee} disabled />
            </Box>
            <Box>
              <label>
                Date <span className="required">*</span>
              </label>
              <TextField
                fullWidth
                name="date"
                type="date"
                value={formik.values.date}
                onChange={formik.handleChange}
                error={formik.touched.date && Boolean(formik.errors.date)}
                helperText={formik.touched.date && formik.errors.date}
              />
            </Box>
            <Box sx={{ display: "flex", gap: "16px" }}>
              <Box sx={{ width: "100%" }}>
                <label>
                  Check In <span className="required">*</span>
                </label>
                <TextField
                  fullWidth
                  name="checkIn"
                  type="datetime-local"
                  value={formik.values.checkIn}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.checkIn && Boolean(formik.errors.checkIn)
                  }
                  helperText={formik.touched.checkIn && formik.errors.checkIn}
                />
              </Box>
              <Box sx={{ width: "100%" }}>
                <label>
                  Check Out <span className="required">*</span>
                </label>
                <TextField
                  fullWidth
                  name="checkOut"
                  type="datetime-local"
                  value={formik.values.checkOut}
                  onChange={formik.handleChange}
                  error={
                    formik.touched.checkOut && Boolean(formik.errors.checkOut)
                  }
                  helperText={formik.touched.checkOut && formik.errors.checkOut}
                />
              </Box>
            </Box>
            <Box sx={{ display: "flex", gap: "16px" }}>
              <Box sx={{ width: "100%" }}>
                <label>
                  Break (minutes) <span className="required">*</span>
                </label>
                <TextField
                  fullWidth
                  name="break"
                  value={formik.values.break}
                  onChange={formik.handleChange}
                  error={formik.touched.break && Boolean(formik.errors.break)}
                  helperText={formik.touched.break && formik.errors.break}
                />
              </Box>
              <Box sx={{ width: "100%" }}>
                <label>
                  Late (minutes) <span className="required">*</span>
                </label>
                <TextField
                  fullWidth
                  name="late"
                  value={formik.values.late}
                  onChange={formik.handleChange}
                  error={formik.touched.late && Boolean(formik.errors.late)}
                  helperText={formik.touched.late && formik.errors.late}
                />
              </Box>
            </Box>
            <Box>
              <label>
                Status <span className="required">*</span>
              </label>
              <TextField
                fullWidth
                name="status"
                select
                value={formik.values.status}
                onChange={formik.handleChange}
                error={formik.touched.status && Boolean(formik.errors.status)}
                helperText={formik.touched.status && formik.errors.status}
              >
                <MenuItem value="Present">Present</MenuItem>
                <MenuItem value="Absent">Absent</MenuItem>
              </TextField>
            </Box>
            <DialogActions>
              <Button
                sx={{
                  fontWeight: "400",
                  fontSize: "14px",
                  color: "#111827",
                  border: "1px solid #E5E7EB",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                onClick={onClose}
              >
                Cancel
              </Button>
              <Button
                sx={{
                  display: "flex",
                  gap: "8px",
                  backgroundColor: "#F26522",
                  borderColor: "#F26522",
                  color: "#FFF",
                  fontWeight: 400,
                  fontSize: "14px",
                  borderRadius: "5px",
                  textTransform: "none",
                  padding: "8px 13.6px",
                }}
                type="submit"
                variant="contained"
              >
                Save Changes
              </Button>
            </DialogActions>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EditAttendanceModal;

.drawer {
  ul {
    padding: 0px 16px;

    .MuiListItem-root {
      &.active {
        background-color: #000000;
        border-radius: 5px;
        // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &::before {
          content: "";
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 2px;
          background-color: #f97316; // Orange indicator
          border-radius: 2px;
        }

        // Change text color for active items
        .MuiListItemText-root span {
          color: #f97316;
          font-weight: 600;
        }
      }

      padding: 4px;
      border-radius: 5px;

      &:hover:not(.active) {
        background-color: rgba(232, 233, 234, 0.5);
      }

      // sidebar icons style for position
      .MuiListItemIcon-root {
        min-width: 0;

        svg {
          width: 14px;
          height: 14px;
        }
      }

      .MuiListItemText-root {
        span {
          font-size: 14px;
          font-weight: 500;
          white-space: nowrap;
          color: #111827;
          margin-left: 8px;
        }
      }
    }

    .MuiCollapse-root {
      .MuiCollapse-wrapper {
        .MuiList-root {
          .MuiListItem-root {
            max-height: 34px;

            .MuiListItemText-root {
              span {
                font-size: 12px;
                position: relative;
                color: #6b7280;
              }
            }

            &:hover:not(.active) {
              background-color: transparent;

              .MuiListItemText-root span {
                color: #f97316;
              }
            }

            &.active {
              background-color: #e8e9ea;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

              .MuiListItemText-root span {
                color: #f97316;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }

  .logo-heading {
    position: fixed;
    z-index: 1;
    background-color: white;
    transition: width 0.3s ease-in-out;
    border-right: 1px solid rgba(0, 0, 0, 0.12);
  }

  .collapse-children {
    ul::before {
      content: "";
      position: absolute;
      background: #f3f4f6;
      width: 1.5px;
      height: 100%;
      left: 14px;
      top: 0;
    }
  }

  // Drawer paper styles
  .MuiDrawer-paper {
    transition: width 0.3s ease-in-out;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
  }

  // List item icon styles
  .list-item-icon {
    &.collapsed {
      min-width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &.expanded {
      min-width: 40px;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
    }
  }

  // List item text styles
  .list-item-text {
    &.hidden {
      display: none;
      transition: display 0.3s ease-in-out;
    }

    &.visible {
      display: block;
      transition: display 0.3s ease-in-out;
    }
  }

  // Expand icons
  .expand-icon {
    width: 16px;
    height: 14px;
  }

  // Footer styles
  .sidebar-footer {
    position: relative;
    min-height: 180px;
    margin-top: auto;
  }

  // Logo link styles
  .logo-link {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    img {
      object-fit: contain;
      max-height: 70px;
      width: auto;
    }
  }

  // Content box styles
  .content-box {
    margin-top: 7rem;
  }
}

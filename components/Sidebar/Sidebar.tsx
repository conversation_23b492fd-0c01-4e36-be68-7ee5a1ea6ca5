"use client";

import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import "./Sidebar.scss";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Collapse,
  Drawer,
  Typography,
} from "@mui/material";
import {
  ExpandLess,
  ExpandMore,
  PeopleOutline,
  CalendarMonth,
  ContactPage,
  School,
  EditNote,
  OpenInNew,
  HighlightOff,
  SpaceDashboard,
  LoginOutlined,
  AssignmentInd,
  Assignment,
  HomeWork,
  Payments,
  ElectricBike,
  PeopleAlt,
  ConfirmationNumber,
  Star,
} from "@mui/icons-material";
import NewspaperIcon from '@mui/icons-material/Newspaper';

import useAuthStore from "@/store/authStore";
import Image from "next/image";
import { getVersion, getCompany } from "@/app/services/setting.service";
import { getDepartments } from "@/app/services/department.service";
import useMediaQuery from "@mui/material/useMediaQuery";
import { ProjectIcon } from "@/public/assets";

// Define interfaces for navigation items
interface NavItem {
  title: string;
  path?: string;
  icon?: React.ReactNode;
  roles?: string[];
  children?: NavItem[];
  alternativePaths?: string[]; // Add this new property
}

const drawerWidth = 251;
const collapsedWidth = 80;

const NAV_ITEMS: NavItem[] = [
  {
    title: "Admin Dashboard",
    path: "/Dashboard/Admin-Dashboard",
    icon: <SpaceDashboard />,
    roles: ["Admin", "HR", "SuperAdmin", "Manager"],
  },
  {
    title: "My Dashboard",
    path: "/Dashboard/Employee-Dashboard",
    icon: <SpaceDashboard />,
    roles: ["Employee"],
  },
  {
    title: "Employee Management",
    path: "",
    icon: <PeopleOutline />,
    children: [
      {
        title: "Employees",
        path: "/employees/employeesList",
        roles: ["Admin", "HR", "SuperAdmin", "Manager"],
        alternativePaths: ["/employees/employeesGrid"],
      },
      // {
      //   title: "Employees Details",
      //   path: "/employees/employeesDetails",
      //   roles: ["Admin", "Employee", "Manager", "HR", "SuperAdmin"],
      // },
      {
        title: "Departments",
        path: "/employees/department",
        roles: ["Admin", "HR", "SuperAdmin"],
      },
      {
        title: "Designation",
        path: "/employees/designation",
        roles: ["Admin", "HR", "SuperAdmin"],
      },
      {
        title: "Company Policies",
        path: "/employees/policies",
        roles: ["Admin", "HR", "SuperAdmin", "Employee"],
      },
    ],
  },
  {
    title: "Tickets",
    path: "",
    icon: <ConfirmationNumber />,
    children: [
      {
        title: "Tickets",
        path: "/tickets",
        roles: ["Admin", "Employee", "Manager", "HR", "SuperAdmin"],
      },
      {
        title: "Tickets Category",
        path: "/ticketcategory",
        roles: ["Admin", "Manager", "HR", "SuperAdmin"],
      },
    ],
  },
  {
    title: "Holiday Calendar",
    path: "/holidays",
    icon: <CalendarMonth />,
    roles: ["Admin", "Employee", "Manager", "HR", "SuperAdmin"],
  },
  {
    title: "Assets Management",
    path: "/assets",
    icon: <Assignment />,
    roles: ["Admin", "Manager", "HR", "SuperAdmin"],
  },
  // {
  //   title: "Calendar",
  //   path: "/calendar",
  //   icon: <CalendarMonth />,
  //   roles: ["Admin", "Employee", "Manager", "HR", "SuperAdmin"],
  // },
  {
    title: "Attendance",
    path: "",
    icon: <ContactPage />,
    children: [
      {
        title: "Leave Management",
        path: "",
        children: [
          {
            title: "Leave Approvals",
            path: "/Attendance/leaves/leaves-admin",
            roles: ["Admin", "HR", "SuperAdmin", "Manager"],
          },
          {
            title: "My Leaves",
            path: "/Attendance/leaves/leaves-employee",
            roles: ["Employee", "Manager", "HR", "Admin"],
          },
          {
            title: "Leave Settings",
            path: "/Attendance/leaves/leaves-settings",
            roles: ["Admin", "HR", "SuperAdmin", "Manager"],
          },
        ],
      },
      {
        title: "Attendance Dashboard",
        path: "/Attendance/attendance-admin",
        roles: ["Admin", "HR", "SuperAdmin"],
      },
      {
        title: "My Attendance",
        path: "/Attendance/attendance-employee",
        roles: ["Employee", "Admin", "HR", "Manager"],
      },
    ],
  },
  {
    title: "Performance Management",
    path: "",
    icon: <School />,
    children: [
      {
        title: "KPI Metrics",
        path: "/performance/performance-indicator",
        roles: ["Admin", "Manager", "HR", "SuperAdmin"],
      },
      {
        title: "Employee Reviews",
        path: "/performance/performance-review",
        roles: ["Admin", "Manager", "HR", "SuperAdmin", "Employee"],
      },
      {
        title: "Performance Review List",
        path: "/performance/performancereviewlist",
        roles: ["Admin", "Manager", "HR", "SuperAdmin"],
      },
      {
        title: "Appraisal Process",
        path: "/performance/performance-appraisal",
        roles: ["Admin", "Manager", "HR", "SuperAdmin"],
      },
      {
        title: "Goals",
        path: "/performance/goals",
        roles: ["Admin", "Manager", "HR", "SuperAdmin"],
      },
      {
        title: "Goal Type",
        path: "/performance/goaltype",
        roles: ["Admin", "Manager", "HR", "SuperAdmin"],
      },
    ],
  },
  {
    title: "Client",
    path: "/client",
    icon: <AssignmentInd />,
    roles: ["Admin", "Manager", "HR", "SuperAdmin"],
  },
  {
    title: "Projects",
    path: "",
    icon: <HomeWork />,
    children: [
      {
        title: "Projects",
        path: "/projects",
        roles: ["Admin", "Manager", "HR", "Employee", "SuperAdmin"],
      },
      {
        title: "Tasks",
        path: "/tasks",
        roles: ["Admin", "Manager", "HR", "SuperAdmin"],
      },
       {
        title: "Task Board",
        path: "/taskboard",
        roles: ["Admin", "Manager", "HR", "SuperAdmin"],
      },
    ],
  },
 {
    title: "Time Sheet",
    path: "/timesheet",
    icon: <NewspaperIcon />,
    roles: ["Admin", "Employee", "Manager", "HR", "SuperAdmin"],
  },
  {
    title: "Recruitment",
    path: "",
    icon: <ElectricBike />,
    children: [
      {
        title: "Jobs",
        path: "/jobs",
        roles: ["Admin", "Manager", "HR", "SuperAdmin"],
      },
      {
        title: "Candidates",
        path: "/candidates",
        icon: <PeopleAlt />,
        roles: ["Admin", "HR", "SuperAdmin", "Manager"],
      },
      {
        title: "Referrals",
        path: "/referrals",
        icon: <PeopleAlt />,
        roles: ["Admin", "HR", "SuperAdmin", "Employee", "Manager"],
      },
    ],
  },
  {
    title: "Training & Development",
    icon: <EditNote />,
    children: [
      {
        title: "Training Programs",
        path: "/training",
        roles: ["Admin", "Employee", "Manager", "HR", "SuperAdmin"],
      },
      {
        title: "Trainers",
        path: "/trainers",
        roles: ["Admin", "HR", "SuperAdmin"],
      },
      {
        title: "Training Categories",
        path: "/trainingtype",
        roles: ["Admin", "HR", "SuperAdmin"],
      },
    ],
  },
  {
    title: "Promotion",
    path: "/promotion",
    icon: <Star />,
    roles: ["Admin", "HR", "Manager", "SuperAdmin"],
  },
  {
    title: "Resignation Portal",
    path: "/resignation",
    icon: <OpenInNew />,
    roles: ["Admin", "Employee", "HR", "SuperAdmin"],
  },
  {
    title: "Employment Termination",
    path: "/termination",
    icon: <HighlightOff />,
    roles: ["Admin", "HR", "SuperAdmin", "Employee"],
  },
  {
    title: "Payroll",
    path: "",
    icon: <Payments />,
    children: [
      {
        title: "Employee Salary",
        path: "/payroll/employee-salary",
        roles: ["Admin", "HR", "SuperAdmin"],
      },
      {
        title: "Payslip",
        path: "/payroll/payslip",
        roles: ["Admin", "HR", "SuperAdmin"],
      },
      {
        title: "Payroll Items",
        path: "/payroll/payroll-items",
        roles: ["Admin", "HR", "SuperAdmin"],
      },
    ],
  },

  /* {
    title: "User Management",
    path: "",
    icon: <HighlightOff />,
    children: [
      {
        title: "User",
        path: "/user-management/users",
        roles: ["Admin", "SuperAdmin"],
      },
      {
        title: "Roles & Permissions",
        path: "/user-management/roles",
        roles: ["Admin", "SuperAdmin"],
      },
    ],
  }, */
];

interface SidebarProps {
  isCollapsed: boolean;
  isMobileOpen?: boolean;
  onMobileClose?: () => void;
}

interface VersionData {
  versionNumber: string;
}

export default function Sidebar({
  isCollapsed,
  isMobileOpen,
  onMobileClose,
}: SidebarProps) {
  const isTablet = useMediaQuery("(max-width:1199px)");
  const [openMenus, setOpenMenus] = useState<string[]>([]);
  const [isHovered, setIsHovered] = useState(false);
  const [versionNum, setVersionNum] = useState<VersionData | null>(null);
  const pathname = usePathname();
  const { roles } = useAuthStore(); // Get roles from Zustand store
  const router = useRouter();
  const [companyLogo, setCompanyLogo] = useState<string>(
    "/assets/Aadvik-noBG.png"
  );

  const handleLogout = () => {
    sessionStorage.removeItem("token");
    sessionStorage.removeItem("userEmail");
    router.push("/login");
    window.location.reload();
  };

  useEffect(() => {
    const activeItem = NAV_ITEMS.find((item) =>
      item.children?.some((child) => child.path === pathname)
    );
    if (activeItem) {
      setOpenMenus([activeItem.title]);
    }
  }, [pathname]);

  const handleToggle = (menuTitle: string) => {
    setOpenMenus((prevOpenMenus) =>
      prevOpenMenus.includes(menuTitle)
        ? prevOpenMenus.filter((title) => title !== menuTitle)
        : [...prevOpenMenus, menuTitle]
    );
  };

  useEffect(() => {
    const fetchVersionData = async () => {
      try {
        const response = await getVersion();
        console.log("Version Response", response);
        if (response && response.version) {
          setVersionNum({
            versionNumber: response.version.version,
          });
        }
      } catch (error) {
        console.log("failed to fetch Version Data:", error);
      }
    };
    fetchVersionData();
  }, []);

  useEffect(() => {
    const fetchCompanyData = async () => {
      try {
        const response = await getCompany();
        if (response && response.company && response.company.logoUrl) {
          setCompanyLogo(response.company.logoUrl);
        }
      } catch (error) {
        console.log("Failed to fetch company logo:", error);
        // Keep default logo if fetch fails
      }
    };
    fetchCompanyData();
  }, []);

  // Check if any child is active
  const isChildActive = (children: NavItem[] | undefined): boolean => {
    return children
      ? children.some(
          (child) => child.path === pathname || isChildActive(child.children)
        )
      : false;
  };

  // Filter navigation items based on user roles
  const filterItemsByRole = (items: NavItem[]): NavItem[] => {
    return items
      .map((item) => {
        if (item.children) {
          const filteredChildren = filterItemsByRole(item.children);
          if (filteredChildren.length === 0) return null;
          return { ...item, children: filteredChildren };
        }
        if (
          item.roles &&
          !item.roles.some((role: string) => roles.includes(role))
        ) {
          return null;
        }
        return item;
      })
      .filter((item): item is NavItem => item !== null); // Type guard to filter out null
  };

  const renderMenuItems = (
    items: NavItem[],
    level: number = 0
  ): React.ReactNode => {
    return items.map((item) => {
      const isActive = pathname === item.path || isChildActive(item.children);
      const isParentActive = isChildActive(item.children);
      return (
        <React.Fragment key={item.title}>
          <ListItem
            component={item.path ? Link : "div"}
            href={item.path || "#"}
            onClick={() => {
              if (item.children) {
                handleToggle(item.title);
              }
            }}
            className={isActive ? "active" : ""}
            sx={{
              backgroundColor:
                isParentActive && !item.children ? "#e5e7eb" : "inherit",
            }}
          >
            {level === 0 && (
              <ListItemIcon
                className={
                  isCollapsed && !isHovered
                    ? "list-item-icon collapsed"
                    : "list-item-icon expanded"
                }
              >
                {item.icon}
              </ListItemIcon>
            )}
            <ListItemText
              primary={item.title}
              className={
                isCollapsed && !isHovered
                  ? "list-item-text hidden"
                  : "list-item-text visible"
              }
            />
            {item.children &&
              (!isCollapsed || isHovered) &&
              (openMenus.includes(item.title) ? (
                <ExpandLess className="expand-icon" />
              ) : (
                <ExpandMore className="expand-icon" />
              ))}
          </ListItem>

          {item.children && (!isCollapsed || isHovered) && (
            <Collapse
              className="collapse-children"
              in={openMenus.includes(item.title)}
              timeout="auto"
              unmountOnExit
            >
              <List disablePadding>
                {renderMenuItems(item.children, level + 1)}
              </List>
            </Collapse>
          )}
        </React.Fragment>
      );
    });
  };

  const filteredNavItems = filterItemsByRole(NAV_ITEMS);

  return (
    <Drawer
      className="drawer"
      variant={isTablet ? "temporary" : "permanent"}
      open={isTablet ? isMobileOpen : true}
      onClose={onMobileClose}
      sx={{
        width: isCollapsed && !isHovered ? collapsedWidth : drawerWidth,
        display: { xs: "block", lg: "block" },
        "& .MuiDrawer-paper": {
          width: isCollapsed && !isHovered ? collapsedWidth : drawerWidth,
        },
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Box
        p={2}
        className="logo-heading"
        sx={{
          width: isCollapsed && !isHovered ? collapsedWidth : drawerWidth,
        }}
      >
        <Typography variant="h6">
          <Link href="/" className="logo logo-link">
            <Image
              src={companyLogo}
              alt="Company Logo"
              width={isCollapsed && !isHovered ? 50 : 150}
              height={isCollapsed && !isHovered ? 50 : 50}
              style={{
                maxWidth: isCollapsed && !isHovered ? "50px" : "150px",
              }}
            />
          </Link>
        </Typography>
      </Box>
      <Box className="content-box">
        <List>{renderMenuItems(filteredNavItems)}</List>
      </Box>

      <Box className="sidebar-footer">
        {/* Version info */}
        {/* <Box
          sx={{
            position: "relative", // Changed from fixed to relative
            zIndex: 1,
            width: isCollapsed && !isHovered ? collapsedWidth : drawerWidth,
            transition: "width 0.3s ease-in-out",
            padding: "10px",
            textAlign: "center",
            backgroundColor: "#fff", // Add background color
            marginBottom: "50px", // Space for logout button
          }}
        >
          <Typography
            variant="body2"
            sx={{
              fontSize: "12px",
              color: "#000",
              display: isCollapsed && !isHovered ? "none" : "block",
            }}
          >
            {versionNum?.versionNumber}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontSize: "12px",
              color: "#000",
              display: isCollapsed && !isHovered ? "none" : "block",
            }}
          >
            © 2023 Aadvik HRMS. All rights reserved.
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontSize: "12px",
              color: "#000",
              display: isCollapsed && !isHovered ? "none" : "block",
            }}
          >
            Developed by Aadvik HRMS
          </Typography>
        </Box> */}

        {/* Logout */}
        {/* <Box
          sx={{
            position: "fixed",
            bottom: 0,
            left: 0,
            zIndex: 2,
            width: isCollapsed && !isHovered ? collapsedWidth : drawerWidth,
            transition: "width 0.3s ease-in-out",
            padding: "10px",
            textAlign: "center",
            backgroundColor: "#fff", // Add background color
          }}
        >
          <Box
            className="card-footer"
            sx={{
              borderTop: "1px solid",
              borderColor: "rgb(229, 231, 235)",
              background: "#3B7080",
              color: "#fff",
              borderRadius: "5px",
              padding: "0.5rem 1.25rem",
              display: "flex",
              alignItems: "center",
              justifyContent:
                isCollapsed && !isHovered ? "center" : "flex-start",
            }}
          >
            <Link
              className="dropdown-item"
              onClick={handleLogout}
              style={{
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                width: "100%",
                justifyContent: "center",
              }}
              href={""}
            >
              <LoginOutlined
                sx={{
                  width: "14px",
                  height: "14px",
                  display: "flex",
                  justifyContent: "center",
                }}
              />
              {(!isCollapsed || isHovered) && <span>Log out</span>}
            </Link>
          </Box>
        </Box> */}
      </Box>
    </Drawer>
  );
}

import { DataGrid } from "@mui/x-data-grid";
import { styled } from "@mui/material/styles";
export const CustomDataGrid = styled(DataGrid)(({}) => ({
  border: "none",
  "& .MuiDataGrid-filler": {
    display: "none !important",
  },
  "& .MuiDataGrid-row": {
    minHeight: "53px !important",
    maxHeight: "none !important",
    "&:hover": {
      backgroundColor: "#EAEAEA",
    },
  },
  "& .MuiDataGrid-cell": {
    minHeight: "53px !important",
    maxHeight: "53px !important",
    display: "flex !important",
    alignItems: "center !important",
    padding: "10px 20px !important",
    "&:hover": {
      backgroundColor: "#EAEAEA",
    },
    "&:focus, &:focus-within": {
      outline: "none !important",
    },
    "&.MuiDataGrid-iconButtonContainer": {
      display: "none !important",
    },
  },
  "& .MuiDataGrid-columnHeaders": {
    minHeight: "42.9px !important",
    maxHeight: "42.9px !important",
    backgroundColor: "#F9FAFB",
  },
  "& .MuiDataGrid-columnHeader": {
    minHeight: "42.9px !important",
    maxHeight: "42.9px !important",
    display: "flex !important",
    alignItems: "center !important",
    padding: "0 !important",
    "& .MuiDataGrid-columnHeaderTitleContainer": {
      display: "flex !important",
      alignItems: "center !important",
      width: "100% !important",
    },
  },

  // Styles for center-aligned columns including Department and Designation
  "& .MuiDataGrid-cell[data-field='actions']": {
    justifyContent: "center !important",
  },
  // Header alignment for center-aligned columns
  "& .MuiDataGrid-columnHeader[data-field='actions']": {
    justifyContent: "center !important",
    "& .MuiDataGrid-columnHeaderTitleContainer": {
      justifyContent: "center !important",
      "& .MuiDataGrid-columnHeaderTitleContainerContent": {
        justifyContent: "center !important",
        "& .MuiDataGrid-columnHeaderTitle": {
          textAlign: "center !important",
          width: "100% !important",
        },
      },
    },
  },

  // "& .MuiDataGrid-columnSeparator": {
  //   color: "#111827 !important",
  // },
  "& .MuiDataGrid-columnHeaderTitle": {
    textAlign: "center !important",
    width: "100% !important",
    fontWeight: "600 !important",
  },
  // Remove search bar underline and set font size
  "& .grid-search": {
    "& .MuiInputBase-root": {
      fontSize: "0.8rem",
      "&:before": {
        borderBottom: "none !important",
      },
      "&:after": {
        borderBottom: "none !important",
      },
      "&:hover:before": {
        borderBottom: "none !important",
      },
    },
  },
  "@media (max-width: 768px)": {
    "& .MuiDataGrid-root": {
      minWidth: "100%",
      overflowX: "auto",
    },
    "& .MuiDataGrid-virtualScroller": {
      overflowX: "auto !important",
      "& .MuiDataGrid-virtualScrollerContent": {
        minWidth: "max-content !important",
      },
    },
    "& .MuiDataGrid-columnHeader": {
      minWidth: "150px !important", // Minimum width for each column
      whiteSpace: "nowrap",
    },
    "& .MuiDataGrid-cell": {
      minWidth: "150px !important", // Match column header width
      whiteSpace: "normal !important",
      overflow: "visible !important",
      textOverflow: "clip !important",
    },
    // Ensure consistent column widths
    "& .MuiDataGrid-columnHeaders": {
      minWidth: "max-content !important",
    },
    // Enable smooth scrolling on touch devices
    "& *": {
      WebkitOverflowScrolling: "touch",
    },
  },

  // Add specific styles for the Reason column
  "& .MuiDataGrid-cell[data-field='reason']": {
    minHeight: "auto !important",
    maxHeight: "none !important",
    height: "auto !important",
    whiteSpace: "normal !important",
    lineHeight: "1.5 !important",
    padding: "16px 20px !important",
    alignItems: "flex-start !important",
    "& div": {
      whiteSpace: "pre-wrap !important",
      wordBreak: "break-word !important",
      overflowWrap: "break-word !important",
      width: "100% !important",
    },
  },

  // Make reason column header match the content
  "& .MuiDataGrid-columnHeader[data-field='reason']": {
    height: "auto !important",
    maxHeight: "none !important",
    whiteSpace: "normal !important",
  },

  // Make the row height dynamic only when reason column has expanded content
  "& .MuiDataGrid-row:has(.MuiDataGrid-cell[data-field='reason']:not(:empty))":
    {
      height: "auto !important",
      minHeight: "47px !important",
    },

  // Additional styles to ensure proper row height calculation
  "& .MuiDataGrid-virtualScroller": {
    "& .MuiDataGrid-virtualScrollerContent": {
      height: "auto !important",
    },
    "& .MuiDataGrid-virtualScrollerRenderZone": {
      height: "auto !important",
    },
  },
}));

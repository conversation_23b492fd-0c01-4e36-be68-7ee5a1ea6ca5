import React, { useState } from "react";
import { Box, Button, Typography } from "@mui/material";

interface ReadMoreProps {
  text: string;
  maxChars?: number;
  sx?: object;
}

const ReadMore: React.FC<ReadMoreProps> = ({
  text,
  maxChars = 200,
  sx = {},
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!text) return null;

  // Remove HTML tags for character count
  const stripHtml = (html: string) => {
    const tmp = document.createElement("div");
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || "";
  };

  const plainText = stripHtml(text);
  const needsReadMore = plainText.length > maxChars;

  // If text is shorter than maxChars, just display it
  if (!needsReadMore) {
    return <div dangerouslySetInnerHTML={{ __html: text }} />;
  }

  // For collapsed state, find the last word boundary before maxChars
  const truncatedHtml = isExpanded ? text : truncateHtml(text, maxChars);

  return (
    <Box
      sx={{
        width: "100%",
        backgroundColor: "transparent !important",
        "& *": {
          backgroundColor: "transparent !important",
        },
        ...sx,
      }}
    >
      <Typography
        sx={{
          fontSize: "14px",
          width: "100%",
          backgroundColor: "transparent !important",
          whiteSpace: "normal",
          display: "inline-flex",
          flexWrap: "wrap",
          alignItems: "center",
          "&:hover": {
            backgroundColor: "transparent !important",
          },
        }}
      >
        <div
          dangerouslySetInnerHTML={{ __html: truncatedHtml }}
          style={{
            display: "inline",
            marginRight: "4px",
            wordBreak: "break-word",
          }}
        />
        {!isExpanded && "..."}
        <Button
          onClick={() => setIsExpanded(!isExpanded)}
          sx={{
            p: 0,
            minWidth: "auto",
            textTransform: "none",
            backgroundColor: "transparent !important",
            whiteSpace: "nowrap",
            color: "#F26522",
            "&:hover": {
              backgroundColor: "transparent !important",
            },
          }}
        >
          {isExpanded ? " read less" : " read more"}
        </Button>
      </Typography>
    </Box>
  );
};

// Helper function to truncate HTML content
const truncateHtml = (html: string, maxChars: number): string => {
  const div = document.createElement("div");
  div.innerHTML = html;

  let charCount = 0;
  let truncated = "";

  function processNode(node: Node) {
    if (charCount >= maxChars) return false;

    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent || "";
      const remainingChars = maxChars - charCount;
      if (charCount + text.length <= maxChars) {
        truncated += text;
        charCount += text.length;
      } else {
        // Find the last word boundary within remainingChars
        const partialText = text.substr(0, remainingChars);
        const lastSpace = partialText.lastIndexOf(" ");
        truncated +=
          lastSpace !== -1 ? partialText.substr(0, lastSpace) : partialText;
        charCount = maxChars;
        return false;
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      truncated += `<${(node as Element).tagName.toLowerCase()}>`;
      for (let i = 0; i < node.childNodes.length; i++) {
        if (processNode(node.childNodes[i]) === false) {
          break;
        }
      }
      truncated += `</${(node as Element).tagName.toLowerCase()}>`;
    }
    return true;
  }

  for (let i = 0; i < div.childNodes.length; i++) {
    if (processNode(div.childNodes[i]) === false) {
      break;
    }
  }

  return truncated;
};

export default ReadMore;

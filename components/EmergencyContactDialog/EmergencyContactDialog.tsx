"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  TextField,
  Button,
  Box,
  Typography,
  MenuItem,
} from "@mui/material";
import { Cancel, Close as CloseIcon } from "@mui/icons-material";
import IconButton from "@mui/material/IconButton";
import {
  addEmergencyContact,
  updateEmergencyContact,
} from "@/app/services/users.service";
import { toast } from "react-toastify";
import * as Yup from "yup";

enum RelationshipType {
  Father = "Father",
  Mother = "Mother",
  Son = "Son",
  Brother = "Brother",
  Sister = "Sister",
  Wife = "Wife",
  Friend = "Friend",
}

interface EmergencyContactData {
  primaryName: string;
  primaryRelationship: string;
  primaryPhone1: string;
  primaryPhone2: string;
  secondaryName: string;
  secondaryRelationship: string;
  secondaryPhone1: string;
  secondaryPhone2: string;
  primaryId?: string;
  secondaryId?: string;
}

interface EmergencyContactDialogProps {
  open: boolean;
  onClose: () => void;
  initialData: EmergencyContactData;
  onSave: (data: EmergencyContactData) => void;
  employeeId: string | null;
}

const validationSchema = Yup.object().shape({
  primaryName: Yup.string().required("Name is required"),
  primaryRelationship: Yup.string().required("Relationship is required"),
  primaryPhone1: Yup.string()
    .required("Phone number is required")
    .matches(/^\d+$/, "Phone number should only contain digits")
    .length(10, "Phone number must be exactly 10 digits"),
  primaryPhone2: Yup.string()
    .matches(/^\d+$/, "Phone number should only contain digits")
    .length(10, "Phone number must be exactly 10 digits")
    .nullable(),
  // Remove validation for secondary contact fields
  secondaryName: Yup.string(),
  secondaryPhone1: Yup.string(),
  secondaryPhone2: Yup.string(),
  secondaryRelationship: Yup.string(),
});

const styles = {
  requiredIndicator: {
    color: "red",
    ml: 0.5,
  },
  fieldLabel: {
    fontSize: "14px",
    mb: 1,
    display: "flex",
    alignItems: "center",
  },
  formRow: {
    display: "grid",
    gridTemplateColumns: "1fr 1fr",
    gap: "16px",
    mb: 2,
  },
  dialogContent: {
    padding: "24px",
  },
};

const EmergencyContactDialog: React.FC<EmergencyContactDialogProps> = ({
  open,
  onClose,
  initialData,
  onSave,
  employeeId,
}) => {
  const [formData, setFormData] = useState<EmergencyContactData>(initialData);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});

  // Reset errors when dialog opens/closes
  useEffect(() => {
    if (open) {
      setFormData(initialData);
      setErrors({});
      setTouched({});
    }
  }, [open, initialData]);

  const validateField = async (name: string, value: string) => {
    try {
      await validationSchema.validateAt(name, { [name]: value });
      setErrors((prev) => ({ ...prev, [name]: "" }));
      return true;
    } catch (error: any) {
      setErrors((prev) => ({ ...prev, [name]: error.message }));
      return false;
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Only validate primary phone numbers
    if (name === "primaryPhone1" || name === "primaryPhone2") {
      // Only allow digits
      if (!/^\d*$/.test(value)) {
        return;
      }
      // Limit to 10 digits
      if (value.length > 10) {
        return;
      }
    }

    setFormData((prev) => ({ ...prev, [name]: value }));
    setTouched((prev) => ({ ...prev, [name]: true }));
    validateField(name, value);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setTouched((prev) => ({ ...prev, [name]: true }));
    validateField(name, value);
  };

  const validateForm = async () => {
    try {
      await validationSchema.validate(formData, { abortEarly: false });
      return true;
    } catch (error: any) {
      const validationErrors: { [key: string]: string } = {};
      error.inner.forEach((err: any) => {
        if (err.path) {
          validationErrors[err.path] = err.message;
          // Mark all fields with errors as touched
          setTouched((prev) => ({ ...prev, [err.path]: true }));
        }
      });
      setErrors(validationErrors);
      return false;
    }
  };

  const handleSubmit = async () => {
    // Validate all fields before submission
    const isValid = await validateForm();
    if (!isValid) {
      // toast.error("Please correct the errors before submitting");
      return;
    }

    if (!employeeId) {
      // toast.error("Unable to save contact details. Please try again.");
      return;
    }

    try {
      const updatedData = { ...formData };

      const primaryContact = {
        name: formData.primaryName,
        relationship: formData.primaryRelationship,
        phone1: formData.primaryPhone1,
        phone2: formData.primaryPhone2 || "",
        userId: employeeId,
      };

      if (formData.primaryId) {
        // Update existing contact
        try {
          const response = await updateEmergencyContact(
            formData.primaryId,
            primaryContact
          );

          if (response) {
            // toast.success("Emergency contact updated successfully");
            onSave(updatedData);
            onClose();
          } else {
            // toast.error("Unable to update emergency contact. Please try again.");
          }
        } catch (error) {
          console.error("Failed to update primary contact:", error);
          // toast.error("Unable to update emergency contact. Please try again.");
          return;
        }
      } else {
        // Add new contact
        try {
          const response = await addEmergencyContact(primaryContact);

          if (response) {
            // toast.success("Emergency contact added successfully");
            onSave(updatedData);
            onClose();
          } else {
            // toast.error("Unable to add emergency contact. Please try again.");
          }
        } catch (error) {
          console.error("Failed to add primary contact:", error);
          // toast.error("Unable to add emergency contact. Please try again.");
          return;
        }
      }
    } catch (error) {
      console.error("Failed to save emergency contact:", error);
      // toast.error("Unable to save emergency contact. Please try again.");
    }
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          width: "800px",
          // height: "570px",
          maxWidth: "800px",
          maxHeight: "570px",
          borderRadius: "8px",
          m: 0,
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography
          sx={{ fontSize: "20px", fontWeight: "600", color: "#202C4B" }}
        >
          Emergency Contact Details
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: "#6B7280",
            bgcolor: "#F3F4F6",
            borderRadius: "50%",
            padding: "4px",
            "&:hover": {
              bgcolor: "#E5E7EB",
            },
          }}
        >
          <Cancel />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ padding: "1rem !important" }}>
        <Typography
          sx={{ fontSize: "16px", fontWeight: 600, color: "#202C4B", mb: 2 }}
        >
          Primary Contact Details
        </Typography>
        <Box sx={styles.formRow}>
          <Box>
            <Typography sx={styles.fieldLabel}>
              Name
              <Box component="span" sx={styles.requiredIndicator}>
                *
              </Box>
            </Typography>
            <TextField
              name="primaryName"
              value={formData.primaryName}
              onChange={handleChange}
              required
              fullWidth
              size="small"
              placeholder="Enter name"
              InputLabelProps={{ shrink: false }}
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: "4px" } }}
            />
          </Box>
          <Box>
            <Typography sx={styles.fieldLabel}>
              Relationship
              <Box component="span" sx={styles.requiredIndicator}>
                *
              </Box>
            </Typography>
            <TextField
              select
              name="primaryRelationship"
              value={formData.primaryRelationship}
              onChange={handleChange}
              required
              fullWidth
              size="small"
              placeholder="Select relationship"
              InputLabelProps={{ shrink: false }}
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: "4px" } }}
            >
              {Object.values(RelationshipType).map((relation) => (
                <MenuItem key={relation} value={relation}>
                  {relation}
                </MenuItem>
              ))}
            </TextField>
          </Box>
        </Box>
        <Box sx={styles.formRow}>
          <Box>
            <Typography sx={styles.fieldLabel}>
              Phone No 1
              <Box component="span" sx={styles.requiredIndicator}>
                *
              </Box>
            </Typography>
            <TextField
              name="primaryPhone1"
              value={formData.primaryPhone1}
              onChange={handleChange}
              required
              fullWidth
              size="small"
              placeholder="Enter phone number"
              InputLabelProps={{ shrink: false }}
              error={!formData.primaryPhone1.match(/^\d{10}$/)}
              helperText={
                !formData.primaryPhone1.match(/^\d{10}$/) &&
                "Please enter a valid 10-digit phone number"
              }
              inputProps={{
                maxLength: 10,
                inputMode: "numeric",
                pattern: "[0-9]*",
              }}
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: "4px" } }}
            />
          </Box>
          <Box>
            <Typography sx={styles.fieldLabel}>Phone No 2</Typography>
            <TextField
              name="primaryPhone2"
              value={formData.primaryPhone2 || ""}
              onChange={handleChange}
              fullWidth
              size="small"
              placeholder="Enter phone number"
              InputLabelProps={{ shrink: false }}
              error={
                Boolean(formData.primaryPhone2) &&
                !formData.primaryPhone2?.match(/^\d{10}$/)
              }
              helperText={
                Boolean(formData.primaryPhone2) &&
                !formData.primaryPhone2?.match(/^\d{10}$/)
                  ? "Please enter a valid 10-digit phone number"
                  : ""
              }
              inputProps={{
                maxLength: 10,
                inputMode: "numeric",
                pattern: "[0-9]*",
              }}
              sx={{ "& .MuiOutlinedInput-root": { borderRadius: "4px" } }}
            />
          </Box>
        </Box>

        {/* Commented out Secondary Contact Details */}
        {/*
        <Typography sx={{ fontSize: "16px", fontWeight: 600, color: "#202C4B", mb: 2 }}>
          Secondary Contact Details
        </Typography>
        <Box>
          <Box sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "16px", mb: 2 }}>
            <Box>
              <Typography sx={{ fontSize: "14px", mb: 1 }}>
                Name <span style={{ color: "red" }}>*</span>
              </Typography>
              <TextField
                name="secondaryName"
                value={formData.secondaryName}
                onChange={handleChange}
                required
                fullWidth
                size="small"
                placeholder="Enter name"
                InputLabelProps={{ shrink: false }}
                sx={{ "& .MuiOutlinedInput-root": { borderRadius: "4px" } }}
              />
            </Box>
            <Box>
              <Typography sx={{ fontSize: "14px", mb: 1 }}>Relationship</Typography>
              <TextField
                select
                name="secondaryRelationship"
                value={formData.secondaryRelationship}
                onChange={handleChange}
                fullWidth
                size="small"
                placeholder="Select relationship"
                InputLabelProps={{ shrink: false }}
                sx={{ "& .MuiOutlinedInput-root": { borderRadius: "4px" } }}
              >
                {Object.values(RelationshipType).map((relation) => (
                  <MenuItem key={relation} value={relation}>
                    {relation}
                  </MenuItem>
                ))}
              </TextField>
            </Box>
          </Box>
          <Box sx={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "16px" }}>
            <Box>
              <Typography sx={{ fontSize: "14px", mb: 1 }}>
                Phone No 1 <span style={{ color: "red" }}>*</span>
              </Typography>
              <TextField
                name="secondaryPhone1"
                value={formData.secondaryPhone1}
                onChange={handleChange}
                required
                fullWidth
                size="small"
                placeholder="Enter phone number"
                InputLabelProps={{ shrink: false }}
                error={formData.secondaryPhone1 && !formData.secondaryPhone1.match(/^\d{10}$/)}
                helperText={formData.secondaryPhone1 && !formData.secondaryPhone1.match(/^\d{10}$/) && "Please enter a valid 10-digit phone number"}
                inputProps={{
                  maxLength: 10,
                  inputMode: "numeric",
                  pattern: "[0-9]*"
                }}
                sx={{ "& .MuiOutlinedInput-root": { borderRadius: "4px" } }}
              />
            </Box>
            <Box>
              <Typography sx={{ fontSize: "14px", mb: 1 }}>Phone No 2</Typography>
              <TextField
                name="secondaryPhone2"
                value={formData.secondaryPhone2}
                onChange={handleChange}
                fullWidth
                size="small"
                placeholder="Enter phone number"
                InputLabelProps={{ shrink: false }}
                error={formData.secondaryPhone2 && !formData.secondaryPhone2.match(/^\d{10}$/)}
                helperText={formData.secondaryPhone2 && !formData.secondaryPhone2.match(/^\d{10}$/) && "Please enter a valid 10-digit phone number"}
                inputProps={{
                  maxLength: 10,
                  inputMode: "numeric",
                  pattern: "[0-9]*"
                }}
                sx={{ "& .MuiOutlinedInput-root": { borderRadius: "4px" } }}
              />
            </Box>
          </Box>
        </Box>
        */}
      </DialogContent>
      <DialogActions
        sx={{
          padding: "16px 24px",
          borderTop: "1px solid #E5E7EB",
          justifyContent: "flex-end",
        }}
      >
        <Button
          onClick={onClose}
          sx={{
            textTransform: "none",
            color: "#202C4B",
            border: "1px solid #E5E7EB",
            borderRadius: "5px",
            padding: "8px 16px",
            mr: 1,
            fontWeight: 500,
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          sx={{
            textTransform: "none",
            backgroundColor: "#F26522",
            color: "#FFF",
            borderRadius: "5px",
            padding: "8px 16px",
            "&:hover": { backgroundColor: "#D55A1E" },
            fontWeight: 500,
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EmergencyContactDialog;

.bank-accordion {
    margin-bottom: 24px;

    .accordion-summary {
        .MuiAccordionSummary-content {
            margin: 0px;
            display: flex;
            justify-content: space-between;
        }
    }

    .accordion-details {

        border-top: 1px solid #E5E7EB !important;

        .span {
            font-size: 13px;
            color: #677788;
        }

        h6 {
            font-size: 14px;
            font-weight: 500;
            margin-top: 4px;
            color: #202C4B;
        }

        .bank-name,
        .bank-account,
        .bank-ifsc,
        .bank-branch {
            width: 25%;
        }

    }

}

.edit-dialog {
    .MuiPaper-root {
        min-width: 800px;

        .MuiDialogContent-root {
            padding-top: 16px;
        }
    }

    .MuiFormControl-root {
        margin-bottom: 16px;
    }


    .MuiInputBase-root {
        min-height: 38px; // Set the desired height
        max-height: 38px;
    }

    .MuiOutlinedInput-root {
        min-height: 38px; // Set the desired height
        max-height: 38px;
    }

    .MuiInputBase-input,
    .MuiOutlinedInput-input {
        padding: 8px 10px !important;
    }

    .MuiInputLabel-root {
        font-size: 14px !important; // Set the desired font size
        transform: translate(14px, 12px) scale(1); // Adjust the label position
        // Add transition to ensure smooth movement
        transition: all 0.2s ease-out;
    }

    .MuiInputLabel-shrink {
        transform: translate(14px, -6px) scale(0.75); // Adjust the label position when focused
    }

    .MuiFormLabel-root {
        // position: relative !important;
    }

    label {
        font-size: 14px;
        font-weight: 500;
        color: #202C4B;
        margin-bottom: .5rem;
        margin-top: 0px !important;
    }

}

@media screen and (max-width: 767px) {

    .bank-accordion .accordion-details .bank-name,
    .bank-accordion .accordion-details .bank-account,
    .bank-accordion .accordion-details .bank-ifsc,
    .bank-accordion .accordion-details .bank-branch {
        width: 100%;
    }



    .accordion-details {
        padding: 20px;
        flex-direction: column;


    }
}
import * as React from "react";
import Link from "next/link";
import "./BankEmpAccordion.scss";
import {
  Box,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Dialog,
  DialogActions,
  TextField,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import Typography from "@mui/material/Typography";
import { Close, EditNote, ExpandMore } from "@mui/icons-material";
import { addBank, updateBank } from "@/app/services/users.service";
import { toast } from "react-toastify";
import * as Yup from "yup";

interface BankEmpAccordionProps {
  userData: {
    user: {
      _id: string;
      bank: {
        _id?: string;
        bankName: string;
        accountNumber: string;
        ifsc: string;
        address: string;
      }[];
    };
  } | null; // Allow null to handle initial loading state
}

interface BankEmpIdProps {
  employeeId: string | null;
}

// Add validation schema
const bankValidationSchema = Yup.object().shape({
  bankName: Yup.string().required("Bank name is required"),
  accountNo: Yup.string()
    .required("Account number is required")
    .matches(/^\d+$/, "Account number should only contain digits")
    .min(9, "Account number must be between 9 and 18 digits")
    .max(18, "Account number must be between 9 and 18 digits"),
  ifsc: Yup.string()
    .required("IFSC code is required")
    .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, "Invalid IFSC code format"),
  branch: Yup.string().required("Branch name is required"),
});

export default function BankEmpAccordion({
  userData,
  employeeId,
}: BankEmpAccordionProps & BankEmpIdProps) {
  const [open, setOpen] = React.useState(false);
  const [bankData, setBankData] = React.useState({
    bankId: employeeId || "",
    bankName: "",
    accountNo: "",
    ifsc: "",
    branch: "",
  });
  const [errors, setErrors] = React.useState<{ [key: string]: string }>({});

  //const bankId = userData?.user?.bank[0]?._id;
  const userId = userData?.user?._id;

  // Sync bankData with userData when userData changes
  React.useEffect(() => {
    if (userData?.user?.bank && userData?.user?.bank.length > 0) {
      setBankData({
        ...bankData,
        bankName: userData?.user?.bank[0]?.bankName || "",
        accountNo: userData.user.bank[0]?.accountNumber || "",
        ifsc: userData.user.bank[0]?.ifsc || "",
        branch: userData.user.bank[0]?.address || "",
      });
    }
  }, [userData]);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const validateField = async (name: string, value: string) => {
    try {
      await bankValidationSchema.validateAt(name, { [name]: value });
      setErrors((prev) => ({ ...prev, [name]: "" }));
      return true;
    } catch (error: any) {
      setErrors((prev) => ({ ...prev, [name]: error.message }));
      return false;
    }
  };

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBankData((prev) => ({ ...prev, [name]: value }));
    await validateField(name, value);
  };

  const handleSave = async () => {
    try {
      // Validate all fields
      await bankValidationSchema.validate(bankData, { abortEarly: false });

      const bankPayload = {
        userId: employeeId || userId,
        bankName: bankData.bankName,
        accountNumber: bankData.accountNo,
        ifsc: bankData.ifsc.toUpperCase(), // Ensure IFSC is uppercase
        address: bankData.branch,
      };

      try {
        // Always use addBank (POST API)
        await addBank(bankPayload);
        //toast.success("Bank details saved successfully");
        handleClose();
      } catch (error) {
        console.error("Failed to save bank details:", error);
        // toast.error("Failed to save bank details");
      }
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        // Handle validation errors
        const validationErrors: { [key: string]: string } = {};
        error.inner.forEach((err) => {
          if (err.path) {
            validationErrors[err.path] = err.message;
          }
        });
        setErrors(validationErrors);
        // toast.error("Please check all fields and try again");
      } else {
        console.error(error);
        // toast.error("Failed to save bank details");
      }
    }
  };

  return (
    <>
      <Accordion className="bank-accordion">
        <AccordionSummary
          className="accordion-summary"
          expandIcon={<ExpandMore />}
          aria-controls="panel1-content"
          id="panel1-header"
          sx={{ padding: "20px" }}
        >
          <Typography
            className="span"
            component="span"
            sx={{
              margin: "0px",
              fontSize: "16px",
              fontWeight: 600,
              display: "flex",
              justifyContent: "space-between",
              minWidth: "100%",
            }}
          >
            Bank Information
            <Link
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleOpen();
              }}
            >
              <EditNote sx={{ fontSize: "16px", cursor: "pointer" }} />
            </Link>
          </Typography>
        </AccordionSummary>
        <AccordionDetails
          className="accordion-details"
          sx={{ padding: "20px", display: "flex" }}
        >
          <Box className="bank-name">
            <Typography
              className="span"
              component="span"
              sx={{ margin: "0px" }}
            >
              Bank Name
            </Typography>
            <Typography variant="h6">{bankData.bankName}</Typography>
          </Box>
          <Box className="bank-account">
            <Typography
              className="span"
              component="span"
              sx={{ margin: "0px" }}
            >
              Bank account no
            </Typography>
            <Typography variant="h6">{bankData.accountNo}</Typography>
          </Box>
          <Box className="bank-ifsc">
            <Typography
              className="span"
              component="span"
              sx={{ margin: "0px" }}
            >
              IFSC Code
            </Typography>
            <Typography variant="h6">{bankData.ifsc}</Typography>
          </Box>
          <Box className="bank-branch">
            <Typography
              className="span"
              component="span"
              sx={{ margin: "0px" }}
            >
              Branch
            </Typography>
            <Typography variant="h6">{bankData.branch}</Typography>
          </Box>
        </AccordionDetails>
      </Accordion>

      <Dialog
        className="edit-dialog"
        open={open}
        onClose={(event, reason) => {
          if (reason !== "backdropClick") {
            handleClose();
          }
        }}
        disableEscapeKeyDown
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Typography
            variant="h6"
            sx={{ fontSize: "20px", fontWeight: "600", color: "#202C4B" }}
          >
            Bank Details
          </Typography>

          <IconButton
            onClick={handleClose}
            aria-label="close"
            sx={{
              backgroundColor: "#6b7280",
              backgroundImage: "none",
              borderRadius: "50%",
              color: "#fff",
              height: "20px",
              width: "20px",
              margin: 0,
              padding: 0,
              "&:hover": {
                backgroundColor: "#d55a1d",
              },
              "& .MuiSvgIcon-root": {
                fontSize: "14px",
              },
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{
            display: "flex",
            flexDirection: "column",
            padding: "1rem",
          }}
        >
          <Box sx={{ width: "100%" }}>
            <label>
              Bank Name <span className="required">*</span>
            </label>
            <TextField
              name="bankName"
              value={bankData.bankName}
              onChange={handleChange}
              error={Boolean(errors.bankName)}
              helperText={errors.bankName}
              fullWidth
            />
          </Box>
          <Box sx={{ width: "100%" }}>
            <label>
              Bank Account No <span className="required">*</span>
            </label>
            <TextField
              name="accountNo"
              value={bankData.accountNo}
              onChange={handleChange}
              error={Boolean(errors.accountNo)}
              helperText={errors.accountNo}
              fullWidth
              inputProps={{
                inputMode: "numeric",
                pattern: "[0-9]*",
                maxLength: 18,
              }}
            />
          </Box>
          <Box sx={{ width: "100%" }}>
            <label>
              IFSC Code <span className="required">*</span>
            </label>
            <TextField
              name="ifsc"
              value={bankData.ifsc}
              onChange={handleChange}
              error={Boolean(errors.ifsc)}
              helperText={errors.ifsc || "Format: ABCD0123456 (11 characters)"}
              fullWidth
              inputProps={{
                style: { textTransform: "uppercase" },
                maxLength: 11,
              }}
            />
          </Box>
          <Box sx={{ width: "100%" }}>
            <label>
              Branch Address <span className="required">*</span>
            </label>
            <TextField
              name="branch"
              value={bankData.branch}
              onChange={handleChange}
              error={Boolean(errors.branch)}
              helperText={errors.branch}
              fullWidth
            />
          </Box>

          <DialogActions
            sx={{ padding: "0px !important", paddingTop: "1rem !important" }}
          >
            <Button
              sx={{
                fontWeight: "400",
                fontSize: "14px",
                color: "#111827",
                border: "1px solid #E5E7EB",
                borderRadius: "5px",
                textTransform: "none",
                padding: "8px 13.6px",
              }}
              onClick={handleClose}
              color="secondary"
            >
              Cancel
            </Button>
            <Button
              sx={{
                backgroundColor: "#F26522",
                border: "1px solid #F26522",
                color: "#FFF",
                borderRadius: "5px",
                padding: "0.5rem 0.85rem",
                fontSize: "14px",
                transition: "all 0.5s",
                fontWeight: 500,
                textTransform: "none",
              }}
              onClick={handleSave}
              color="primary"
            >
              Save
            </Button>
          </DialogActions>
        </DialogContent>
      </Dialog>
    </>
  );
}

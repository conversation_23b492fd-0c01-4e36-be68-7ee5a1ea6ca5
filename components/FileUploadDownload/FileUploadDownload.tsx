import React, { useState, useRef } from "react";
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Alert,
  Snackbar,
  Tooltip,
} from "@mui/material";
import { CloudUpload, Download, Error } from "@mui/icons-material";
import "./FileUploadDownload.scss";
import { toast } from "react-toastify";
import {
  uploadBulkEmployees,
  downloadEmployeeTemplate,
} from "@/app/services/bulkEmployees.service";

interface FileUploadDownloadProps {
  onUploadSuccess: () => void;
}

const FileUploadDownload: React.FC<FileUploadDownloadProps> = ({
  onUploadSuccess,
}) => {
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [isDownloading, setIsDownloading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    const fileExtension = file.name.split(".").pop()?.toLowerCase();

    // Validate file type
    if (
      fileExtension !== "csv" &&
      fileExtension !== "xlsx" &&
      fileExtension !== "xls"
    ) {
      setError("Please upload a CSV or Excel file");
      return;
    }

    try {
      setIsUploading(true);
      setError(null);

      // Upload the file and process it
      const result = await uploadBulkEmployees(file);

      if (result.success) {
        toast.success("Employees data uploaded successfully!");
        // Reset the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
        // Trigger the callback to refresh the datagrid
        onUploadSuccess();
      } else {
        setError(result.message || "Failed to upload employees data");
      }
    } catch (err) {
      console.error("Error uploading file:", err);
      setError("An error occurred while uploading the file. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  const handleUploadClick = () => {
    // Trigger the hidden file input
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      setIsDownloading(true);
      setError(null);

      // Download the template file
      await downloadEmployeeTemplate();

      toast.success("Template downloaded successfully!");
    } catch (err) {
      console.error("Error downloading template:", err);
      setError(
        "An error occurred while downloading the template. Please try again."
      );
    } finally {
      setIsDownloading(false);
    }
  };

  const handleCloseError = () => {
    setError(null);
  };

  return (
    <Box className="file-upload-download-container">
      <Box className="buttons-container">
        <Tooltip title="Upload CSV or Excel file with employee data">
          <Button
            variant="outlined"
            startIcon={
              isUploading ? (
                <CircularProgress size={16} color="inherit" />
              ) : (
                <CloudUpload />
              )
            }
            onClick={handleUploadClick}
            disabled={isUploading}
            className="upload-button"
          >
            {isUploading ? "Uploading..." : "Import"}
          </Button>
        </Tooltip>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".csv,.xlsx,.xls"
          style={{ display: "none" }}
        />

        <Tooltip title="Download template file with required columns">
          <Button
            variant="outlined"
            startIcon={
              isDownloading ? (
                <CircularProgress size={16} color="inherit" />
              ) : (
                <Download />
              )
            }
            onClick={handleDownloadTemplate}
            disabled={isDownloading}
            className="download-button"
          >
            {isDownloading ? "Downloading..." : "Template"}
          </Button>
        </Tooltip>
      </Box>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={handleCloseError}
      >
        <Alert severity="error" onClose={handleCloseError} icon={<Error />}>
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default FileUploadDownload;

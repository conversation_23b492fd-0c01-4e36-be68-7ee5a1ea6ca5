"use client";
import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Content,
  Typography,
  Avatar,
  Box,
  IconButton,
} from "@mui/material";
import {
  Edit,
  Message,
  StarBorder,
  Newspaper,
  EventAvailable,
  LocalPhone,
  Email,
  Male,
  Cake,
  WhereToVote,
  BusinessCenter,
  BookmarkAdd,
  Work,
  ChildFriendly,
  Verified,
  FamilyRestroom,
  EditNote,
  Person as PersonIcon,
} from "@mui/icons-material";
import Image from "next/image";
import "./EmployeeCard.scss";
import { getUserById } from "@/app/services/users.service";
import PersonalInfoDialog from "../PersonalInfoDialog/PersonalInfoDialog";
import AddEmployeeForm from "../AddEmployeeForm/AddEmployeeForm";
import Loader from "../Loader/Loader";

interface EmployeeInfo {
  _id?: string;
  name: string;
  position: string;
  previousExp?: string; // Updated from `experience`
  clientId?: string; // Kept as optional, not in API
  team?: string; // Mapped from `departmentName`
  joinDate: string;
  reportTo?: string; // Mapped from `roName`
  phone: string;
  email: string;
  gender?: string; // New field from API
  birthday: string;
  address?: string; // New field from API
  passportNo?: string;
  passportExp?: string;
  nationality?: string;
  religion?: string;
  maritalStatus: string;
  spouseEmployment?: string;
  children?: number;
  avatar?: string | null;
  personalInfoId?: string;
  roles?: string[];
  company?: string;
  about?: string;
  departmentId?: string;
  designationId?: string;
  weddingAnniversaryDate?: string;
  roName?: string; // Reporting officer name
  departmentName?: string; // Added this field
}

interface EditEmployeeFormData {
  _id: string;
  roles: string[];
  firstName: string;
  lastName: string;
  avatar: string;
  email: string;
  phone: string;
  company: string;
  about: string;
  joiningDate: string;
  departmentId: string;
  designationId: string;
  birthDate: string;
  maritalStatus: string;
  weddingAnniversaryDate: string;
  address: string; // Add this line
}

interface EmployeeCardProps {
  employeeId: string | null;
}

const EmployeeCard: React.FC<EmployeeCardProps> = ({ employeeId }) => {
  const [employee, setEmployee] = useState<EmployeeInfo | null>(null);
  const [openPersonalDialog, setOpenPersonalDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [editEmployeeData, setEditEmployeeData] =
    useState<EditEmployeeFormData | null>(null);

  useEffect(() => {
    const fetchEmployeeData = async () => {
      if (!employeeId) {
        setEmployee(null);
        setIsLoading(false);
        return;
      }

      try {
        const response = await getUserById(employeeId);
        const userData = response.user;
        const personalInfo = userData.personalInfo || {};

        // Extract department and designation names from objects if available
        const departmentName = userData.departmentId && typeof userData.departmentId === 'object' 
          ? userData.departmentId.departmentName 
          : userData.departmentName || "N/A";
          
        const designationName = userData.designationId && typeof userData.designationId === 'object'
          ? userData.designationId.designationName
          : userData.designationName || "N/A";

        const mappedEmployee: EmployeeInfo = {
          _id: userData._id,
          name: `${userData.firstName} ${userData.lastName}`,
          position: designationName,
          previousExp: userData.previousExp || "N/A",
          team: departmentName,
          joinDate: userData.joiningDate
            ? new Date(userData.joiningDate).toLocaleDateString()
            : "N/A",
          departmentName: departmentName,
          reportTo: userData.roName || "N/A",
          phone: userData.phone || "N/A",
          email: userData.email || "N/A",
          gender: userData.gender || "N/A",
          birthday: userData.birthDate
            ? new Date(userData.birthDate).toLocaleDateString()
            : "N/A",
          address: userData.address || "N/A",
          maritalStatus:
            personalInfo.maritalStatus || userData.maritalStatus || "N/A",
          avatar: userData.avatar || null,
          passportNo: personalInfo.passportNumber || "N/A",
          passportExp: personalInfo.passportExpiry
            ? new Date(personalInfo.passportExpiry).toLocaleDateString()
            : "N/A",
          nationality: personalInfo.nationality || "N/A",
          religion: personalInfo.religion || "N/A",
          spouseEmployment: personalInfo.employementSpouse || "N/A",
          children:
            personalInfo.childrens !== undefined
              ? Number(personalInfo.childrens)
              : undefined,
          personalInfoId: personalInfo._id,
          roles: userData.roles || ["Employee"],
          company: userData.company || "",
          about: userData.about || "",
          departmentId: userData.departmentId || "",
          designationId: userData.designationId || "",
          weddingAnniversaryDate: userData.weddingAnniversaryDate
            ? new Date(userData.weddingAnniversaryDate).toLocaleDateString()
            : "",
          roName: userData.roName || "N/A",
        };

        setEmployee(mappedEmployee);
      } catch (error) {
        console.error("Failed to fetch employee data:", error);
        setEmployee(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEmployeeData();
  }, [employeeId]);

  if (isLoading) {
    return <Loader loading={isLoading} />;
  }

  if (!employee) {
    return <Typography>No employee data available</Typography>;
  }

  const handleEditPersonalInfo = () => {
    setOpenPersonalDialog(true);
  };

  const handleEditInfo = async () => {
    if (!employeeId) return;

    try {
      setIsLoading(true);
      const response = await getUserById(employeeId);
      const userData = response.user;
      const personalInfo = userData.personalInfo || {};

      // Extract department and designation IDs properly
      const departmentId = userData.departmentId && typeof userData.departmentId === 'object'
        ? userData.departmentId._id
        : userData.departmentId || "";
        
      const designationId = userData.designationId && typeof userData.designationId === 'object'
        ? userData.designationId._id
        : userData.designationId || "";

      // Extract names for display
      const departmentName = userData.departmentId && typeof userData.departmentId === 'object'
        ? userData.departmentId.departmentName
        : userData.departmentName || "N/A";
        
      const designationName = userData.designationId && typeof userData.designationId === 'object'
        ? userData.designationId.designationName
        : userData.designationName || "N/A";

      const employeeDataForForm: EditEmployeeFormData = {
        _id: userData._id || "",
        roles: userData.roles || ["Employee"],
        firstName: userData.firstName || "",
        lastName: userData.lastName || "",
        avatar: userData.avatar || "",
        email: userData.email || "",
        phone: userData.phone || "",
        company: userData.company || "",
        about: userData.about || "",
        joiningDate: userData.joiningDate
          ? new Date(userData.joiningDate).toISOString().split("T")[0]
          : "",
        departmentId: departmentId,
        designationId: designationId,
        birthDate: userData.birthDate
          ? new Date(userData.birthDate).toISOString().split("T")[0]
          : "",
        maritalStatus:
          personalInfo.maritalStatus || userData.maritalStatus || "Single",
        weddingAnniversaryDate: userData.weddingAnniversaryDate
          ? new Date(userData.weddingAnniversaryDate)
              .toISOString()
              .split("T")[0]
          : "",
        address: userData.address || "",
      };

      setEditEmployeeData(employeeDataForForm);
      setOpenEditDialog(true);

      setEmployee((prev) => ({
        ...prev!,
        name: `${userData.firstName} ${userData.lastName}`,
        position: designationName,
        team: departmentName,
        joinDate: userData.joiningDate
          ? new Date(userData.joiningDate).toLocaleDateString()
          : "N/A",
        reportTo: userData.roName || "N/A",
        phone: userData.phone || "N/A",
        email: userData.email || "N/A",
        gender: userData.gender || "N/A",
        birthday: userData.birthDate
          ? new Date(userData.birthDate).toLocaleDateString()
          : "N/A",
        address: userData.address || "N/A",
        maritalStatus:
          personalInfo.maritalStatus || userData.maritalStatus || "N/A",
        avatar: userData.avatar || null,
        passportNo: personalInfo.passportNumber || "N/A",
        passportExp: personalInfo.passportExpiry
          ? new Date(personalInfo.passportExpiry).toLocaleDateString()
          : "N/A",
        nationality: personalInfo.nationality || "N/A",
        religion: personalInfo.religion || "N/A",
        spouseEmployment: personalInfo.employementSpouse || "N/A",
        children:
          personalInfo.childrens !== undefined
            ? Number(personalInfo.childrens)
            : undefined,
        personalInfoId: personalInfo._id,
        roles: userData.roles || ["Employee"],
        company: userData.company || "",
        about: userData.about || "",
        departmentId: userData.departmentId || "",
        designationId: userData.designationId || "",
        weddingAnniversaryDate: userData.weddingAnniversaryDate
          ? new Date(userData.weddingAnniversaryDate).toLocaleDateString()
          : "",
        roName: userData.roName || "N/A",
      }));
    } catch (error) {
      console.error("Failed to fetch employee data for edit:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSavePersonalInfo = (data: {
    passportNo?: string;
    passportExp?: string;
    nationality?: string;
    religion?: string;
    maritalStatus: string;
    spouseEmployment?: string;
    children?: number;
  }) => {
    setEmployee((prev) => {
      if (!prev) return prev;
      return {
        ...prev,
        passportNo: data.passportNo || prev.passportNo,
        passportExp: data.passportExp || prev.passportExp,
        nationality: data.nationality || prev.nationality,
        religion: data.religion || prev.religion,
        maritalStatus: data.maritalStatus || prev.maritalStatus,
        spouseEmployment: data.spouseEmployment || prev.spouseEmployment,
        children: data.children !== undefined ? data.children : prev.children,
      };
    });
  };

  const handleRefreshEmployeeData = async () => {
    if (!employeeId) return;
    try {
      const response = await getUserById(employeeId);
      const userData = response.user;
      const personalInfo = userData.personalInfo || {};

      const mappedEmployee: EmployeeInfo = {
        _id: userData._id,
        name: `${userData.firstName} ${userData.lastName}`,
        position: userData.designationName || "N/A",
        previousExp: userData.previousExp || "N/A",
        team: userData.departmentName || "N/A",
        joinDate: userData.joiningDate
          ? new Date(userData.joiningDate).toLocaleDateString()
          : "N/A",
        reportTo: userData.roName || "N/A",
        phone: userData.phone || "N/A",
        email: userData.email || "N/A",
        gender: userData.gender || "N/A",
        birthday: userData.birthDate
          ? new Date(userData.birthDate).toLocaleDateString()
          : "N/A",
        address: userData.address || "N/A",
        maritalStatus:
          personalInfo.maritalStatus || userData.maritalStatus || "N/A",
        avatar: userData.avatar || null,
        passportNo: personalInfo.passportNumber || "N/A",
        passportExp: personalInfo.passportExpiry
          ? new Date(personalInfo.passportExpiry).toLocaleDateString()
          : "N/A",
        nationality: personalInfo.nationality || "N/A",
        religion: personalInfo.religion || "N/A",
        spouseEmployment: personalInfo.employementSpouse || "N/A",
        children:
          personalInfo.childrens !== undefined
            ? Number(personalInfo.childrens)
            : undefined,
        personalInfoId: personalInfo._id,
        roles: userData.roles || ["Employee"],
        company: userData.company || "",
        about: userData.about || "",
        departmentId: userData.departmentId || "",
        designationId: userData.designationId || "",
        weddingAnniversaryDate: userData.weddingAnniversaryDate
          ? new Date(userData.weddingAnniversaryDate).toLocaleDateString()
          : "",
        roName: userData.roName || "N/A",
        departmentName: userData.departmentName || "N/A",
      };

      setEmployee(mappedEmployee);
    } catch (error) {
      console.error("Failed to refresh employee data:", error);
    }
  };

  return (
    <Card sx={{ marginBottom: "1.5rem" }} className="employee-card">
      <Box className="header">
        <Avatar className="avatar">
          {employee.avatar ? (
            <Image
              src={employee.avatar}
              alt="User Avatar"
              width={54}
              height={54}
              style={{ objectFit: "cover" }}
              onError={(e) => {
                console.error("Image failed to load:", e);
                e.currentTarget.src = "/assets/default-avatar.png";
              }}
            />
          ) : (
            <Image
              src="/assets/default-avatar.png"
              alt="Default User Avatar"
              width={54}
              height={54}
              style={{ objectFit: "cover" }}
              onError={(e) => {
                console.error("Image failed to load:", e);
                e.currentTarget.src = "/assets/default-avatar.png";
              }}
            />
          )}
        </Avatar>
        <Typography variant="h6" className="name">
          {employee.name}
          <Verified sx={{ width: "16px", height: "16px", color: "#03C95A" }} />
        </Typography>
        <Box sx={{ display: "flex", justifyContent: "center" }}>
          <Typography className="position">{employee.position}</Typography>
          {/* {employee.previousExp && (
            <Typography className="experience">{employee.previousExp}</Typography>
          )} */}
        </Box>
      </Box>

      <CardContent className="details">
         <Typography className="info" component="span">
          <Box>
            <Newspaper sx={{ maxHeight: "14px", minWidth: "14px" }} />
            Company :
          </Box>
          <Typography>{employee.company || "N/A"}</Typography>
        </Typography>
       {/* <Typography className="info" component="span">
          <Box>
            <StarBorder sx={{ maxHeight: "14px", minWidth: "14px" }} />
            Team:
          </Box>
          <Typography>{employee.team}</Typography>
        </Typography> */}
        <Typography className="info" component="span">
          <Box>
            <EventAvailable sx={{ maxHeight: "14px", minWidth: "14px" }} />
            Date of Join:
          </Box>
          <Typography>{employee.joinDate}</Typography>
        </Typography>
        <Typography className="info" component="span">
          <Box>
            <EventAvailable sx={{ maxHeight: "14px", minWidth: "14px" }} />
            Department:
          </Box>
          <Typography sx={{ display: "flex", alignItems: "center" }}>
            {/* <Avatar
              src="/assets/users/user-08.jpg"
              sx={{ maxWidth: "24px", height: "24px", marginRight: ".5rem" }}
            /> */}
            {employee.departmentName || "N/A"}
          </Typography>
        </Typography>

        <Box className="actions">
          <Button
            variant="contained"
            sx={{
              background: "#111827 !important",
              borderColor: "#111827 !important",
              boxShadow: "none",
              color: "#FFF",
              borderRadius: "5px",
              padding: "0.5rem 0.85rem",
              fontSize: "14px",
              transition: "all 0.5s",
              fontWeight: 500,
              textTransform: "none",
              width: "100%",
              margin: "0 5px",
            }}
            startIcon={<Edit />}
            onClick={handleEditInfo}
          >
            Edit Info
          </Button>
          {/* <Button
            variant="contained"
            sx={{
              margin: "0 5px",
              width: "100%",
              textTransform: "none",
              color: "#fff",
              backgroundColor: "#F26522 !important",
              border: "1px solid #F26522 !important",
            }}
            startIcon={<Message />}
          >
            Message
          </Button> */}
        </Box>

        <Box className="section">
          <Typography
            sx={{ fontSize: "14px", fontWeight: 600, color: "#202C4B" }}
            variant="h6"
          >
            Basic Information
          </Typography>
          <Typography className="info" component="span">
            <Box>
              <LocalPhone sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Phone:
            </Box>
            <Typography>{employee.phone}</Typography>
          </Typography>
          <Typography className="info" component="span">
            <Box>
              <Email sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Email:
            </Box>
            <Typography>{employee.email}</Typography>
          </Typography>
          <Typography className="info" component="span">
            <Box>
              <Male sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Gender:
            </Box>
            <Typography>{employee.gender}</Typography>
          </Typography>
          <Typography className="info" component="span">
            <Box>
              <Cake sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Birthday:
            </Box>
            <Typography>{employee.birthday}</Typography>
          </Typography>
          <Typography className="info" component="span">
            <Box>
              <WhereToVote sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Address:
            </Box>
            <Typography 
              sx={{ 
                maxWidth: "200px", 
                overflow: "hidden",
                textOverflow: "ellipsis",
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                lineHeight: "1.2em",
                maxHeight: "2.4em",
                "&:hover": {
                  overflow: "visible",
                  WebkitLineClamp: "unset",
                  maxHeight: "none",
                  backgroundColor: "#f5f5f5",
                  position: "relative",
                  zIndex: 1,
                  boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                  borderRadius: "4px",
                  padding: "4px"
                }
              }}
            >
              {employee.address}
            </Typography>
          </Typography>
          {/* <Typography className="info" component="span">
            <Box>
              <Work sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Previous Experience:
            </Box>
            <Typography>{employee.previousExp}</Typography>
          </Typography> */}
        </Box>

        <Box className="section">
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography
              sx={{ fontSize: "14px", fontWeight: 600, color: "#202C4B" }}
              variant="h6"
            >
              Personal Information
            </Typography>
            <IconButton onClick={handleEditPersonalInfo}>
              <EditNote  sx={{ fontSize: "16px" }} />
            </IconButton>
          </Box>
          <Typography className="info">
            <Box>
              <BusinessCenter sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Passport No:
            </Box>
            <Typography>{employee.passportNo}</Typography>
          </Typography>
          <Typography className="info">
            <Box>
              <EventAvailable sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Passport Exp Date:
            </Box>
            <Typography>{employee.passportExp}</Typography>
          </Typography>
          <Typography className="info">
            <Box>
              <Male sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Nationality:
            </Box>
            <Typography>{employee.nationality}</Typography>
          </Typography>
          <Typography className="info">
            <Box>
              <BookmarkAdd sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Religion:
            </Box>
            <Typography>{employee.religion}</Typography>
          </Typography>
          <Typography className="info">
            <Box>
              <FamilyRestroom sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Marital Status:
            </Box>
            <Typography>{employee.maritalStatus}</Typography>
          </Typography>
          <Typography className="info">
            <Box>
              <Work sx={{ maxHeight: "14px", minWidth: "14px" }} />
              Employment of Spouse:
            </Box>
            <Typography>{employee.spouseEmployment}</Typography>
          </Typography>
          <Typography className="info">
            <Box>
              <ChildFriendly sx={{ maxHeight: "14px", minWidth: "14px" }} />
              No. of Children:
            </Box>
            <Typography>
              {employee.children !== undefined ? employee.children : "N/A"}
            </Typography>
          </Typography>
        </Box>
      </CardContent>

      <PersonalInfoDialog
        open={openPersonalDialog}
        onClose={() => setOpenPersonalDialog(false)}
        initialData={{
          userId: employeeId || undefined,
          personalInfoId: employee.personalInfoId,
          passportNo: employee.passportNo === "N/A" ? "" : employee.passportNo,
          passportExp:
            employee.passportExp === "N/A" ? "" : employee.passportExp,
          nationality:
            employee.nationality === "N/A" ? "Indian" : employee.nationality, // Set default to "Indian" if "N/A"
          religion: employee.religion === "N/A" ? "" : employee.religion,
          maritalStatus:
            employee.maritalStatus === "N/A" ? "" : employee.maritalStatus,
          spouseEmployment:
            employee.spouseEmployment === "N/A"
              ? ""
              : employee.spouseEmployment,
          children: employee.children,
        }}
        onSave={handleSavePersonalInfo}
        mode={employee.passportNo !== "N/A" ? "edit" : "add"}
      />

      <AddEmployeeForm
        open={openEditDialog}
        onClose={() => setOpenEditDialog(false)}
        isEdit={true}
        employeeData={
          editEmployeeData || {
            _id: "",
            roles: ["Employee"],
            firstName: "",
            lastName: "",
            avatar: "",
            email: "",
            phone: "",
            company: "",
            about: "",
            joiningDate: "",
            departmentId: "",
            designationId: "",
            birthDate: "",
            maritalStatus: "Single",
            weddingAnniversaryDate: "",
            address: "",
          }
        }
        refreshList={handleRefreshEmployeeData}
      />
    </Card>
  );
};

export default EmployeeCard;

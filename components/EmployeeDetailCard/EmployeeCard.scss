.employee-card {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md);

  .header {
    background-image: url("/assets/card-bg.webp");
    background-size: cover;
    max-height: 90px;
    background-repeat: no-repeat;
    padding: 4rem var(--spacing-md) 0px;
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    text-align: center;
    display: flex;
    flex-direction: column;
    margin-bottom: 110px;

    .avatar {
      width: var(--avatar-lg);
      height: var(--avatar-lg);
      margin: 0 auto;
      background: white;
      color: var(--primary-color);
      font-size: 24px;
      border-radius: 50%;
      border: 2px solid #fff;
    }

    .name {
      margin-top: var(--spacing-sm);
      margin-bottom: var(--spacing-xs);
      font-size: 16px;
      font-weight: 600;
      color: var(--text-dark);
    }

    .position {
      font-size: 12px;
      background: rgba(33, 37, 41, 0.1);
      color: var(--text-primary);
      padding: var(--spacing-xs) var(--spacing-sm);
      font-weight: 500;
      letter-spacing: 0.5px;
      border-radius: var(--border-radius-sm);
      margin-right: var(--spacing-sm);
      align-items: center;
      display: flex;
      margin-top: var(--spacing-xs);
    }

    .experience {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--border-radius-sm);
      font-size: 12px;
      margin-top: var(--spacing-xs);
      display: inline-block;
      background: rgba(59, 112, 128, 0.1);
      color: var(--secondary-color);
    }
  }
}

.details {
  padding: var(--spacing-md);

  .info {
    font-size: 14px;
    margin: var(--spacing-xs) 0;
    display: flex;
    justify-content: space-between;
    color: var(--text-secondary);

    p {
      color: var(--text-primary);
      font-size: 14px;
    }
  }

  .actions {
    display: flex;
    justify-content: space-between;
    margin: var(--spacing-md) 0;
  }

  .section {
    margin-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-sm);
  }
}

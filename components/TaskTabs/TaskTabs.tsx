"use client";

import type React from "react";
import { useState, useEffect } from "react";
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Card,
  CardContent,
  LinearProgress,
  Stack,
  Checkbox,
  Avatar,
  AvatarGroup,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  TextField,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import StarIcon from "@mui/icons-material/Star";
import StarBorderIcon from "@mui/icons-material/StarBorder";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import "./TaskTabs.scss";
import Loader from "../Loader/Loader";
import { deleteTask } from "@/app/services/projects/project.service";
import { toast } from "react-toastify";
import DeleteConfirmationDialog from "@/components/DeleteDialog/DeleteConfirmationDialog";
import AddTaskDialog from "@/components/AddTaskDialog/AddTaskDialog";
import TaskDialog from "../TaskDialog/TaskDialog";
import {
  DndContext,
  closestCenter,
  useSensor,
  useSensors,
  PointerSensor,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

interface Task {
  id: string;
  title: string;
  date: string;
  completed: boolean;
  tags: string[];
  avatars: string[];
  starred: boolean;
  description?: string;
  createdOn?: string;
  status: { _id: string; title: string };
  teamMembers?: Array<{
    _id: string;
    firstName: string;
    lastName: string;
    avatar: string;
    email?: string;
    employeeId?: string;
  }>;
}

interface TaskTabsProps {
  tasks: Task[];
  loading: boolean;
  error: string | null;
  onEditTask: (taskId: string) => void;
  refreshTasks?: () => void;
  projectName: string;
}

const priorities = ["All", "High", "Medium", "Low"];

const SortableTaskItem = ({
  task,
  anchorEl,
  selectedTaskId,
  handleMenuOpen,
  handleMenuClose,
  onEditTask,
  handleDeleteClick,
  handleViewClick,
}: {
  task: Task;
  anchorEl: HTMLElement | null;
  selectedTaskId: string | null;
  handleMenuOpen: (event: React.MouseEvent<HTMLButtonElement>, taskId: string) => void;
  handleMenuClose: () => void;
  onEditTask: (taskId: string) => void;
  handleDeleteClick: (taskId: string) => void;
  handleViewClick: (taskId: string) => void;
}) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: task.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      variant="outlined"
      className={`task-item ${task.completed ? "completed" : ""}`}
    >
      <Stack direction="row" justifyContent="space-between" alignItems="center" width="100%">
        <Stack direction="row" spacing={2} alignItems="center" className="task-item-left">
          <Checkbox checked={task.completed} className="task-checkbox" />
          <IconButton className="task-star">
            {task.starred ? <StarIcon className="starred" /> : <StarBorderIcon />}
          </IconButton>
          <Typography className="task-title">{task.title}</Typography>
          <Chip label={task.date} size="small" className="task-date" />
        </Stack>

        <Stack direction="row" spacing={1} alignItems="center" className="task-item-right">
          <Stack direction="row" spacing={1} className="task-tags">
            {task.tags.map((tag, i) => {
              const tagClass = `tag-${tag.toLowerCase().replace(/\s+/g, "-")}`;
              return (
                <Chip
                  key={i}
                  label={tag}
                  size="small"
                  className={`task-tag ${tagClass}`}
                />
              );
            })}
          </Stack>
          <AvatarGroup max={3} className="task-avatars">
            {task.avatars.length > 0 ? (
              task.avatars.map((src, i) => (
                <Avatar
                  key={i}
                  src={src}
                  className="task-avatar"
                  alt={`Team member ${i + 1}`}
                />
              ))
            ) : (
              <Avatar className="task-avatar" alt="No member">
                N/A
              </Avatar>
            )}
          </AvatarGroup>
          <IconButton onClick={(e) => handleMenuOpen(e, task.id)} className="task-more">
            <MoreVertIcon sx={{ fontSize: "20px" }} />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl) && selectedTaskId === task.id}
            onClose={handleMenuClose}
          >
            <MenuItem
              onClick={() => {
                onEditTask(task.id);
                handleMenuClose();
              }}
              sx={{ gap: "8px" }}
            >
              <EditIcon fontSize="small" sx={{ color: "#000" }} />
              Edit
            </MenuItem>
            <MenuItem
              onClick={() => {
                handleDeleteClick(task.id);
                handleMenuClose();
              }}
              sx={{ gap: "8px" }}
            >
              <DeleteIcon fontSize="small" sx={{ color: "#000" }} />
              Delete
            </MenuItem>
            <MenuItem
              onClick={() => {
                handleViewClick(task.id);
                handleMenuClose();
              }}
              sx={{ gap: "8px" }}
            >
              <VisibilityIcon fontSize="small" sx={{ color: "#000" }} />
              View
            </MenuItem>
          </Menu>
        </Stack>
      </Stack>
    </Card>
  );
};

export default function TaskTabs({
  tasks,
  loading,
  error,
  onEditTask,
  refreshTasks,
  projectName,
}: TaskTabsProps) {
  const [tab, setTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [filteredTasks, setFilteredTasks] = useState<Task[]>(tasks);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [taskToDelete, setTaskToDelete] = useState<string | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState<boolean>(false);
  const [viewTaskId, setViewTaskId] = useState<string | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: { distance: 5 },
    })
  );

  useEffect(() => {
    let updatedTasks = tasks;
    if (tab !== 0) {
      const priority = priorities[tab];
      updatedTasks = tasks.filter((task) => task.tags.includes(priority));
    }
    if (selectedDate) {
      updatedTasks = updatedTasks.filter(
        (task) => new Date(task.date).toISOString().split("T")[0] === selectedDate
      );
    }
    setFilteredTasks(updatedTasks);
  }, [tab, selectedDate, tasks]);

  const handleChange = (_: React.SyntheticEvent, newValue: number) => setTab(newValue);
  const handleMenuOpen = (event: React.MouseEvent<HTMLButtonElement>, taskId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedTaskId(taskId);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTaskId(null);
  };

  const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedDate(event.target.value);
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    const oldIndex = filteredTasks.findIndex((task) => task.id === active.id);
    const newIndex = filteredTasks.findIndex((task) => task.id === over.id);

    const reorderedTasks = [...filteredTasks];
    const [movedTask] = reorderedTasks.splice(oldIndex, 1);
    reorderedTasks.splice(newIndex, 0, movedTask);

    setFilteredTasks(reorderedTasks);
  };

  const completedTasks = filteredTasks.filter((t) => t.completed).length;
  const completionPercentage = filteredTasks.length
    ? Math.round((completedTasks / filteredTasks.length) * 100)
    : 0;

  if (loading) {
    return <Loader loading={loading} />;
  }

  if (error) {
    return <Typography color="error">{error}</Typography>;
  }

  const handleDeleteClick = (taskId: string) => {
    setTaskToDelete(taskId);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleConfirmDelete = async () => {
    if (taskToDelete) {
      try {
        setIsDeleting(true);
        const response = await deleteTask(taskToDelete);
        toast.success(response.message || "Task deleted successfully");

        if (refreshTasks) {
          refreshTasks();
        }
      } catch (error: any) {
        console.error("Error deleting task:", error);
        const errorMessage = error.response?.data?.message || "Failed to delete task";
        toast.error(errorMessage);
      } finally {
        setIsDeleting(false);
        setDeleteDialogOpen(false);
        setTaskToDelete(null);
      }
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setTaskToDelete(null);
  };

  const handleViewClick = (taskId: string) => {
    setViewTaskId(taskId);
    setViewDialogOpen(true);
    handleMenuClose();
  };

  return (
    <Box className="task-dashboard">
      <Box className="filters-container">
        <Stack direction="row" spacing={1} className="priority-section">
          <Typography className="priority-label">Priority</Typography>
          <Tabs
            value={tab}
            onChange={handleChange}
            className="priority-tabs"
            TabIndicatorProps={{ style: { display: "none" } }}
          >
            {priorities.map((label, idx) => (
              <Tab
                key={idx}
                label={label}
                className={tab === idx ? "active-tab" : "tab"}
              />
            ))}
          </Tabs>
        </Stack>

        <Stack direction="row" spacing={2} className="filter-controls">
          <TextField
            type="date"
            size="small"
            value={selectedDate}
            onChange={handleDateChange}
            className="date-picker"
            sx={{
              "& .MuiOutlinedInput-root": {
                "& fieldset": { borderColor: "transparent" },
                "&:hover fieldset": { borderColor: "transparent" },
                "&.Mui-focused fieldset": { borderColor: "transparent" },
              },
            }}
          />
        </Stack>
      </Box>

      {projectName && (
        <Card variant="outlined" className="summary-card">
          <CardContent>
            <Typography variant="h6" className="summary-title">
              {projectName} Tasks
            </Typography>
            <Box className="summary-content">
              <Typography variant="body2" className="summary-label">
                Tasks Done
              </Typography>
              <Typography variant="h6" className="summary-count">
                {completedTasks} / {filteredTasks.length}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={completionPercentage}
                className="summary-progress"
              />
              <Typography
                variant="body2"
                color="text.secondary"
                className="summary-percentage"
              >
                {completionPercentage}% Completed
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext items={filteredTasks.map((task) => task.id)} strategy={verticalListSortingStrategy}>
          <Box className="task-list">
            {filteredTasks.length > 0 ? (
              filteredTasks.map((task) => (
                <SortableTaskItem
                  key={task.id}
                  task={task}
                  anchorEl={anchorEl}
                  selectedTaskId={selectedTaskId}
                  handleMenuOpen={handleMenuOpen}
                  handleMenuClose={handleMenuClose}
                  onEditTask={onEditTask}
                  handleDeleteClick={handleDeleteClick}
                  handleViewClick={handleViewClick}
                />
              ))
            ) : (
              <Typography variant="body2" className="no-tasks">
                No tasks available for this priority.
              </Typography>
            )}
          </Box>
        </SortableContext>
      </DndContext>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Task"
        message="Are you sure you want to delete this task? This action cannot be undone."
      />

      {viewTaskId && (
        <TaskDialog
          open={viewDialogOpen}
          onClose={() => {
            setViewDialogOpen(false);
            setViewTaskId(null);
          }}
          taskId={viewTaskId}
        />
      )}
    </Box>
  );
}
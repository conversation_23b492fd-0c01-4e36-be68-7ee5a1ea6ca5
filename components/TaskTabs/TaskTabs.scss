.task-dashboard {
    margin: 0 auto;
  
  .filters-container {
    display: flex;
    justify-content: space-between;
    // align-items: center;
    margin-bottom: 1.5rem;
    // gap: 1rem;
  }
  
  .priority-section {
    display: flex;
  
    .priority-label {
      font-weight: 500;
      color: #333;
      margin-top: 15px;
    }
  
    .priority-tabs {
      border: 1px solid #e2e8f0;
      padding: 5px 0   ;
      .MuiTabs-flexContainer {
        background-color: #f9f9f9;
        border-radius: 0.5rem;
        padding: 0.25rem;
      }
  
      .tab {
        min-width: 30px;
        min-height: 30px;
        font-size: 0.875rem;
        text-transform: none;
        font-weight: 500;
        color: #64748b;
        padding: 0.25rem 0.5rem;
        margin: 0;
        border-radius: 0.375rem;
      }
  
      .active-tab {
        @extend .tab;
        background-color: white;
        color: #0f172a;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }
  }
  
  .filter-controls {
    display: flex;
    // align-items: center;
    // gap: 1rem;
  
    .date-picker {
      min-width: 150px;
  
      .MuiInputBase-root {
        height: 36px;
        font-size: 0.875rem;
        border-radius: 0.375rem;
        background-color: #FFFFFF;
        &:focus {
          outline: none !important;
          box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.3);
        }
        &:hover {
          background-color: #FFFFFF;
        }
        &::selection {
          background-color: #FFFFFF;
        }
      }
  
      .MuiInputBase-input {
        padding: 0.5rem;
      }
    }
  }
  
  .summary-card {
    margin-bottom: 1.5rem;
    border-color: #e2e8f0;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  
    .summary-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 0.75rem;
    }
  
    .summary-content {
      background-color: #f8fafc;
      padding: 8px;
    .summary-label {
      font-size: 0.875rem;
      color: #64748b;
      margin-bottom: 0.25rem;
    }
  
    .summary-count {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 0.25rem;
    }
  
    .summary-percentage {
      font-size: 0.875rem;
      color: #64748b;
      margin-bottom: 0.5rem;
      margin-top: 10px;
    }
  }
  
    .summary-progress {
      height: 0.5rem;
      border-radius: 1rem;
      background-color: #e2e8f0;
  
      .MuiLinearProgress-bar {
        background-color: #3b82f6;
        border-radius: 1rem;
      }
    }
  }
  
  .task-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .task-item {
    padding: 1rem 1rem;
    border-color: #e2e8f0;
    // border-radius: 0.375rem;
    transition: all 0.2s ease;
  
    &:hover {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
  
    &.completed {
      opacity: 0.7;
  
      .task-title {
        text-decoration: line-through;
        color: #94a3b8;
      }
    }
  
    .task-item-left {
      .task-checkbox {
        padding: 0;
        color: #F26522;
  
        &.Mui-checked {
          color: #F26522;
        }
      }
  
      .task-star {
        padding: 0.25rem;
        color: #cbd5e1;
  
        .starred {
          color: #f59e0b;
        }
      }
  
      .task-title {
        font-weight: 500;
        color: #1e293b;
      }
  
      .task-date {
        background-color: #f8fafc;
        border: none;
        color: #64748b;
        height: 24px;
  
        .MuiChip-label {
          font-size: 0.75rem;
          padding-left: 0.25rem;
        }
      }
    }
  
    .task-item-right {
      .task-tags {
        .task-tag {
          font-size: 0.75rem;
          font-weight: 500;
          height: 24px;
  
          &.tag-web-design {
            background-color: #e0f2fe;
            color: #0284c7;
          }
  
          &.tag-onhold {
            background-color: #fef3c7;
            color: #d97706;
          }
  
          &.tag-social {
            background-color: #dbeafe;
            color: #2563eb;
          }
  
          &.tag-inprogress {
            background-color: #f3e8ff;
            color: #9333ea;
          }
  
          &.tag-meetings {
            background-color: #d1fae5;
            color: #059669;
          }
  
          &.tag-pending {
            background-color: #fee2e2;
            color: #dc2626;
          }
  
          &.tag-research {
            background-color: #e0e7ff;
            color: #4f46e5;
          }
        }
      }
  
      .task-avatars {
        .MuiAvatarGroup-avatar {
          width: 28px;
          height: 28px;
          border: 2px solid white;
          font-size: 0.75rem;
          background-color: #F26522;
          transition: transform 0.3s ease-in-out;
          
          &:hover {
            transform: translateY(-5px);
            z-index: 2;
          }
        }
        
        .task-avatar {
          width: 28px;
          height: 28px;
          transition: transform 0.3s ease-in-out;
          
          &:hover {
            transform: translateY(-5px);
          }
        }
      }
  
      .task-more {
        padding: 0.25rem;
        color: #64748b;
      }
    }
  }
  
  @media (max-width: 768px) {
    .filters-container {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .filter-controls {
      width: 100%;
      flex-wrap: wrap;
    }
  
    .task-item {
      .MuiStack-root {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
      }
  
      .task-item-right {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}

.MuiMenu-paper {
  .MuiMenuItem-root {
    font-size: 14px;
    min-height: 40px;
    
    .MuiSvgIcon-root {
      font-size: 18px;
      margin-right: 2px;
    }
    
    &:hover {
      background-color: rgba(242, 101, 34, 0.08);
    }
  }
}

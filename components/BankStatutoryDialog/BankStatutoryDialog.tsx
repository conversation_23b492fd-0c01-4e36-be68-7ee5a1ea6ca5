import React from "react";
import "./BankStatutoryDialog.scss";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  TextField,
  MenuItem,
  Typography,
  Divider,
  IconButton,
} from "@mui/material";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  addBankStatutory,
  updateBankStatutory,
} from "@/app/services/users.service";
import { toast } from "react-toastify";
import { Close } from "@mui/icons-material";

// Define the interface for bank and statutory data
interface BankStatutoryData {
  _id?: string;
  salaryBasis?: string;
  salaryCurrency?: string;
  paymentType?: string;
  pfContribution?: string;
  pfNumber?: string;
  pfRate?: string;
  pfAdditionalRate?: string;
  totalRate?: string;
  esiContribution?: string;
  esiNumber?: string;
  esiRate?: string;
  esiAdditionalRate?: string;
}

interface Props {
  open: boolean;
  onClose: () => void;
  userId: string;
  bankAndStatutory?: BankStatutoryData;
}

const BankStatutoryDialog: React.FC<Props> = ({
  open,
  onClose,
  userId,
  bankAndStatutory,
}) => {
  const isEditMode = !!bankAndStatutory?._id;

  // console.log("Bank statutory data in props:", bankAndStatutory);

  const formik = useFormik({
    initialValues: {
      salaryBasis: bankAndStatutory?.salaryBasis || "",
      salaryCurrency: bankAndStatutory?.salaryCurrency || "N/A",
      paymentType: bankAndStatutory?.paymentType || "",
      pfContribution: bankAndStatutory?.pfContribution || "",
      pfNumber: bankAndStatutory?.pfNumber || "",
      pfRate: bankAndStatutory?.pfRate || "",
      pfAdditionalRate: bankAndStatutory?.pfAdditionalRate || "",
      totalRate: bankAndStatutory?.totalRate || "",
      esiContribution: bankAndStatutory?.esiContribution || "",
      esiNumber: bankAndStatutory?.esiNumber || "",
      esiRate: bankAndStatutory?.esiRate || "",
      esiAdditionalRate: bankAndStatutory?.esiAdditionalRate || "",
    },
    enableReinitialize: true,
    validationSchema: Yup.object({
      salaryBasis: Yup.string().required("Salary Basis is required"),
      salaryCurrency: Yup.string().required("Salary is required"),
      paymentType: Yup.string().required("Payment Type is required"),
      pfContribution: Yup.string().required("PF Contribution is required"),
      pfNumber: Yup.string().required("PF Number is required"),
      pfRate: Yup.string().required("PF Rate is required"),
      pfAdditionalRate: Yup.string().required("Additional Rate is required"),
      totalRate: Yup.string().required("Total Rate is required"),
      esiContribution: Yup.string().required("ESI Contribution is required"),
      esiNumber: Yup.string().required("ESI Number is required"),
      esiRate: Yup.string().required("ESI Rate is required"),
      esiAdditionalRate: Yup.string().required("Additional Rate is required"),
    }),
    onSubmit: async (values, { setSubmitting }) => {
      const body = { userId, ...values };
      try {
        if (isEditMode) {
          if (bankAndStatutory._id) {
            await updateBankStatutory(bankAndStatutory._id, body);
          }
          console.log("Bank & Statutory updated successfully");
          // toast.success("Bank & Statutory updated successfully");
        } else {
          await addBankStatutory(body);
          console.log("Bank & Statutory added successfully");
          // toast.success("Bank & Statutory added successfully");
        }
        onClose();
      } catch (error) {
        console.error("Failed to save bank & statutory data:", error);
        // toast.error("Failed to save bank & statutory data");
      } finally {
        setSubmitting(false);
      }
    },
  });

  return (
    <Dialog
      className="dialog"
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      maxWidth="md"
      fullWidth
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "1rem !important",
        }}
      >
        <Typography
          variant="h6"
          sx={{ fontSize: "20px", fontWeight: "600", color: "#202C4B" }}
        >
          {isEditMode ? "Edit Bank & Statutory" : "Bank & Statutory"}
        </Typography>
        <IconButton
          onClick={onClose}
          aria-label="close"
          sx={{
            backgroundColor: "#6b7280",
            backgroundImage: "none",
            borderRadius: "50%",
            color: "#fff",
            height: "20px",
            width: "20px",
            margin: 0,
            padding: 0,
            "&:hover": {
              backgroundColor: "#D93D3D",
            },
            "& .MuiSvgIcon-root": {
              fontSize: "14px",
            },
          }}
        >
          <Close />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ padding: "1rem !important" }}>
        <Box
          component="form"
          onSubmit={formik.handleSubmit}
          display="flex"
          flexDirection="column"
          gap={2}
        >
          {/* Basic Salary Information */}
          <Typography
            variant="subtitle1"
            fontWeight="bold"
            sx={{ color: "#202C4B" }}
          >
            Basic Salary Information
          </Typography>
          <Box display="flex" gap={2}>
            <Box sx={{ width: "100%" }}>
              <label>
                Salary Basis <span className="required">*</span>
              </label>
              <TextField
                name="salaryBasis"
                value={formik.values.salaryBasis}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                select
                fullWidth
                error={
                  formik.touched.salaryBasis && !!formik.errors.salaryBasis
                }
              >
                <MenuItem value="Weekly">Weekly</MenuItem>
                <MenuItem value="Monthly">Monthly</MenuItem>
                <MenuItem value="Annually">Annually</MenuItem>
              </TextField>
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>Salary</label>
              <TextField
                name="salaryCurrency"
                value={formik.values.salaryCurrency}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                fullWidth
                error={
                  formik.touched.salaryCurrency &&
                  !!formik.errors.salaryCurrency
                }
              />
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>
                Payment Type <span className="required">*</span>
              </label>
              <TextField
                name="paymentType"
                value={formik.values.paymentType}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                select
                fullWidth
                error={
                  formik.touched.paymentType && !!formik.errors.paymentType
                }
              >
                <MenuItem value="Cash">Cash</MenuItem>
                <MenuItem value="Debit Card">Debit Card</MenuItem>
                <MenuItem value="Mobile Payment">Mobile Payment</MenuItem>
              </TextField>
            </Box>
          </Box>

          <Divider />

          {/* PF Information */}
          <Typography
            variant="subtitle1"
            fontWeight="bold"
            sx={{ color: "#202C4B" }}
          >
            PF Information
          </Typography>
          <Box display="flex" gap={2}>
            <Box sx={{ width: "100%" }}>
              <label>
                PF Contribution <span className="required">*</span>
              </label>
              <TextField
                name="pfContribution"
                value={formik.values.pfContribution}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                select
                fullWidth
                error={
                  formik.touched.pfContribution &&
                  !!formik.errors.pfContribution
                }
              >
                <MenuItem value="EmployeeContribution">
                  Employee Contribution
                </MenuItem>
                <MenuItem value="EmployerContribution">
                  Employer Contribution
                </MenuItem>
                <MenuItem value="ProvidentFundInterest">
                  Provident Fund Interest
                </MenuItem>
              </TextField>
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>
                PF No <span className="required">*</span>
              </label>
              <TextField
                name="pfNumber"
                value={formik.values.pfNumber}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                fullWidth
                error={formik.touched.pfNumber && !!formik.errors.pfNumber}
              />
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>
                Employee PF Rate <span className="required">*</span>
              </label>
              <TextField
                name="pfRate"
                value={formik.values.pfRate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                fullWidth
                error={formik.touched.pfRate && !!formik.errors.pfRate}
              />
            </Box>
          </Box>
          <Box display="flex" gap={2}>
            <Box sx={{ width: "100%" }}>
              <label>
                Additional Rate <span className="required">*</span>
              </label>
              <TextField
                name="pfAdditionalRate"
                value={formik.values.pfAdditionalRate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                select
                fullWidth
                error={
                  formik.touched.pfAdditionalRate &&
                  !!formik.errors.pfAdditionalRate
                }
              >
                <MenuItem value="ESI">ESI</MenuItem>
                <MenuItem value="EPS">EPS</MenuItem>
                <MenuItem value="EPF">EPF</MenuItem>
              </TextField>
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>
                Total Rate <span className="required">*</span>
              </label>
              <TextField
                name="totalRate"
                value={formik.values.totalRate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                fullWidth
                error={formik.touched.totalRate && !!formik.errors.totalRate}
              />
            </Box>
          </Box>

          <Divider />

          {/* ESI Information */}
          <Typography
            variant="subtitle1"
            fontWeight="bold"
            sx={{ color: "#202C4B" }}
          >
            ESI Information
          </Typography>
          <Box display="flex" gap={2}>
            <Box sx={{ width: "100%" }}>
              <label>
                ESI Contribution <span className="required">*</span>
              </label>
              <TextField
                name="esiContribution"
                value={formik.values.esiContribution}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                select
                fullWidth
                error={
                  formik.touched.esiContribution &&
                  !!formik.errors.esiContribution
                }
              >
                <MenuItem value="EmployeeContribution">
                  Employee Contribution
                </MenuItem>
                <MenuItem value="EmployerContribution">
                  Employer Contribution
                </MenuItem>
                <MenuItem value="MaternityBenefit">Maternity Benefit</MenuItem>
              </TextField>
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>
                ESI Number <span className="required">*</span>
              </label>
              <TextField
                name="esiNumber"
                value={formik.values.esiNumber}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                fullWidth
                error={formik.touched.esiNumber && !!formik.errors.esiNumber}
              />
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>
                Employee ESI Rate <span className="required">*</span>
              </label>
              <TextField
                name="esiRate"
                value={formik.values.esiRate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                fullWidth
                error={formik.touched.esiRate && !!formik.errors.esiRate}
              />
            </Box>
          </Box>
          <Box display="flex" gap={2}>
            <Box sx={{ width: "100%" }}>
              <label>
                Additional Rate <span className="required">*</span>
              </label>
              <TextField
                name="esiAdditionalRate"
                value={formik.values.esiAdditionalRate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                select
                fullWidth
                error={
                  formik.touched.esiAdditionalRate &&
                  !!formik.errors.esiAdditionalRate
                }
              >
                <MenuItem value="ESI">ESI</MenuItem>
                <MenuItem value="EPS">EPS</MenuItem>
                <MenuItem value="EPF">EPF</MenuItem>
              </TextField>
            </Box>
            <Box sx={{ width: "100%" }}>
              <label>
                Total Rate <span className="required">*</span>
              </label>
              <TextField
                name="totalRate"
                value={formik.values.totalRate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                fullWidth
                error={formik.touched.totalRate && !!formik.errors.totalRate}
              />
            </Box>
          </Box>
        </Box>

        <DialogActions
          sx={{ padding: "0px !important", paddingTop: "1rem !important" }}
        >
          <Button
            onClick={onClose}
            variant="outlined"
            sx={{
              borderColor: "#E5E7EB",
              color: "#6B7280",
              textTransform: "none",
              borderRadius: "5px",
              padding: "6px 16px",
              minWidth: "80px",
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={() => formik.handleSubmit()}
            variant="contained"
            disabled={formik.isSubmitting}
            sx={{
              backgroundColor: "#F26522",
              color: "#FFF",
              textTransform: "none",
              borderRadius: "5px",
              padding: "6px 16px",
              minWidth: "80px",
              "&:hover": { backgroundColor: "#E55A1B" },
            }}
          >
            {isEditMode ? "Update" : "Save"}
          </Button>
        </DialogActions>
      </DialogContent>
    </Dialog>
  );
};

export default BankStatutoryDialog;

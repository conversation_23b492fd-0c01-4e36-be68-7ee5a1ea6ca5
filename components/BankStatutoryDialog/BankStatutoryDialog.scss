.dialog {
  // We can remove most of these styles as they're now in the global theme

  .MuiInputLabel-root {
    transform: translate(14px, 12px) scale(1);
    transition: all 0.2s ease-out;
  }

  .MuiInputLabel-shrink {
    transform: translate(14px, -6px) scale(0.75);
  }

  // Override the global margin-top for labels in this component
  label {
    margin-top: 0px !important;
  }

  // Form layout
  .form-row {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);

    @media (max-width: 767px) {
      flex-direction: column;
    }
  }
}

@media screen and (max-width: 767px) {
  .MuiDialog-paper {
    min-width: 80% !important;
  }
}

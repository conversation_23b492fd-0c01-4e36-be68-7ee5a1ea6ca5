"use client";
import { useState } from "react";
import {
  Text<PERSON>ield,
  Button,
  Typography,
  Box,
  InputAdornment,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import "./ResetPasswordForm.scss";
import { toast, ToastContainer } from "react-toastify";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { resetPassword } from "@/app/services/auth/auth.service";
import Loader from "../Loader/Loader";

// Validation schema using Yup
const ResetPasswordFormSchema = Yup.object().shape({
  password: Yup.string()
    .min(8, "Password must be at least 8 characters")
    .max(64, "Max 64 chars allowed")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>]).*$/,
      "Need 1 lowercase, 1 uppercase, 1 special char"
    )
    .required("Password is required"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("password"), undefined], "Passwords must match")
    .required("Confirm Password is required"),
});

const ResetPasswordForm: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] =
    useState<boolean>(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  // Initial form values
  const initialValues = {
    password: "",
    confirmPassword: "",
  };

  const togglePasswordVisibility = () => setShowPassword(!showPassword);
  const toggleConfirmPasswordVisibility = () =>
    setShowConfirmPassword(!showConfirmPassword);

  const handleLoginAccount = () => {
    router.push("/login");
  };

  const handleSubmit = async (values: {
    password: string;
    confirmPassword: string;
  }) => {
    if (!token) {
      // toast.error("Invalid or missing token. Please use a valid reset link.");
      return;
    }

    setIsLoading(true);
    try {
      await resetPassword({
        token: token,
        newPassword: values.password,
      });
      setIsLoading(false);
      toast.success("Password reset successfully!");
      setTimeout(() => router.push("/success"), 2000);
    } catch (error) {
      setIsLoading(false);
      // toast.error("Failed to reset password. Please try again.");
      console.error("API Error:", error);
    }
  };

  return (
    <div className="login-form-container">
      <ToastContainer />
      <div className="logo-container">
        <div className="logo">
          <Typography variant="h6" component="span" className="logo-text">
            <Image
              src="/assets/Aadvik-noBG.png"
              alt="Logo"
              width={300}
              height={100}
              className="logo-image"
            />
          </Typography>
        </div>
      </div>

      <Box className="form-wrapper">
        <Typography variant="h4" component="h1" className="form-title">
          Reset Password
        </Typography>
        <Typography
          variant="body2"
          color="textSecondary"
          className="form-subtitle"
        >
          Your new password must be different from previously used passwords.
        </Typography>

        <Formik
          initialValues={initialValues}
          validationSchema={ResetPasswordFormSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isSubmitting }) => (
            <Form className="login-form">
              <div className="form-field">
                <Typography variant="body2" className="field-label">
                  Password
                </Typography>
                <Field
                  as={TextField}
                  name="password"
                  type={showPassword ? "text" : "password"}
                  fullWidth
                  placeholder="Enter your password"
                  variant="outlined"
                  disabled={isLoading || isSubmitting}
                  error={touched.password && !!errors.password}
                  helperText={<ErrorMessage name="password" />}
                  className="input-field"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Button
                          className="visibility-toggle"
                          onClick={togglePasswordVisibility}
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </Button>
                      </InputAdornment>
                    ),
                  }}
                />
              </div>

              <div className="form-field">
                <Typography variant="body2" className="field-label">
                  Confirm Password
                </Typography>
                <Field
                  as={TextField}
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  fullWidth
                  placeholder="Confirm your password"
                  variant="outlined"
                  disabled={isLoading || isSubmitting}
                  error={touched.confirmPassword && !!errors.confirmPassword}
                  helperText={<ErrorMessage name="confirmPassword" />}
                  className="input-field"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Button
                          className="visibility-toggle"
                          onClick={toggleConfirmPasswordVisibility}
                        >
                          {showConfirmPassword ? (
                            <VisibilityOff />
                          ) : (
                            <Visibility />
                          )}
                        </Button>
                      </InputAdornment>
                    ),
                  }}
                />
              </div>

              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                disabled={isLoading || isSubmitting || !token}
                className="submit-button"
                startIcon={isLoading ? <Loader loading={isLoading} /> : null}
              >
                {isLoading ? "Submitting..." : "Submit"}
              </Button>
            </Form>
          )}
        </Formik>

        <div className="account-options">
          <Typography variant="body2" className="no-account">
            Return to{" "}
            <Button
              variant="text"
              color="primary"
              className="create-account"
              disabled={isLoading}
              onClick={handleLoginAccount}
            >
              Sign In
            </Button>
          </Typography>
        </div>

        <Typography variant="body2" className="copyright">
          Copyright © 2024 - 2025 AAdvik Labs
        </Typography>
      </Box>
    </div>
  );
};

export default ResetPasswordForm;

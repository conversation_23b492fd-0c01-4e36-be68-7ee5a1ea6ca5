/* "use client";

import { useEffect, useState } from "react";
import { getCompany } from "@/app/services/setting.service";

const DynamicFavicon = () => {
  const [faviconUrl, setFaviconUrl] = useState<string>("/assets/Aadvik-noBG.png");

  useEffect(() => {
    const fetchCompanyLogo = async () => {
      try {
        const response = await getCompany();
        if (response && response.company && response.company.logoUrl) {
          setFaviconUrl(response.company.logoUrl);
        }
      } catch (error) {
        console.log("Failed to fetch company logo for favicon:", error);
        // Keep default favicon if fetch fails
      }
    };

    fetchCompanyLogo();
  }, []);

  useEffect(() => {
    // Update favicon link element
    const linkElements = document.querySelectorAll("link[rel*='icon']");
    
    // Remove existing favicon links
    linkElements.forEach(element => {
      element.parentNode?.removeChild(element);
    });
    
    // Create new favicon link
    const link = document.createElement("link");
    link.rel = "icon";
    link.href = faviconUrl;
    document.head.appendChild(link);
    
    // Also update apple-touch-icon for iOS devices
    const appleLink = document.createElement("link");
    appleLink.rel = "apple-touch-icon";
    appleLink.href = faviconUrl;
    document.head.appendChild(appleLink);
    
  }, [faviconUrl]);

  return null; // This component doesn't render anything
};

export default DynamicFavicon; */
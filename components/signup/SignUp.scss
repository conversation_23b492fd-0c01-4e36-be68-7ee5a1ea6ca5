// @import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
$primary-color: #F26522;
$secondary-color: #3B7080;
$gray-900: #111827;
$gray-700: #202c4b;
$gray-500: #6B7280;
$light-bg: #F8F9FA;
$white: #FFFAF8;

.login-page {
  display: flex;
  min-height: 100vh;
  width: 100%;
  font-family: "Roboto", sans-serif;
  overflow: hidden;
  background-color: $white;

  @media (max-width: 991.98px) {
    flex-direction: column;
  }

  .login-left {
    flex: 0.67;
    background: linear-gradient(180deg, #FF7B3D 0%, #5C260C 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      background: linear-gradient(180deg, rgba(255, 123, 61, 0.8) 0%, rgba(92, 38, 12, 0.8) 100%);
      backdrop-filter: blur(6px);
      z-index: 0;
    }

    .login-left-content {
      max-width: 570px;
      color: $white;
      z-index: 1;
      padding: 40px;
      background: rgba(255, 247, 243, 0.25);
      border-radius: 15px;
      backdrop-filter: blur(26px);
      text-align: center;
      border: 1px solid #e5e7eb !important;

      h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        line-height: 1.2;
        color: $white;
      }

      p {
        font-size: 1.25rem;
        font-weight: 500;
        margin-bottom: 2rem;
        opacity: 0.9;
        color: $white;
      }

      .login-image {
        margin: 2rem auto;
        max-width: 400px;

        img {
          width: 100%;
          height: auto;
          border-radius: 8px;
        }
      }
    }

    @media (max-width: 991.98px) {
      display: none;
    }
  }

  // Right side with login form
  .login-right {
    flex: 1;
    background-color: $white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }
}

.login-form-container {
  width: 100%;
  max-width: 490px;

  .signup-logo-container {
    display: flex;
    justify-content: center;

    .logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .aadvik-logo-text {
        font-weight: 700;
        font-size: 1.5rem;
        color: $gray-900;
        margin-bottom: 50px;
      }
    }
  }

  .form-wrapper {
    width: 100%;
    // padding: 0 1rem;

    .form-title {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 0.5rem;
      color: $gray-900;
      text-align: center;
    }

    .form-subtitle {
      margin-bottom: 2rem;
      color: $gray-500;
      text-align: center;
      font-size: 0.875rem;
    }

    .error-message {
      margin-bottom: 1rem;
      padding: 0.5rem;
      background-color: rgba(231, 13, 13, 0.1);
      border-radius: 4px;
      color: #E70D0D;
      text-align: center;
    }

    .login-form {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      margin-bottom: 1.5rem;

      .form-field {
        .field-label {
          margin-bottom: 0.5rem;
          display: block;
          font-weight: 500;
          color: $gray-700;
          font-size: 14px;
        }

        .input-field {
          .MuiOutlinedInput-root {
            border-radius: 5px;
            background-color: #FFFFFF;
            font-size: 0.95rem;


            &:hover .MuiOutlinedInput-notchedOutline {
              border-color: $primary-color;
            }

            &.Mui-focused .MuiOutlinedInput-notchedOutline {
              border-color: $primary-color;
              border-width: 2px;
            }

            .MuiOutlinedInput-input {
              padding: 0.5rem 0.85rem;
              color: $gray-700;
              min-height: 32px;
            }
          }

          .field-icon {
            color: $gray-500;
          }

          .visibility-toggle {
            color: $gray-500;

            &:hover {
              color: $primary-color;
            }
          }
        }
      }

      .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .remember-me {
          .MuiFormControlLabel-label {
            font-size: 0.875rem;
            color: $gray-700;
          }

          .remember-checkbox {
            // color: $primary-color;

            &.Mui-checked {
              color: $primary-color;
            }
          }
        }

        .forgot-password {
          font-size: 0.95rem;
          color: #e70d0d;
          text-transform: none;
          padding: 0;
          min-width: auto;

          &:hover {
            // color: darken(#e70d0d , 10%);
            background-color: transparent;
          }
        }
      }

      .submit-button {
        background-color: $primary-color;
        color: $white;
        padding: 0.65rem 1rem;
        border-radius: 5px;
        font-weight: 500;
        font-size: 0.95rem;
        text-transform: none;
        transition: all 0.5s ease;
        box-shadow: 0 4px 12px rgba($primary-color, 0.2);

        &:hover {
          // background-color: darken($primary-color, 10%);
          box-shadow: 0 6px 16px rgba($primary-color, 0.3);
        }

        &.Mui-disabled {
          // background-color: lighten($primary-color, 20%);
          color: rgba($white, 0.7);
        }
      }
    }

    .account-options {
      text-align: center;
      margin: 1.5rem 0;

      .no-account {
        color: $gray-500;
        font-size: 0.875rem;
      }

      .create-account {
        color: $primary-color;
        font-weight: 600;
        text-transform: none;
        padding: 0 4px;
        min-width: auto;

        &:hover {
          // color: darken($primary-color, 10%);
          background-color: transparent;
        }
      }
    }

    .copyright {
      text-align: center;
      color: $gray-500;
      margin-top: 2rem;
      font-size: 0.75rem;
    }
  }
}

// MUI Overrides
.MuiButton-root {
  &.MuiButton-contained {
    &.MuiButton-containedPrimary {
      background-color: $primary-color;

      &:hover {
        // background-color: darken($primary-color, 10%);
      }
    }
  }

  &.MuiButton-text {
    &.MuiButton-textPrimary {
      color: $primary-color;
    }
  }
}

.MuiCheckbox-root {
  &.MuiCheckbox-colorPrimary {
    &.Mui-checked {
      color: $primary-color;
    }
  }
}
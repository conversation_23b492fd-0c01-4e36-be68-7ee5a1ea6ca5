"use client";
import { useState } from "react";
import {
  <PERSON><PERSON>ield,
  <PERSON>ton,
  Typography,
  CircularProgress,
  Box,
  InputAdornment,
  IconButton,
  Checkbox,
  FormControlLabel,
  Autocomplete,
} from "@mui/material";
import { useRouter } from "next/navigation";
import { Visibility, VisibilityOff, Email } from "@mui/icons-material";
import toast, { Toaster } from "react-hot-toast";
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from "formik";
import { addSignupSection } from "@/app/services/auth/auth.service";
import * as Yup from "yup";
import "./SignUp.scss";
import Image from "next/image";

// Update the validation schema to reflect role as a string
const validationSchema = Yup.object({
  firstName: Yup.string().required("First name is required"),
  lastName: Yup.string().required("Last name is required"),
  email: Yup.string()
    .email("Please enter a valid email address")
    .required("Email is required"),
  password: Yup.string()
    .min(6, "Password must be at least 6 characters long")
    .required("Password is required"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("password"), undefined], "Passwords do not match")
    .required("Confirm password is required"),
  role: Yup.string().required("Role is required"),
  rememberMe: Yup.boolean()
    .oneOf([true], "Please agree to Terms & Privacy")
    .required("Please agree to Terms & Privacy"),
});

const roleOptions = ["Admin", "Employee", "Manager", "HR", "SuperAdmin"];

const SignUp: React.FC = () => {
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] =
    useState<boolean>(false);
  const router = useRouter();

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword((prev) => !prev);
  };

  const initialValues = {
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "",
    rememberMe: false,
  };

  // Update handleSubmit to pass role as a string directly
  const handleSubmit = async (
    values: typeof initialValues,
    { setSubmitting }: FormikHelpers<typeof initialValues>
  ) => {
    try {
      const {  firstName, lastName, email, password, role } = values;
      console.log("Signup Payload:", {
        
        firstName,
        lastName,
        email,
        password,
        role,
      });

      // Pass role as a string instead of converting to array
      await addSignupSection({
        
        firstName,
        lastName,
        email,
        password,
        roles: role,
      });

      // toast.success("Successfully signed up!");
      setTimeout(() => {
        router.push("/login");
      }, 1500);
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "An error occurred during signup. Please try again.";
      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="login-form-container">
      <Toaster position="top-right" reverseOrder={false} />
      <div className="signup-logo-container">
        <div className="logo">
          <Typography
            variant="h6"
            component="span"
            className="aadvik-logo-text"
          >
            <Image
              src="/assets/Aadvik-noBG.png"
              alt="Logo"
              width={200}
              height={10}
              className="logo-image"
            />
          </Typography>
        </div>
      </div>

      <Box className="form-wrapper">
        <Typography variant="h4" component="h1" className="form-title">
          Sign Up
        </Typography>
        <Typography
          variant="body2"
          color="textSecondary"
          className="form-subtitle"
        >
          Please enter your details to sign Up
        </Typography>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, errors, touched, setFieldValue, values }) => (
            <Form className="login-form">
              <div className="form-field">
                <Typography variant="body2" className="field-label">
                  First Name
                </Typography>
                <Field
                  as={TextField}
                  name="firstName"
                  type="text"
                  fullWidth
                  placeholder="Enter your first name"
                  variant="outlined"
                  disabled={isSubmitting}
                  error={touched.firstName && !!errors.firstName}
                  helperText={<ErrorMessage name="firstName" />}
                  className="input-field"
                />
              </div>

              <div className="form-field">
                <Typography variant="body2" className="field-label">
                  Last Name
                </Typography>
                <Field
                  as={TextField}
                  name="lastName"
                  type="text"
                  fullWidth
                  placeholder="Enter your last name"
                  variant="outlined"
                  disabled={isSubmitting}
                  error={touched.lastName && !!errors.lastName}
                  helperText={<ErrorMessage name="lastName" />}
                  className="input-field"
                />
              </div>

              <div className="form-field">
                <Typography variant="body2" className="field-label">
                  Email Address
                </Typography>
                <Field
                  as={TextField}
                  name="email"
                  type="email"
                  fullWidth
                  placeholder="Enter your email"
                  variant="outlined"
                  disabled={isSubmitting}
                  error={touched.email && !!errors.email}
                  helperText={<ErrorMessage name="email" />}
                  className="input-field"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Email className="field-icon" />
                      </InputAdornment>
                    ),
                  }}
                />
              </div>

              <div className="form-field">
                <Typography variant="body2" className="field-label">
                  Password
                </Typography>
                <Field
                  as={TextField}
                  name="password"
                  type={showPassword ? "text" : "password"}
                  fullWidth
                  placeholder="Enter your password"
                  variant="outlined"
                  disabled={isSubmitting}
                  error={touched.password && !!errors.password}
                  helperText={<ErrorMessage name="password" />}
                  className="input-field"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={togglePasswordVisibility}
                          edge="end"
                          disabled={isSubmitting}
                          className="visibility-toggle"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </div>

              <div className="form-field">
                <Typography variant="body2" className="field-label">
                  Confirm Password
                </Typography>
                <Field
                  as={TextField}
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  fullWidth
                  placeholder="Confirm your password"
                  variant="outlined"
                  disabled={isSubmitting}
                  error={touched.confirmPassword && !!errors.confirmPassword}
                  helperText={<ErrorMessage name="confirmPassword" />}
                  className="input-field"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle confirm password visibility"
                          onClick={toggleConfirmPasswordVisibility}
                          edge="end"
                          disabled={isSubmitting}
                          className="visibility-toggle"
                        >
                          {showConfirmPassword ? (
                            <VisibilityOff />
                          ) : (
                            <Visibility />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </div>

              <div className="form-field">
                <Typography variant="body2" className="field-label">
                  Role
                </Typography>
                <Autocomplete
                  options={roleOptions}
                  value={values.role}
                  onChange={(_, newValue) =>
                    setFieldValue("role", newValue || "")
                  }
                  disabled={isSubmitting}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      variant="outlined"
                      placeholder="Select role"
                      error={touched.role && !!errors.role}
                      helperText={<ErrorMessage name="role" />}
                      className="input-field"
                    />
                  )}
                />
              </div>

              <div className="form-options">
                <FormControlLabel
                  control={
                    <Field
                      as={Checkbox}
                      name="rememberMe"
                      color="primary"
                      disabled={isSubmitting}
                      className="remember-checkbox"
                    />
                  }
                  label=" Agree to Terms & Privacy"
                  className="remember-me"
                />
                <ErrorMessage
                  name="rememberMe"
                  component="div"
                  className="error-message"
                />
              </div>

              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                disabled={isSubmitting}
                className="submit-button"
                startIcon={
                  isSubmitting ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : null
                }
              >
                {isSubmitting ? "Signing Up..." : "Sign Up"}
              </Button>
            </Form>
          )}
        </Formik>

        <div className="account-options">
          <Typography variant="body2" className="no-account">
            Already have an account?{" "}
            <Button
              variant="text"
              color="primary"
              onClick={() => router.push("/login")}
              className="create-account"
              disabled={false}
            >
              Sign In
            </Button>
          </Typography>
        </div>

        <Typography variant="body2" className="copyright">
          Copyright © 2024 - 2025 AAdvik Labs
        </Typography>
      </Box>
    </div>
  );
};

export default SignUp;

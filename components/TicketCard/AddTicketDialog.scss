.comment-dialog {
  .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px !important;

    .title-container {
      display: flex;
      align-items: center;
      gap: 12px;

      .dialog-title-text {
        font-size: 20px;
        color: #111827;
        font-weight: 600;
      }
    }
  }

  .dialog-content {
    padding: 16px !important;
    max-height: 90vh;
    overflow-y: auto;

    .form-container {
      display: flex;
      flex-direction: column;
    }

    .form-field-row {
      display: flex;
      gap: 16px;
      margin-bottom: 1rem !important;

      .form-field {
        flex: 1;
        width: 50%;
      }
    }

    .form-field {
      margin-bottom: 0px;
      width: 100%;

      .required {
        color: red;
      }

      .custom-autocomplete {
        .MuiInputBase-root {
          padding: 8px !important;
          display: flex;
          flex-wrap: wrap;
          height: auto !important;
          min-height: 38px !important;
          max-height: none !important;
        }

        .MuiInputBase-input {
          padding: 0 !important;
          height: auto !important;
        }

        .MuiAutocomplete-endAdornment {
          position: absolute;
          right: 9px;
          top: 50%;
          transform: translateY(-50%);
        }

        .MuiAutocomplete-tag {
          margin: 3px;
        }
      }

      .autocomplete-input {
        width: 100%;

        .MuiOutlinedInput-root {
          height: auto;
          min-height: 38px;
        }
      }
    }

    .dialog-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin: 0px !important;

      .cancel-button {
        color: #666;
        border-color: #ccc;
        text-transform: none;
      }

      .save-button {
        background-color: #f26522;
        text-transform: none;

        &:hover {
          background-color: #e55a1b;
        }
      }
    }
  }
}

.input-field {
  // margin-bottom: 1rem !important;
}

label {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #202C4B !important;
  margin-bottom: 0.5rem !important;
  margin-top: 0px !important;
}

.cancel-button {
  font-weight: 500 !important;
  font-size: 14px !important;
  color: #111827 !important;
  border: 1px solid #E5E7EB !important;
  border-radius: 5px !important;
  text-transform: none !important;
  padding: 8px 13.6px !important;

  &:hover {
    border-color: #d1d5db;
  }
}

.submit-button {
  background-color: #F26522 !important;
  color: #FFF !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  border-radius: 5px !important;
  text-transform: none !important;
  padding: 8px 13.6px !important;

  &:hover {
    background-color: #d0551d;
  }
}
.issue-card {
    width: 100%;

    .issue-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-color: #E5E7EB;
        position: relative;
        background: transparent;
        padding: 1rem 1.25rem 1rem;
        border-bottom: 1px solid rgb(0, 0, 0, 0.175);
    }

    .issue-card-content {
        .issue-card-title {
            display: flex;

            .issue-card-title-text {
                font-size: 16px;
                font-weight: 600;
                color: #111827;
                cursor: pointer;
            }
        }

        .issue-card-assigned-to {
            // margin-right: .5rem;
            font-size: 14px;
            color: #6B7280;

            .text-assigned-to {
                color: #212529;
                margin-left: .25rem;
            }
        }
    }
}
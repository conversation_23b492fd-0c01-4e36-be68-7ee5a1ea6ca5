"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON>alogContent,
  <PERSON>alogActions,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  Box,
  Typography,
  FormHelperText,
  Autocomplete,
  Chip,
  IconButton,
} from "@mui/material";
import { Cancel, Close } from "@mui/icons-material";
import { useState, useEffect, useRef } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import TextEditor from "../TextEditor/TextEditor";
import useAuthStore from "@/store/authStore";
import {
  addTicket,
  updateTicket,
  getTicketById,
  getAllTicketCategories,
} from "@/app/services/tickets/tickets.service";
import { getUsers } from "@/app/services/users.service";
import { getDepartments } from "@/app/services/department.service";
import Loader from "@/components/Loader/Loader";
import "./AddTicketDialog.scss";

interface Department {
  _id: string;
  departmentName: string;
  isActive: boolean;
  isDeleted: boolean;
  departmentHead: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    avatar: string;
    isActive: boolean;
    isDeleted: boolean;
  } | null;
}

interface TicketCategory {
  _id: string;
  category: string;
  departmentId: {
    _id: string;
    departmentName: string;
    isActive: boolean;
    isDeleted: boolean;
  };
  isActive: boolean;
  isDeleted: boolean;
}

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
}

interface AddTicketDialogProps {
  open: boolean;
  onClose: () => void;
  onAdd: () => void;
  isEditing: boolean;
  ticketId?: string;
  initialPayload?: {
    empId: string;
    category: string;
    departmentId?: string;
    subject: string;
    cc: string[];
    description: string;
    priority: string;
    status: string;
  };
}

const stripHtml = (html: string) => {
  const div = document.createElement("div");
  div.innerHTML = html;
  return div.textContent || div.innerText || "";
};

const validationSchema = Yup.object({
  department: Yup.object().nullable().required("Department is required"),
  category: Yup.object().nullable(),
  subject: Yup.string().required("Subject is required"),
  cc: Yup.array().of(Yup.string()),
  description: Yup.string().required("Description is required"),
  priority: Yup.string()
    .oneOf(["High", "Medium", "Low"], "Invalid priority")
    .required("Priority is required"),
  status: Yup.string()
    .oneOf(["Open", "Closed", "Reopened", "OnHold"], "Invalid status")
    .required("Status is required"),
});

const AddTicketDialog: React.FC<AddTicketDialogProps> = ({
  open,
  onClose,
  onAdd,
  isEditing,
  ticketId,
  initialPayload,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [departmentsLoading, setDepartmentsLoading] = useState(false);
  const [categories, setCategories] = useState<TicketCategory[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const { employeeId: authEmployeeId } = useAuthStore();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Initialize form with reset values
  const formik = useFormik({
    initialValues: {
      department: null as Department | null,
      category: null as TicketCategory | null,
      subject: "",
      cc: [] as string[],
      description: "",
      priority: "",
      status: "Open", // Default to "Open"
    },
    validationSchema,
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      if (!authEmployeeId) {
        toast.error("Employee ID is missing. Please log in again.");
        setSubmitting(false);
        return;
      }

      const ticketPayload = {
        empId: authEmployeeId,
        category: values.category?._id || null,
        departmentId: values.department?._id || "",
        subject: values.subject,
        cc: values.cc,
        description: stripHtml(values.description),
        priority: values.priority,
        status: values.status,
      };

      try {
        let response: { message: string };
        if (isEditing && ticketId) {
          response = await updateTicket(ticketId, ticketPayload);
          toast.success(response.message || "Ticket updated successfully");
        } else {
          response = await addTicket(ticketPayload);
          toast.success(response.message || "Ticket added successfully");
        }
        onAdd();
        resetForm();
        onClose();
      } catch (err: any) {
        console.error(
          `Error ${isEditing ? "updating" : "adding"} ticket:`,
          err
        );
        const errorMessage =
          err.response?.data?.message || "Failed to save ticket";
        toast.error(errorMessage);
      } finally {
        setSubmitting(false);
      }
    },
  });

  // Reset form and ensure status is "Open" in add mode
  useEffect(() => {
    if (open && !isEditing) {
      formik.resetForm();
      formik.setFieldValue("status", "Open"); // Explicitly set status to "Open"
      setCategories([]);
    }
  }, [open, isEditing]);

  // Fetch departments and users
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setDepartmentsLoading(true);
        const response = await getDepartments();
        if (response.success && Array.isArray(response.departments.results)) {
          setDepartments(response.departments.results);
        } else {
          toast.error("Failed to load departments");
        }
      } catch (error) {
        console.error("Error fetching departments:", error);
        toast.error("Failed to load departments");
      } finally {
        setDepartmentsLoading(false);
      }
    };

    const fetchUsers = async () => {
      try {
        setUsersLoading(true);
        const response = await getUsers();
        const fetchedUsers: User[] = response.users.results.map(
          (user: any) => ({
            _id: user._id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
          })
        );
        setUsers(fetchedUsers);
      } catch (error) {
        console.error("Error fetching users:", error);
        toast.error("Failed to load users");
      } finally {
        setUsersLoading(false);
      }
    };

    if (open) {
      fetchDepartments();
      fetchUsers();
    }
  }, [open]);

  // Fetch ticket categories based on selected department
  useEffect(() => {
    const fetchCategories = async () => {
      if (!formik.values.department?._id) {
        setCategories([]);
        formik.setFieldValue("category", null);
        return;
      }

      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      try {
        setCategoriesLoading(true);
        const response = await getAllTicketCategories();

        if (abortControllerRef.current.signal.aborted) return;

        if (
          response.success &&
          Array.isArray(response.ticketCategories.results)
        ) {
          const filteredCategories = response.ticketCategories.results.filter(
            (cat: TicketCategory) =>
              cat.departmentId._id === formik.values.department?._id
          );
          setCategories(filteredCategories);

          if (
            formik.values.category &&
            !filteredCategories.find(
              (cat: TicketCategory) => cat._id === formik.values.category?._id
            )
          ) {
            formik.setFieldValue("category", null);
          }
        } else {
          toast.error("Failed to load ticket categories");
          setCategories([]);
          formik.setFieldValue("category", null);
        }
      } catch (error: any) {
        if (error.name === "AbortError") return;
        console.error("Error fetching categories:", error);
        toast.error("Failed to load ticket categories");
        setCategories([]);
        formik.setFieldValue("category", null);
      } finally {
        if (!abortControllerRef.current.signal.aborted) {
          setCategoriesLoading(false);
        }
      }
    };

    if (open && formik.values.department?._id) {
      fetchCategories();
    }

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [open, formik.values.department?._id]);

  // Fetch ticket details for edit mode
  useEffect(() => {
    const abortController = new AbortController();
    if (open && isEditing && ticketId && !isLoading) {
      (async () => {
        try {
          setIsLoading(true);
          const response = await getTicketById(ticketId);
          if (abortController.signal.aborted) return;
          const ticket = response.ticket;

          const department = ticket.departmentId
            ? {
                _id: ticket.departmentId._id,
                departmentName: ticket.departmentId.departmentName,
                isActive: ticket.departmentId.isActive,
                isDeleted: ticket.departmentId.isDeleted,
                departmentHead: ticket.departmentId.departmentHead,
              }
            : null;
          formik.setFieldValue("department", department);

          formik.setFieldValue(
            "category",
            ticket.category
              ? {
                  _id: ticket.category._id,
                  category: ticket.category.category,
                  departmentId: ticket.category.departmentId,
                  isActive: ticket.category.isActive,
                  isDeleted: ticket.category.isDeleted,
                }
              : null
          );

          formik.setFieldValue("subject", ticket.subject || "");
          formik.setFieldValue(
            "cc",
            ticket.cc.map((user: User) => user._id) || []
          );
          formik.setFieldValue("description", ticket.description || "");
          formik.setFieldValue("priority", ticket.priority || "");
          formik.setFieldValue("status", ticket.status || "Open"); // Fallback to "Open" if status is invalid
        } catch (error: any) {
          if (error.name === "AbortError") return;
          console.error("Error fetching ticket:", error);
          toast.error("Failed to load ticket data");
        } finally {
          if (!abortController.signal.aborted) {
            setIsLoading(false);
          }
        }
      })();
    }
    return () => {
      abortController.abort();
    };
  }, [open, isEditing, ticketId]);

  const getUserLabel = (option: User) => {
    const sameNameUsers = users.filter(
      (u) =>
        `${u.firstName} ${u.lastName}` ===
        `${option.firstName} ${option.lastName}`
    );
    return sameNameUsers.length > 1
      ? `${option.firstName} ${option.lastName} (${option.email})`
      : `${option.firstName} ${option.lastName}`;
  };

  return (
    <>
      {isLoading || departmentsLoading || categoriesLoading || usersLoading ? (
        <Loader
          loading={
            isLoading || departmentsLoading || categoriesLoading || usersLoading
          }
        />
      ) : (
        <Dialog
          open={open}
          onClose={(event, reason) => {
            if (reason !== "backdropClick") {
              onClose();
            }
          }}
          disableEscapeKeyDown
          maxWidth="md"
          fullWidth
          className="comment-dialog"
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "1rem !important",
            }}
          >
            <Typography
              sx={{ fontSize: "20px", fontWeight: 600, color: "#202C4B" }}
            >
              {isEditing ? "Edit Ticket" : "Add Ticket"}
            </Typography>
            <IconButton
              onClick={() => {
                formik.resetForm();
                onClose();
              }}
              sx={{
                backgroundColor: "#6b7280",
                borderRadius: "50%",
                color: "#fff",
                height: "20px",
                width: "20px",
                margin: 0,
                padding: 0,
                "&:hover": { backgroundColor: "#d55a1d" },
                "& .MuiSvgIcon-root": { fontSize: "14px" },
              }}
            >
              <Close />
            </IconButton>
          </DialogTitle>
          <DialogContent className="dialog-content">
            <form onSubmit={formik.handleSubmit}>
              <Box className="form-container">
                <Box className="form-field input-field">
                  <Typography variant="subtitle1" component="label">
                    Subject <span className="required">*</span>
                  </Typography>
                  <TextField
                    fullWidth
                    variant="outlined"
                    name="subject"
                    value={formik.values.subject}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={
                      formik.touched.subject && Boolean(formik.errors.subject)
                    }
                    helperText={formik.touched.subject && formik.errors.subject}
                  />
                </Box>
                <Box className="form-field-row">
                  <Box className="form-field input-field">
                    <Typography variant="subtitle1" component="label">
                      Department <span className="required">*</span>
                    </Typography>
                    <Autocomplete
                      options={departments}
                      getOptionLabel={(option: Department) =>
                        option.departmentName
                      }
                      value={formik.values.department}
                      onChange={(_, newValue: Department | null) => {
                        formik.setFieldValue("department", newValue);
                        formik.setFieldValue("category", null);
                      }}
                      onBlur={() => formik.setFieldTouched("department", true)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          variant="outlined"
                          error={
                            formik.touched.department &&
                            Boolean(formik.errors.department)
                          }
                          helperText={
                            formik.touched.department &&
                            formik.errors.department
                          }
                        />
                      )}
                      disabled={departmentsLoading}
                      noOptionsText={
                        departmentsLoading
                          ? "Loading..."
                          : "No departments available"
                      }
                      className="custom-autocomplete"
                    />
                  </Box>
                  <Box className="form-field input-field">
                    <Typography variant="subtitle1" component="label">
                      Ticket Category
                    </Typography>
                    <Autocomplete
                      options={categories}
                      getOptionLabel={(option: TicketCategory) =>
                        option.category
                      }
                      value={formik.values.category}
                      onChange={(_, newValue: TicketCategory | null) => {
                        formik.setFieldValue("category", newValue);
                      }}
                      onBlur={() => formik.setFieldTouched("category", true)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          variant="outlined"
                          error={
                            formik.touched.category &&
                            Boolean(formik.errors.category)
                          }
                          helperText={
                            formik.touched.category && formik.errors.category
                          }
                        />
                      )}
                      disabled={categoriesLoading || !formik.values.department}
                      noOptionsText={
                        !formik.values.department
                          ? "Please select a department first"
                          : categoriesLoading
                            ? "Loading..."
                            : "No categories available"
                      }
                      className="custom-autocomplete"
                    />
                  </Box>
                </Box>
                <Box className="form-field input-field">
                  <Typography variant="subtitle1" component="label">
                    CC (Employees)
                  </Typography>
                  <Autocomplete
                    multiple
                    options={users}
                    getOptionLabel={getUserLabel}
                    value={users.filter((user) =>
                      formik.values.cc.includes(user._id)
                    )}
                    onChange={(_, newValue: User[]) => {
                      const newIds = newValue.map((user) => user._id);
                      formik.setFieldValue("cc", newIds);
                    }}
                    onBlur={() => formik.setFieldTouched("cc", true)}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => {
                        const { key, ...otherProps } = getTagProps({ index });
                        return (
                          <Chip
                            key={key}
                            label={getUserLabel(option)}
                            deleteIcon={<Close />}
                            sx={{
                              margin: "2px",
                              borderRadius: "4px",
                              "& .MuiChip-label": { fontSize: "12px" },
                            }}
                            {...otherProps}
                          />
                        );
                      })
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        placeholder="Select employees to CC"
                        error={formik.touched.cc && Boolean(formik.errors.cc)}
                        helperText={formik.touched.cc && formik.errors.cc}
                      />
                    )}
                    disabled={usersLoading}
                    noOptionsText={
                      usersLoading ? "Loading..." : "No users available"
                    }
                    className="custom-autocomplete"
                    // Fixed typo: Added missing closing brace
                  />
                </Box>
                <Box className="form-field input-field">
                  <Typography variant="subtitle1" component="label">
                    Ticket Description <span className="required">*</span>
                  </Typography>
                  <TextEditor
                    value={formik.values.description}
                    onChange={(newValue: string) =>
                      formik.setFieldValue("description", newValue)
                    }
                  />
                  {formik.touched.description && formik.errors.description && (
                    <FormHelperText error>
                      {formik.errors.description}
                    </FormHelperText>
                  )}
                </Box>
                <Box className="form-field-row">
                  <Box className="form-field input-field">
                    <Typography variant="subtitle1" component="label">
                      Priority <span className="required">*</span>
                    </Typography>
                    <FormControl
                      fullWidth
                      variant="outlined"
                      error={
                        formik.touched.priority &&
                        Boolean(formik.errors.priority)
                      }
                    >
                      <Select
                        name="priority"
                        value={formik.values.priority}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                      >
                        <MenuItem value="Low">Low</MenuItem>
                        <MenuItem value="Medium">Medium</MenuItem>
                        <MenuItem value="High">High</MenuItem>
                      </Select>
                      {formik.touched.priority && formik.errors.priority && (
                        <FormHelperText error>
                          {formik.errors.priority}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Box>
                  <Box className="form-field input-field">
                    <Typography variant="subtitle1" component="label">
                      Status <span className="required">*</span>
                    </Typography>
                    <FormControl
                      fullWidth
                      variant="outlined"
                      error={
                        formik.touched.status && Boolean(formik.errors.status)
                      }
                    >
                      <Select
                        name="status"
                        value={isEditing ? formik.values.status : "Open"}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        disabled={isEditing}
                      >
                        <MenuItem value="Open">Open</MenuItem>
                        <MenuItem value="Closed">Closed</MenuItem>
                        <MenuItem value="Reopened">Reopened</MenuItem>
                        <MenuItem value="OnHold">OnHold</MenuItem>
                      </Select>
                      {formik.touched.status && formik.errors.status && (
                        <FormHelperText error>
                          {formik.errors.status}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Box>
                </Box>
              </Box>
              <DialogActions className="dialog-actions">
                <Button
                  onClick={() => {
                    formik.resetForm();
                    onClose();
                  }}
                  className="cancel-button"
                  variant="outlined"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  className="submit-button"
                  disabled={
                    formik.isSubmitting ||
                    isLoading ||
                    departmentsLoading ||
                    categoriesLoading ||
                    usersLoading
                  }
                >
                  {isEditing ? "Update Ticket" : "Add Ticket"}
                </Button>
              </DialogActions>
            </form>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default AddTicketDialog;

"use client";

import { <PERSON>, Card, CardContent, Typo<PERSON>, <PERSON>, Avatar, Stack } from "@mui/material";
import "./TicketCard.scss";
import CommentIcon from "@mui/icons-material/Comment";
import { Circle, Event } from "@mui/icons-material";
import { useRouter } from "next/navigation";

interface TicketCardProps {
  _id: string;
  ticketId: string;
  title: string;
  assignedTo: string;
  userAvatar: string;
  updatedAt: string;
  commentsCount: number;
  status: "Open" | "OnHold" | "Reopened" | "Closed";
  priority: "High" | "Low" | "Medium";
  departmentName: string;
}

const getColor = (label: string) => {
  switch (label) {
    case "High":
      return { bg: "#ff1744" };
    case "Medium":
      return { bg: "#ffca28" };
    case "Low":
      return { bg: "#00e676" };
    case "Open":
      return { bg: "#f50057" };
    case "OnHold":
      return { bg: "#ffeb3b" };
    case "Reopened":
      return { bg: "#e91e63" };
    default:
      return { bg: "#ccc" };
  }
};

const getStatusColor = (label: string) => {
  switch (label) {
    case "Open":
      return { color: "#FD3995" };
    case "OnHold":
      return { color: "#FFC107" };
    case "Reopened":
      return { color: "#AB47BC" };
    default:
      return { color: "#ccc" };
  }
};

const getRelativeTime = (dateString: string): string => {
  // Check if dateString is valid
  if (!dateString || typeof dateString !== "string") {
    return "Unknown time";
  }

  try {
    let updatedDate: Date;

    // Handle DD/MM/YYYY, HH:mm:ss format (e.g., "27/05/2025, 15:48:22")
    const dateTimeMatch = dateString.match(/^(\d{2})\/(\d{2})\/(\d{4}),\s(\d{2}:\d{2}:\d{2})$/);
    if (dateTimeMatch) {
      const [, day, month, year, time] = dateTimeMatch;
      // Convert to ISO-like format: YYYY-MM-DDTHH:mm:ss
      const isoDateString = `${year}-${month}-${day}T${time}`;
      updatedDate = new Date(isoDateString);
    } else {
      // Try parsing as-is for other formats
      updatedDate = new Date(dateString);
    }

    // Check if the date is invalid
    if (isNaN(updatedDate.getTime())) {
      return "Invalid date";
    }

    const now = new Date();
    const diffInMs = now.getTime() - updatedDate.getTime();

    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    const diffInWeeks = Math.floor(diffInMs / (1000 * 60 * 60 * 24 * 7));
    const diffInMonths = Math.floor(diffInMs / (1000 * 60 * 60 * 24 * 30));
    const diffInYears = Math.floor(diffInMs / (1000 * 60 * 60 * 24 * 365));

    if (diffInMinutes < 1) {
      return "Updated just now";
    } else if (diffInMinutes < 60) {
      return `Updated ${diffInMinutes} minute${diffInMinutes === 1 ? "" : "s"} ago`;
    } else if (diffInHours < 24) {
      return `Updated ${diffInHours} hour${diffInHours === 1 ? "" : "s"} ago`;
    } else if (diffInDays < 7) {
      return `Updated ${diffInDays} day${diffInDays === 1 ? "" : "s"} ago`;
    } else if (diffInWeeks < 4) {
      return `Updated ${diffInWeeks} week${diffInWeeks === 1 ? "" : "s"} ago`;
    } else if (diffInMonths < 12) {
      return `Updated ${diffInMonths} month${diffInMonths === 1 ? "" : "s"} ago`;
    } else {
      return `Updated ${diffInYears} year${diffInYears === 1 ? "" : "s"} ago`;
    }
  } catch (error) {
    console.error("Error parsing date:", error);
    return "Unknown time";
  }
};

const TicketCard = ({
  _id,
  ticketId,
  title,
  assignedTo,
  userAvatar,
  updatedAt,
  commentsCount,
  status,
  priority,
  departmentName,
}: TicketCardProps) => {
  const router = useRouter();

  const handleTitleClick = () => {
    router.push(`/ticket-details?id=${_id}`);
  };

  return (
    <Card className="issue-card">
      <Box className="issue-card-header">
        <Typography variant="body2" color="primary" fontWeight={600}>
          {departmentName}
        </Typography>
        <Chip
          icon={
            <Circle
              sx={{
                fontSize: "6px !important",
                color: "#FFF !important",
              }}
            />
          }
          size="small"
          label={priority}
          sx={{
            bgcolor: getColor(priority).bg,
            color: "#fff",
            fontWeight: 600,
            fontSize: "12px",
            padding: "0.25rem 0.45rem",
            letterSpacing: "0.5px",
            borderRadius: "4px",
            "& .MuiChip-icon": {
              margin: 0,
            },
          }}
        />
      </Box>
      <CardContent className="issue-card-content">
        <Chip
          size="small"
          label={`${ticketId}`}
          sx={{
            fontWeight: 600,
            background: "#1B84FF",
            color: "#FFF",
            fontSize: "12px",
            padding: "0.25rem 0.45rem",
            letterSpacing: "0.5px",
            marginBottom: "0.5rem",
          }}
        />

        <Box className="issue-card-title">
          <Typography
            className="issue-card-title-text"
            onClick={handleTitleClick}
            sx={{ cursor: "pointer" }}
          >
            {title}
          </Typography>
          <Chip
            size="small"
            label={status}
            sx={{
              background: "transparent",
              padding: "0.25rem 0.45rem",
              fontWeight: "600",
              letterSpacing: "0.5px",
              borderRadius: "4px",
              color: getStatusColor(status).color,
              border: `1px solid ${getStatusColor(status).color}`,
              marginLeft: "0.5rem",
            }}
          />
        </Box>

        <Stack direction="row" alignItems="center" spacing={1} mt={1}>
          <Avatar
            src={userAvatar}
            alt={assignedTo}
            sx={{ width: 20, height: 20 }}
          />
          <Typography className="available">
            Assigned to{" "}
            <span className="text-assigned-to">
              {assignedTo === "N/A" ? "N/A" : assignedTo}
            </span>
          </Typography>
          <Event sx={{ fontSize: 16, color: "#6B7280" }} />
          <Typography variant="body2" sx={{ color: "#555" }}>
            {getRelativeTime(updatedAt)}
          </Typography>
          <CommentIcon sx={{ fontSize: 16, ml: 0, color: "#555" }} />
          <Typography variant="body2" sx={{ color: "#555" }}>
            {commentsCount} Comments
          </Typography>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default TicketCard;
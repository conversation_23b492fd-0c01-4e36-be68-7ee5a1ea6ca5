"use client";
import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Button,
  Box,
  IconButton,
  Typography,
  FormControl,
  FormLabel,
  Select,
} from "@mui/material";
import { Close as CloseIcon, CalendarToday } from "@mui/icons-material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import dayjs, { type Dayjs } from "dayjs";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import {
  addPersonalInfo,
  updatePersonalInfo,
} from "@/app/services/users.service";
import { toast } from "react-toastify";

// Add enum for religion options
enum Religion {
  HINDU = "Hindu",
  MUSLIM = "Muslim",
  CHRISTIAN = "Christian",
  Sikh = "Sikh",
  JAIN = "Jain",
}

const validationSchema = Yup.object({
  passportNo: Yup.string()
    .required("Passport No is required")
    .matches(
      /^[A-Z][0-9]{7}$/,
      "Passport number must start with a letter followed by 7 digits"
    ),
  passportExp: Yup.string()
    .required("Passport Expiry Date is required")
    .test(
      "is-future",
      "Passport expiry date must be in the future",
      (value) => {
        if (!value) return false;
        const expiryDate = dayjs(value, "MM/DD/YYYY");
        return expiryDate.isAfter(dayjs(), "day");
      }
    ),
  nationality: Yup.string().required("Nationality is required"),
  maritalStatus: Yup.string().required("Marital Status is required"),
  children: Yup.number().when("maritalStatus", {
    is: "Married",
    then: () =>
      Yup.number()
        .min(0, "Number of children cannot be negative")
        .required("Number of children is required"),
    otherwise: () => Yup.number().notRequired(),
  }),
  spouseEmployment: Yup.string().when("maritalStatus", {
    is: "Married",
    then: () => Yup.string(),
    otherwise: () => Yup.string().notRequired(),
  }),
});

interface PersonalInfoDialogProps {
  open: boolean;
  onClose: () => void;
  initialData: {
    userId?: string;
    personalInfoId?: string;
    passportNo?: string;
    passportExp?: string;
    nationality?: string;
    religion?: string;
    maritalStatus: string;
    spouseEmployment?: string;
    children?: number;
  };
  onSave: (data: {
    passportNo?: string;
    passportExp?: string;
    nationality?: string;
    religion?: string;
    maritalStatus: string;
    spouseEmployment?: string;
    children?: number;
  }) => void;
  mode: "add" | "edit";
}

const PersonalInfoDialog: React.FC<PersonalInfoDialogProps> = ({
  open,
  onClose,
  initialData,
  onSave,
  mode,
}) => {
  const [passportExpDate, setPassportExpDate] = useState<Dayjs | null>(
    initialData.passportExp && initialData.passportExp !== "N/A"
      ? dayjs(initialData.passportExp, "MM/DD/YYYY")
      : null
  );

  const initialValues = {
    passportNo: initialData.passportNo || "",
    passportExp: initialData.passportExp || "",
    nationality: initialData.nationality || "Indian", // Set default to "Indian" but allow override from initialData
    religion: initialData.religion || "",
    maritalStatus: initialData.maritalStatus || "",
    spouseEmployment:
      initialData.maritalStatus === "Married"
        ? initialData.spouseEmployment || ""
        : "",
    children:
      initialData.maritalStatus === "Married" ? initialData.children || 0 : 0,
  };

  const handleDateChange = (
    date: Dayjs | null,
    setFieldValue: (field: string, value: string) => void
  ) => {
    setPassportExpDate(date);
    setFieldValue("passportExp", date ? date.format("MM/DD/YYYY") : "");
  };

  const labelSx = {
    color: "#202C4B",
    fontSize: "14px",
    fontWeight: 500,
  };

  const inputSx = {
    "& .MuiOutlinedInput-root": {
      borderRadius: "5px",
      borderColor: "#E5E7EB",
    },
    "& .MuiInputLabel-root": {
      color: "#202C4B",
      fontSize: "14px",
    },
  };

  const handleSubmit = async (values: typeof initialValues) => {
    const personalInfoData = {
      passportNumber: values.passportNo,
      passportExpiry: values.passportExp,
      nationality: values.nationality,
      religion: values.religion,
      maritalStatus: values.maritalStatus,
      employementSpouse: values.spouseEmployment,
      childrens: values.children || 0,
    };

    try {
      if (mode === "add" && initialData.userId) {
        const addData = {
          ...personalInfoData,
          userId: initialData.userId,
        };
        await addPersonalInfo(addData);
        toast.success("Personal info added successfully");
      } else if (mode === "edit" && initialData.personalInfoId) {
        await updatePersonalInfo(initialData.personalInfoId, {
          ...personalInfoData,
          userId: initialData.userId || "",
        });
        toast.success("Personal info updated successfully");
      }
      onSave(values);
      onClose();
    } catch (error) {
      console.error(`${mode === "add" ? "Add" : "Update"} failed:`, error);
      // toast.error("Failed to save personal info");
    }
  };

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== "backdropClick") {
          onClose();
        }
      }}
      disableEscapeKeyDown
      sx={{
        "& .MuiDialog-paper": {
          width: "798px",
          maxWidth: "798px",
          borderRadius: "8px",
          overflow: "hidden",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "16px 24px",
          borderBottom: "1px solid #E5E7EB",
        }}
      >
        <Typography
          variant="h6"
          sx={{ fontSize: "18px", fontWeight: 600, color: "#202C4B" }}
        >
          {mode === "add" ? "Add Personal Info" : "Edit Personal Info"}
        </Typography>
        <IconButton onClick={onClose} size="small" sx={{ padding: "4px" }}>
          <CloseIcon sx={{ color: "#6B7280", fontSize: "20px" }} />
        </IconButton>
      </DialogTitle>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ setFieldValue, errors, touched, values }) => {
          const handleMaritalStatusChange = (
            e: React.ChangeEvent<any>,
            setFieldValue: (field: string, value: any) => void
          ) => {
            const value = e.target.value;
            setFieldValue("maritalStatus", value);

            // Reset spouse and children fields if not married
            if (value !== "Married") {
              setFieldValue("spouseEmployment", "");
              setFieldValue("children", 0);
            }
          };

          return (
            <Form>
              <DialogContent sx={{ padding: "24px" }}>
                <Box
                  sx={{ display: "flex", flexDirection: "column", gap: "16px" }}
                >
                  <Box sx={{ display: "flex", gap: "16px" }}>
                    <Box sx={{ flex: 1 }}>
                      <FormLabel sx={{ ...labelSx, display: "block", mb: 1 }}>
                        Passport No <span style={{ color: "#FF0000" }}>*</span>
                      </FormLabel>
                      <Field
                        as={TextField}
                        name="passportNo"
                        fullWidth
                        variant="outlined"
                        size="small"
                        placeholder="Enter passport number"
                        sx={inputSx}
                        error={touched.passportNo && !!errors.passportNo}
                        helperText={<ErrorMessage name="passportNo" />}
                      />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <FormLabel sx={{ ...labelSx, display: "block", mb: 1 }}>
                        Passport Expiry Date{" "}
                        <span style={{ color: "#FF0000" }}>*</span>
                      </FormLabel>
                      <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <DatePicker
                          value={passportExpDate}
                          onChange={(date) =>
                            handleDateChange(date, setFieldValue)
                          }
                          format="MM/DD/YYYY"
                          slots={{ openPickerIcon: CalendarToday }}
                          minDate={dayjs()} // Add this line to disable past dates
                          slotProps={{
                            textField: {
                              size: "small",
                              fullWidth: true,
                              placeholder: "MM/DD/YYYY",
                              name: "passportExp",
                              error:
                                touched.passportExp && !!errors.passportExp,
                              helperText: <ErrorMessage name="passportExp" />,
                              sx: {
                                "& .MuiOutlinedInput-root": {
                                  borderRadius: "5px",
                                  borderColor: "#E5E7EB",
                                },
                              },
                            },
                          }}
                        />
                      </LocalizationProvider>
                    </Box>
                  </Box>

                  <Box sx={{ display: "flex", gap: "16px" }}>
                    <Box sx={{ flex: 1 }}>
                      <FormLabel sx={{ ...labelSx, display: "block", mb: 1 }}>
                        Nationality <span style={{ color: "#FF0000" }}>*</span>
                      </FormLabel>
                      <Field
                        as={TextField}
                        name="nationality"
                        fullWidth
                        variant="outlined"
                        size="small"
                        placeholder="Enter nationality"
                        sx={inputSx}
                        error={touched.nationality && !!errors.nationality}
                        helperText={<ErrorMessage name="nationality" />}
                      />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <FormLabel sx={{ ...labelSx, display: "block", mb: 1 }}>
                        Religion <span style={{ color: "#FF0000" }}>*</span>
                      </FormLabel>
                      <Field
                        as={Select}
                        name="religion"
                        fullWidth
                        variant="outlined"
                        size="small"
                        sx={inputSx}
                        error={touched.religion && !!errors.religion}
                      >
                        <MenuItem value="">Select Religion</MenuItem>
                        {Object.values(Religion).map((religion) => (
                          <MenuItem key={religion} value={religion}>
                            {religion}
                          </MenuItem>
                        ))}
                      </Field>
                      <Typography
                        sx={{ color: "#d32f2f", fontSize: "12px", mt: 0.5 }}
                      >
                        <ErrorMessage name="religion" />
                      </Typography>
                    </Box>
                  </Box>

                  <Box>
                    <FormLabel sx={{ ...labelSx, display: "block", mb: 1 }}>
                      Marital Status <span style={{ color: "#FF0000" }}>*</span>
                    </FormLabel>
                    <FormControl fullWidth size="small" sx={inputSx}>
                      <Field
                        as={Select}
                        name="maritalStatus"
                        displayEmpty
                        sx={{ borderRadius: "5px" }}
                        error={touched.maritalStatus && !!errors.maritalStatus}
                        onChange={(e: React.ChangeEvent<any>) =>
                          handleMaritalStatusChange(e, setFieldValue)
                        }
                      >
                        <MenuItem value="">Select</MenuItem>
                        <MenuItem value="Single">Single</MenuItem>
                        <MenuItem value="Married">Married</MenuItem>
                        <MenuItem value="Divorced">Divorced</MenuItem>
                        <MenuItem value="Widowed">Widowed</MenuItem>
                      </Field>
                      <Typography
                        sx={{ color: "#d32f2f", fontSize: "12px", mt: 0.5 }}
                      >
                        <ErrorMessage name="maritalStatus" />
                      </Typography>
                    </FormControl>
                  </Box>

                  {values.maritalStatus === "Married" && (
                    <Box sx={{ display: "flex", gap: "16px" }}>
                      <Box sx={{ flex: 1 }}>
                        <FormLabel sx={{ ...labelSx, display: "block", mb: 1 }}>
                          Employment Spouse
                        </FormLabel>
                        <Field
                          as={TextField}
                          name="spouseEmployment"
                          fullWidth
                          variant="outlined"
                          size="small"
                          placeholder="Enter spouse employment"
                          sx={inputSx}
                        />
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <FormLabel sx={{ ...labelSx, display: "block", mb: 1 }}>
                          No. of Children
                        </FormLabel>
                        <Field
                          as={TextField}
                          name="children"
                          type="number"
                          fullWidth
                          variant="outlined"
                          size="small"
                          placeholder="Enter number of children"
                          sx={inputSx}
                          error={touched.children && !!errors.children}
                          helperText={<ErrorMessage name="children" />}
                        />
                      </Box>
                    </Box>
                  )}
                </Box>
              </DialogContent>

              <DialogActions
                sx={{
                  padding: "16px 24px",
                  borderTop: "1px solid #E5E7EB",
                  justifyContent: "flex-end",
                  gap: "12px",
                }}
              >
                <Button
                  onClick={onClose}
                  variant="outlined"
                  sx={{
                    borderColor: "#E5E7EB",
                    color: "#6B7280",
                    textTransform: "none",
                    borderRadius: "5px",
                    padding: "6px 16px",
                    minWidth: "80px",
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  sx={{
                    backgroundColor: "#F26522",
                    color: "#FFF",
                    textTransform: "none",
                    borderRadius: "5px",
                    padding: "6px 16px",
                    minWidth: "80px",
                    "&:hover": { backgroundColor: "#E55A1B" },
                  }}
                >
                  {mode === "add" ? "Add" : "Save"}
                </Button>
              </DialogActions>
            </Form>
          );
        }}
      </Formik>
    </Dialog>
  );
};

export default PersonalInfoDialog;

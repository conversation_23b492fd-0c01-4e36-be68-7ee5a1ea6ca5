type CandidateStatus = "New" | "Scheduled" | "Interviewed" | "Offered" | "Hired" | "Rejected";

interface Candidate {
    name: string;
    candidateId: string;
    email: string;
    role: string;
    date: string;
    status: CandidateStatus;
    imageUrl: string;
}

export const CandidatesData: Candidate[] = [
    {
        name: "<PERSON>",
        candidateId: "CAN-001",
        email: "<EMAIL>",
        role: "Software Engineer",
        date: "12 Sep 2024",
        status: "New",
        imageUrl: "https://example.com/avatars/michael.jpg",
    },
    {
        name: "<PERSON>",
        candidateId: "CAN-002",
        email: "<EMAIL>",
        role: "UI/UX Designer",
        date: "05 Oct 2023",
        status: "Scheduled",
        imageUrl: "https://example.com/avatars/sarah.jpg",
    },
    {
        name: "<PERSON>",
        candidateId: "CAN-003",
        email: "<EMAIL>",
        role: "Product Manager",
        date: "10 Oct 2023",
        status: "Interviewed",
        imageUrl: "https://example.com/avatars/james.jpg",
    },
    {
        name: "<PERSON>",
        candidateId: "CAN-004",
        email: "<EMAIL>",
        role: "DevOps Engineer",
        date: "15 Oct 2023",
        status: "Offered",
        imageUrl: "https://example.com/avatars/emily.jpg",
    },
    {
        name: "Raj Patel",
        candidateId: "CAN-005",
        email: "<EMAIL>",
        role: "Frontend Developer",
        date: "20 Oct 2023",
        status: "Hired",
        imageUrl: "https://example.com/avatars/raj.jpg",
    },
    {
        name: "Lisa Wong",
        candidateId: "CAN-006",
        email: "<EMAIL>",
        role: "Data Scientist",
        date: "01 Nov 2023",
        status: "Rejected",
        imageUrl: "https://example.com/avatars/lisa.jpg",
    },
    {
        name: "David Smith",
        candidateId: "CAN-007",
        email: "<EMAIL>",
        role: "Backend Developer",
        date: "05 Nov 2023",
        status: "Hired",
        imageUrl: "https://example.com/avatars/david.jpg",
    },
    {
        name: "Anna Kowalski",
        candidateId: "CAN-008",
        email: "<EMAIL>",
        role: "QA Engineer",
        date: "10 Nov 2023",
        status: "Scheduled",
        imageUrl: "https://example.com/avatars/anna.jpg",
    },
    {
        name: "Tom Wilson",
        candidateId: "CAN-009",
        email: "<EMAIL>",
        role: "System Administrator",
        date: "15 Nov 2023",
        status: "New",
        imageUrl: "https://example.com/avatars/tom.jpg",
    },
    {
        name: "Maria Garcia",
        candidateId: "CAN-010",
        email: "<EMAIL>",
        role: "Mobile Developer",
        date: "20 Nov 2023",
        status: "Hired",
        imageUrl: "https://example.com/avatars/maria.jpg",
    }
];
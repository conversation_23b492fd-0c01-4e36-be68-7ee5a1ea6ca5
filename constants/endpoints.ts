export const API_ENDPOINTS = {

    // Departement
    DEPARTMENT: "/departments",

    //Training
    TRAINING: "/training",

    //TRAINERS
    TRAINERS: "/trainer",

    //TrainingTYpe
    TRAINING_TYPE: "/trainingtype",

    // Departement
    DESIGNATION: "/designation",

    // Policies
    POLICIES: "/policies",

    //Holidays
    HOLIDAY: "/holiday",

    // Resignation
    RESIGNATION: "/resignation",

    // Termination
    TERMINATION: "/termination",

    // Users
    USERS: "/users",
    PERSONAL_INFO: "/users/personal-info",
    EMERGENCY_CONTACT: "/users/emergencycontact",
    BANK: "/users/bank",
    FAMILY: "/users/family",
    EXPERIENCE: "/users/experience",
    EDUCATION: "/users/education",
    ASSETS_ISSUE: "/users/assetsissue",
    BANK_STATUTORY: "/users/statutory",
    // Assets
    ASSETS: "/users/assets",
    ASSET: "/assets",
    ASSET_ASSIGN: "/assets/assign",

    //attendance
    ATTENDANCE: "/attendance",

    //attendance
    ATTENDANCE_BY_ID: "/attendance/singleattendance",

    //Punch In 
    PUNCH_IN: "/attendance/punch-in",

    //Punch Out
    PUNCH_OUT: "/attendance/punch-out",

    //leave
    LEAVE: "/leave",

    LEAVE_EMP: "/leave/employee",
    //leaveby id
    LEAVE_BY_ID: "/leave/singleleave",

    //leave setting
    LEAVE_SETTING: "/leavesetting",

    //ATTENDANCE SUMMARY
    ATTENDANCE_SUMMARY: "/attendance/summary/{date}",

    //Performance
    PERFORMANCE: "/performance",
    PERFORMANCE_APPRAISAL: "/performance-appraisal",


    PROFESSIONAL_EXCELLENCE: "/performancereview/professionalexcellence",
    PERSONAL_EXCELLENCE: "/performancereview/personalexcellence",
    SPECIAL_ACHIEVEMENT: "/performancereview/specialachievment",
    COMMENT_ON_ROLE: "/performancereview/commentonrole",
    COMMENT_ON_REVIEW_ROLE: "/performancereview/reviewonrole",
    COMMENT_ON_ROLE_BY_RO: "/performancereview/reviewbyro",
    COMMENT_ON_ROLE_BY_HOD: "/performancereview/reviewbyhod",
    ASSESSMENT_BY_RO: "/performancereview/assessmentbyro",
    ASSESSMENT_BY_HOD: "/performancereview/assessmentbyhod",
    PERFORMANCE_APPROVAL: "/performancereview/approval",
    PERSONAL_GOALS: "/performancereview/personalgoals",
    PERSONAL_UPDATES: "/performancereview/personalupdates",
    PERFORMANCE_TRAINING: "/performancereview/training",
    PERFORMANCE_OTHERS: "/performancereview/others",
    PERFORMANCE_REVIEW_BY_EMPLOYEE_ID: "/performancereview/employee",
    PROFESSIONAL_GOALS: "/performancereview/professionalgoals",
    
    PERFORMANCE_REVIEW: "/performancereview",
    //change password
    CHANGE_PASSWORD: "/auth/change-password",
    
    //CLIENTS
    CLIENTS: "/clients",
    
    //PROJECTS
    PROJECTS: "/projects",
    TASK: "/task",
    STATUS_BOARD: "/taskboard",

    //PAYROLL
    PAYROLLITEM: "/payrollitem",

    PAYROLL: "/payroll",

    //jOBS
    JOB: "/job",

    //Refrerral
    REFERRAL: "/refferals",

    //Tickets
    TICKETS: "/ticket",

    TICKETS_CATEGORY: "/ticketcategory",

    TICKETS_CATEGORY_COUNT: "/ticketcategory/category-ticket-counts",


    //Goals type
    GOAL_TYPE: "/goaltype",
    GOAL: "/goals",

    // Promotion
    PROMOTION: "/promotion",


    // Roles
    ROLES: "/roles",
    //signup 
    SIGNUP: "/users",
    //login
    LOGIN: "/auth/login",

    //FORGET PASSWORD
    FORGET_PASSWORD: "/auth/forgot-password",

    //RESET PASSWORD
    RESET_PASSWORD: "/auth/reset-password",

    // VersionSetting
    VERSION: "/setting/version",

    //Company Setting
    COMPANY: "/setting/company",

    //NoticePeriod Setting
    NOTICE_PERIOD: "/setting/notice-period",

    //presigned url
    GET_PRE_SIGNED_URL: "/utils/upload-file",

    //Time Sheet
    TIME_SHEET: "/timesheet",
}